package cn.abcyun.cis.dispensing.util;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeConstants;
import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispenseConst;
import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispensingFormItemGoodsSnap;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.*;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.config.GoodsClinicTraceCodeConfigView;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.config.GoodsConfigView;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.config.TraceCodeGoodsType;
import cn.abcyun.bis.rpc.sdk.cis.model.patientorder.BusinessLockReq;
import cn.abcyun.bis.rpc.sdk.cis.model.patientorder.BusinessLockWithStatusVO;
import cn.abcyun.bis.rpc.sdk.his.model.advice.AdviceRuleType;
import cn.abcyun.bis.rpc.sdk.his.model.advice.AdviceStatus;
import cn.abcyun.cis.commons.model.CisPatientInfo;
import cn.abcyun.cis.commons.rpc.charge.*;
import cn.abcyun.cis.commons.rpc.crm.PatientInfo;
import cn.abcyun.cis.commons.rpc.outpatient.ExtendDiagnosisInfo;
import cn.abcyun.cis.commons.rpc.outpatient.UsageInfo;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.commons.util.DateUtils;
import cn.abcyun.cis.commons.util.*;
import cn.abcyun.cis.dispensing.base.exception.DispenseSheetChangedException;
import cn.abcyun.cis.dispensing.base.exception.DispensingServiceError;
import cn.abcyun.cis.dispensing.base.exception.DispensingServiceException;
import cn.abcyun.cis.dispensing.controller.DTOConverter;
import cn.abcyun.cis.dispensing.domain.*;
import cn.abcyun.cis.dispensing.service.dto.DispenseUsageInfo;
import cn.abcyun.cis.dispensing.service.dto.DispensingFormItemBasicView;
import cn.abcyun.cis.dispensing.service.dto.DispensingFormItemView;
import cn.abcyun.cis.dispensing.service.dto.DispensingSheetView;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Slf4j
public class DispensingUtils {


    public static BigDecimal toDispenseUnitCount(boolean isDismounting,
                                                 BigDecimal pieceNum,
                                                 BigDecimal stockPieceCount,
                                                 BigDecimal stockPackageCount) {
        pieceNum = MathUtils.wrapBigDecimalOrZero(pieceNum);
        stockPackageCount = MathUtils.wrapBigDecimalOrZero(stockPackageCount);
        if (isDismounting) {
            // 全转成小单位
            return pieceNum.multiply(stockPackageCount).add(stockPieceCount);
        } else {
            // 全转成大单位
            return stockPieceCount.divide(pieceNum, 2, RoundingMode.DOWN).add(stockPackageCount);
        }
    }

    public static void checkChargeSheetIsRefunding(List<BusinessLockWithStatusVO> businessLockWithStatusVOS) {

        if (org.apache.commons.collections.CollectionUtils.isEmpty(businessLockWithStatusVOS)) {
            return;
        }

        BusinessLockWithStatusVO chargeSheetRefundLockVO = businessLockWithStatusVOS.stream()
                .filter(businessLockWithStatusVO -> businessLockWithStatusVO.getValue() != null && businessLockWithStatusVO.getValue().getBusinessScene() == BusinessLockReq.ChargeBusinessScene.CHARGE_SHEET_REFUND)
                .findFirst()
                .orElse(null);

        if (Objects.isNull(chargeSheetRefundLockVO)) {
            return;
        }

        throw new DispensingServiceException(DispensingServiceError.CHARGE_SHEET_IS_REFUNDING);
    }

    /**
     * 开关常量
     */
    public static class SwitchFlag {
        public static final int OFF = 0;
        public static final int ON = 1;
    }

    /**
     * 新建或修改发药单 被触发的场景
     */
    public static class CreateDispenseSheetFromType {
        public static final int FROM_CHARGE_MESSAGE = 0; //收费消息
        public static final int FROM_CHARGE_DIRECT = 1;//收费同时发药
    }

    /**
     * 推送到发药机的场景
     */
    public static class SmartDispensingScene {
        /**
         * 发药后
         */
        public static final int DISPENSE = 0;
        /**
         * 收费后
         */
        public static final int CHARGE = 1;
        /**
         * 审核后推送到发药机
         */
        public static final int AUDIT = 2;
    }

    /***
     * 删除标记
     * */
    public static class DeleteFlag {
        public static final int NOT_DELETED = 0; // 未删除
        public static final int DELETED = 1;//收费同时发药
    }

    /**
     * 新需求需要刷选的发药单处方类型:中药饮片处方  中药颗粒处方 中西药处方 输注处方 材料处方
     * Sheet和Form上都会打上这个标记 用于刷选 ，目前刷选的地方有两个：发药打列表 和打印的地方
     */
    public static class DispensingTag {
        /**
         * 处方类型打的TAG
         */
        public static final int DISPENSING_TAG_WESTERN = 0x00000001; // tag SourceFormType.PRESCRIPTION_WESTERN
        public static final int DISPENSING_TAG_CHINESE_PIECES = 0x00000002; // tag SourceFormType.PRESCRIPTION_CHINESE and formItem has 中药颗粒
        public static final int DISPENSING_AG_CHINESE_GRANULE = 0x00000004; // tag SourceFormType.PRESCRIPTION_CHINESE and formItem has 中药颗粒
        public static final int DISPENSING_TAG_INFUSION = 0x00000008; // tag SourceFormType.PRESCRIPTION_INFUSION
        public static final int DISPENSING_TAG_MATERIAL = 0x00000010; // SourceFormType.ADDITIONAL_FORM,SourceFormType.ADDITIONAL_SALE_PRODUCT_FORM,SourceFormType.MATERIAL,SourceFormType.GIFT_PRODUCT,SourceFormType.COMPOSE_PRODUCT
        /**
         * 标识发药单有进行过发药
         */
        public static final int DISPENSING_TAG_HAS_DISPENSE = 1 << 5;
        /**
         * 标识发药单中存在药品在发药前就已经确定了批次
         * <p>
         * 影响：只能按照原批次进行重新发药
         */
        public static final int DISPENSING_TAG_BATCH_PRE_LOCKED = 1 << 6;
        /**
         * 标识发药单有进行过退药
         */
        public static final int DISPENSING_TAG_HAS_UNDISPENSE = 1 << 7;
        /**
         * 标识发药单存在可退药项
         */
        public static final int DISPENSING_TAG_HAS_UNDISPENSABLE = 1 << 8;
        /**
         * 医院发药单重新发药，已退的单据重新生成新的发药单，之前的老发药单已重新生成标识
         */
        public static final int DISPENSING_TAG_HOSPITAL_RENEW_OLD_SHEET_FLAG = 1 << 9;

        /**
         * 1.标记这个发药单是否刷过医保，如果产品想要展示方便。因为是bit位,不能刷选。
         * 2.理论上未发药的发药单不会太多，未发药的刷医保的发药单更少，
         * 直接让用户刷选未发药的自己再查看
         * 3.药房-发药单，如果对应收费单是医保结算的，部分发药时，点击「发药」时，弹窗提示:
         * 前端检查这个bit位知道是否刷的医保
         */
        public static final int DISPENSING_TAG_SHEBAO_PAY_FLAG = 1 << 10;

    }

    public static List<Integer> HOSPITAL_UNDISPENSE_DISPLAY_STATUS =
            Lists.newArrayList(DispenseConst.Status.UNDISPENSED, DispenseConst.Status.APPLY_UNDISPNSE,
                    DispenseConst.Status.APPLY_UNDISPENSE_REJECT, DispenseConst.Status.REAPPLY_UNDISPENSE);
    public static List<Integer> HOSPITAL_DISPENSE_DISPLAY_STATUS =
            Lists.newArrayList(DispenseConst.Status.WAITING, DispenseConst.Status.DISPENSED,
                    DispenseConst.Status.APPLY_DISPENSE_REJECT, DispenseConst.Status.REAPPLY_DISPENSE,
                    DispenseConst.Status.RECORD_NOT_DISPENSE, DispenseConst.Status.STOCK_NOT_DISPENSE);

    public static List<Integer> remarkUseSpecialTypeList = Lists.newArrayList((int) GoodsConst.GoodsType.MEDICINE,
            (int) GoodsConst.GoodsType.EYE);

    public static List<DispensingForm> getDispensingForms(DispensingSheet dispensingSheet) {
        if (dispensingSheet == null || dispensingSheet.getDispensingForms() == null) {
            return new ArrayList<>();
        }
        return dispensingSheet.getDispensingForms();
    }


    public static List<DispensingFormItem> getDispensingFormItems(DispensingSheet dispensingSheet) {
        if (dispensingSheet == null || dispensingSheet.getDispensingForms() == null) {
            return new ArrayList<>();
        }
        return dispensingSheet.getDispensingForms()
                .stream()
                .filter(dispensingForm -> dispensingForm.getDispensingFormItems() != null)
                .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream())
                .collect(Collectors.toList());
    }

    public static List<DispensingFormItem> getDispensingFormItems(DispensingForm dispensingForm) {
        if (dispensingForm == null || dispensingForm.getDispensingFormItems() == null) {
            return new ArrayList<>();
        }
        return dispensingForm.getDispensingFormItems();
    }

    public static List<DispensingFormItemView> getDispensingFormItemViews(DispensingSheetView dispensingSheet, Set<String> dispensingFormIdSet) {
        if (dispensingSheet == null || dispensingSheet.getDispensingForms() == null) {
            return new ArrayList<>();
        }
        return dispensingSheet.getDispensingForms()
                .stream()
                .filter(dispensingForm -> dispensingForm.getDispensingFormItems() != null)
                .filter(dispensingForm -> dispensingFormIdSet != null ? dispensingFormIdSet.contains(dispensingForm.getId()) : true)
                .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream())
                .collect(Collectors.toList());
    }

    public static List<DispensingFormItemView> getDispensingFormItemViewsFilterMaterialAndCompose(DispensingSheetView dispensingSheet) {
        if (dispensingSheet == null || dispensingSheet.getDispensingForms() == null) {
            return new ArrayList<>();
        }
        return dispensingSheet.getDispensingForms()
                .stream()
                .filter(dispensingForm -> dispensingForm.getSourceFormType() != Constants.SourceFormType.MATERIAL
                        && dispensingForm.getSourceFormType() != Constants.SourceFormType.COMPOSE_PRODUCT)
                .filter(dispensingForm -> dispensingForm.getDispensingFormItems() != null)
                .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream())
                .collect(Collectors.toList());
    }

    public static List<DispensingFormItemView> getDispensingFormItemViewsFilterMaterialAndProductAndCompose(DispensingSheetView dispensingSheet, Set<String> dispensingFormIdSet) {
        if (dispensingSheet == null || dispensingSheet.getDispensingForms() == null) {
            return new ArrayList<>();
        }
        return dispensingSheet.getDispensingForms()
                .stream()
                .filter(dispensingForm -> (dispensingForm.getSourceFormType() != Constants.SourceFormType.MATERIAL
                        && dispensingForm.getSourceFormType() != Constants.SourceFormType.ADDITIONAL_SALE_PRODUCT_FORM
                        && dispensingForm.getSourceFormType() != Constants.SourceFormType.COMPOSE_PRODUCT
                        && dispensingForm.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_EXTERNAL
                        && dispensingForm.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_OPTOMETRIST
                ))
                .filter(dispensingForm -> dispensingForm.getDispensingFormItems() != null)
                .filter(dispensingForm -> dispensingFormIdSet != null ? dispensingFormIdSet.contains(dispensingForm.getId()) : true)
                .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream())
                .collect(Collectors.toList());
    }


    public static boolean isPartDispensedSheet(DispensingSheet dispensingSheet) {
        List<DispensingFormItem> dispensingFormItems = getDispensingFormItems(dispensingSheet);
        return dispensingSheet.getStatus() == DispensingSheet.Status.WAITING &&
                dispensingFormItems.stream().anyMatch(dispensingFormItem -> DispensingFormItem.Status.hasDispense.contains(dispensingFormItem.getStatus()));
    }

    /**
     * 发药单是否可以退药
     * 1. 发药单已发药
     * 2. 发药单待发药，但是发药的 formItem 有可退的
     */
    public static boolean dispensingSheetCanUnDispense(DispensingSheet dispensingSheet) {
        List<DispensingFormItem> dispensingFormItems = getDispensingFormItems(dispensingSheet);
        boolean hasUnDispensableFormItem = dispensingFormItems.stream().anyMatch(dispensingFormItem -> DispensingFormItem.Status.unDispenseable.contains(dispensingFormItem.getStatus()));
        return (dispensingSheet.getStatus() == DispensingSheet.Status.DISPENSED)
                || (dispensingSheet.getStatus() == DispensingSheet.Status.WAITING && hasUnDispensableFormItem);
    }

    public static boolean isPartDispensedForm(DispensingForm dispensingForm) {
        List<DispensingFormItem> dispensingFormItems = getDispensingFormItems(dispensingForm);
        return dispensingFormItems.stream().anyMatch(dispensingFormItem -> dispensingFormItem.getStatus() == DispensingFormItem.Status.DISPENSED) &&
                dispensingFormItems.stream().anyMatch(dispensingFormItem -> DispensingFormItem.Status.dispenseable.contains(dispensingFormItem.getStatus()));
    }

    public static int getSheetChineseMedicineUndispenseType(DispensingSheet dispensingSheet) {
        List<DispensingFormItem> dispensingFormItems = getDispensingFormItems(dispensingSheet);
        List<DispensingFormItem> undispensedChineseMedicineFormItems = dispensingFormItems.stream().filter(dispensingFormItem ->
                        dispensingFormItem.getProductType() == Constants.ProductType.MEDICINE
                                && dispensingFormItem.getProductSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE
                                && dispensingFormItem.getStatus() == DispensingFormItem.Status.UNDISPENSED)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (undispensedChineseMedicineFormItems.size() <= 0) {
            return DispensingSheet.ChineseMedicineUndispenseType.NONE;
        } else if (undispensedChineseMedicineFormItems.stream().anyMatch(item -> item.getUndispenseType() == DispensingFormItem.UndispenseType.BY_UNIT)) {
            return DispensingSheet.ChineseMedicineUndispenseType.BY_UNIT;
        }
        return DispensingSheet.ChineseMedicineUndispenseType.BY_DOSE;
    }

    public static boolean isExecutableItem(DispensingFormItem dispensingFormItem, int includeExternal) {
        if (dispensingFormItem == null) {
            return false;
        }

        DispenseUsageInfo firstItemUsageInfo = DispensingUtils.toDbUsageInfo(dispensingFormItem.getUsageInfoJson());
        if (firstItemUsageInfo != null) {
            return includeExternal == 1 ? ExecutableUtils.isExecutableUsage(firstItemUsageInfo.getUsage()) : ExecutableUtils.isExecutableUsageOnly(firstItemUsageInfo.getUsage());
        }
        return false;
    }

    /**
     * 检查 FormItem是否是 可执行项目
     * 方法主要是看Item里面的UsageInfo是否包含了 可执行 相关的字符串
     *
     * @param dispensingFormItemView 发药FormItemView
     * @param includeExternal        配置，是否包含外置处方 1 包含
     */
    public static boolean isExecutableItem(DispensingFormItemView dispensingFormItemView, int includeExternal) {
        if (dispensingFormItemView == null) {
            return false;
        }

        UsageInfo firstItemUsageInfo = JsonUtils.readValue(dispensingFormItemView.getUsageInfoJson(), UsageInfo.class);
        if (firstItemUsageInfo != null && !TextUtils.isEmpty(firstItemUsageInfo.getUsage())) {
            return includeExternal == 1 ? ExecutableUtils.isExecutableUsage(firstItemUsageInfo.getUsage()) : ExecutableUtils.isExecutableUsageOnly(firstItemUsageInfo.getUsage());
        }
        return false;
    }


    public static CisPatientInfo parseCisPatientInfoFromPatientOrder(PatientOrder patientOrder) {
        if (patientOrder == null) {
            return null;
        }

        CisPatientInfo cisPatientInfo = new CisPatientInfo();
        if (patientOrder.getPatientInfo() != null) {
            BeanUtils.copyProperties(patientOrder.getPatientInfo(), cisPatientInfo);
        }
        cisPatientInfo.setName(patientOrder.getPatientName());
        cisPatientInfo.setSex(patientOrder.getPatientSex());
        cisPatientInfo.setId(patientOrder.getPatientId());
        cisPatientInfo.setMobile(patientOrder.getPatientMobile());
        cisPatientInfo.setIsMember(patientOrder.getIsMember());
        cisPatientInfo.setAge(patientOrder.getPatientAge());
        cisPatientInfo.setBirthday(patientOrder.getPatientBirthday());
        return cisPatientInfo;
    }

    public static PatientInfo parsePatientInfoFromPatientOrder(PatientOrder patientOrder) {
        if (patientOrder == null) {
            return null;
        }

        PatientInfo patientInfo = new PatientInfo();
        patientInfo.setName(patientOrder.getPatientName());
        patientInfo.setSex(patientOrder.getPatientSex());
        patientInfo.setId(patientOrder.getPatientId());
        patientInfo.setMobile(patientOrder.getPatientMobile());
        patientInfo.setIsMember(patientOrder.getIsMember());
        patientInfo.setAge(patientOrder.getPatientAge());
        patientInfo.setBirthday(patientOrder.getPatientBirthday());
        return patientInfo;
    }

    public static boolean isNotCombineForm(int sourceFormType) {

        return sourceFormType == DispensingForm.SourceFormType.PRESCRIPTION_WESTERN
                || sourceFormType == DispensingForm.SourceFormType.PRESCRIPTION_INFUSION
                || sourceFormType == DispensingForm.SourceFormType.PRESCRIPTION_CHINESE
                || sourceFormType == DispensingForm.SourceFormType.GIFT_PRODUCT
                || sourceFormType == DispensingForm.SourceFormType.PRESCRIPTION_EXTERNAL // 2022.10.17外治处方支持开库存药品
                || sourceFormType == DispensingForm.SourceFormType.PRESCRIPTION_OPTOMETRIST
                || sourceFormType == DispensingForm.SourceFormType.MATERIAL
                || sourceFormType == DispensingForm.SourceFormType.ADDITIONAL_SALE_PRODUCT_FORM
                ;
    }

    /**
     * dispensingSheet 深拷贝
     * 避免dispensingSheet被写入db，有时需要拷贝出来
     */
    public static DispensingSheet deepCopyDispensingSheet(DispensingSheet dispensingSheet) {
        String str = JsonUtils.dump(dispensingSheet);
        DispensingSheet copyDispensingSheet = JsonUtils.readValue(str, DispensingSheet.class);
        return copyDispensingSheet;
    }

    /**
     * dispensingForm 深拷贝
     * 避免dispensingForm被写入db
     */
    public static DispensingForm deepCopyDispensingForm(DispensingForm dispensingform) {
        String str = JsonUtils.dump(dispensingform);
        DispensingForm copyDispensingForm = JsonUtils.readValue(str, DispensingForm.class);
        return copyDispensingForm;
    }

    public static <T extends DispensingFormItemBasicView> Map<String, BigDecimal> getUndispensedCountIdMapByUndispenseTypeForView(List<T> existedItems, int undispenseType) {
        boolean isUndispenseByDose = undispenseType == DispensingFormItem.UndispenseType.BY_DOSE;
        Map<String, BigDecimal> undispensedCountIdMap = existedItems.stream()
                .filter(dispensingFormItem ->
                        dispensingFormItem.getStatus() == DispensingFormItem.Status.UNDISPENSED
                                && dispensingFormItem.getUndispenseType() == undispenseType)
                .filter(dispensingFormItem -> !TextUtils.isEmpty(dispensingFormItem.getAssociateFormItemId()))
                .collect(Collectors.toMap(
                        T::getAssociateFormItemId,
                        dispensingFormItem -> MathUtils.wrapBigDecimal(isUndispenseByDose ? dispensingFormItem.getDoseCount() : dispensingFormItem.getUnitCount(), BigDecimal.ZERO),
                        (item1, item2) -> item1.add(item2))
                );
        return undispensedCountIdMap;
    }

    public static Map<String, BigDecimal> getUndispensedCountIdMapByUndispenseType(List<DispensingFormItem> dispensingFormItems, int undispenseType) {
        boolean isUndispenseByDose = undispenseType == DispensingFormItem.UndispenseType.BY_DOSE;
        Map<String, BigDecimal> undispensedCountIdMap = dispensingFormItems.stream()
                .filter(dispensingFormItem ->
                        dispensingFormItem.getStatus() == DispensingFormItem.Status.UNDISPENSED
                                && dispensingFormItem.getUndispenseType() == undispenseType
                                && dispensingFormItem.getIsHistoryItem() != 1)// 不是历史退药项
                .filter(dispensingFormItem -> !TextUtils.isEmpty(dispensingFormItem.getAssociateFormItemId()))
                .collect(Collectors.toMap(
                        DispensingFormItem::getAssociateFormItemId,
                        dispensingFormItem -> MathUtils.wrapBigDecimal(isUndispenseByDose ? dispensingFormItem.getDoseCount() : dispensingFormItem.getUnitCount(), BigDecimal.ZERO),
                        (item1, item2) -> item1.add(item2))
                );
        return undispensedCountIdMap;
    }

    /**
     * 用hashSet 提升 效率
     */
    public static Set<Integer> gCanDispensingFormTypeSet = null;

    /**
     * 工具函数 用来判断chargeForm是否能发要
     *
     * @return true  可以
     */
    public static final boolean canDispenseChargeFormSourceFormType(int chargeFormType) {
        if (gCanDispensingFormTypeSet == null) {
            gCanDispensingFormTypeSet = new HashSet<>();
            gCanDispensingFormTypeSet.add(cn.abcyun.cis.commons.rpc.charge.Constants.SourceFormType.PRESCRIPTION_WESTERN);
            gCanDispensingFormTypeSet.add(cn.abcyun.cis.commons.rpc.charge.Constants.SourceFormType.PRESCRIPTION_CHINESE);
            gCanDispensingFormTypeSet.add(cn.abcyun.cis.commons.rpc.charge.Constants.SourceFormType.PRESCRIPTION_INFUSION);
            gCanDispensingFormTypeSet.add(cn.abcyun.cis.commons.rpc.charge.Constants.SourceFormType.ADDITIONAL_FORM);
            gCanDispensingFormTypeSet.add(cn.abcyun.cis.commons.rpc.charge.Constants.SourceFormType.ADDITIONAL_SALE_PRODUCT_FORM);
            gCanDispensingFormTypeSet.add(cn.abcyun.cis.commons.rpc.charge.Constants.SourceFormType.MATERIAL);
            gCanDispensingFormTypeSet.add(cn.abcyun.cis.commons.rpc.charge.Constants.SourceFormType.GIFT_PRODUCT);
            gCanDispensingFormTypeSet.add(cn.abcyun.cis.commons.rpc.charge.Constants.SourceFormType.COMPOSE_PRODUCT);
            //2022.10.17外治处方可以开库存药品
            gCanDispensingFormTypeSet.add(cn.abcyun.cis.commons.rpc.charge.Constants.SourceFormType.PRESCRIPTION_EXTERNAL);
            gCanDispensingFormTypeSet.add(cn.abcyun.cis.commons.rpc.charge.Constants.SourceFormType.PRESCRIPTION_OPTOMETRIST);
            gCanDispensingFormTypeSet.add(Constants.SourceFormType.SINGLE_PROMOTION_GIFT_PRODUCT);
            gCanDispensingFormTypeSet.add(Constants.SourceFormType.MARKED_GIFT_PRODUCT);
            gCanDispensingFormTypeSet.add(Constants.SourceFormType.NON_FORMULATED_DECOCTION_PIECES);
        }
        return gCanDispensingFormTypeSet.contains(chargeFormType);
    }

    /**
     * chageFormType 到  dispsingFormType的转化
     * {@link DispensingUtils#canDispenseChargeFormSourceFormType(int)}  }
     */
    public static int getDispensingFormType(int chargeFormType) {
        switch (chargeFormType) {
            case cn.abcyun.cis.commons.rpc.charge.Constants.SourceFormType.PRESCRIPTION_WESTERN:
                return DispensingForm.SourceFormType.PRESCRIPTION_WESTERN;
            case cn.abcyun.cis.commons.rpc.charge.Constants.SourceFormType.PRESCRIPTION_INFUSION:
                return DispensingForm.SourceFormType.PRESCRIPTION_INFUSION;
            case cn.abcyun.cis.commons.rpc.charge.Constants.SourceFormType.PRESCRIPTION_CHINESE:
                return DispensingForm.SourceFormType.PRESCRIPTION_CHINESE;
            case cn.abcyun.cis.commons.rpc.charge.Constants.SourceFormType.ADDITIONAL_FORM:
                return DispensingForm.SourceFormType.ADDITIONAL_FORM;
            case cn.abcyun.cis.commons.rpc.charge.Constants.SourceFormType.ADDITIONAL_SALE_PRODUCT_FORM:
                return DispensingForm.SourceFormType.ADDITIONAL_SALE_PRODUCT_FORM;
            case cn.abcyun.cis.commons.rpc.charge.Constants.SourceFormType.MATERIAL:
                return DispensingForm.SourceFormType.MATERIAL;
            case cn.abcyun.cis.commons.rpc.charge.Constants.SourceFormType.GIFT_PRODUCT:
                return DispensingForm.SourceFormType.GIFT_PRODUCT;
            case cn.abcyun.cis.commons.rpc.charge.Constants.SourceFormType.COMPOSE_PRODUCT:
                return DispensingForm.SourceFormType.COMPOSE_PRODUCT;
            case cn.abcyun.cis.commons.rpc.charge.Constants.SourceFormType.PRESCRIPTION_EXTERNAL:
                return DispensingForm.SourceFormType.PRESCRIPTION_EXTERNAL;
            case cn.abcyun.cis.commons.rpc.charge.Constants.SourceFormType.PRESCRIPTION_OPTOMETRIST:
                return DispensingForm.SourceFormType.PRESCRIPTION_OPTOMETRIST;
            case Constants.SourceFormType.SINGLE_PROMOTION_GIFT_PRODUCT:
                return DispensingForm.SourceFormType.SINGLE_PROMOTION_GIFT_PRODUCT;
            case Constants.SourceFormType.MARKED_GIFT_PRODUCT:
                return DispensingForm.SourceFormType.MARKED_GIFT_PRODUCT;
            case Constants.SourceFormType.NON_FORMULATED_DECOCTION_PIECES:
                return DispensingForm.SourceFormType.NON_FORMULATED_DECOCTION_PIECES;
        }
        return 0;
    }

    /**
     * 代煎代配上的chargeForm的快递结构 转成  ChargeDeliveryInfo
     */
    public static ChargeDeliveryInfo transChargeLogisticsToChargeDelivery(ChargeForm chargeForm) {
        ChargeAirPharmacyLogisticsView formDeliverView = chargeForm.getChargeAirPharmacyLogistics();
        ChargeDeliveryInfo oldDeliveryInfo = new ChargeDeliveryInfo();
        oldDeliveryInfo.setDeliveryOrderNo(formDeliverView.getDeliveryNo());
        oldDeliveryInfo.setAddressProvinceName(formDeliverView.getAddressProvinceName());
        oldDeliveryInfo.setAddressCityName(formDeliverView.getAddressCityName());
        oldDeliveryInfo.setAddressDistrictName(formDeliverView.getAddressDistrictName());
        oldDeliveryInfo.setAddressDetail(formDeliverView.getAddressDetail());
        oldDeliveryInfo.setDeliveryPayType(formDeliverView.getDeliveryPayType());
        oldDeliveryInfo.setDeliveryName(formDeliverView.getDeliveryName());
        oldDeliveryInfo.setDeliveryMobile(formDeliverView.getDeliveryMobile());
        if (formDeliverView.getDeliveryCompany() != null) {
            oldDeliveryInfo.setDeliveryCompany(new ChargeDeliveryCompany());
            oldDeliveryInfo.getDeliveryCompany().setName(formDeliverView.getDeliveryCompany().getName());
        }
        return oldDeliveryInfo;
    }

    public static boolean isChineseMedicine(int goodsType, int goodsSubType) {
        return goodsType == GoodsConst.GoodsType.MEDICINE && goodsSubType == GoodsConst.GoodsMedicineSubType.CHINESE_MEDICINE;
    }


    /**
     * 安全的判断Long是否相等的工具方法
     * 如果两边都为 null 相等
     * null 等于0
     */
    public static boolean compareLongEqual(Long left, Long right) {
        left = left == null ? 0L : left;
        right = right == null ? 0L : right;
        return left.compareTo(right) == 0;
    }

    public static boolean isStockGoods(int goodsType) {
        return (goodsType == GoodsConst.GoodsType.MEDICINE || goodsType == GoodsConst.GoodsType.MATERIAL || goodsType == GoodsConst.GoodsType.ADDITION);
    }

    public static final String DEFAULT_HOUR = "08:00";

    /**
     * 医嘱规则是否指定了总量
     */
    public static boolean isAdviceRuleHasTotalCount(BigDecimal count) {
        return count != null && count.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 医嘱规则里面哪些医嘱需要生成发药单
     * 目前只有中药医嘱和中西药医嘱才需要生成发药单
     */
    public static boolean adviceRuleTypeNeedCreateDispenseSheet(int ruleType) {
        return ruleType == AdviceRuleType.CHINESE_GRANULE
                || ruleType == AdviceRuleType.CHINESE_PIECE
                || ruleType == AdviceRuleType.WESTERN
                || ruleType == AdviceRuleType.MEDICINE_MATERIAL
                || ruleType == AdviceRuleType.SELF_PRODUCT
                || ruleType == AdviceRuleType.HEALTH_MEDICINE
                || ruleType == AdviceRuleType.HEALTH_FOOD
                || ruleType == AdviceRuleType.OTHER_PRODUCT;
    }

    public static boolean isChineseAdviceRuleType(int ruleType) {
        return ruleType == AdviceRuleType.CHINESE_GRANULE
                || ruleType == AdviceRuleType.CHINESE_PIECE;
    }

    /**
     * 延时消息延时时间
     *
     * @param nextOrderTime 下一时间段 08:00
     * @param nextDateSend  是否是第二天发送
     * @return 定时任务执行时间
     */
    public static long getDelayMessageTimestamp(String nextOrderTime, boolean nextDateSend) {
        LocalDateTime now = LocalDateTime.now();
        LocalDate nowLocalDate = LocalDate.now();
        String nowStr = DateUtils.formatLocalDateTime(now, DateUtils.sFormatterHourMin);
        LocalTime nextLocalTime = DateUtils.parseLocalTime(nextOrderTime, DateUtils.sFormatterHourMin);
        long delayTimestamp;
        if (nowStr.compareTo(nextOrderTime) >= 0 || nextDateSend) {
            // 第二天
            LocalDate nextLocalDate = nowLocalDate.plusDays(1L);
            delayTimestamp = DateUtils.toInstant(LocalDateTime.of(nextLocalDate, nextLocalTime)).toEpochMilli();
        } else {
            // 当天
            delayTimestamp = DateUtils.toInstant(LocalDateTime.of(nowLocalDate, nextLocalTime)).toEpochMilli();
        }
        return delayTimestamp;
    }

    public static void formatDispenseItemRemainingCount(DispensingSheet dispensingSheet) {
        if (dispensingSheet == null || CollectionUtils.isEmpty(dispensingSheet.getDispensingForms())) {
            return;
        }

        for (DispensingForm dispensingForm : dispensingSheet.getDispensingForms()) {
            formatDispenseItemRemainingCount(dispensingForm);
        }
    }

    public static void formatDispenseItemRemainingCount(List<DispensingSheet> dispensingSheets) {
        if (CollectionUtils.isEmpty(dispensingSheets)) {
            return;
        }

        dispensingSheets.forEach(DispensingUtils::formatDispenseItemRemainingCount);
    }

    private static void formatDispenseItemRemainingCount(DispensingForm dispensingForm) {
        if (dispensingForm == null || CollectionUtils.isEmpty(dispensingForm.getDispensingFormItems())) {
            return;
        }
        for (DispensingFormItem dispensingFormItem : dispensingForm.getDispensingFormItems()) {
            formatDispenseItemRemainingCount(dispensingFormItem);
        }
    }

    /**
     * 格式化发药项的剩余数量
     * 兼容发药项状态为待发药，但是剩余可发数量都是 0 的情况（因为未发数量字段为新加字段，默认值为 0，所以老数据都是 0）
     * 这种情况下，需要将剩余可发数量重置为总数量
     *
     * @param dispenseItem 发药项
     */
    public static void formatDispenseItemRemainingCount(DispensingFormItem dispenseItem) {
        if (dispenseItem == null) {
            return;
        }

        if (dispenseItem.getStatus() == DispensingFormItem.Status.WAITING
                && MathUtils.isZeroOrNull(dispenseItem.getRemainingUnitCount())
                && MathUtils.isZeroOrNull(dispenseItem.getRemainingDoseCount())) {
            dispenseItem.setRemainingDoseCount(dispenseItem.getDoseCount());
            dispenseItem.setRemainingUnitCount(dispenseItem.getUnitCount());
        }
    }

    public static BigDecimal getRealDispenseItemRemainingUnitCount(DispensingFormItem dispensingItem) {
        if (dispensingItem == null) {
            return null;
        }

        if (dispensingItem.getStatus() == DispensingFormItem.Status.WAITING
                && MathUtils.isZeroOrNull(dispensingItem.getRemainingUnitCount())) {
            return dispensingItem.getUnitCount();
        }
        return dispensingItem.getRemainingUnitCount();
    }

    public static BigDecimal getRealDispenseItemRemainingDoseCount(DispensingFormItem dispensingItem) {
        if (dispensingItem == null) {
            return null;
        }

        if (dispensingItem.getStatus() == DispensingFormItem.Status.WAITING
                && MathUtils.isZeroOrNull(dispensingItem.getRemainingDoseCount())) {
            return dispensingItem.getDoseCount();
        }
        return dispensingItem.getRemainingDoseCount();
    }

    /**
     * 发药单是否支持重新发药
     */
    public static int dispensingSheetSupportReDispense(DispensingSheet dispensingSheet, List<DispensingLogItem> dispensingItemDispenseLogs) {
        if (dispensingSheet == null || CollectionUtils.isEmpty(dispensingItemDispenseLogs)) {
            return 0;
        }

        int status = dispensingSheet.getStatus();
        if (status != DispensingSheet.Status.UNDISPENSED) {
            return 0;
        }

        if (Objects.equals(dispensingSheet.getPayStatus(), DispensingSheet.PayStatus.REFUNED)) {
            return 0;
        }

        /*
            可部分发药需求上线前的历史发药单不支持重新发药
            可部分数量发药需求上线之前的发药单不支持的重新发药的原因（operation 中没有记录 stockDealId）
            因为 goods 不感知重新发药这个操作，并且每次发药都会返回历史的所有发药 stockDealId，为了保证重新发药后是一个"新的开始"，就需要过滤掉历史的发药 stockDealId，
            由于 dispensingFormItem 上的记录 stockDealId 每次都会被覆盖，所以只要重新发药后就会丢失这个 stockDealId，那么就没办法过滤掉这个历史 stockDealId，
            导致发药数量计算不正确
         */
        // 判断依据：获取发药单发药项发药日志，如果 stockDealId 或 operationId 为空时则不允许重新发药
        for (DispensingLogItem dispensingItemDispenseLog : dispensingItemDispenseLogs) {
            if (StringUtils.isAnyBlank(dispensingItemDispenseLog.getStockDealId(), dispensingItemDispenseLog.getOperationId())) {
                return 0;
            }
        }

        return 1;
    }

    /**
     * 获取已发的剂数
     */
    public static BigDecimal getDispensedDoseCount(DispensingFormItem dispenseFormItem) {
        if (dispenseFormItem == null || !DispensingFormItem.Status.hasDispense.contains(dispenseFormItem.getStatus())) {
            return BigDecimal.ZERO;
        }

        BigDecimal remainingDoseCount = getRealDispenseItemRemainingDoseCount(dispenseFormItem);
        return MathUtils.wrapBigDecimalSubtract(dispenseFormItem.getDoseCount(), remainingDoseCount);
    }

    /**
     * 获取已发的单位数量
     */
    public static BigDecimal getDispensedUnitCount(DispensingFormItem dispenseFormItem) {
        if (dispenseFormItem == null || !DispensingFormItem.Status.hasDispense.contains(dispenseFormItem.getStatus())) {
            return BigDecimal.ZERO;
        }

        BigDecimal remainingUnitCount = getRealDispenseItemRemainingUnitCount(dispenseFormItem);
        return MathUtils.wrapBigDecimalSubtract(dispenseFormItem.getUnitCount(), remainingUnitCount);
    }

    /**
     * 获取goods扣库的dealId
     * 发药/退药失败，goods扣库成功，再次发药/退药，goods不扣库，会返回之前成功的信息，发药取第一条(不存在多次发的情况)，退药取对应的
     */
    public static String getGoodsDispenseResultItemDealId(GoodsDispenseResultItem dispenseResultItem, boolean dispense) {
        if (dispenseResultItem == null) {
            return null;
        }
        if (!org.springframework.util.StringUtils.isEmpty(dispenseResultItem.getDealId())) {
            return dispenseResultItem.getDealId();
        }
        if (org.springframework.util.CollectionUtils.isEmpty(dispenseResultItem.getDispenseBatchInfoList())) {
            return null;
        }
        if (dispense) {
            dispenseResultItem.setDealId(dispenseResultItem.getDispenseBatchInfoList().get(0).getDealId());
            return dispenseResultItem.getDispenseBatchInfoList().get(0).getDealId();
        }
        for (GoodsDispenseResultItem.DispenseBatchInfo dispenseBatchInfo : dispenseResultItem.getDispenseBatchInfoList()) {
            if (StringUtils.equals(dispenseBatchInfo.getDealId(), dispenseResultItem.getUnDispenseItemId())) {
                dispenseResultItem.setDealId(dispenseBatchInfo.getDealId());
                return dispenseBatchInfo.getDealId();
            }
        }
        log.info("医院退药失败, {}", dispenseResultItem);
        throw new DispenseSheetChangedException();
    }

    public static String generateSheetAdviceGroupId(Long adviceGroupId, Instant planExecuteTime, String sheetId) {
        String key = String.valueOf(adviceGroupId) + (planExecuteTime != null ? planExecuteTime.toEpochMilli() : "");
        if (sheetId != null) {
            key = key + sheetId;
        }
        return MD5Utils.sign(key);
    }

    public static void doWithDispensingItem(Collection<DispensingSheet> dispensingSheets, Consumer<DispensingFormItem> consumer) {
        if (dispensingSheets == null || CollectionUtils.isEmpty(dispensingSheets)) {
            return;
        }

        dispensingSheets.forEach(dispensingSheet -> {
            doWithDispensingItem(dispensingSheet, consumer);
        });
    }

    public static void doWithDispensingItemV2(Collection<DispensingSheetV2> dispensingSheets, Consumer<DispensingFormItemV2> consumer) {
        if (dispensingSheets == null || CollectionUtils.isEmpty(dispensingSheets)) {
            return;
        }

        dispensingSheets.forEach(dispensingSheet -> {
            doWithDispensingItem(dispensingSheet, consumer);
        });
    }

    public static void doWithDispensingItem(DispensingSheetV2 dispensingSheet, Consumer<DispensingFormItemV2> consumer) {
        if (dispensingSheet == null || CollectionUtils.isEmpty(dispensingSheet.getDispensingForms())) {
            return;
        }

        dispensingSheet.getDispensingForms().forEach(form -> doWithDispensingItem(form, consumer));
    }

    public static void doWithDispensingItem(DispensingSheet dispensingSheet, Consumer<DispensingFormItem> consumer) {
        if (dispensingSheet == null || CollectionUtils.isEmpty(dispensingSheet.getDispensingForms())) {
            return;
        }

        dispensingSheet.getDispensingForms().forEach(form -> doWithDispensingItem(form, consumer));
    }

    public static void doWithDispensingItemBatch(Collection<DispensingSheet> dispensingSheets, Consumer<DispensingFormItemBatchInfo> consumer) {
        if (CollectionUtils.isEmpty(dispensingSheets)) {
            return;
        }
        dispensingSheets.forEach(dispensingSheet -> {
            doWithDispensingItemBatch(dispensingSheet, consumer);
        });
    }

    public static void doWithV2DispensingItemBatch(Collection<DispensingSheetV2> dispensingSheets, Consumer<DispensingFormItemBatchInfo> consumer) {
        if (CollectionUtils.isEmpty(dispensingSheets)) {
            return;
        }
        doWithDispensingItemV2(dispensingSheets, item -> {
            if (item == null || CollectionUtils.isEmpty(item.getDispensingFormItemBatches())) {
                return;
            }

            item.getDispensingFormItemBatches().forEach(batchInfo -> {
                if (batchInfo == null) {
                    return;
                }

                consumer.accept(batchInfo);
            });
        });
    }


    public static void doWithDispensingItemBatch(DispensingSheet dispensingSheet, Consumer<DispensingFormItemBatchInfo> consumer) {
        doWithDispensingItem(dispensingSheet, item -> {
            if (item == null || CollectionUtils.isEmpty(item.getDispensingFormItemBatches())) {
                return;
            }

            item.getDispensingFormItemBatches().forEach(batchInfo -> {
                if (batchInfo == null) {
                    return;
                }

                consumer.accept(batchInfo);
            });
        });
    }

    public static void doWithDispensingItem(DispensingFormV2 form, Consumer<DispensingFormItemV2> consumer) {
        if (form == null || CollectionUtils.isEmpty(form.getDispensingFormItems())) {
            return;
        }

        form.getDispensingFormItems().forEach(item -> {
            if (item == null) {
                return;
            }

            consumer.accept(item);

            if (!CollectionUtils.isEmpty(item.getComposeChildren())) {
                item.getComposeChildren().forEach(childrenItem -> {
                    if (childrenItem == null) {
                        return;
                    }

                    consumer.accept(childrenItem);
                });
            }
        });
    }

    public static void doWithDispensingItem(DispensingForm form, Consumer<DispensingFormItem> consumer) {
        if (form == null || CollectionUtils.isEmpty(form.getDispensingFormItems())) {
            return;
        }

        form.getDispensingFormItems().forEach(item -> {
            if (item == null) {
                return;
            }

            consumer.accept(item);

            if (!CollectionUtils.isEmpty(item.getComposeChildren())) {
                item.getComposeChildren().forEach(childrenItem -> {
                    if (childrenItem == null) {
                        return;
                    }

                    consumer.accept(childrenItem);
                });
            }
        });
    }

    public static void doWithDispensingForm(List<DispensingSheet> dispensingSheets, Consumer<DispensingForm> consumer) {
        if (CollectionUtils.isEmpty(dispensingSheets)) {
            return;
        }

        dispensingSheets.forEach(dispensingSheet -> {
            doWithDispensingForm(dispensingSheet, consumer);
        });
    }

    public static void doWithDispensingForm(DispensingSheet dispensingSheet, Consumer<DispensingForm> consumer) {
        if (dispensingSheet == null || CollectionUtils.isEmpty(dispensingSheet.getDispensingForms())) {
            return;
        }

        dispensingSheet.getDispensingForms().forEach(form -> doWithDispensingForm(form, consumer));
    }

    private static void doWithDispensingForm(DispensingForm form, Consumer<DispensingForm> consumer) {
        if (form == null) {
            return;
        }

        consumer.accept(form);
    }

    public static DispenseUsageInfo toDbUsageInfo(String strJsonNode) {
        if (StringUtils.isEmpty(strJsonNode)) {
            return null;
        }
        DispenseUsageInfo dbUsageInfo = JsonUtils.readValue(strJsonNode, DispenseUsageInfo.class);
        if (dbUsageInfo == null) {
            return null;
        }
        if (dbUsageInfo.getDoseCount() == null) {
            dbUsageInfo.setDoseCount(BigDecimal.ZERO);
        }
        if (dbUsageInfo.getIsDecoction() == null) {
            dbUsageInfo.setIsDecoction(false);
        }
        if (dbUsageInfo.getProcessBagUnitCount() == null) {
            dbUsageInfo.setProcessBagUnitCount(0);
        }
        if (dbUsageInfo.getVerifySignatureStatus() == null) {
            dbUsageInfo.setVerifySignatureStatus(0);
        }
        return dbUsageInfo;
    }

    public static JsonNode toJsonNodeUsageInfo(String strJsonNode) {
        DispenseUsageInfo dbUsageInfo = toDbUsageInfo(strJsonNode);
        if (dbUsageInfo == null) {
            return null;
        }
        return JsonUtils.dumpAsJsonNode(dbUsageInfo);
    }

    public static String chargeUsageInfoToDBUsageInfo(JsonNode usageInfo) {
        if (usageInfo == null) {
            return null;
        }
        DispenseUsageInfo dbUsageInfo = JsonUtils.readValue(usageInfo, DispenseUsageInfo.class);
        if (dbUsageInfo == null || dbUsageInfo.isEmpty()) {
            return null;
        }
        return JsonUtils.dump(dbUsageInfo);
    }

    public static void doWithDispensingItem(cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispensingSheet dispensingSheet,
                                            Consumer<cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispensingFormItem> consumer) {
        if (dispensingSheet == null || CollectionUtils.isEmpty(dispensingSheet.getDispensingForms())) {
            return;
        }

        for (cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispensingForm form : dispensingSheet.getDispensingForms()) {
            if (form == null || CollectionUtils.isEmpty(form.getDispensingFormItems())) {
                continue;
            }
            for (cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispensingFormItem item : form.getDispensingFormItems()) {
                if (item == null) {
                    continue;
                }
                consumer.accept(item);

                if (!CollectionUtils.isEmpty(item.getComposeChildren())) {
                    for (cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispensingFormItem childrenItem : item.getComposeChildren()) {
                        if (childrenItem == null) {
                            continue;
                        }
                        consumer.accept(childrenItem);
                    }
                }
            }
        }
    }

    public static BigDecimal calculateStockCount(boolean isDismounting, BigDecimal packageCount, BigDecimal pieceNum,
                                                 BigDecimal pieceCount) {
        if (isDismounting) {
            return MathUtils.wrapBigDecimalAdd(cn.abcyun.cis.commons.util.MathUtils.wrapBigDecimalMultiply(packageCount, pieceNum), pieceCount);
        } else {
            return cn.abcyun.cis.commons.util.MathUtils.wrapBigDecimalOrZero(packageCount);
        }
    }

    /**
     * 和V1的区别就是不需要判断是否为null
     */
    public static BigDecimal calculateStockCountV2(boolean isDismounting,
                                                   BigDecimal packageCount,
                                                   BigDecimal clientReqOriginFlatPieceCount,
                                                   BigDecimal clientReqOriginFlatPackageCount,
                                                   BigDecimal pieceNum,
                                                   BigDecimal pieceCount) {
        if (isDismounting) {
            if (clientReqOriginFlatPieceCount != null) {
                return clientReqOriginFlatPieceCount;
            }
            return MathUtils.wrapBigDecimalAdd(cn.abcyun.cis.commons.util.MathUtils.wrapBigDecimalMultiply(packageCount, pieceNum), pieceCount);
        } else {
            if (clientReqOriginFlatPackageCount != null) {
                return clientReqOriginFlatPackageCount; //SCGoods直接返回了大的数量
            }
            BigDecimal realPackageCount = BigDecimal.ZERO;
            if (MathUtils.wrapBigDecimalCompare(pieceCount, BigDecimal.ZERO) > 0) {
                realPackageCount = MathUtils.wrapBigDecimalAdd(realPackageCount, pieceCount.divide(pieceNum, 2, RoundingMode.DOWN));
            }
            return MathUtils.wrapBigDecimalAdd(cn.abcyun.cis.commons.util.MathUtils.wrapBigDecimalOrZero(packageCount), realPackageCount);
        }
    }

    public static int getItemCalculateUnitPriceScale(int goodsType, int goodsSubType) {
        int scale = 2;
        if (goodsType == GoodsConst.GoodsType.MEDICINE
                && (goodsSubType == GoodsConst.GoodsMedicineSubType.CHINESE_MEDICINE || goodsSubType == GoodsConst.GoodsMedicineSubType.WESTERN_MEDICINE)) {
            scale = 4;
        }
        return scale;
    }

    public static BigDecimal calculateTotalCostPrice(BigDecimal unitCount, BigDecimal doseCount, BigDecimal goodsPackageCostPrice, BigDecimal pieceNum, int isDismounting) {
        unitCount = unitCount == null ? BigDecimal.ZERO : unitCount;
        doseCount = doseCount == null ? BigDecimal.ONE : doseCount;
        BigDecimal unitCostPrice = calculateUnitCostPrice(goodsPackageCostPrice, pieceNum, isDismounting);
        return MathUtils.wrapBigDecimalMultiply(MathUtils.wrapBigDecimalMultiply(unitCount, doseCount), unitCostPrice);
    }

    public static BigDecimal calculateUnitCostPrice(BigDecimal goodsPackageCostPrice, BigDecimal pieceNum, int isDismounting) {
        if (goodsPackageCostPrice == null) {
            return BigDecimal.ZERO;
        }

        if (isDismounting == 1) {

            if (pieceNum == null || pieceNum.compareTo(BigDecimal.ZERO) == 0) {
                pieceNum = BigDecimal.ONE;
            }

            return goodsPackageCostPrice.divide(cn.abcyun.cis.commons.util.MathUtils.wrapBigDecimalOrZero(pieceNum), 4, RoundingMode.HALF_UP);
        }
        return goodsPackageCostPrice;
    }

    /**
     * 获取可退药的项数量
     */
    public static long getUndispensableItemCount(DispensingSheet dispensingSheet) {
        return dispensingSheet.getDispensingForms()
                .stream()
                .filter(dispensingForm -> dispensingForm.getDispensingFormItems() != null)
                .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream())
                .filter(dispensingFormItem ->
                        dispensingFormItem.getProductType() != Constants.ProductType.COMPOSE_PRODUCT
                                && dispensingFormItem.getStatus() != DispensingFormItem.Status.CANCELED
                                // 同时还不能是历史的退药项
                                && dispensingFormItem.getIsHistoryItem() == 0
                )
                .collect(
                        Collectors.toMap(
                                item -> {
                                    if (item.getStatus() != DispensingFormItem.Status.UNDISPENSED) {
                                        return item.getId();
                                    } else {
                                        return item.getAssociateFormItemId();
                                    }
                                },
                                item -> {
                                    BigDecimal itemTotalCount;
                                    if (DispensingFormItem.Status.hasDispense.contains(item.getStatus())) {
                                        itemTotalCount = MathUtils.calculateTotalCount(item.getDispensedUnitCount(), item.getDispensedDoseCount());
                                    } else if (DispensingFormItem.Status.UNDISPENSED == item.getStatus()) {
                                        itemTotalCount = MathUtils.calculateTotalCount(item.getUnitCount(), item.getDoseCount());
                                    } else {
                                        itemTotalCount = BigDecimal.ZERO;
                                    }

                                    if (item.getStatus() != DispensingFormItem.Status.UNDISPENSED) {
                                        return MathUtils.wrapBigDecimal(itemTotalCount, BigDecimal.ZERO);
                                    } else {
                                        return MathUtils.wrapBigDecimal(itemTotalCount, BigDecimal.ZERO).negate();
                                    }
                                },
                                (a, b) -> a.add(b)
                        ))
                .values()
                .stream().filter(value -> value.compareTo(BigDecimal.ZERO) > 0)
                .count();
    }


    public static boolean compareIntegerEqual(Integer left, Integer right) {
        boolean leftEmpty = left == null;
        boolean rightEmpty = right == null;
        if (leftEmpty && rightEmpty) {
            return true;
        }
        if (!leftEmpty && !rightEmpty) {
            return left.equals(right);
        }
        return false;
    }

    public static boolean isAutoDispenseMethod(int dispenseMethod) {
        return dispenseMethod == DispensingSheet.DispenseMethod.AUTO;
    }

    public static boolean compareStrEqual(String left, String right) {
        boolean leftEmpty = left == null || org.springframework.util.StringUtils.isEmpty(left.trim());
        boolean rightEmpty = right == null || org.springframework.util.StringUtils.isEmpty(right.trim());
        if (leftEmpty && rightEmpty) {
            return true;
        }
        if (!leftEmpty && !rightEmpty) {
            return left.trim().compareTo(right.trim()) == 0;
        }
        return false;
    }

    public static boolean isAdviceExecuteSourceSheetType(int sourceSheetType) {
        return sourceSheetType == DispenseConst.SourceSheetType.ADVICE_EXECUTE
                || sourceSheetType == DispenseConst.SourceSheetType.ADVICE_SUPPLEMENT_ALONE
                || sourceSheetType == DispenseConst.SourceSheetType.ADVICE_SUPPLEMENT_IN_EXECUTE;
    }

    public static boolean isAdviceSourceSheetType(int sourceSheetType) {
        return sourceSheetType == DispenseConst.SourceSheetType.ADVICE;
    }

    public static boolean isAdviceOrExecuteSourceSheetType(int sourceSheetType) {
        return isAdviceExecuteSourceSheetType(sourceSheetType) || isAdviceSourceSheetType(sourceSheetType);
    }

    public static boolean isDispensingOrderWaitingApplyStatus(int dispensingOrderStatus) {
        return dispensingOrderStatus == DispensingOrder.Status.NORMAL || dispensingOrderStatus == DispensingOrder.Status.NO_ORDER_CONFIG;
    }

    public static boolean isMedicineMaterial(int goodsType, int goodsSubType) {
        return goodsType == GoodsConst.GoodsType.MATERIAL && goodsSubType == GoodsConst.GoodsMaterialSubType.MEDICINE_MATERIAL;
    }

    public static boolean isWestMedicine(int type, int subType) {
        return type == GoodsConst.GoodsType.MEDICINE && subType != GoodsConst.GoodsMedicineSubType.CHINESE_MEDICINE;
    }

    public static boolean isMaterialMaterialMedicine(int type, int subType) {
        return type == GoodsConst.GoodsType.MATERIAL && subType == GoodsConst.GoodsMaterialSubType.MEDICINE_MATERIAL;
    }

    public static DispensingFormItemGoodsSnap mergeProductSnap(DispensingFormItemGoodsSnap dstProductSnap, DispensingFormItemGoodsSnap srcProductSnap) {
        if (dstProductSnap == null || srcProductSnap == null) {
            return dstProductSnap == null ? srcProductSnap : dstProductSnap;
        }

        if (CollectionUtils.isEmpty(srcProductSnap.getTraceableCodeNoInfoList())) {
            return dstProductSnap;
        }

        List<TraceableCodeNoInfo> dstTraceableCodeNoInfoList = dstProductSnap.getTraceableCodeNoInfoList() == null ? new ArrayList<>() : dstProductSnap.getTraceableCodeNoInfoList();
        List<TraceableCodeNoInfo> srcTraceableCodeNoInfoList = srcProductSnap.getTraceableCodeNoInfoList();
        for (TraceableCodeNoInfo srcTraceableCodeNoInfo : srcTraceableCodeNoInfoList) {
            if (dstTraceableCodeNoInfoList.stream().anyMatch(dstTraceableCodeNoInfo -> Objects.equals(dstTraceableCodeNoInfo.getNo(), srcTraceableCodeNoInfo.getNo()))) {
                continue;
            }
            // 找到了 no 相同的就忽略，否则加添加到列表中
            dstTraceableCodeNoInfoList.add(srcTraceableCodeNoInfo);
        }

        dstProductSnap.setTraceableCodeNoInfoList(dstTraceableCodeNoInfoList);
        return dstProductSnap;
    }


    /**
     * 补全“无码”追溯码
     * <p> 正常（预期）流程下，根据采集提醒配是发药时采集还是收费时采集，由前端在这两个场景下自动生成无码追溯码，然后提交到后台，但是会存在用户没有开启采集提醒，但是又标识了这个药是“无追溯码”。
     * 导致前端没有时机去生成，所以后端需要在这里补全
     */
    public static void fillNoTraceableCode(List<DispensingFormItem> clientDispenseItems,
                                           Map<String, DispensingFormItem> dispenseItemIdToDispensableDispenseItem,
                                           GoodsConfigView goodsConfig) {
        if (CollectionUtils.isEmpty(clientDispenseItems)) {
            return;
        }

        List<Integer> usableTraceCodeGoodsTypeIds = Optional.ofNullable(goodsConfig).map(GoodsConfigView::getTraceCodeConfig)
                .map(GoodsClinicTraceCodeConfigView::getUsableTraceCodeGoodsTypeIdList)
                .orElseGet(Collections::emptyList).stream().filter(Objects::nonNull).map(TraceCodeGoodsType::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(usableTraceCodeGoodsTypeIds)) {// 不需要采集的话，就不需要后台自动补码
            return;
        }

        clientDispenseItems.forEach(clientDispenseItem -> {
            if (clientDispenseItem == null || !CollectionUtils.isEmpty(clientDispenseItem.getTraceableCodeList())) {
                return;
            }

            DispensingFormItem dispensableDispenseItem = dispenseItemIdToDispensableDispenseItem.get(clientDispenseItem.getId());
            if (dispensableDispenseItem == null || dispensableDispenseItem.getProductSnap() == null
                    || CollectionUtils.isEmpty(dispensableDispenseItem.getProductSnap().getTraceableCodeNoInfoList())) {
                return;
            }

            DispensingFormItemGoodsSnap goodsItem = dispensableDispenseItem.getProductSnap();
            TraceableCodeNoInfo noCodeTraceableCodeNoInfo = goodsItem.getTraceableCodeNoInfoList().stream()
                    .filter(traceableCodeNoInfo -> Objects.equals(traceableCodeNoInfo.getType(), GoodsConst.DrugIdentificationCodeType.NO_CODE))
                    .findFirst().orElse(null);
            if (noCodeTraceableCodeNoInfo == null || !usableTraceCodeGoodsTypeIds.contains(goodsItem.getTypeId())) {
                return;
            }

            TraceableCode traceableCode = new TraceableCode();
            traceableCode.setType(GoodsConst.DrugIdentificationCodeType.NO_CODE);
            traceableCode.setNo(noCodeTraceableCodeNoInfo.getDrugIdentificationCode());
            traceableCode.setCount(BigDecimal.ONE);
            clientDispenseItem.setTraceableCodeList(Collections.singletonList(traceableCode));
        });
    }

    public static int calculateTraceableCodeNum(DispensingFormItem dispensingFormItem, BigDecimal unitCount) {
        if (GoodsUtils.isChineseMedcine(dispensingFormItem.getProductType(), dispensingFormItem.getProductSubType())) {
            // 中药只采集一个
            return 1;
        } else {
            if (dispensingFormItem.getUseDismounting() == 1) {// 拆零也只采集一个
                return 1;
            } else {
                return unitCount != null ? unitCount.intValue() : 0;
            }
        }
    }

    /**
     * 不能发药的医嘱状态
     */
    public static List<Integer> canNotDispenseAdviceStatusList = Lists.newArrayList(AdviceStatus.INIT,
            AdviceStatus.UNDONE, AdviceStatus.UNDONE_CONFIRM);

    public static class AdviceReDispenseActionType {
        /**
         * 按总量
         */
        public static final int ADVICE = 1;
        /**
         * 按总量且有医嘱任务
         */
        public static final int EXECUTE_IN_ADVICE = 2;
        /**
         * 医嘱任务
         */
        public static final int EXECUTE = 3;
    }

    public static boolean checkFlagOn(Integer checkField, int bitCheck) {
        if (checkField != null) {
            return (checkField & bitCheck) > 0;
        }
        return false;
    }

    public static Integer onFlag(Integer field, int bitSet) {
        if (field != null) {
            return field | bitSet;
        }
        return null;
    }

    public static int offFlag(Integer field, int bitSet) {
        return field & ~bitSet;
    }

    /**
     * 可以重新生成发药的sourceSheetType
     */
    public static List<Integer> canReNewSheetSourceSheetTypeList =
            Lists.newArrayList(DispenseConst.SourceSheetType.ADVICE, DispenseConst.SourceSheetType.ADVICE_EXECUTE,
                    DispenseConst.SourceSheetType.ADVICE_SUPPLEMENT_ALONE,
                    DispenseConst.SourceSheetType.ADVICE_SUPPLEMENT_IN_EXECUTE, DispenseConst.SourceSheetType.USAGE_ASSOCIATION);

    /**
     * 获取所有可重发的 items
     */
    public static List<DispensingFormItem> getReDispensableItems(ChargeSheet chargeSheet, DispensingSheet dispensingSheet) {
        if (chargeSheet == null || dispensingSheet == null) {
            return new ArrayList<>();
        }

        if (dispensingSheet.getStatus() != DispensingSheet.Status.UNDISPENSED) {
            return new ArrayList<>();
        }

        // 所有已发的发药项
        List<DispensingFormItem> dispensedItems = DTOConverter.collectDispensingSheetItemsWithStatus(dispensingSheet, DispensingFormItem.Status.DISPENSED)
                .stream().filter(formItem -> formItem.getSourceItemType() == DispensingFormItem.SourceItemType.NORMAL).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dispensedItems)) {
            return new ArrayList<>();
        }

        List<ChargeFormItem> chargedChargeFormItems = DTOConverter.collectChargeSheetItemsWithStatus(chargeSheet, ChargeConstants.ChargeFormItemStatus.CHARGED);
        if (CollectionUtils.isEmpty(chargedChargeFormItems)) {
            return new ArrayList<>();
        }
        Map<String, ChargeFormItem> chargeFormItemIdToChargeFormItem = ListUtils.toMap(chargedChargeFormItems, ChargeFormItem::getId);

        return dispensedItems.stream().filter(dispenseItem -> {
            ChargeFormItem chargeFormItem = chargeFormItemIdToChargeFormItem.get(dispenseItem.getSourceFormItemId());
            if (chargeFormItem != null) {
                dispenseItem.setProductInfo(chargeFormItem.getProductInfo());
                return true;
            }
            return false;
        }).collect(Collectors.toList());
    }

    public static void checkTraceCodeNoLength(List<TraceableCode> traceableCodeList) {
        if (CollectionUtils.isEmpty(traceableCodeList)) {
            return;
        }
        traceableCodeList.forEach(it -> {
            if (org.springframework.util.StringUtils.isEmpty(it.getNo())) {
                return;
            }
            if (it.getNo().length() > 128) {
                throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR,
                        "追溯码" + it.getNo() + "错误");
            }
        });
    }

    public static boolean isSupportCloudDecoction(GoodsSupplierView cloudDecoctionSupplier, DispensingSheet dispensingSheet) {
        if (cloudDecoctionSupplier == null
                || cloudDecoctionSupplier.getShadowId() == null
                || cloudDecoctionSupplier.getExtendInfo() == null
                || cloudDecoctionSupplier.getExtendInfo().getCloudDecoctionSupplierInfo() == null
                || cloudDecoctionSupplier.getExtendInfo().getCloudDecoctionSupplierInfo().getPlatformType() == null) {
            return false;
        }

        CloudDecoctionSupplierInfo decoctionSupplierInfo = cloudDecoctionSupplier.getExtendInfo().getCloudDecoctionSupplierInfo();
        if (decoctionSupplierInfo.getPlatformType() == CloudDecoctionSupplierInfo.PlatformType.TANG_GU_YUAN_ZHI) {
            if (StringUtils.isEmpty(decoctionSupplierInfo.getOrganCode()) || StringUtils.isEmpty(decoctionSupplierInfo.getVirtualPharmacyCode())) {
                return false;
            }
        }

        return dispensingSheet.getDispensingForms().stream().anyMatch(form -> Objects.equals(String.valueOf(form.getVendorId()), cloudDecoctionSupplier.getShadowId()));
    }


    public static boolean isSupportCloudDecoctionWithView(GoodsSupplierView cloudDecoctionSupplier, DispensingSheetView dispensingSheet) {
        if (cloudDecoctionSupplier == null
                || cloudDecoctionSupplier.getShadowId() == null
                || cloudDecoctionSupplier.getExtendInfo() == null
                || cloudDecoctionSupplier.getExtendInfo().getCloudDecoctionSupplierInfo() == null) {
            return false;
        }

        CloudDecoctionSupplierInfo decoctionSupplierInfo = cloudDecoctionSupplier.getExtendInfo().getCloudDecoctionSupplierInfo();
        if (decoctionSupplierInfo.getPlatformType() == CloudDecoctionSupplierInfo.PlatformType.TANG_GU_YUAN_ZHI) {
            if (StringUtils.isEmpty(decoctionSupplierInfo.getOrganCode()) || StringUtils.isEmpty(decoctionSupplierInfo.getVirtualPharmacyCode())) {
                return false;
            }
        }

        return dispensingSheet.getDispensingForms().stream().anyMatch(form -> Objects.equals(String.valueOf(form.getVendorId()), cloudDecoctionSupplier.getShadowId()));
    }

    public static String getDiagnosisInfoText(List<DiagnosisInfo> diagnosisInfosReq, List<ExtendDiagnosisInfo> extendDiagnosisInfosReq, String diagnosisReq) {
        if (extendDiagnosisInfosReq != null) {
            return extendDiagnosisInfosReq.stream()
                    .map(dentistryDiagnosis -> {
                        List<DiagnosisInfo> value = dentistryDiagnosis.getValue();
                        if (org.springframework.util.CollectionUtils.isEmpty(value)) {
                            return null;
                        }
                        return value.stream().map(DiagnosisInfo::getName).filter(name -> !StringUtils.isEmpty(name)).collect(Collectors.joining("，"));
                    }).filter(item -> !StringUtils.isEmpty(item))
                    .collect(Collectors.joining("；"));
        } else if (diagnosisInfosReq != null) {
            return diagnosisInfosReq.stream().map(DiagnosisInfo::getName).filter(name -> !StringUtils.isEmpty(name)).collect(Collectors.joining("，"));
        } else if (!StringUtils.isEmpty(diagnosisReq)) {
            return diagnosisReq;
        }
        return null;
    }

    public static Map<String, DispensingForm> getDispensingFormMap(DispensingSheet dispensingSheet) {
        Map<String, DispensingForm> dispensingFormMap = new HashMap<>();

        doWithDispensingForm(dispensingSheet, form -> dispensingFormMap.put(form.getId(), form));

        return dispensingFormMap;
    }

}
