package cn.abcyun.cis.dispensing.util;

import org.apache.commons.lang.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Date;

public class DateUtils {

    public static final String DATE_FORMAT_YYYYMMDD_HHMM = "yyyy-MM-dd HH:mm";
    public static final String WECHAT_TIME_FORMAT = "yyyy-MM-dd HH:mm";
    public static final String WECHAT_PAY_SUCCESS_TIME_FORMAT = "yyyy-MM-dd";
    public static String DATE_FORMAT_COMPACT_DATETIME = "yyyyMMddHHmmss";

    public static String convertInstantToString(Instant instant, String format){
        if(instant == null){
            return "";
        }
        Date tmpDate= Date.from(instant);
        SimpleDateFormat formatter=new SimpleDateFormat(format);
        String time = formatter.format(tmpDate);
        return time;
    }

    public static SimpleDateFormat datetimeFormat(String format) {
        if (StringUtils.isEmpty(format)) {
            throw new RuntimeException("format不能为空");
        }
        return new SimpleDateFormat(format);
    }

    public static Date parseToDate(String dateStr, String format) {
        if(StringUtils.isEmpty(dateStr)) {
            return null;
        }

        Date date = null;
        try {
            date = datetimeFormat(format).parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return date;
    }

    /**
     * 是否为当月
     */
    public static boolean isCurrentMonth(Instant date) {
        if (date == null) {
            return false;
        }

        return isCurrentMonth(cn.abcyun.cis.commons.util.DateUtils.toLocalDateTime(date).toLocalDate(), LocalDate.now());
    }

    private static boolean isCurrentMonth(LocalDate date, LocalDate now) {
        return date.getYear() == now.getYear() && date.getMonthValue() == now.getMonthValue();
    }

}
