package cn.abcyun.cis.dispensing.util;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.TraceableCode;
import cn.abcyun.cis.commons.util.ObjectUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 发药服务追溯码工具类
 *
 * <AUTHOR>
 * @since 10/07/25 下午9:49
 **/
public class TraceCodeUtils {

    public static List<TraceableCode> mergeTraceableCode(BigDecimal pieceNum, List<TraceableCode> traceableCodeList) {
        if (CollectionUtils.isEmpty(traceableCodeList) || traceableCodeList.size() == 1) {
            return traceableCodeList;
        }

        // 合并 no、type、used 状态值都一样的追溯码
        Map<String, TraceableCode> traceableCodeMap = new LinkedHashMap<>();
        for (TraceableCode traceableCode : traceableCodeList) {
            String key = traceableCode.getNo() + ObjectUtils.getOrDefault(traceableCode.getType(), GoodsConst.DrugIdentificationCodeType.NORMAL) + traceableCode.getUsed();
            TraceableCode existTraceableCode = traceableCodeMap.get(key);
            if (existTraceableCode == null) {
                traceableCodeMap.put(key, traceableCode);
            } else {
                if (MathUtils.wrapBigDecimalCompare(traceableCode.getHisPieceCount(), BigDecimal.ZERO) > 0 || MathUtils.wrapBigDecimalCompare(traceableCode.getHisPackageCount(), BigDecimal.ZERO) > 0) {
                    if (MathUtils.wrapBigDecimalCompare(existTraceableCode.getHisPieceCount(), BigDecimal.ZERO) > 0
                            || MathUtils.wrapBigDecimalCompare(existTraceableCode.getHisPackageCount(), BigDecimal.ZERO) > 0) {
                        BigDecimal totalPieceCount = BigDecimal.ZERO;
                        if (existTraceableCode.getHisPieceCount() != null) {
                            totalPieceCount = MathUtils.wrapBigDecimalAdd(totalPieceCount, existTraceableCode.getHisPieceCount());
                        }
                        if (traceableCode.getHisPieceCount() != null) {
                            totalPieceCount = MathUtils.wrapBigDecimalAdd(totalPieceCount, traceableCode.getHisPieceCount());
                        }
                        if (traceableCode.getHisPackageCount() != null) {
                            BigDecimal tmp = traceableCode.getHisPackageCount().multiply(pieceNum);
                            totalPieceCount = MathUtils.wrapBigDecimalAdd(totalPieceCount, tmp);
                            traceableCode.setHisPackageCount(null);
                            traceableCode.setHisPieceCount(tmp);
                            if (StringUtils.isEmpty(traceableCode.getDismountingSn())) {
                                traceableCode.setDismountingSn(AbcIdUtils.getUUID());
                            }
                        }
                        if (existTraceableCode.getHisPackageCount() != null) {
                            BigDecimal tmp = existTraceableCode.getHisPackageCount().multiply(pieceNum);
                            totalPieceCount = MathUtils.wrapBigDecimalAdd(totalPieceCount, tmp);
                            existTraceableCode.setHisPackageCount(null);
                        }
                        existTraceableCode.setHisPieceCount(totalPieceCount);
                        if (StringUtils.isEmpty(existTraceableCode.getDismountingSn())) {
                            existTraceableCode.setDismountingSn(AbcIdUtils.getUUID());
                        }
                    } else {
                        existTraceableCode.setHisPackageCount(MathUtils.wrapBigDecimalAdd(existTraceableCode.getHisPackageCount(), traceableCode.getHisPackageCount()));
                    }
                } else {
                    //历史数据兼容
                    existTraceableCode.setCount(MathUtils.wrapBigDecimalAdd(
                            ObjectUtils.getOrDefault(existTraceableCode.getCount(), BigDecimal.ONE),
                            ObjectUtils.getOrDefault(traceableCode.getCount(), BigDecimal.ONE)));

                }
            }
        }

        return new ArrayList<>(traceableCodeMap.values());
    }


}
