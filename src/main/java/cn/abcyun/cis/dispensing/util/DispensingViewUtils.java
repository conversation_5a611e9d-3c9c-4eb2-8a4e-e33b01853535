package cn.abcyun.cis.dispensing.util;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeConstants;
import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispenseConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.*;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslocking.GoodsLockingFormItem;
import cn.abcyun.bis.rpc.sdk.property.base.PropertyKey;
import cn.abcyun.bis.rpc.sdk.property.model.ChainBasic;
import cn.abcyun.bis.rpc.sdk.property.model.PrintMedicalDocumentsInfusion;
import cn.abcyun.bis.rpc.sdk.property.service.PropertyService;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.model.CisPatientInfo;
import cn.abcyun.cis.commons.model.ShebaoCardInfo;
import cn.abcyun.cis.commons.rpc.charge.*;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.dispensing.api.protocol.DispensingSheetMessageRsp;
import cn.abcyun.cis.dispensing.base.DispensingConstants;
import cn.abcyun.cis.dispensing.controller.DTOConverter;
import cn.abcyun.cis.dispensing.controller.DispensingItemMerger;
import cn.abcyun.cis.dispensing.controller.StatusNameTranslator;
import cn.abcyun.cis.dispensing.domain.*;
import cn.abcyun.cis.dispensing.repository.DispensingLogRepository;
import cn.abcyun.cis.dispensing.rpc.client.OutpatientClient;
import cn.abcyun.cis.dispensing.rpc.model.outpatient.OutpatientSheetInfoDispensing;
import cn.abcyun.cis.dispensing.service.ChargeService;
import cn.abcyun.cis.dispensing.service.EmployeeService;
import cn.abcyun.cis.dispensing.service.PatientOrderService;
import cn.abcyun.cis.dispensing.service.dto.EmployeeView;
import cn.abcyun.cis.dispensing.service.dto.*;
import cn.abcyun.cis.dispensing.service.rpc.ClinicClient;
import cn.abcyun.cis.dispensing.service.rpc.GoodsLockingFeignClient;
import cn.abcyun.cis.dispensing.service.rpc.ScGoodsFeignClient;
import cn.abcyun.common.model.AbcServiceResponseBody;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 协议转化类 不用BeanUtils 是方便查看到底拷贝了哪些字段
 */
@Slf4j
public class DispensingViewUtils {

    /**
     * 自动打印消息 从 dispensingSheetView 构造 打印消息
     */
    public static DispensingSheetMessageRsp autoPrintMessageViewFromDispensingView(DispensingSheetView dispensingSheetView,
                                                                                   PrintMedicalDocumentsInfusion printConfig,
                                                                                   ChainBasic chainBasic) {
        DispensingSheetMessageRsp transView = new DispensingSheetMessageRsp();
        transView.setPatientOrderId(dispensingSheetView.getPatientOrderId());
        transView.setChainId(dispensingSheetView.getChainId());
        transView.setClinicId(dispensingSheetView.getClinicId());
        transView.setDoctorId(dispensingSheetView.getDoctorId());
        transView.setSourceSheetId(dispensingSheetView.getSourceSheetId());
        transView.setStatus(dispensingSheetView.getStatus());
        transView.setDeliveryType(dispensingSheetView.getDeliveryType());
        transView.setDispensedTime(dispensingSheetView.getDispensedTime());
        transView.setDispensedBy(dispensingSheetView.getDispensedBy());
        transView.setIsOnline(dispensingSheetView.getIsOnline());
        transView.setIsDecoction(dispensingSheetView.getIsDecoction());
        transView.setPrintedNumber(dispensingSheetView.getPrintedNumber());
        transView.setDispensingTag(dispensingSheetView.getDispensingTag());
        transView.setPharmacyType(dispensingSheetView.getPharmacyType());
        transView.setPharmacyNo(dispensingSheetView.getPharmacyNo());


        //---- 补下发这三个字段，这三个字段直接从sheet上出
        transView.setPatientId(dispensingSheetView.getPatientId());
        transView.setOrderByDate(dispensingSheetView.getOrderByDate());
        transView.setIsPatientSelfPay(dispensingSheetView.getIsPatientSelfPay());

        /**
         * 特殊处理字段部分
         * */
        transView.setDispensingId(dispensingSheetView.getId());
        /**
         * 核心在这里，这立也是需要  dispensingSheetView 的核心所在
         * */
        transView.setPrintable(DispensingViewUtils.generateDispensingSheetPrintable(dispensingSheetView,
                (printConfig == null) ? new PrintMedicalDocumentsInfusion() : printConfig,
                chainBasic == null ? new ChainBasic() : chainBasic));
        return transView;
    }

    /**
     * 将Sheet吐成View
     * TODO 历史原因 组织view和拉组装view的代码混到一起了
     *
     * @param chargeSheet  如果是收费同时发药，需要用那边传过来的收费单对象
     * @param clinicClient 通过这个对象是否位null来决定是否需要拉门诊单医生科室 目前只有通知到openAPI的消息需要拉
     *                     控制拉的场景，降低压力
     */
    public static DispensingSheetView fillDispensingSheetView(DispensingSheet dispensingSheet,
                                                              PatientOrderService patientOrderService,
                                                              ScGoodsFeignClient scGoodsFeignClient,
                                                              EmployeeService employeeService,
                                                              OutpatientClient outpatientClient,
                                                              PropertyService propertyService,
                                                              ChargeService chargeService,
                                                              ChargeSheet chargeSheet,
                                                              ClinicClient clinicClient) {
        /**
         * 拉取patientOrder
         * */
        PatientOrder patientOrder = null;
        //拉过避免重复拉
        if (dispensingSheet != null && dispensingSheet.getPatientOrder() != null) {
            patientOrder = dispensingSheet.getPatientOrder();
        } else {
            patientOrder = patientOrderService.findPatientOrder(dispensingSheet.getPatientOrderId());
            if (dispensingSheet != null) {
                dispensingSheet.setPatientOrder(patientOrder);
            }
        }
        DispensingSheetView dispensingSheetView = DispensingViewUtils.dispensingSheetViewFromDispensingSheet(dispensingSheet, patientOrder, scGoodsFeignClient, employeeService);
        if (dispensingSheetView == null) {
            throw new NotFoundException();
        }

        /**
         * 拉取打印配置 和收费单配置
         * */
        PrintMedicalDocumentsInfusion printConfig = propertyService.getPropertyValueByKey(PropertyKey.PRINT_MEDICAL_DOCUMENTS_INFUSION, dispensingSheetView.getClinicId(), PrintMedicalDocumentsInfusion.class);
        ChainBasic chainBasic = propertyService.getPropertyValueByKey(PropertyKey.CHAIN_BASIC, dispensingSheet.getChainId(), ChainBasic.class);
        /**
         * 收费同时发药 只能用调用里面传过来的chargeSheet 不能去查
         * 因为charge那边这个时候数据还没落地，去查也查不到
         * */
        if (chargeSheet == null) {
            //拉过避免重复拉
            if (dispensingSheet.getChargeSheet() != null) {
                chargeSheet = dispensingSheet.getChargeSheet();
            } else {
                chargeSheet = chargeService.getChargeSheetById(dispensingSheet.getSourceSheetId());
                dispensingSheet.setChargeSheet(chargeSheet);
            }
        }
        if (chargeSheet != null) {
            //非空为OPENAPI 避免拉多了这里有性能问题
            if (clinicClient != null) {
                if (!StringUtils.isEmpty(chargeSheet.getDepartmentId())) {
                    dispensingSheetView.setDepartment(clinicClient.queryDepartmentById(chargeSheet.getDepartmentId()));
                }
                dispensingSheetView.setDoctor(clinicClient.queryEmployeeById(chargeSheet.getDoctorId(), dispensingSheet.getChainId()));
            }
            Map<String, ChargeFormItem> chargeFormIdToFormItem = chargeSheet.getChargeForms().stream().flatMap(it -> it.getChargeFormItems().stream()).collect(Collectors.toMap(ChargeFormItem::getId, Function.identity(), (a, b) -> a));
            AtomicReference<BigDecimal> totalFee = new AtomicReference<>(BigDecimal.ZERO);
            dispensingSheet.getDispensingForms().stream().flatMap(it -> it.getDispensingFormItems().stream()).forEach(dispensingFormItem -> {

                ChargeFormItem chargeFormItem = chargeFormIdToFormItem.get(dispensingFormItem.getSourceFormItemId());
                if (chargeFormItem != null && chargeFormItem.getReceivedPrice() != null) {
                    totalFee.set(totalFee.get().add(chargeFormItem.getReceivedPrice()));
                }
            });
            dispensingSheetView.setNetIncomeFee(totalFee.get());
            /**
             * 社保支付方式
             * */
            if (chargeSheet.getChargeTransactions() != null) {
                for (ChargeTransaction chargeTransaction : chargeSheet.getChargeTransactions()) {
                    if (chargeTransaction.getIsPaidback() == 0
                            && chargeTransaction.getPayMode() == ChargeConstants.ChargePayMode.HEALTH_CARD) {
                        dispensingSheetView.setIsShebaoPay(1);
                        break;
                    }
                }
            } else {
                if (dispensingSheet.hasDispensingTag(DispensingUtils.DispensingTag.DISPENSING_TAG_SHEBAO_PAY_FLAG)) {
                    dispensingSheetView.setIsShebaoPay(1);
                }
            }

            //TODO  以下注释字段会在多药房分支上补齐，目前这个消息还没有用户用
            DispensingViewUtils.mergeDispensingSheetView(dispensingSheetView, printConfig, null, outpatientClient, chainBasic);
            dispensingSheetView.setChargedTime(chargeSheet.getChargedTime());
            dispensingSheetView.setDiagnose(chargeSheet.getDiagnosis());
            /**
             * TODO 目前这个分支只能本地药房发消息走进来，代煎代配的分支复杂删掉，不要了
             * */
            if (dispensingSheetView.getPharmacyType() == GoodsConst.PharmacyType.LOCAL_PHARMACY && chargeSheet.getDeliveryInfo() != null) {
                //两个对象里面的deliverInfo不一样 手动设置下
                ChargeDeliveryInfoView chargeDeliveryInfoView = new ChargeDeliveryInfoView();
                BeanUtils.copyProperties(chargeSheet.getDeliveryInfo(), chargeDeliveryInfoView);
                if (chargeSheet.getDeliveryInfo().getDeliveryCompany() != null) {
                    ChargeDeliveryCompanyView deliveryCompany = new ChargeDeliveryCompanyView();
                    deliveryCompany.setId(chargeSheet.getDeliveryInfo().getDeliveryCompany().getId());
                    deliveryCompany.setName(chargeSheet.getDeliveryInfo().getDeliveryCompany().getName());
                    chargeDeliveryInfoView.setDeliveryCompany(deliveryCompany);
                }
                dispensingSheetView.setDeliveryInfo(chargeDeliveryInfoView);
            }
        }
        return dispensingSheetView;
    }

    /**
     * 将Sheets批量吐成Views
     */
    public static List<DispensingSheetView> batchFillDispensingSheetView(String chainId,
                                                                         String clinicId,
                                                                         String sourceSheetId,
                                                                         String patientOrderId,
                                                                         List<DispensingSheet> allDispensingSheets,
                                                                         PatientOrderService patientOrderService,
                                                                         ScGoodsFeignClient scGoodsFeignClient,
                                                                         EmployeeService employeeService,
                                                                         OutpatientClient outpatientClient,
                                                                         PropertyService propertyService,
                                                                         ChargeService chargeService,
                                                                         DispensingLogRepository dispensingItemLogRepository,
                                                                         GoodsLockingFeignClient goodsLockingFeignClient) {
        /**
         * 拉取patientOrder
         * */
        PatientOrder patientOrder = patientOrderService.findPatientOrder(patientOrderId);
        OutpatientSheetInfoDispensing outpatientSheet = Optional.ofNullable(outpatientClient.getSimpleOutpatientSheetByPatientOrderId(chainId, patientOrderId)).map(AbcServiceResponseBody::getData).orElse(null);
        SimpleChargeInfo simpleChargeInfo = chargeService.getSimpleChargeInfo(sourceSheetId);

        List<DispensingSheetView> dispensingSheetViews =
                DispensingViewUtils.batchDispensingSheetViewsFromDispensingSheets(chainId, clinicId,
                        allDispensingSheets, patientOrder, scGoodsFeignClient, employeeService,
                        dispensingItemLogRepository, chargeService, goodsLockingFeignClient, simpleChargeInfo);
        if (CollectionUtils.isEmpty(dispensingSheetViews)) {
            return dispensingSheetViews;
        }

        /**
         * 拉取打印配置 和收费单配置
         * */
        PrintMedicalDocumentsInfusion printConfig = propertyService.getPropertyValueByKey(PropertyKey.PRINT_MEDICAL_DOCUMENTS_INFUSION, clinicId, PrintMedicalDocumentsInfusion.class);
        ChainBasic chainBasic = propertyService.getPropertyValueByKey(PropertyKey.CHAIN_BASIC, chainId, ChainBasic.class);
        //SimpleChargeInfo simpleChargeInfo = chargeService.getSimpleChargeInfo(sourceSheetId);
        dispensingSheetViews.forEach(dispensingSheetView -> DispensingViewUtils.mergeDispensingSheetView(dispensingSheetView, printConfig, simpleChargeInfo, outpatientSheet, true, chainBasic));

        // 填充批次信息
        fillDispensingFormItemBatchInfo(clinicId, dispensingSheetViews, allDispensingSheets, scGoodsFeignClient);

        return dispensingSheetViews;
    }

    private static void fillDispensingFormItemBatchInfo(String clinicId,
                                                        List<DispensingSheetView> dispensingSheetViews,
                                                        List<DispensingSheet> dispensingSheets,
                                                        ScGoodsFeignClient scGoodsFeignClient) {
        if (CollectionUtils.isEmpty(dispensingSheetViews) || CollectionUtils.isEmpty(dispensingSheets)) {
            return;
        }

        Map<String, GetGoodsStockBatchesReq.GetGoodsStockBatch> goodsIdToGetGoodsStockBatch = new HashMap<>();
        DispensingUtils.doWithDispensingItem(dispensingSheets, item -> {
            List<DispensingFormItemBatchInfo> batchInfoList = item.getDispensingFormItemBatches();
            if (CollectionUtils.isEmpty(batchInfoList)) {
                return;
            }

            for (DispensingFormItemBatchInfo batchInfo : batchInfoList) {
                // 如果已经拉过goods了而且goods批次上有这个批次，一个发药项不可能锁很多批次，性能上直接for
                if (item.getGoodsItem() != null && !org.springframework.util.CollectionUtils.isEmpty(item.getGoodsItem().getGoodsBatchInfoList())) {
                    for (GoodsBatchInfo goodsBatchInfo : item.getGoodsItem().getGoodsBatchInfoList()) {
                        //因为一个fromItem里面一个批次只能锁一次，这个GoodsItem已经通过keyId精确回射到这个FormItem的了
                        //所以这里批次信息不可能叉，找到一个就可以退了
                        if (goodsBatchInfo.getBatchId() != null && goodsBatchInfo.getBatchId().compareTo(batchInfo.getBatchId()) == 0) {
                            batchInfo.setGoodsBatchInfo(goodsBatchInfo);
                            break;
                        }
                    }
                }
            }
        });

        // 查询批次信息
        DispensingUtils.doWithDispensingItemBatch(dispensingSheets, batchInfo -> {
            //拉goods已经拉到批次，已经把Goods批次绑定到了DispensingFormItemBatchInfo上
            if (batchInfo.getGoodsBatchInfo() != null) {
                return;
            }
            GetGoodsStockBatchesReq.GetGoodsStockBatch goodsStockBatch = goodsIdToGetGoodsStockBatch.get(batchInfo.getProductId());
            if (goodsStockBatch == null) {
                goodsStockBatch = new GetGoodsStockBatchesReq.GetGoodsStockBatch();
                goodsStockBatch.setGoodsId(batchInfo.getProductId());
                goodsStockBatch.setBatchIds(new ArrayList<>());
                goodsIdToGetGoodsStockBatch.put(batchInfo.getProductId(), goodsStockBatch);
            }
            goodsStockBatch.getBatchIds().add(batchInfo.getBatchId());
        });

        // TODO ...历史逻辑兼容，如果拉回来的goods没有，用原来代码逻辑补一手
        // TODO ... 这个分支返回的批次信息，无法实现批次是否不足的判断
        if (!goodsIdToGetGoodsStockBatch.isEmpty()) {
            GetGoodsStockBatchesRsp goodsStockBatchesRsp = scGoodsFeignClient.getGoodsStockBatches(clinicId, new ArrayList<>(goodsIdToGetGoodsStockBatch.values()));
            // 一个批次被调拨 「药房间调拨」落到不同药房；「门店间调拨」落到不同门店；因为这里只查当前门店的批次，所以这里只用批次ID和药房号作为key
            Map<Pair<Long, Integer>, GoodsBatchInfo> batchIdPharmacyNoToBatchInfo = Optional.ofNullable(goodsStockBatchesRsp).map(GetGoodsStockBatchesRsp::getList)
                    .orElseGet(Lists::newArrayList).stream()
                    .map(BatchesGoodsStockItem::getList).flatMap(Collection::stream)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(batchInfo -> Pair.of(batchInfo.getBatchId(), batchInfo.getPharmacyNo()), Function.identity(), (a, b) -> a));
            log.info("保护下理论上永远进不了这个分支,这个分支返回的批次信息，无法实现批次是否不足的判断:{}", batchIdPharmacyNoToBatchInfo);
            //绑定回去 原始for 少点匿名对象
            for (DispensingSheet dispensingSheet : dispensingSheets) {
                if (CollectionUtils.isEmpty(dispensingSheet.getDispensingForms())) {
                    continue;
                }
                for (DispensingForm dispensingForm : dispensingSheet.getDispensingForms()) {
                    if (CollectionUtils.isEmpty(dispensingForm.getDispensingFormItems())) {
                        continue;
                    }
                    for (DispensingFormItem dispensingFormItem : dispensingForm.getDispensingFormItems()) {
                        if (CollectionUtils.isEmpty(dispensingFormItem.getDispensingFormItemBatches())) {
                            continue;
                        }
                        for (DispensingFormItemBatchInfo dispensingFormItemBatch : dispensingFormItem.getDispensingFormItemBatches()) {
                            if (dispensingFormItemBatch.getGoodsBatchInfo() != null) {
                                continue;
                            }
                            Pair<Long, Integer> key = Pair.of(dispensingFormItemBatch.getBatchId(), dispensingFormItem.getPharmacyNo());
                            GoodsBatchInfo goodsBatchInfo = batchIdPharmacyNoToBatchInfo.get(key);
                            if (goodsBatchInfo == null) {
                                continue;
                            }
                            dispensingFormItemBatch.setGoodsBatchInfo(goodsBatchInfo);
                        }
                    }
                }
            }
        }

        Map<String, DispensingSheet> dispensingSheetIdToDispensingSheet = ListUtils.toMap(dispensingSheets, DispensingSheet::getId);
        dispensingSheetViews.forEach(dispensingSheetView -> {
            DispensingSheet dispensingSheet = dispensingSheetIdToDispensingSheet.get(dispensingSheetView.getId());
            fillDispensingFormItemBatchInfo(dispensingSheetView, dispensingSheet);
        });
    }

    private static void fillDispensingFormItemBatchInfo(DispensingSheetView dispensingSheetView,
                                                        DispensingSheet dispensingSheet) {
        if (dispensingSheetView == null || dispensingSheet == null) {
            return;
        }


        List<DispensingFormItemBatchInfo> allDispensingFormItemBatches = dispensingSheet.getAllDispensingFormItemBatches();
        Map<String, List<DispensingFormItemBatchInfo>> dispenseFormItemIdToBatchInfoList = ListUtils.groupByKey(allDispensingFormItemBatches, DispensingFormItemBatchInfo::getDispensingFormItemId);
        DispensingViewUtils.doWithDispensingItemView(dispensingSheetView, formItemView -> {
            String dispenseFormItemId = !StringUtils.isBlank(formItemView.getAssociateFormItemId()) ? formItemView.getAssociateFormItemId() : formItemView.getId();
            List<DispensingFormItemBatchInfo> batchInfoList = dispenseFormItemIdToBatchInfoList.get(dispenseFormItemId);
            if (CollectionUtils.isEmpty(batchInfoList)) {
                return;
            }

            // 将批次信息进行拆分，分成已发药、待发药、已退药
            List<DispensingFormItemBatchInfoView> dispenseBatchList = new ArrayList<>();
            List<DispensingFormItemBatchInfoView> waitDispenseBatchList = new ArrayList<>();
            List<DispensingFormItemBatchInfoView> undispenseBatchList = new ArrayList<>();
            for (DispensingFormItemBatchInfo batchInfo : batchInfoList) {
                // 待发药数量
                BigDecimal waitDispenseCount = MathUtils.wrapBigDecimalSubtract(batchInfo.getUnitCount(), batchInfo.getDispenseUnitCount());
                // 已发药数量
                BigDecimal dispenseCount = MathUtils.wrapBigDecimalSubtract(batchInfo.getDispenseUnitCount(), batchInfo.getUndispenseUnitCount());
                // 已退药数量
                BigDecimal undispenseCount = batchInfo.getUndispenseUnitCount();
                if (MathUtils.wrapBigDecimalCompare(waitDispenseCount, BigDecimal.ZERO) > 0) {
                    waitDispenseBatchList.add(DispensingFormItemBatchInfoView.from(batchInfo, waitDispenseCount, batchInfo.getGoodsBatchInfo()));
                }
                if (MathUtils.wrapBigDecimalCompare(dispenseCount, BigDecimal.ZERO) > 0) {
                    dispenseBatchList.add(DispensingFormItemBatchInfoView.from(batchInfo, dispenseCount, batchInfo.getGoodsBatchInfo()));
                }
                if (MathUtils.wrapBigDecimalCompare(undispenseCount, BigDecimal.ZERO) > 0) {
                    undispenseBatchList.add(DispensingFormItemBatchInfoView.from(batchInfo, undispenseCount, batchInfo.getGoodsBatchInfo()));
                }
            }

            if (formItemView.getStatus() == DispensingFormItem.Status.WAITING) {
                formItemView.setDispensingFormItemBatches(waitDispenseBatchList);
            } else if (formItemView.getStatus() == DispensingFormItem.Status.DISPENSED) {
                formItemView.setDispensingFormItemBatches(dispenseBatchList);
            } else if (formItemView.getStatus() == DispensingFormItem.Status.PART_DISPENSE) {
                formItemView.setDispensingFormItemBatches(waitDispenseBatchList);
                if (!CollectionUtils.isEmpty(formItemView.getOperationLogs())) {
                    formItemView.getOperationLogs().forEach(operationLog -> {
                        operationLog.setDispensingFormItemBatches(dispenseBatchList);
                    });
                }
            } else if (formItemView.getStatus() == DispensingFormItem.Status.UNDISPENSED) {
                formItemView.setDispensingFormItemBatches(undispenseBatchList);
            } else if (formItemView.getStatus() == DispensingFormItem.Status.CANCELED) {
                formItemView.setDispensingFormItemBatches(waitDispenseBatchList);
            }
        });
    }

    /**
     * 协议封装
     */
    public static List<DispensingSheetView> batchDispensingSheetViewsFromDispensingSheets(String chainId,
                                                                                          String clinicId,
                                                                                          List<DispensingSheet> dispensingSheets,
                                                                                          PatientOrder patientOrder,
                                                                                          ScGoodsFeignClient scGoodsFeignClient,
                                                                                          EmployeeService employeeService,
                                                                                          DispensingLogRepository dispensingItemLogRepository,
                                                                                          ChargeService chargeService,
                                                                                          GoodsLockingFeignClient goodsLockingFeignClient,
                                                                                          SimpleChargeInfo simpleChargeInfo) {

        // 1、获取goodsItem集合
        Map<Integer, List<GoodsItem>> pharmacyNoToGoodsItemList = queryAllGoodsItems(chainId, clinicId, dispensingSheets, scGoodsFeignClient, simpleChargeInfo);

        // 2、获取所有发药人信息集合
        Function<DispensingSheet, Stream<String>> dispensingSheetDispensingByIdsFunc = dispensingSheet -> {
            Set<String> employeeIds = new HashSet<>();
            if (StringUtils.isNotBlank(dispensingSheet.getDispensedBy()) && !StringUtils.equals("00000000000000000000000000000000", dispensingSheet.getDispensedBy())) {
                employeeIds.add(dispensingSheet.getDispensedBy());
            }
            if (StringUtils.isNotEmpty(dispensingSheet.getDoctorId()) && !StringUtils.equals("00000000000000000000000000000000", dispensingSheet.getDoctorId())) {
                employeeIds.add(dispensingSheet.getDoctorId());
            }
            employeeIds.addAll(
                    Optional.ofNullable(dispensingSheet.getDispensedByIds()).orElse(Lists.newArrayList())
                            .stream()
                            .filter(dispensingById -> StringUtils.isNotBlank(dispensingSheet.getDispensedBy()) && !StringUtils.equals("00000000000000000000000000000000", dispensingSheet.getDispensedBy()))
                            .collect(Collectors.toSet())
            );
            return employeeIds.stream();
        };
        List<EmployeeView> employeeViews = EmployeeView.from(
                employeeService.findEmployees(
                        dispensingSheets
                                .stream()
                                .flatMap(dispensingSheetDispensingByIdsFunc)
                                .distinct()
                                .collect(Collectors.toList()), chainId
                )
        );

        // 3、所有药房的集合
        Map<Integer, GoodsPharmacyView> pharmacyNoViewMap = scGoodsFeignClient.findPharmacyByClinic(chainId, clinicId)
                .stream()
                .collect(Collectors.toMap(GoodsPharmacyView::getNo, Function.identity()));

        // 4. 如果有发药单状态为已退药的，需要标识发药单是否支持重新发药
        List<String> supportReDispensingDispensingSheetIds = getSupportReDispensingSheetIds(dispensingSheets, dispensingItemLogRepository);
        Set<String> chargedChargeFormItemIds;
        if (!CollectionUtils.isEmpty(supportReDispensingDispensingSheetIds)) {
            // 由于目前的场景传进来的发药单列表都是同一个收费单，所以取第一个就行了
            ChargeSheet chargeSheet = chargeService.getChargeSheetById(dispensingSheets.get(0).getSourceSheetId());
            chargedChargeFormItemIds = DTOConverter.collectChargeSheetItemsWithStatus(chargeSheet, ChargeConstants.ChargeFormItemStatus.CHARGED)
                    .stream().map(ChargeFormItem::getId).collect(Collectors.toSet());
        } else {
            chargedChargeFormItemIds = Collections.emptySet();
        }

        // 5、转成view
        return dispensingSheets
                .stream()
                .map(dispensingSheet -> {
                    DispensingSheetView dispensingSheetView = DispensingSheetView.from(dispensingSheet);
                    // 社保信息
                    if (patientOrder != null) {
                        dispensingSheetView.setSource(patientOrder.getSource());
                        dispensingSheetView.setPatientOrderNo(String.valueOf(patientOrder.getNo()));
                        dispensingSheetView.setAirPharmacyOrderId(patientOrder.getAirPharmacyOrderId());
                        // 之前写入的 shebaoCardInfo 是经历过 jsonDump -> jsonRead 流程的，不知道什么原因，在进价加成里面改成直接 set 进行，如果有问题再改回去
                        dispensingSheetView.setShebaoCardInfo(patientOrder.getShebaoCardInfo());
                    }

                    // 直接从patientOrde里面哪 patient信（DispensingPrintService.findDispensingPrintLabel  是通过crm拿患者信息 都一次rpc感觉都差不多）
                    CisPatientInfo patient = DispensingUtils.parseCisPatientInfoFromPatientOrder(patientOrder);
                    dispensingSheetView.setPatient(patient);

                    List<GoodsItem> goodsItems = pharmacyNoToGoodsItemList.get(dispensingSheet.getPharmacyNo());
                    if (!CollectionUtils.isEmpty(goodsItems)) {
                        dispensingSheetView.bindProductInfo(ListUtils.toMap(goodsItems, GoodsItem::getKeyId));
                    }

                    // 发药人 多发药人
                    dispensingSheetView.bindDispenseUserName(employeeViews, dispensingSheet.getDispensedByIds());

                    // 药品备注
                    dispensingSheetView.initMaterialRemark();

                    // 设置发药单、发药item药房名称
                    GoodsPharmacyView sheetPharmacyView = pharmacyNoViewMap.getOrDefault(dispensingSheetView.getPharmacyNo(), new GoodsPharmacyView());
                    dispensingSheetView.setPharmacyName(sheetPharmacyView.getName());
                    dispensingSheetView.setPharmacyExtendInfo(sheetPharmacyView.getExtendInfo());

                    // 发药单支持重新发药，并且由可重新发药的 item
                    if (supportReDispensingDispensingSheetIds.contains(dispensingSheet.getId())) {
                        dispensingSheetView.setSupportReDispense(markDispenseItemViewSupportReDispense(dispensingSheetView, chargedChargeFormItemIds));
                    }

                    Optional.ofNullable(dispensingSheetView.getDispensingForms())
                            .orElse(Lists.newArrayList())
                            .stream()
                            .filter(dispensingFormView -> !CollectionUtils.isEmpty(dispensingFormView.getDispensingFormItems()))
                            .flatMap(dispensingFormView -> dispensingFormView.getDispensingFormItems().stream())
                            .forEach(dispensingFormItemView -> {
                                GoodsPharmacyView itemPharmacyView = pharmacyNoViewMap.getOrDefault(dispensingFormItemView.getPharmacyNo(), new GoodsPharmacyView());
                                dispensingFormItemView.setPharmacyName(itemPharmacyView.getName());
                                dispensingFormItemView.setPharmacyExtendInfo(itemPharmacyView.getExtendInfo());
//                                GoodsLockingFormItem goodsLockingFormItem = lockingFormItemIdToFormItem.get(dispensingFormItemView.getSourceFormItemId());
//                                if (goodsLockingFormItem != null) {
//                                    dispensingFormItemView.setGoodsLockBatchItemList(goodsLockingFormItem.getGoodsLockBatchItemList());
//                                }
                            });

                    if (simpleChargeInfo != null) {
                        // 优先以是 charge 查询到的信息为准
                        dispensingSheetView.setIsShebaoPay(simpleChargeInfo.getHasShebaoPay());
                    }

                    // 是当月创建的发药单，则支持补报追溯码
                    if (DateUtils.isCurrentMonth(dispensingSheet.getCreated())) {
                        dispensingSheetView.setCanReReportTraceCode(1);
                    }

                    return dispensingSheetView;
                })
                .collect(Collectors.toList());
    }

    private static int markDispenseItemViewSupportReDispense(DispensingSheetView dispensingSheet, Set<String> chargedChargeFormItemIds) {
        if (dispensingSheet == null || CollectionUtils.isEmpty(chargedChargeFormItemIds) || dispensingSheet.getIsDeleted() == 1) {
            return 0;
        }

        List<DispensingFormView> dispensingForms = dispensingSheet.getDispensingForms();
        if (CollectionUtils.isEmpty(dispensingForms)) {
            return 0;
        }

        boolean hasSupportReDispenseItem = false;
        for (DispensingFormView dispensingForm : dispensingForms) {
            List<DispensingFormItemView> dispensingFormItems = dispensingForm.getDispensingFormItems();
            if (CollectionUtils.isEmpty(dispensingFormItems)) {
                continue;
            }

            for (DispensingFormItemView dispensingFormItem : dispensingFormItems) {
                if (dispensingFormItem == null) {
                    continue;
                }

                if (chargedChargeFormItemIds.contains(dispensingFormItem.getSourceFormItemId())) {
                    dispensingFormItem.setSupportReDispense(1);
                    hasSupportReDispenseItem = true;
                }

                List<DispensingFormItemView> composeChildren = dispensingFormItem.getComposeChildren();
                if (CollectionUtils.isEmpty(composeChildren)) {
                    continue;
                }
                for (DispensingFormItemView composeChildDispensingFormItem : composeChildren) {
                    if (composeChildDispensingFormItem == null) {
                        continue;
                    }

                    if (chargedChargeFormItemIds.contains(composeChildDispensingFormItem.getSourceFormItemId())) {
                        composeChildDispensingFormItem.setSupportReDispense(1);
                        hasSupportReDispenseItem = true;
                    }
                }
            }
        }
        return hasSupportReDispenseItem ? 1 : 0;
    }

    private static List<String> getSupportReDispensingSheetIds(List<DispensingSheet> dispensingSheets, DispensingLogRepository dispensingItemLogRepository) {
        List<String> unDispensedDispensingSheetIds = dispensingSheets.stream()
                .filter(dispensingSheet -> dispensingSheet.getStatus() == DispensingSheet.Status.UNDISPENSED)
                .map(DispensingSheet::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(unDispensedDispensingSheetIds)) {
            return new ArrayList<>();
        }

        List<DispensingLogItem> allDispensingItemDispenseLogs = dispensingItemLogRepository.findAllByDispensingSheetIdInAndType(unDispensedDispensingSheetIds, DispensingLogItem.Type.DISPENSE);
        Map<String, List<DispensingLogItem>> dispensingSheetIdToDispensingItemDispenseLogs = ListUtils.groupByKey(allDispensingItemDispenseLogs, DispensingLogItem::getDispensingSheetId);
        return dispensingSheets.stream().filter(dispensingSheet -> {
            List<DispensingLogItem> dispensingItemDispenseLogs = dispensingSheetIdToDispensingItemDispenseLogs.get(dispensingSheet.getId());
            if (CollectionUtils.isEmpty(dispensingItemDispenseLogs)) {
                return false;
            }

            // 判断已退药的发药单是否支持重新发药的核心逻辑
            int dispensingSheetSupportReDispense = DispensingUtils.dispensingSheetSupportReDispense(dispensingSheet, dispensingItemDispenseLogs);
            return dispensingSheetSupportReDispense == 1;
        }).map(DispensingSheet::getId).collect(Collectors.toList());
    }

    private static Map<Integer, List<GoodsItem>> queryAllGoodsItems(String chainId, String clinicId,
                                                                    List<DispensingSheet> dispensingSheets,
                                                                    ScGoodsFeignClient scGoodsFeignClient,
                                                                    SimpleChargeInfo simpleChargeInfo) {
        String departmentId;
        if (simpleChargeInfo != null) {
            departmentId = StringUtils.isEmpty(simpleChargeInfo.getDepartmentId()) ? simpleChargeInfo.getSellerDepartmentId() : simpleChargeInfo.getDepartmentId();
        } else {
            departmentId = null;
        }
        Map<String, DispensingForm> formIdToForm = dispensingSheets.stream().filter(it -> it.getDispensingForms() != null)
                .flatMap(it -> it.getDispensingForms().stream()).collect(Collectors.toMap(DispensingForm::getId, Function.identity(), (a, b) -> a));
        Map<Integer, QueryGoodsInPharmacyByIdsAndStockCountReq.QueryPharmacyGoodsReq> pharmacyNoToQueryPharmacyGoodsReq = new HashMap<>();
        dispensingSheets.forEach(dispensingSheet -> DispensingUtils.doWithDispensingItem(dispensingSheet, dispensingFormItem -> {
            int pharmacyNo = dispensingFormItem.getPharmacyNo();
            Integer processType;
            Integer processSubType;
            DispensingForm dispensingForm = formIdToForm.get(dispensingFormItem.getDispensingFormId());
            if (dispensingForm != null && dispensingForm.getSourceFormType() == DispensingForm.SourceFormType.PRESCRIPTION_CHINESE) {
                DispenseUsageInfo dispenseUsageInfo = JsonUtils.readValue(dispensingForm.getUsageInfoJson(), DispenseUsageInfo.class);
                if (dispenseUsageInfo != null) {
                    processType = dispenseUsageInfo.getUsageType();
                    processSubType = dispenseUsageInfo.getUsageSubType();
                } else {
                    processType = null;
                    processSubType = null;
                }
            } else {
                processType = null;
                processSubType = null;
            }
            QueryGoodsInPharmacyByIdsAndStockCountReq.QueryPharmacyGoodsReq queryPharmacyGoodsReq = pharmacyNoToQueryPharmacyGoodsReq.computeIfAbsent(pharmacyNo, k -> {
                QueryGoodsInPharmacyByIdsAndStockCountReq.QueryPharmacyGoodsReq req = new QueryGoodsInPharmacyByIdsAndStockCountReq.QueryPharmacyGoodsReq();
                req.setPharmacyNo(pharmacyNo);
                req.setSceneType(GoodsConst.SceneType.OUTPATIENT);
                req.setDepartmentId(departmentId);
                req.setProcessType(processType);
                req.setProcessSubType(processSubType);
                req.setList(Lists.newArrayList());
                return req;
            });
            QueryGoodsInPharmacyByIdsAndStockCountReq.QueryGoodsWithStock queryGoodsWithStock = new QueryGoodsInPharmacyByIdsAndStockCountReq.QueryGoodsWithStock();
            queryGoodsWithStock.setGoodsId(dispensingFormItem.getProductId());
            if (dispensingFormItem.isDispensable()) {
                queryGoodsWithStock.setLockId(dispensingFormItem.getLockId()); //如果没有lockId拉回来的也就是现在的Goods
            }
            queryGoodsWithStock.setKeyId(dispensingFormItem.getId()); //每个formItem的返回都不一样
            queryPharmacyGoodsReq.getList().add(queryGoodsWithStock);
        }));

        List<QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp> queryPharmacyGoodsRsps = scGoodsFeignClient.queryGoodsInPharmacyByIds(clinicId, chainId, true, 1, new ArrayList<>(pharmacyNoToQueryPharmacyGoodsReq.values()));
        /**
         * 把GoodsItem挂回到dispenseingFormItem上
         * */
        Map<String, GoodsItem> dispenseFormItemIdToGoodsItem = new HashMap<>();
        for (QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp queryPharmacyGoodsRsp : queryPharmacyGoodsRsps) {
            if (CollectionUtils.isEmpty(queryPharmacyGoodsRsp.getList())) {
                continue;
            }
            for (GoodsItem goodsItem : queryPharmacyGoodsRsp.getList()) {
                dispenseFormItemIdToGoodsItem.put(goodsItem.getKeyId(), goodsItem);
            }
        }
        dispensingSheets.forEach(dispensingSheet -> DispensingUtils.doWithDispensingItem(dispensingSheet, dispensingFormItem -> {
            GoodsItem goodsItem = dispenseFormItemIdToGoodsItem.get(dispensingFormItem.getId());
            if (goodsItem != null) {
                dispensingFormItem.setGoodsItem(goodsItem);
            }
        }));

        return Optional.ofNullable(queryPharmacyGoodsRsps)
                .orElse(new ArrayList<>())
                .stream()
                .filter(queryPharmacyGoodsRsp -> !CollectionUtils.isEmpty(queryPharmacyGoodsRsp.getList()))
                // 药房号分组
                .flatMap(queryPharmacyGoodsRsp ->
                        queryPharmacyGoodsRsp.getList().stream()
                                .map(goodsItem -> new AbstractMap.SimpleEntry<>(queryPharmacyGoodsRsp.getPharmacyNo(), goodsItem)))
                .collect(Collectors.groupingBy(Map.Entry::getKey, Collectors.mapping(Map.Entry::getValue, Collectors.toList())));
    }

    /**
     * 协议封装
     */
    public static DispensingSheetView dispensingSheetViewFromDispensingSheet(DispensingSheet dispensingSheet,
                                                                             PatientOrder patientOrder,
                                                                             ScGoodsFeignClient scGoodsFeignClient,
                                                                             EmployeeService employeeService) {

        DispensingSheetView dispensingSheetView = DispensingSheetView.from(dispensingSheet);
        /**
         * 社保信息
         * */
        if (patientOrder != null) {
            dispensingSheetView.setSource(patientOrder.getSource());
            dispensingSheetView.setPatientOrderNo(patientOrder.getNo() + "");
            dispensingSheetView.setAirPharmacyOrderId(patientOrder.getAirPharmacyOrderId());
            // todo 适配common包改动对象转换为JsonNode
            ShebaoCardInfo shebaoCardInfo = null;
            if (Objects.nonNull(patientOrder.getShebaoCardInfo())) {
                ObjectMapper mapper = new ObjectMapper();
                try {
                    String jsonStr = mapper.writeValueAsString(patientOrder.getShebaoCardInfo());
                    shebaoCardInfo = mapper.readValue(jsonStr, ShebaoCardInfo.class);
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                }
            }
            dispensingSheetView.setShebaoCardInfo(shebaoCardInfo);
        }
        /**
         * 直接从patientOrde里面哪 patient信（DispensingPrintService.findDispensingPrintLabel  是通过crm拿患者信息 都一次rpc感觉都差不多）
         * */
        CisPatientInfo patient = DispensingUtils.parseCisPatientInfoFromPatientOrder(patientOrder);
        dispensingSheetView.setPatient(patient);

        /**
         * 从scGoods拿当前goods信息,这里应该不是快照感觉也有问题？
         * */
        dispensingSheetView.refreshProductInfo(scGoodsFeignClient, dispensingSheet);

        /**
         * 发药人 多发药人
         * */
        dispensingSheetView.bindDispenseUserName(employeeService, dispensingSheet.getDispensedByIds(), dispensingSheet.getChainId());

        /**
         * ？？
         * */
        dispensingSheetView.initMaterialRemark();
        return dispensingSheetView;
    }

    public static void filterHistoryItem(DispensingSheetView dispensingSheet) {
        if (dispensingSheet == null || CollectionUtils.isEmpty(dispensingSheet.getDispensingForms())) {
            return;
        }

        List<DispensingFormView> dispensingForms = dispensingSheet.getDispensingForms();
        dispensingForms.forEach(dispensingFormView -> {
            if (dispensingFormView == null || CollectionUtils.isEmpty(dispensingFormView.getDispensingFormItems())) {
                return;
            }

            dispensingFormView.setDispensingFormItems(dispensingFormView.getDispensingFormItems().stream()
                    .filter(item -> item.getIsHistoryItem() == 0).collect(Collectors.toList()));
        });
    }

    /**
     * 完整发药单 自动打印消息 告知前端这个发药单能打印哪些东西 ，前端再根据这个开关来拉数据
     *
     * @param dispensingSheetView 发药sheetView
     * @param printConfig         打印配置
     */
    public static DispensingSheetPrintable generateDispensingSheetPrintable(DispensingSheetView dispensingSheetView,
                                                                            PrintMedicalDocumentsInfusion printConfig,
                                                                            ChainBasic chainBasic) {
        DispensingSheetPrintable dispensingSheetPrintable = new DispensingSheetPrintable();
        boolean isPartPrint = false;
        final Set<String> dispensingFormIdSet = new HashSet();
        if (!CollectionUtils.isEmpty(dispensingSheetView.getPrintDispensingFormIdList())) {
            isPartPrint = dispensingSheetView.getPrintDispensingFormIdList().size() < dispensingSheetView.getDispensingForms().size() ? true : false;
            dispensingFormIdSet.addAll(dispensingSheetView.getPrintDispensingFormIdList().stream().collect(Collectors.toSet()));
        }
        /**
         * 只处理部分form的打印
         * */
        if (isPartPrint) {
            //发药form
            dispensingSheetPrintable.setPrintDispensingFormIdList(dispensingSheetView.getPrintDispensingFormIdList());
            //收费form
            dispensingSheetPrintable.setPrintChargeFormIdList(dispensingSheetView.getDispensingForms().stream()
                    .filter(form -> dispensingFormIdSet.contains(form.getId()))
                    .map(DispensingFormView::getSourceFormId)
                    .collect(Collectors.toList()));
            //门诊form
            dispensingSheetPrintable.setPrintOutpatientFormIdList(dispensingSheetView.getPrintOutpatientFormIdList());
        }


        /**
         * 发药小票
         * */
        dispensingSheetPrintable.setDispensingSheet(dispensingSheetView.getStatus() == DispensingSheet.Status.WAITING || dispensingSheetView.getStatus() == DispensingSheet.Status.DISPENSED);

        /**
         * 输液注射单 打印
         * */
        List<DispensingFormItemView> printFormItemList = null;
        if (isPartPrint) {
            printFormItemList = DispensingUtils.getDispensingFormItemViews(dispensingSheetView, dispensingFormIdSet);
        } else {
            printFormItemList = DispensingUtils.getDispensingFormItemViews(dispensingSheetView, null);
        }
        dispensingSheetPrintable.setExecuteInfusionSheet(printFormItemList.stream()
                .filter(dispensingFormItem -> DispensingFormItem.Status.available.contains(dispensingFormItem.getStatus()))
                .anyMatch(item -> DispensingUtils.isExecutableItem(item, printConfig.getContent() != null ? printConfig.getContent().getIncludeExternal() : 0)));

        /**
         * 用药标签
         * */
        List<DispensingFormItemView> medicineFormViewList = null;
        if (isPartPrint) {
            medicineFormViewList = DispensingUtils.getDispensingFormItemViewsFilterMaterialAndProductAndCompose(dispensingSheetView, dispensingFormIdSet);
        } else {
            medicineFormViewList = DispensingUtils.getDispensingFormItemViewsFilterMaterialAndProductAndCompose(dispensingSheetView, null);
        }
        if (dispensingSheetView.getSourceSheetType() != null && dispensingSheetView.getSourceSheetType() == ChargeSheet.Type.DIRECT_SALE) {
            //零售只有中药处方才打印用药标签
            dispensingSheetPrintable.setMedicineTag(Optional.ofNullable(dispensingSheetView.getDispensingForms()).orElse(new ArrayList<>())
                    .stream()
                    .filter(dispensingForm -> dispensingForm.getSourceFormType() == cn.abcyun.cis.commons.rpc.charge.Constants.SourceFormType.PRESCRIPTION_CHINESE)
                    .filter(dispensingForm -> dispensingForm.getDispensingFormItems() != null)
                    .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream())
                    .anyMatch(dispensingFormItem -> DispensingFormItem.Status.available.contains(dispensingFormItem.getStatus())));
        } else {
            dispensingSheetPrintable.setMedicineTag(medicineFormViewList.stream()
                    .anyMatch(dispensingFormItem -> DispensingFormItem.Status.available.contains(dispensingFormItem.getStatus())));
        }

        /**
         * 患者标签
         * */
        dispensingSheetPrintable.setPatientTag(dispensingSheetView.getPatient() != null && !DispensingConstants.ANONYMOUS_PATIENT_ID.equals(dispensingSheetView.getPatient().getId()));

        /**
         * 是否打印处方
         * */
        if (dispensingSheetView.getSourceSheetType() != null && dispensingSheetView.getSourceSheetType() == ChargeSheet.Type.CLONE_PRESCRIPTION) {
            // https://www.tapd.cn/43780818/bugtrace/bugs/view/1143780818001058155
            // 续房不打印发药单，收费去掉了历史续房，药房这里漏了
            dispensingSheetPrintable.setPrescription(false);
        } else {
            dispensingSheetPrintable.setPrescription(medicineFormViewList.stream()
                    .filter(dispensingFormItem -> DispensingFormItem.Status.available.contains(dispensingFormItem.getStatus()))
                    .anyMatch(dispensingFormItem -> !TextUtils.isEmpty(dispensingFormItem.getOutpatientFormItemId())));
        }

        dispensingSheetPrintable.setExecuteTransfusionSheet(dispensingSheetPrintable.isExecuteInfusionSheet());

        dispensingSheetPrintable.setExecuteTherapySheet(dispensingSheetPrintable.isExecuteInfusionSheet());

        final boolean[] existUndispense = {false};
        DispensingViewUtils.doWithDispensingItemView(dispensingSheetView, item -> {
            if (existUndispense[0]) {
                return;
            }

            existUndispense[0] = item.getStatus() == DispensingFormItem.Status.UNDISPENSED;
        });
        dispensingSheetPrintable.setUndispenseSheet(existUndispense[0]);
        dispensingSheetPrintable.setDecoctionCraftCard(chainBasic.getIsEnableDecoctionCraftCard() == YesOrNo.YES);

        return dispensingSheetPrintable;
    }

    /**
     * OpenApi需要同不门诊相关信息出去
     */
    public static DispensingSheetView mergeDispensingSheetView(DispensingSheetView dispensingSheetView,
                                                               PrintMedicalDocumentsInfusion printConfig,
                                                               SimpleChargeInfo simpleChargeInfo,
                                                               OutpatientClient outpatientClient,
                                                               ChainBasic chainBasic) {
        if (dispensingSheetView == null) {
            return null;
        }

        boolean needPrintable = false;
        OutpatientSheetInfoDispensing outpatientSheet = null;
        if (outpatientClient != null && dispensingSheetView.getOutpatientSheet() == null) {
            needPrintable = true;

            AbcServiceResponseBody<OutpatientSheetInfoDispensing> responseBody = outpatientClient.getSimpleOutpatientSheetByPatientOrderId(dispensingSheetView.getChainId(), dispensingSheetView.getPatientOrderId());
            /**
             * 有的外部api医馆是要先检查就诊信息再上传处方
             * 所以这里直接全吐了，也不管是什么处方
             * */
            if (Objects.nonNull(responseBody) && Objects.nonNull(responseBody.getData())) {
                /***
                 * 存起来，如果这里拉了，后面用的地方就不用拉了
                 * */
                outpatientSheet = responseBody.getData();
            }
        }

        return mergeDispensingSheetView(dispensingSheetView, printConfig, simpleChargeInfo, outpatientSheet, needPrintable, chainBasic);
    }

    /**
     * OpenApi需要同不门诊相关信息出去
     */
    public static DispensingSheetView mergeDispensingSheetView(DispensingSheetView dispensingSheetView,
                                                               PrintMedicalDocumentsInfusion printConfig,
                                                               SimpleChargeInfo simpleChargeInfo,
                                                               OutpatientSheetInfoDispensing outpatientSheet,
                                                               boolean needPrintable,
                                                               ChainBasic chainBasic) {

        if (dispensingSheetView == null) {
            return null;
        }

        DispensingItemMerger.mergeForDispensingSheetView(dispensingSheetView);
        StatusNameTranslator.translate(dispensingSheetView);

        /**
         * 可以打印信息的统计
         * */
        sortDispensingSheet(dispensingSheetView);
        printConfig = (printConfig == null) ? new PrintMedicalDocumentsInfusion() : printConfig;
        chainBasic = (chainBasic == null) ? new ChainBasic() : chainBasic;
        /**
         * 不是所有场景都需要
         * */
        if (needPrintable) {
            dispensingSheetView.setPrintable(DispensingViewUtils.generateDispensingSheetPrintable(dispensingSheetView, printConfig, chainBasic));
        }
        if (outpatientSheet != null && dispensingSheetView.getOutpatientSheet() == null) {
            dispensingSheetView.setOutpatientSheet(outpatientSheet);
        }
        if (dispensingSheetView.getOutpatientSheet() != null) {
            dispensingSheetView.setMedicalRecord(dispensingSheetView.getOutpatientSheet().getMedicalRecord());
            dispensingSheetView.setDiagnosedDate(dispensingSheetView.getOutpatientSheet().getDiagnosedDate());
        }

        /**
         * 收费单上的信息
         * */
        if (simpleChargeInfo != null) {
            dispensingSheetView.setChargedByName(simpleChargeInfo.getChargedByName());
            dispensingSheetView.setChargedTime(simpleChargeInfo.getChargedTime());
            dispensingSheetView.setNetIncomeFee(simpleChargeInfo.getNetIncomeFee());
            dispensingSheetView.setDoctorAdvice(simpleChargeInfo.getDoctorAdvice());
            dispensingSheetView.setDiagnose(simpleChargeInfo.getDiagnose());
            boolean noDoctorId = StringUtils.isEmpty(simpleChargeInfo.getDoctorId());
            dispensingSheetView.setSellerDepartmentId(noDoctorId ? simpleChargeInfo.getSellerDepartmentId() : simpleChargeInfo.getDepartmentId());
            dispensingSheetView.setSellerDepartmentName(noDoctorId ? simpleChargeInfo.getSellerDepartmentName() : simpleChargeInfo.getDepartmentName());
            dispensingSheetView.setSellerId(noDoctorId ? simpleChargeInfo.getSellerId() : simpleChargeInfo.getDoctorId());
            dispensingSheetView.setSellerName(noDoctorId ? simpleChargeInfo.getSellerName() : simpleChargeInfo.getDoctorName());
            /**
             * 代煎代配发药单 要从药房发药了，需要把快递加工信息吐出去
             * */
            if (dispensingSheetView.getPharmacyType() == GoodsConst.PharmacyType.LOCAL_PHARMACY) {
                dispensingSheetView.setDeliveryInfo(simpleChargeInfo.getDeliveryInfo());
            } else if (dispensingSheetView.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY) {
                /**
                 * 代煎代配药房 一个form 拆了一个发药单
                 * 把代煎代配的快递信息 提到 外围
                 * */
                if (!CollectionUtils.isEmpty(dispensingSheetView.getDispensingForms())) {
                    DispensingFormView dispensingForm = dispensingSheetView.getDispensingForms().get(0);
                    SimpleChargeInfo.ChargeFormSimpleView chargeForm = simpleChargeInfo.getVirtualPharmacyChargeForms().stream().filter(c -> c.getId().compareTo(dispensingForm.getSourceFormId()) == 0).findFirst().orElse(null);
                    /**
                     * 返回煎药类型
                     * */
                    if (chargeForm != null) {
                        dispensingSheetView.setMedicineStateScopeId(chargeForm.getMedicineStateScopeId());
                    }
                    if (chargeForm != null && chargeForm.getDeliveryInfo() != null) {
                        dispensingSheetView.setDeliveryInfo(chargeForm.getDeliveryInfo());
                    }
                }
            }
        }
        return dispensingSheetView;
    }

    private static void sortDispensingSheet(DispensingSheetView sheet) {
        if (sheet == null || sheet.getDispensingForms() == null) {
            return;
        }

        sheet.getDispensingForms().sort((a, b) -> {
            if (ObjectUtils.compare(a.getSourceFormType(), b.getSourceFormType()) != 0) {
                return ObjectUtils.compare(a.getSourceFormType(), b.getSourceFormType());
            } else {
                return ObjectUtils.compare(a.getSort(), b.getSort());
            }
        });

        sheet.getDispensingForms().stream()
                .filter(dispensingForm -> dispensingForm.getDispensingFormItems() != null)
                .forEach(dispensingForm -> dispensingForm.getDispensingFormItems().sort((a, b) -> ObjectUtils.compare(a.getSort(), b.getSort())));
    }

    public static DispensingSheetView fillDispensingSheetView(DispensingSheetV2 dispensingSheet,
                                                              PatientOrderService patientOrderService,
                                                              ScGoodsFeignClient scGoodsFeignClient,
                                                              EmployeeService employeeService,
                                                              OutpatientClient outpatientClient) {
        /**
         * 拉取patientOrder
         * */
        PatientOrder patientOrder = null;
        //拉过避免重复拉
        if (dispensingSheet != null && dispensingSheet.getPatientOrder() != null) {
            patientOrder = dispensingSheet.getPatientOrder();
        } else {
            patientOrder = patientOrderService.findPatientOrder(dispensingSheet.getPatientOrderId());
            if (dispensingSheet != null) {
                dispensingSheet.setPatientOrder(patientOrder);
            }
        }
        DispensingSheetView dispensingSheetView = DispensingViewUtils.dispensingSheetViewFromDispensingSheet(dispensingSheet, patientOrder, scGoodsFeignClient, employeeService, outpatientClient);
        if (dispensingSheetView == null) {
            throw new NotFoundException();
        }
        if (patientOrder != null) {
            dispensingSheetView.setDepartmentId(patientOrder.getId());
        }
        dispensingSheetView.setDispensingOrder(null);
        return dispensingSheetView;
    }

    /**
     * 协议封装
     */
    public static DispensingSheetView dispensingSheetViewFromDispensingSheet(DispensingSheetV2 dispensingSheet,
                                                                             PatientOrder patientOrder,
                                                                             ScGoodsFeignClient scGoodsFeignClient,
                                                                             EmployeeService employeeService,
                                                                             OutpatientClient outpatientClient) {

        DispensingSheetView dispensingSheetView = DispensingSheetView.from(dispensingSheet);
        // 过滤自备药
        dispensingSheetView.getDispensingForms().forEach(form -> {
            List<DispensingFormItemView> formItemViewList = form.getDispensingFormItems().stream().filter(item -> {
                if (item.isSelfProvided()) {
                    log.info("自备药不收费，不发送消息给收费，formItem = {}", JsonUtils.dump(item));
                    return false;
                }
                return true;
            }).collect(Collectors.toList());
            form.setDispensingFormItems(formItemViewList);
        });
        /**
         * 社保信息
         * */
        if (patientOrder != null) {
            dispensingSheetView.setSource(patientOrder.getSource());
            dispensingSheetView.setPatientOrderNo(patientOrder.getNo() + "");
            dispensingSheetView.setAirPharmacyOrderId(patientOrder.getAirPharmacyOrderId());
            // todo 适配common包改动对象转换为JsonNode
            ShebaoCardInfo shebaoCardInfo = null;
            if (Objects.nonNull(patientOrder.getShebaoCardInfo())) {
                ObjectMapper mapper = new ObjectMapper();
                try {
                    String jsonStr = mapper.writeValueAsString(patientOrder.getShebaoCardInfo());
                    shebaoCardInfo = mapper.readValue(jsonStr, ShebaoCardInfo.class);
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                }
            }
            dispensingSheetView.setShebaoCardInfo(shebaoCardInfo);
        }
        /**
         * 直接从patientOrde里面哪 patient信（DispensingPrintService.findDispensingPrintLabel  是通过crm拿患者信息 都一次rpc感觉都差不多）
         * */
        CisPatientInfo patient = DispensingUtils.parseCisPatientInfoFromPatientOrder(patientOrder);
        dispensingSheetView.setPatient(patient);

        /**
         * 从scGoods拿当前goods信息,这里应该不是快照感觉也有问题？
         * */
        dispensingSheetView.refreshProductInfo(scGoodsFeignClient, null);

        /**
         * 发药人 多发药人
         * */
        dispensingSheetView.bindDispenseUserName(employeeService, Arrays.asList(dispensingSheet.getDispensedBy()), dispensingSheet.getChainId());

        /**
         * ？？
         * */
        dispensingSheetView.initMaterialRemark();
        return dispensingSheetView;
    }

    /**
     * 为了视觉上加起来数量要等于总数量，所以重新计算部分发药的发药项 unitCount、doseCount、totalCount、totalPrice
     *
     * @param dispensingSheetView 发药单
     */
    public static void reCalculatePartDispenseFormItemCount(DispensingSheetView dispensingSheetView) {
        if (dispensingSheetView == null || CollectionUtils.isEmpty(dispensingSheetView.getDispensingForms())) {
            return;
        }

        List<DispensingFormView> forms = dispensingSheetView.getDispensingForms();
        forms.forEach(form -> {
            if (form == null || CollectionUtils.isEmpty(form.getDispensingFormItems())) {
                return;
            }

            int sourceFormType = form.getSourceFormType();
            boolean byDose = sourceFormType == DispenseConst.SourceFormType.PRESCRIPTION_CHINESE;
            List<DispensingFormItemView> items = form.getDispensingFormItems();
            Map<String, BigDecimal> dispenseItemIdToUnDispenseCount = items.stream().filter(item -> item.getStatus() == DispensingFormItem.Status.UNDISPENSED)
                    .collect(Collectors.groupingBy(DispensingFormItemView::getAssociateFormItemId,
                            Collectors.reducing(BigDecimal.ZERO, item -> byDose ? item.getDoseCount() : item.getUnitCount(), MathUtils::wrapBigDecimalAdd)));

            items.forEach(item -> {
                if (item == null) {
                    return;
                }

                if (item.getStatus() != DispensingFormItem.Status.PART_DISPENSE) {
                    return;
                }

                BigDecimal unDispenseCount = dispenseItemIdToUnDispenseCount.getOrDefault(item.getId(), BigDecimal.ZERO);
                if (byDose) {
                    item.setDoseCount(MathUtils.wrapBigDecimalSubtract(item.getDoseCount(), unDispenseCount));
                } else {
                    item.setUnitCount(MathUtils.wrapBigDecimalSubtract(item.getUnitCount(), unDispenseCount));
                }

                List<DispensingFormItemView> operationLogs = item.getOperationLogs();
                operationLogs.forEach(operationLog -> {
                    if (byDose) {
                        item.setDoseCount(MathUtils.wrapBigDecimalSubtract(item.getDoseCount(), operationLog.getDoseCount()));
                    } else {
                        item.setUnitCount(MathUtils.wrapBigDecimalSubtract(item.getUnitCount(), operationLog.getUnitCount()));
                    }
                });

            });
        });
    }

    public static void doWithDispensingFormView(DispensingSheetView dispensingSheetView, Consumer<DispensingFormView> consumer) {
        if (dispensingSheetView == null || CollectionUtils.isEmpty(dispensingSheetView.getDispensingForms())) {
            return;
        }

        dispensingSheetView.getDispensingForms().forEach(form -> {
            if (form == null) {
                return;
            }

            consumer.accept(form);
        });
    }

    public static void doWithDispensingItemView(DispensingSheetView dispensingSheetView, Consumer<DispensingFormItemView> consumer) {
        if (dispensingSheetView == null || CollectionUtils.isEmpty(dispensingSheetView.getDispensingForms())) {
            return;
        }

        dispensingSheetView.getDispensingForms().forEach(form -> doWithDispensingItemView(form, consumer));
    }

    private static void doWithDispensingItemView(DispensingFormView form, Consumer<DispensingFormItemView> consumer) {
        if (form == null || CollectionUtils.isEmpty(form.getDispensingFormItems())) {
            return;
        }

        form.getDispensingFormItems().forEach(item -> {
            if (item == null) {
                return;
            }

            consumer.accept(item);
        });
    }
}
