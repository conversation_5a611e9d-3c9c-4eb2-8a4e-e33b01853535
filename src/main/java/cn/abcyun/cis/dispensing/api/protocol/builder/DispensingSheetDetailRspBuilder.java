package cn.abcyun.cis.dispensing.api.protocol.builder;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsSupplierView;
import cn.abcyun.bis.rpc.sdk.property.service.PropertyService;
import cn.abcyun.cis.dispensing.api.protocol.DispensingSheetDetailRsp;
import cn.abcyun.cis.dispensing.api.protocol.RelateDispensingFormView;
import cn.abcyun.cis.dispensing.controller.DispensingItemMerger;
import cn.abcyun.cis.dispensing.domain.DispensingForm;
import cn.abcyun.cis.dispensing.domain.DispensingFormItem;
import cn.abcyun.cis.dispensing.domain.DispensingSheet;
import cn.abcyun.cis.dispensing.repository.DispensingLogRepository;
import cn.abcyun.cis.dispensing.rpc.client.OutpatientClient;
import cn.abcyun.cis.dispensing.service.ChargeService;
import cn.abcyun.cis.dispensing.service.EmployeeService;
import cn.abcyun.cis.dispensing.service.PatientOrderService;
import cn.abcyun.cis.dispensing.service.calling.DispensingCallingItemService;
import cn.abcyun.cis.dispensing.service.dto.DispensingFormItemView;
import cn.abcyun.cis.dispensing.service.dto.DispensingFormView;
import cn.abcyun.cis.dispensing.service.dto.DispensingSheetView;
import cn.abcyun.cis.dispensing.service.rpc.GoodsLockingFeignClient;
import cn.abcyun.cis.dispensing.service.rpc.ScGoodsFeignClient;
import cn.abcyun.cis.dispensing.util.DispensingUtils;
import cn.abcyun.cis.dispensing.util.DispensingViewUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 发药单详情响应构建器（是一个非常重的构建器）
 */
public class DispensingSheetDetailRspBuilder {

    private String chainId;

    private String clinicId;

    private String sourceSheetId;

    private String patientOrderId;

    /**
     * 选中的发药单
     */
    private String selectDispensingSheetId;

    private DispensingSheet selectDispensingSheet;

    /**
     * 收费单所有的发药单
     */
    private List<DispensingSheet> allDispensingSheets;

    // ******************** Dependency-START ***************************
    private PatientOrderService patientOrderService;
    private ScGoodsFeignClient scGoodsFeignClient;
    private OutpatientClient outpatientClient;
    private EmployeeService employeeService;
    private PropertyService propertyService;
    private ChargeService chargeService;
    private DispensingLogRepository dispensingItemLogRepository;
    private DispensingCallingItemService dispensingCallingItemService;
    private GoodsLockingFeignClient goodsLockingFeignClient;
    // ******************** Dependency-START ***************************

    public DispensingSheetDetailRspBuilder(String chainId,
                                           String clinicId,
                                           DispensingSheet selectDispensingSheet,
                                           List<DispensingSheet> allDispensingSheets) {
        this.chainId = chainId;
        this.clinicId = clinicId;
        this.selectDispensingSheetId = selectDispensingSheet.getId();
        this.selectDispensingSheet = selectDispensingSheet;
        this.sourceSheetId = selectDispensingSheet.getSourceSheetId();
        this.patientOrderId = selectDispensingSheet.getPatientOrderId();
        this.allDispensingSheets = allDispensingSheets;
    }

    public void bindDependency(PatientOrderService patientOrderService,
                               ScGoodsFeignClient scGoodsFeignClient,
                               EmployeeService employeeService,
                               OutpatientClient outpatientClient,
                               PropertyService propertyService,
                               ChargeService chargeService,
                               DispensingLogRepository dispensingItemLogRepository,
                               DispensingCallingItemService dispensingCallingItemService,
                               GoodsLockingFeignClient goodsLockingFeignClient) {
        this.patientOrderService = patientOrderService;
        this.scGoodsFeignClient = scGoodsFeignClient;
        this.employeeService = employeeService;
        this.outpatientClient = outpatientClient;
        this.propertyService = propertyService;
        this.chargeService = chargeService;
        this.dispensingItemLogRepository = dispensingItemLogRepository;
        this.dispensingCallingItemService = dispensingCallingItemService;
        this.goodsLockingFeignClient = goodsLockingFeignClient;
    }

    public DispensingSheetDetailRsp build() {
        List<DispensingSheetView> dispensingSheetViews = DispensingViewUtils.batchFillDispensingSheetView(chainId,
                clinicId, sourceSheetId, patientOrderId, allDispensingSheets, patientOrderService,
                scGoodsFeignClient, employeeService, outpatientClient, propertyService, chargeService,
                dispensingItemLogRepository, goodsLockingFeignClient);

        DispensingSheetView selectDispensingSheetView = dispensingSheetViews.stream()
                .filter(dispensingSheetView -> StringUtils.equals(dispensingSheetView.getId(), selectDispensingSheetId))
                .findFirst().orElse(null);
        List<DispensingSheetView> otherDispensingSheetViews = dispensingSheetViews.stream()
                .filter(dispensingSheetView -> !StringUtils.equals(dispensingSheetView.getId(), selectDispensingSheetId))
                .collect(Collectors.toList());
        List<DispensingSheet> otherDispensingSheets = allDispensingSheets.stream()
                .filter(dispensingSheet -> !StringUtils.equals(dispensingSheet.getId(), selectDispensingSheetId))
                .collect(Collectors.toList());

        // 构建 relateDispensingForm
        Map<String, String> dispensingSheetIdToPharmacyName = otherDispensingSheetViews.stream()
                .filter(dispensingSheetView -> StringUtils.isNotBlank(dispensingSheetView.getPharmacyName()) && StringUtils.isNotBlank(dispensingSheetView.getId()))
                .collect(Collectors.toMap(DispensingSheetView::getId, DispensingSheetView::getPharmacyName, (a, b) -> a));
        fillRelateDispensingForm(selectDispensingSheetView, otherDispensingSheetViews, otherDispensingSheets, dispensingSheetIdToPharmacyName);

        // 重新计算部分发药 formItem 的 unitCount 和 doseCount 等字段
        DispensingViewUtils.reCalculatePartDispenseFormItemCount(selectDispensingSheetView);

        // 查询叫号项ID
        if (selectDispensingSheetView.getStatus() == DispensingSheet.Status.WAITING) {
            Long dispensingCallingItemId = dispensingCallingItemService.getDispensingCallingItemIdByDispensingSheetId(chainId, clinicId, selectDispensingSheetView.getId(), selectDispensingSheetView.getCreated());
            selectDispensingSheetView.setDispensingCallingItemId(dispensingCallingItemId);
        }

        //推送到唐古云煎药服务的不允许部分退药
        if (selectDispensingSheetView.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY) {
            GoodsSupplierView cloudDecoctionSupplier = scGoodsFeignClient.getCloudDecoctionSupplier(selectDispensingSheetView.getChainId(), selectDispensingSheetView.getClinicId());
            if (DispensingUtils.isSupportCloudDecoctionWithView(cloudDecoctionSupplier, selectDispensingSheetView)) {
                selectDispensingSheetView.setCanPartUnDispense(0);
            }
        }

        return new DispensingSheetDetailRsp()
                .setDispensingSheet(selectDispensingSheetView)
                .setOtherDispensingSheets(otherDispensingSheetViews);
    }

    /**
     * 同一个处方，由于处方中的每个药可能在不同的药房中，到了发药这边因为会按照药房的维度拆分发药单，
     * 所以这里主要干的事情就是，展示每个药房 form 它关联的所有其他药房的 form
     */
    private void fillRelateDispensingForm(DispensingSheetView selectDispensingSheet,
                                          List<DispensingSheetView> otherDispensingSheetViews,
                                          List<DispensingSheet> otherDispensingSheets,
                                          Map<String, String> dispensingSheetIdToPharmacyName) {
        if (selectDispensingSheet == null || CollectionUtils.isEmpty(selectDispensingSheet.getDispensingForms())
                || CollectionUtils.isEmpty(otherDispensingSheets)) {
            return;
        }

        List<DispensingFormView> targetDispensingForms = selectDispensingSheet.getDispensingForms();
        List<String> sourceFormIds = targetDispensingForms.stream().map(DispensingFormView::getSourceFormId).collect(Collectors.toList());
        // 从其他发药单的 form 中找到 sourceFormId 相同的 form
        Map<String, List<DispensingForm>> sourceFormIdToRelateDispensingForms = new HashMap<>(sourceFormIds.size());
        otherDispensingSheets.forEach(otherDispensingSheet -> {
            if (otherDispensingSheet == null || CollectionUtils.isEmpty(otherDispensingSheet.getDispensingForms())) {
                return;
            }

            List<DispensingForm> relateDispensingForms = otherDispensingSheet.getDispensingForms().stream()
                    .filter(dispensingForm -> sourceFormIds.contains(dispensingForm.getSourceFormId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(relateDispensingForms)) {
                return;
            }

            relateDispensingForms.forEach(relateDispensingForm -> {
                if (relateDispensingForm == null || CollectionUtils.isEmpty(relateDispensingForm.getDispensingFormItems())) {
                    return;
                }

                String sourceFormId = relateDispensingForm.getSourceFormId();
                List<DispensingForm> dispensingForms = sourceFormIdToRelateDispensingForms.getOrDefault(
                        sourceFormId, Lists.newArrayList());
                dispensingForms.add(relateDispensingForm);
                sourceFormIdToRelateDispensingForms.put(sourceFormId, dispensingForms);
            });
        });
        if (CollectionUtils.isEmpty(sourceFormIdToRelateDispensingForms)) {
            return;
        }

        Map<String, DispensingFormItemView> dispenseItemIdToDispensingItemView = buildDispensingItemMapForFieldFill(otherDispensingSheetViews);

        // 填充关联的发药单
        targetDispensingForms.forEach(targetDispensingForm -> {
            if (targetDispensingForm == null) {
                return;
            }

            List<DispensingForm> relateDispensingForm = sourceFormIdToRelateDispensingForms.get(targetDispensingForm.getSourceFormId());
            if (CollectionUtils.isEmpty(relateDispensingForm)) {
                return;
            }

            // 合并 dispensingForm
            RelateDispensingFormView mergedRelateDispensingFormView = buildRelateDispensingFormView(dispensingSheetIdToPharmacyName, relateDispensingForm, dispenseItemIdToDispensingItemView);
            targetDispensingForm.setRelateDispensingForm(mergedRelateDispensingFormView);
        });
    }

    private Map<String, DispensingFormItemView> buildDispensingItemMapForFieldFill(List<DispensingSheetView> dispensingSheetViews) {
        if (CollectionUtils.isEmpty(dispensingSheetViews)) {
            return new HashMap<>();
        }

        Map<String, DispensingFormItemView> dispenseItemIdToDispensingItemView = new HashMap<>();
        for (DispensingSheetView dispensingSheetView : dispensingSheetViews) {
            dispenseItemIdToDispensingItemView.putAll(buildDispensingItemMapForFieldFill(dispensingSheetView));
        }
        return dispenseItemIdToDispensingItemView;
    }

    private Map<String, DispensingFormItemView> buildDispensingItemMapForFieldFill(DispensingSheetView dispensingSheetView) {
        if (dispensingSheetView == null || CollectionUtils.isEmpty(dispensingSheetView.getDispensingForms())) {
            return new HashMap<>();
        }

        Map<String, DispensingFormItemView> dispenseItemIdToDispensingItemView = new HashMap<>();
        DispensingViewUtils.doWithDispensingItemView(dispensingSheetView, dispensingFormItemView -> {
            String dispenseItemId = StringUtils.isBlank(dispensingFormItemView.getAssociateFormItemId()) ?
                    dispensingFormItemView.getId() : dispensingFormItemView.getAssociateFormItemId();
            dispenseItemIdToDispensingItemView.put(dispenseItemId, dispensingFormItemView);
        });
        return dispenseItemIdToDispensingItemView;
    }

    /**
     * 构建关联的发药单
     * 用关联发药单的原始对象计算出发药项最终状态和并取其剂量和单位数量，用发药单视图做其他参数的填充
     */
    private RelateDispensingFormView buildRelateDispensingFormView(Map<String, String> dispensingSheetIdToPharmacyName,
                                                                   List<DispensingForm> relateDispensingForms,
                                                                   Map<String, DispensingFormItemView> dispenseItemIdToDispensingItemView) {
        if (CollectionUtils.isEmpty(relateDispensingForms)) {
            return null;
        }

        List<DispensingFormItem> relateDispensingFormItems = relateDispensingForms.stream()
                .map(DispensingForm::getDispensingFormItems)
                .flatMap(Collection::stream).collect(Collectors.toList());

        List<DispensingFormItem> mergedDispenseItems = DispensingItemMerger.mergeToDispenseItem(relateDispensingFormItems);
        RelateDispensingFormView mergedRelateDispensingForm = new RelateDispensingFormView();
        List<DispensingFormItemView> relateDispensingFormItemViews = mergedDispenseItems.stream().map(dispensingFormItem -> {
            String dispenseItemId = StringUtils.isNotBlank(dispensingFormItem.getAssociateFormItemId()) ? dispensingFormItem.getAssociateFormItemId() : dispensingFormItem.getId();
            DispensingFormItemView dispensingFormItemView = dispenseItemIdToDispensingItemView.get(dispenseItemId);
            if (dispensingFormItemView == null) {
                dispensingFormItemView = DispensingFormItemView.from(dispensingFormItem);
                // 只有拆过单的才会有关联的 formItem，而只要拆过单的 item 它的药房和发药单的药房就一定是同一个药房，所以直接取发药单的药房名称
                dispensingFormItemView.setPharmacyName(dispensingSheetIdToPharmacyName.get(dispensingFormItem.getDispensingSheetId()));
            } else {
                // 如果有 view，则只覆盖 count，关联项的 count 是原始的 count，和发药项当前的状态无关
                dispensingFormItemView.setUnitCount(dispensingFormItem.getUnitCount());
                dispensingFormItemView.setDoseCount(dispensingFormItem.getDoseCount());
                dispensingFormItemView.setRemainingUnitCount(dispensingFormItem.getRemainingUnitCount());
                dispensingFormItemView.setRemainingDoseCount(dispensingFormItem.getRemainingDoseCount());
            }
            dispensingFormItemView.setStatus(dispensingFormItem.getStatus());
            dispensingFormItemView.setStatusName(DispensingFormItemView.statusName(dispensingFormItem.getStatus()));
            return dispensingFormItemView;
        }).collect(Collectors.toList());
        mergedRelateDispensingForm.setDispensingFormItems(relateDispensingFormItemViews);
        return mergedRelateDispensingForm;
    }

}
