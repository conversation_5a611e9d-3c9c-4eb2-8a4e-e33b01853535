package cn.abcyun.cis.dispensing.api.protocol;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.TraceableCode;
import cn.abcyun.cis.dispensing.util.DispensingUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-21 22:22:09
 */
@Data
public class SaveTraceableCodeDraftReq {

    @JsonIgnore
    private String chainId;
    @JsonIgnore
    private String clinicId;
    @JsonIgnore
    private String employeeId;
    @JsonIgnore
    private String dispensingSheetId;

    /**
     * 是否为补录追溯码
     * <p>
     * 单次请求中，要么全部为补录，要么全部为非补录
     * 补录：修改已经发药的追溯码
     * 非补录：修改待发药的追溯码
     */
    private int isReReport;

    @ApiModelProperty(value = "追溯码")
    private List<TraceableCodeDraft> list;

    @Data
    public static class TraceableCodeDraft {
        @ApiModelProperty(value = "formItemId")
        private String id;

        /**
         * 医保拆零标识位
         * <p>
         * 第 0 位: 是否拆零
         * 第 1 位: 是否允许编辑是否拆零
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Integer shebaoDismountingFlag;

        @ApiModelProperty(value = "追溯码")
        private List<TraceableCode> traceableCodeList;
        @ApiModelProperty(value = "套餐追溯码")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private List<TraceableCodeDraft> composeChildren;

        public void checkParam() {
            if (!CollectionUtils.isEmpty(traceableCodeList)) {
                DispensingUtils.checkTraceCodeNoLength(traceableCodeList);
            }
            if (!CollectionUtils.isEmpty(composeChildren)) {
                composeChildren.forEach(TraceableCodeDraft::checkParam);
            }
        }
    }

    public void checkParam() {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.forEach(TraceableCodeDraft::checkParam);
    }
}
