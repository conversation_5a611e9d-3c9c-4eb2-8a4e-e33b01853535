package cn.abcyun.cis.dispensing.controller;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.UpdateTraceableCodeReq;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.UpdateTraceableCodeRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.*;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.OpsCommonRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.patientorder.PatientOrderHospitalVO;
import cn.abcyun.bis.rpc.sdk.his.message.advice.*;
import cn.abcyun.bis.rpc.sdk.his.message.charge.HisChargeSheetMessage;
import cn.abcyun.cis.commons.CisServiceResponse;
import cn.abcyun.cis.commons.exception.CisCustomException;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.cis.commons.rpc.dispensing.DirectDispensingReq;
import cn.abcyun.cis.commons.rpc.dispensing.DirectDispensingRsp;
import cn.abcyun.cis.commons.util.DateUtils;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import cn.abcyun.cis.dispensing.api.protocol.*;
import cn.abcyun.cis.dispensing.api.protocol.DispensingBatchQueryReq;
import cn.abcyun.cis.dispensing.api.protocol.DispensingBatchQueryWithClinicReq;
import cn.abcyun.cis.dispensing.api.protocol.DispensingFormItemPrintListRsp;
import cn.abcyun.cis.dispensing.api.protocol.DispensingFormItemQuantityBatchRsp;
import cn.abcyun.cis.dispensing.api.protocol.DispensingSheetListRsp;
import cn.abcyun.cis.dispensing.api.protocol.DispensingSheetOperationRsp;
import cn.abcyun.cis.dispensing.api.protocol.GetDispenseSheetListByCreatedReq;
import cn.abcyun.cis.dispensing.api.protocol.GetDispenseSheetListByIdReq;
import cn.abcyun.cis.dispensing.api.protocol.order.CreateDispensingOrderConfigReq;
import cn.abcyun.cis.dispensing.api.protocol.order.DispenseDispenseOrderReq;
import cn.abcyun.cis.dispensing.api.protocol.order.GetDispenseSheetByAdviceIdReq;
import cn.abcyun.cis.dispensing.api.protocol.order.HospitalDispensingOrderPrescriptionBatchPrintReq;
import cn.abcyun.cis.dispensing.base.exception.DispensingServiceError;
import cn.abcyun.cis.dispensing.base.exception.DispensingServiceException;
import cn.abcyun.cis.dispensing.domain.DispensingOrder;
import cn.abcyun.cis.dispensing.domain.DispensingSheet;
import cn.abcyun.cis.dispensing.domain.DispensingSheetV2;
import cn.abcyun.cis.dispensing.mybatis.mapper.DispensingMapper;
import cn.abcyun.cis.dispensing.service.*;
import cn.abcyun.cis.dispensing.service.dto.*;
import cn.abcyun.cis.dispensing.service.dto.DispensingFormItemView;
import cn.abcyun.cis.dispensing.service.dto.DispensingSheetView;
import cn.abcyun.cis.dispensing.service.dto.UndispenseResult;
import cn.abcyun.cis.dispensing.util.DispensingUtils;
import cn.abcyun.cis.dispensing.util.JsonUtils;
import cn.abcyun.cis.dispensing.util.MathUtils;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponse;
import cn.abcyun.common.model.AbcServiceResponseBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/rpc/dispensings")
@Api(value = "DispensingRpcController", description = "药房rpc相关接口", produces = "application/json")
public class DispensingRpcController {
    private static final Logger sLogger = LoggerFactory.getLogger(DispensingRpcController.class);

    @Autowired
    private DispensingService dispensingService;
    @Autowired
    private PatientOrderService patientOrderService;

    @Autowired
    private DispensingMapper dispensingMapper;

    @Autowired
    private MQMessageHandleService mqMessageHandleService;

    @Autowired
    private DispensingSheetDispenseService dispensingSheetDispenseService;
    @Autowired
    private UndispensingService undispensingService;
    @Autowired
    private DispensingTodoService dispensingTodoService;
    @Autowired
    private DispensingServiceV2 dispensingServiceV2;


    @GetMapping("/source/{sourceSheetId}")
    public AbcServiceResponse<DispensingSheet> getDispensingSheetBySourceSheetId(@PathVariable("sourceSheetId") String sourceSheetId) {
        DispensingSheet dispensingSheet = dispensingService.findDispensingSheetBySourceSheetId(sourceSheetId);
        DispensingUtils.formatDispenseItemRemainingCount(dispensingSheet);
        return new AbcServiceResponse<>(dispensingSheet);
    }


    /**
     * 根据sourceSheetId查询发药单列表
     *
     * @param sourceSheetId
     * @return
     */
    @GetMapping("/source/query-list/{sourceSheetId}")
    @LogReqAndRsp
    public AbcServiceResponse<DispensingSheetListRsp> getDispensingSheetListBySourceSheetId(
            @PathVariable("sourceSheetId") String sourceSheetId, @RequestParam(value = "queryGoodsStockLog", required = false, defaultValue = "0") Integer queryGoodsStockLog) {
        DispensingSheetListRsp clientRsp = new DispensingSheetListRsp();
        List<DispensingSheet> dispensingSheets = dispensingService.findDispensingSheetListBySourceSheetId(sourceSheetId, queryGoodsStockLog);
        DispensingUtils.formatDispenseItemRemainingCount(dispensingSheets);
        clientRsp.setDispensingSheets(dispensingSheets);
        return new AbcServiceResponse<>(clientRsp);
    }

    @GetMapping("/view-list/source/{sourceSheetId}")
    @LogReqAndRsp
    @ApiOperation(value = "查询发药单详情", produces = "application/json")
    public AbcServiceResponse<AbcListPage<DispensingSheetView>> listDispensingSheetViewsBySourceSheetId(@RequestParam("chainId") String chainId,
                                                                                                        @PathVariable("sourceSheetId") String sourceSheetId) {
        AbcListPage<DispensingSheetView> listPage = new AbcListPage<>();
        listPage.setRows(dispensingService.listDispensingSheetViewsBySourceSheetId(chainId, sourceSheetId));
        return new AbcServiceResponse<>(listPage);
    }

    @GetMapping("/{id}")
    @LogReqAndRsp
    public AbcServiceResponse<DispensingSheetView> getDispensingSheetById(
            @PathVariable("id") String id) throws NotFoundException, ServiceInternalException {
        return new AbcServiceResponse<>(dispensingService.findDispensingSheetViewById(id));
    }

    @GetMapping("/open-api/{id}")
    @LogReqAndRsp
    public AbcServiceResponse<DispensingSheetView> getDispensingSheetByIdForOpenApi(
            @PathVariable("id") String id) throws NotFoundException, ServiceInternalException {
        return new AbcServiceResponse<>(dispensingService.findDispensingSheetViewByIdForOpenApi(id));
    }

    @GetMapping("/open-api/source/{sourceSheetId}")
    @LogReqAndRsp
    public AbcServiceResponse<AbcListPage<DispensingSheetView>> getDispensingSheetBySourceSheetIdForOpenApi(
            @RequestParam("chainId") String chainId,
            @RequestParam("clinicId") String clinicId,
            @PathVariable("sourceSheetId") String id) throws NotFoundException, ServiceInternalException {
        List<DispensingSheetView> dispensingSheetViews = dispensingService.findDispensingSheetViewBySourceSheetIdForOpenApi(chainId, clinicId, id);
        AbcListPage<DispensingSheetView> listPage = new AbcListPage<>();
        listPage.setRows(dispensingSheetViews);
        return new AbcServiceResponse<>(listPage);
    }


    @PostMapping("/source/query")
    @LogReqAndRsp
    public CisServiceResponse<DispensingBatchQueryRsp> queryDispensingSimpleInfos(@RequestBody @Valid DispensingBatchQueryReq req) {

        List<DispensingBatchQueryRsp.DispensingSimpleInfo> dispensingSimpleInfos = dispensingService.queryDispensingSimpleInfos(req.getChainId(), req.getSourceSheetIds());
        DispensingBatchQueryRsp rsp = new DispensingBatchQueryRsp();
        rsp.setResults(dispensingSimpleInfos);
        return new CisServiceResponse<>(rsp);
    }

    /**
     * 长护险批量查询发药状态
     */
    @PostMapping("/source/query-item-quantity")
    @LogReqAndRsp
    public CisServiceResponse<DispensingFormItemQuantityBatchRsp> queryDispensingFormItemQuantityInfos(@RequestBody @Valid DispensingBatchQueryWithClinicReq req) {

        DispensingFormItemQuantityBatchRsp rsp = dispensingService.queryDispensingFormItemQuantityInfos(req.getChainId(), req.getClinicId(), req.getSourceSheetIds());
        return new CisServiceResponse<>(rsp);
    }

    /**
     * 收费服务RPC【 收费同时发药】
     */
    @PostMapping("/direct")
    @LogReqAndRsp
    public CisServiceResponse<DirectDispensingRsp> postDirectDispenseChargeSheet(@RequestBody DirectDispensingReq directDispensingReq) throws CisCustomException, ServiceInternalException, ParamRequiredException {

        DirectDispensingRsp rsp = new DirectDispensingRsp();
        rsp.setList(new ArrayList());
        return new CisServiceResponse<>(rsp);
    }

    @PutMapping("/source/{sourceSheetId}/renew")
    public CisServiceResponse<Object> putRenewBySourceSheetId(@PathVariable("sourceSheetId") String sourceSheetId, @RequestParam("operatorId") String operatorId) {
        sLogger.info("putRenewBySourceSheetId:{}", sourceSheetId);
        dispensingService.updateDispensingForRenewChargeSheet(sourceSheetId, operatorId);
        return new CisServiceResponse<>(HttpStatus.OK, null, null);
    }


    @GetMapping("/stat/info")
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public CisServiceResponse<DispenseStatInfo> getDispenseStatInfo(@RequestParam(value = "clinicId") String clinicId,
                                                                    @RequestParam(value = "beginDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date beginDate,
                                                                    @RequestParam(value = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        DispenseStatInfo dispenseStatInfo = new DispenseStatInfo();
        if (beginDate != null) {
            beginDate = DateUtils.getStartTime(beginDate);
        }
        if (endDate != null) {
            endDate = DateUtils.getEndTime(endDate);
        }

        dispenseStatInfo.setDispensedCount(dispensingMapper.findDispensedCount(clinicId, beginDate, endDate));
//        dispenseStatInfo.setDispensedPackageCount(MathUtils.setScaleTwo(new BigDecimal(dispensingMapper.findDispensedPackageCount(clinicId, beginDate, endDate))));
//        dispenseStatInfo.setDispensedPrice(MathUtils.setScaleTwo(new BigDecimal(dispensingMapper.findDispensedPrice(clinicId, beginDate, endDate))));
//        dispenseStatInfo.setDispensedWeight(MathUtils.setScaleTwo(new BigDecimal(dispensingMapper.findDispensedWeight(clinicId, beginDate, endDate))));
        dispenseStatInfo.setDispensedCostPrice(MathUtils.setScaleTwo(new BigDecimal(dispensingMapper.findDispensedCostPrice(clinicId, beginDate, endDate))));
        return new CisServiceResponse<>(dispenseStatInfo);
    }

    @GetMapping("/todo")
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    @LogReqAndRsp
    public CisServiceResponse<DispensingTodo> getDispensingTodo(@RequestParam(value = "chainId") String chainId,
                                                                @RequestParam(value = "clinicId") String clinicId,
                                                                @RequestParam(value = "beginDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date beginDate,
                                                                @RequestParam(value = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
                                                                @RequestParam(value = "hisType", required = false) Integer hisType,
                                                                @RequestParam(value = "employeeId") String employeeId) {
        if (beginDate == null) {
            beginDate = new Date();
        }

        if (endDate == null) {
            endDate = beginDate;
        }

        beginDate = DateUtils.getStartTime(beginDate);
        endDate = DateUtils.getEndTime(endDate);
        DispensingTodoCountDto todoCountDto = dispensingTodoService.getDispensingTodoCount(chainId, clinicId, employeeId, hisType, null, beginDate, endDate);
        DispensingTodo todo = new DispensingTodo();
        todo.setCount(todoCountDto.getDispensingWaitingCount());
        todo.setHospitalDispensingOrderCount(todoCountDto.getDispensingOrderWaitingCount());

        return new CisServiceResponse<>(todo);
    }

    @PostMapping("/dashboard/status")
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public CisServiceResponse<DashboardStatusListRsp> getDispensingDashboardStatusList(@RequestBody DashboardStatusListReq req) {
        sLogger.info("getDispensingDashboardStatusList req:{}", JsonUtils.dump(req));
        List<String> patientOrderIds = req.getPatientOrderIds() != null ? req.getPatientOrderIds() : new ArrayList<>();
        List<DashboardStatus> dashboardStatusList = new ArrayList<>();
        if (patientOrderIds.size() > 0) {
            dashboardStatusList = dispensingMapper.findDispensingDashboardStatusList(patientOrderIds);
        }
        Map<String, DashboardStatus> dashboardStatusMap = dashboardStatusList.stream().collect(Collectors.toMap(DashboardStatus::getPatientOrderId, Function.identity(), (a, b) -> a));


        DashboardStatusListRsp rsp = new DashboardStatusListRsp();
        rsp.setList(patientOrderIds.stream().map(patientOrderId -> {
            DashboardStatus dashboardStatus = dashboardStatusMap.getOrDefault(patientOrderId, null);
            if (dashboardStatus == null) {
                dashboardStatus = new DashboardStatus();
                dashboardStatus.setPatientOrderId(patientOrderId);
            }
            return dashboardStatus;
        }).collect(Collectors.toList()));

        sLogger.info("getDispensingDashboardStatusList rsp:{}", JsonUtils.dump(rsp));

        return new CisServiceResponse<>(rsp);
    }

    /**
     * 提供给jenkins调用自动发药
     *
     * @param dispensingSheetId
     * @param operatorId
     * @return
     */
    @PutMapping("/{dispensingSheetId}/auto-dispense")
    @LogReqAndRsp
    public CisServiceResponse<DispenseResult> autoDispensing(
            @PathVariable("dispensingSheetId") String dispensingSheetId,
            @RequestParam("clinicId") String clinicId,
            @RequestParam("operatorId") String operatorId) {
        DispenseResult dispenseResult = mqMessageHandleService.handleDispensingAutoDispenseMessage(dispensingSheetId, clinicId, operatorId);
        return new CisServiceResponse<>(dispenseResult);
    }

    /**
     * 提供给jenkins调用自动退药
     *
     * @param dispensingSheetId
     * @param clinicId          OpenApi暴露出去，一定要限定这个门店内的发药单，不能拿到ID就搞
     * @param operatorId
     * @return
     */
    @PutMapping("/{dispensingSheetId}/auto-un-dispense")
    @LogReqAndRsp
    public CisServiceResponse<UndispenseResult> autoUnDispensing(
            @PathVariable("dispensingSheetId") String dispensingSheetId,
            @RequestParam("clinicId") String clinicId,
            @RequestParam("operatorId") String operatorId) {
        UndispenseResult undispenseResult = mqMessageHandleService.handleDispensingAutoUnDispenseMessage(dispensingSheetId, clinicId, operatorId);
        return new CisServiceResponse<>(undispenseResult);
    }

    /**
     * 根据patientOrderId查询审核记录信息
     *
     * @param patientOrderId
     * @return
     */
    @GetMapping("/{patientOrderId}/dispensing/operation-info")
    @LogReqAndRsp
    public CisServiceResponse<DispensingSheetOperationRsp> findDispensingSheetOperation(
            @PathVariable("patientOrderId") String patientOrderId,
            @RequestParam(value = "chargeSheetId", required = false) String chargeSheetId) {
        return new CisServiceResponse<>(dispensingService.findDispensingSheetOperation(patientOrderId, chargeSheetId));
    }

    /**
     * 内部使用rpc 补推智能发药单  不检查 开关，直接推。
     */
    @PutMapping("/{dispensingSheetId}/smart-dispensing")
    @LogReqAndRsp
    public CisServiceResponse<OpsCommonRsp> rePushSmartDispensing(@PathVariable("dispensingSheetId") String dispensingSheetId) {
        Integer result = dispensingService.rpcRePushSmartDispensing(dispensingSheetId);
        OpsCommonRsp commonRsp = new OpsCommonRsp();
        commonRsp.setCode(result);
        return new CisServiceResponse<>(commonRsp);
    }

    /**
     * 药房发药:
     * 一个发药单里面的药 可以分多次发
     * 一个药 只能一次发完
     * 代煎代配的发药单 一个goods可以两个form存在不同供应商
     */
    @PutMapping("/{id}/dispense")
    @LogReqAndRsp
    public CisServiceResponse<DispenseResult> putDispensingSheetDispense(
            @PathVariable("id") String dispensingSheetId,
            @RequestBody DispenseSheetReq dispenseSheetReq,
            @RequestParam("clinicId") String clinicId,
            @RequestParam("operatorId") String employeeId) throws ParamRequiredException, CisCustomException, ServiceInternalException {

        DispensingSheet dispensingSheet = DTOConverter.convertToDispensingSheet(dispenseSheetReq);
        dispensingSheet.setId(dispensingSheetId);

        List<String> dispensedByIds = Optional.ofNullable(dispenseSheetReq.getDispensedByIds()).orElseGet(() -> Arrays.asList(dispenseSheetReq.getDispensedById()));

        DispenseResult result = dispensingSheetDispenseService.dispenseSheet(dispensingSheet, null, clinicId, false, dispensedByIds, employeeId, 0);
        StatusNameTranslator.translate(result);
        return new CisServiceResponse<>(result);
    }


    /**
     * 药房退药:
     * 一个药 一次可以退部分
     */
    @PutMapping("/{id}/undispense")
    @LogReqAndRsp
    public CisServiceResponse<UndispenseResult> putDispensingSheetUnDispense(
            @PathVariable("id") String dispensingSheetId,
            @RequestBody DispenseSheetReq dispenseSheetReq,
            @RequestParam("clinicId") String clinicId,
            @RequestParam("operatorId") String employeeId) throws ParamRequiredException, CisCustomException, ServiceInternalException {
        DispensingSheet dispensingSheet = DTOConverter.convertToDispensingSheet(dispenseSheetReq);
        dispensingSheet.setId(dispensingSheetId);
        UndispenseResult result = undispensingService.undispenseSheet(dispensingSheet, null, clinicId, employeeId);
        StatusNameTranslator.translate(result);
        return new CisServiceResponse<>(result);
    }

    /**
     * OpenApi 按创建时间拉发药单列表
     *
     * @return
     * @throws ServiceInternalException
     */
    @PostMapping("/list")
    @LogReqAndRsp
    public CisServiceResponse<AbcListPage<DispensingSheetAbstract>> getOpenApiDispensingSheetAbstractList(@RequestBody GetDispenseSheetListByCreatedReq reqBody) throws ServiceInternalException {


        AbcListPage<DispensingSheetAbstract> rspData = dispensingService.getOpenApiDispensingSheetAbstractList(reqBody);
        return new CisServiceResponse<>(rspData);
    }

    /**
     * 通过发药单ID查询发药单
     */
    @PostMapping("/list-by-id")
    @LogReqAndRsp
    public CisServiceResponse<AbcListPage<DispensingSheetAbstract>> getOpenApiDispensingSheetAbstractListById(@Valid @RequestBody GetDispenseSheetListByIdReq req) {
        List<DispensingSheetAbstract> dispensingSheetAbstracts = dispensingService.listOpenApiDispensingSheetAbstractById(req);
        AbcListPage<DispensingSheetAbstract> listPage = new AbcListPage<>();
        listPage.setRows(dispensingSheetAbstracts);
        return new CisServiceResponse<>(listPage);
    }

    @PostMapping("/advice")
    @LogReqAndRsp
    public CisServiceResponse<OpsCommonRsp> getOpenApiDispensingSheetAbstractList(@RequestBody AdviceExecuteMessage adviceExecuteMessage) throws ServiceInternalException {


        PatientOrderHospitalVO patientOrder = patientOrderService.findPatientOrderHospital(adviceExecuteMessage.getChainId(), adviceExecuteMessage.getClinicId(), adviceExecuteMessage.getPatientOrderId());
        if (patientOrder == null) {
            throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "无法加载patientOrder");
        }
        List<AdviceExecuteItemMessage> needDispenseExecuteItemList = adviceExecuteMessage.getExecuteItems().stream()
                .filter(it -> it.getAdviceRuleItem() != null).filter(it -> GoodsConst.isStockGoods(it.getAdviceRuleItem().getGoodsType()))
                .collect(Collectors.toList());
        if (needDispenseExecuteItemList.isEmpty()) {
            throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "没有需要生成的发药单项");
        }
        List<DispensingSheetV2> autoDispensedSheetList = new ArrayList<>();
        Map<Integer, List<AdviceRuleItemMessage>> pharmacyNoToRuleItemMessage = needDispenseExecuteItemList.stream()
                .map(AdviceExecuteItemMessage::getAdviceRuleItem).map(it -> {
                    if (it.getPharmacyNo() == null) {
                        it.setPharmacyNo(0);
                    }
                    if (it.getPharmacyType() == null) {
                        it.setPharmacyType(0);
                    }
                    return it;
                }).collect(Collectors.groupingBy(AdviceRuleItemMessage::getPharmacyNo));
        pharmacyNoToRuleItemMessage.forEach((pharmacyNo, ruleItemMessageList) -> {
            DispensingOrder dispensingOrder =
                    dispensingServiceV2.getOrCreateCurrentDispenseOrder(adviceExecuteMessage.getChainId(),
                            adviceExecuteMessage.getClinicId(), adviceExecuteMessage.getDepartmentId(),
                            Long.parseLong(adviceExecuteMessage.getPatientOrderHospitalVO().getWardId()),
                            ruleItemMessageList.get(0).getPharmacyType(), pharmacyNo,
                            adviceExecuteMessage.getPlanExecuteTime());
            dispensingServiceV2.createDispensingSheetFromAdviceExecuteMessage(adviceExecuteMessage, dispensingOrder, ruleItemMessageList, false);
            autoDispensedSheetList.addAll(dispensingOrder.getAutoDispenseSheetList());
        });
        dispensingServiceV2.autoDispenseDispensingSheet(autoDispensedSheetList, adviceExecuteMessage.getOperatorId());
        return new CisServiceResponse<>(new OpsCommonRsp());
    }

    @PostMapping("/advice/rule")
    @LogReqAndRsp
    public CisServiceResponse<OpsCommonRsp> getOpenApiDispensingSheetAbstractListRule(@RequestBody AdviceMessage adviceMessage) throws ServiceInternalException {


        PatientOrderHospitalVO patientOrder = patientOrderService.findPatientOrderHospital(adviceMessage.getChainId(), adviceMessage.getClinicId(), adviceMessage.getPatientOrderId());
        if (patientOrder == null) {
            throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "无法加载patientOrder");
        }
        /**
         * 目前取第一个Form上的药房号信息
         * 目前业务上一个医嘱里面不会混开多个药房
         * */
        int pharmacyType = 0;
        int pharmacyNo = 0;
        if (!CollectionUtils.isEmpty(adviceMessage.getAdviceRules())) {
            AdviceRuleMessage adviceRuleMessage = adviceMessage.getAdviceRules().get(0);
            pharmacyType = adviceRuleMessage.getPharmacyType() != null ? adviceRuleMessage.getPharmacyType() : pharmacyType;
            pharmacyNo = adviceRuleMessage.getPharmacyNo() != null ? adviceRuleMessage.getPharmacyNo() : pharmacyNo;
        }
        List<DispensingSheetV2> autoDispensedSheetList = new ArrayList<>();
        DispensingOrder dispensingOrder =
                dispensingServiceV2.getOrCreateCurrentDispenseOrder(adviceMessage.getChainId(),
                        adviceMessage.getClinicId(), adviceMessage.getDepartmentId(),
                        Long.parseLong(patientOrder.getWardId()), pharmacyType, pharmacyNo, null);

        dispensingServiceV2.createDispensingSheetFromAdviceMessage(adviceMessage, dispensingOrder);
        autoDispensedSheetList.addAll(dispensingOrder.getAutoDispenseSheetList());
        dispensingServiceV2.autoDispenseDispensingSheet(autoDispensedSheetList, adviceMessage.getOperatorId());
        return new CisServiceResponse<>(new OpsCommonRsp());
    }

    /***
     * 按医嘱ID查返回简版的发药单view
     * */
    @PostMapping("/sheets/query-list-by-advice")
    @LogReqAndRsp
    public AbcServiceResponseBody<AbcListPage<DispensingSheetView>> getDispensingSheetViewByAdvice(@RequestBody GetDispenseSheetByAdviceIdReq clientReq) throws ServiceInternalException {
        return new AbcServiceResponseBody<>(dispensingService.getDispensingSheetViewByAdvice(clientReq));
    }

    /**
     * 药房生成领药单配置rpc
     */
    @PostMapping("/order/config")
    @LogReqAndRsp
    public CisServiceResponse<OpsCommonRsp> createDispensingOrderConfig(@RequestBody @Valid CreateDispensingOrderConfigReq req) {
        req.getWardAreaIdList().forEach(wardAreaId -> dispensingService.createDispensingOrderConfig(req.getChainId(), req.getClinicId(), wardAreaId, req.getPharmacyNoList(), 0));
        return new CisServiceResponse<>(new OpsCommonRsp());
    }

    /**
     * 获取患者未完成发药单
     */
    @GetMapping("/sheets/get-unfinished-sheets/{patientOrderId}")
    @LogReqAndRsp
    public AbcServiceResponseBody<AbcListPage<DispensingSheetUnfinishedView>> getUnfinishedDispensingSheetByPatientOrderId(@RequestParam("chainId") String chainId,
                                                                                                                           @RequestParam("clinicId") String clinicId,
                                                                                                                           @PathVariable("patientOrderId") String patientOrderId) {
        return new AbcServiceResponseBody<>(dispensingService.getUnfinishedDispensingSheetByPatientOrderId(chainId, clinicId, patientOrderId));
    }

    @PostMapping("/orders/pharmacy/prescription/{dispenseOrderId}/print")
    @LogReqAndRsp
    @ApiOperation(value = "rpc获取医院药房患者处方发药单打印信息")
    public AbcServiceResponse<AbcListPage<HospitalDispensingOrderPrescriptionPrintRsp>> getHospitalPrescriptionSheetViewList(@PathVariable("dispenseOrderId") String dispenseOrderId,
                                                                                                                             @RequestBody RpcHospitalDispensingOrderPrescriptionPrintReq req) {
        req.validateParams();

        HospitalDispensingOrderPrescriptionPrintReq prescriptionPrintReq = new HospitalDispensingOrderPrescriptionPrintReq();
        prescriptionPrintReq.setDispensingOrderId(dispenseOrderId);
        prescriptionPrintReq.setChainId(req.getChainId());
        prescriptionPrintReq.setClinicId(req.getClinicId());
        prescriptionPrintReq.setTab(req.getTab());
        prescriptionPrintReq.setDispensingSheetIdList(req.getDispensingSheetIdList());

        return new AbcServiceResponse<>(dispensingServiceV2.getHospitalPrescriptionSheetViewList(prescriptionPrintReq));
    }


    @PostMapping("/orders/pharmacy/prescription/batch-print")
    @LogReqAndRsp
    @ApiOperation(value = "rpc批量获取医院药房患者处方发药单打印信息")
    public AbcServiceResponse<AbcListPage<HospitalDispensingOrderPrescriptionPrintRsp>> getHospitalPrescriptionSheetViewList(
            @RequestBody @Valid HospitalDispensingOrderPrescriptionBatchPrintReq req) {
        return new AbcServiceResponse<>(dispensingServiceV2.batchGetHospitalPrescriptionSheetViewList(req));
    }

    @PostMapping("/sheets/query-list-by-source-type")
    @LogReqAndRsp
    public AbcServiceResponse<AbcListPage<DispensingSheetView>> getDispensingSheetViewBySourceType(@RequestBody GetDispenseSheetBySourceTypeReq clientReq) {
        return new AbcServiceResponse<>(dispensingServiceV2.getDispensingSheetViewBySourceType(clientReq));
    }

    @PutMapping("/his-charge/undispense")
    @LogReqAndRsp
    public AbcServiceResponse<OpsCommonRsp> undispenseForHisCharge(@RequestBody UnDispenseForHisChargeReq clientReq) {
        return new AbcServiceResponse<>(dispensingServiceV2.undispenseForHisCharge(clientReq));
    }

    @PostMapping("/his-charge/supplement-message")
    @LogReqAndRsp
    public AbcServiceResponse<OpsCommonRsp> handlerChargeSupplementMessage(@RequestBody HisChargeSheetMessage hisChargeSheetMessage) {
        mqMessageHandleService.handlerHisChargeSheetMessage(hisChargeSheetMessage);
        return new AbcServiceResponse<>(new OpsCommonRsp(OpsCommonRsp.SUCC, "success"));
    }

    @PostMapping("/his-advice/usage-rule-message")
    @LogReqAndRsp
    public AbcServiceResponse<OpsCommonRsp> handlerAdviceUsageRuleMessage(@RequestBody AdviceUsageRuleItemsAddMessage usageRuleItemsAddMessage) {
        mqMessageHandleService.handlerAdviceUsageRuleItemsAddMessage(usageRuleItemsAddMessage);
        return new AbcServiceResponse<>(new OpsCommonRsp(OpsCommonRsp.SUCC, "success"));
    }

    @PostMapping("/his-advice/advice-supplement-message")
    @LogReqAndRsp
    public AbcServiceResponse<OpsCommonRsp> handlerAdviceSupplementMessage(@RequestBody AdviceExecuteMessage adviceExecuteMessage) {
        mqMessageHandleService.handleAdviceExecuteMessage(adviceExecuteMessage, true);
        return new AbcServiceResponse<>(new OpsCommonRsp(OpsCommonRsp.SUCC, "success"));
    }

    /**
     * 通过医嘱任务id 发药
     * 医院自动发药且未到发药时间的发药单，由医嘱任务执行时触发发药
     */
    @PutMapping("/advice-execute/dispense")
    @LogReqAndRsp
    public AbcServiceResponse<OpsCommonRsp> dispenseFoAdviceExecute(@RequestBody DispenseForAdviceExecuteReq clientReq) {
        return new AbcServiceResponse<>(dispensingServiceV2.dispenseFoAdviceExecute(clientReq));
    }

    /**
     * 更新追溯码
     */
    @PutMapping("/update-traceable-code")
    @LogReqAndRsp
    public AbcServiceResponse<UpdateTraceableCodeRsp> updateDispensingSheetTraceableCode(@RequestBody UpdateTraceableCodeReq req) {
        List<UpdateTraceableCodeRsp.UpdateTraceableCodeItem> list = dispensingServiceV2.updateDispensingSheetTraceableCode(req);

        UpdateTraceableCodeRsp updateTraceableCodeRsp = new UpdateTraceableCodeRsp();
        updateTraceableCodeRsp.setChainId(req.getChainId());
        updateTraceableCodeRsp.setClinicId(req.getClinicId());
        updateTraceableCodeRsp.setEmployeeId(req.getEmployeeId());
        updateTraceableCodeRsp.setChargeSheetId(req.getChargeSheetId());
        updateTraceableCodeRsp.setList(list);
        updateTraceableCodeRsp.setCode(OpsCommonRsp.SUCC);
        updateTraceableCodeRsp.setMessage("success");
        return new AbcServiceResponse<>(updateTraceableCodeRsp);
    }

    /**
     * 医嘱重新发药消息
     */
    @PostMapping("/his-advice/need-re-dispense-message")
    @LogReqAndRsp
    public AbcServiceResponse<OpsCommonRsp> handlerAdviceNeedReDispenseMessage(@RequestBody AdviceNeedReDispenseMessage adviceNeedReDispenseMessage) {
        mqMessageHandleService.handleAdviceNeedReDispenseMessage(adviceNeedReDispenseMessage);
        return new AbcServiceResponse<>(new OpsCommonRsp(OpsCommonRsp.SUCC, "success"));
    }

    /**
     * 住院药房发药rpc
     */
    @PutMapping("/orders/pharmacy/dispense")
    @LogReqAndRsp
    @ApiOperation(value = "住院药房发药", produces = "application/json")
    public AbcServiceResponse<OpsCommonRsp> rpcDispenseDispensingOrder(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
            @RequestBody DispenseDispenseOrderReq dispenseOrderReq
    ) {
        dispenseOrderReq.parameterCheck();
        dispenseOrderReq.setHeaderClinicId(headerClinicId);
        dispenseOrderReq.setHeaderChainId(headerChainId);
        dispenseOrderReq.setHeaderEmployeeId(headerEmployeeId);
        dispenseOrderReq.setFromPharmacy(true);
        for (DispenseDispenseOrderReq.DispenseOrderReq orderReq : dispenseOrderReq.getDispenseOrderReqs()) {
            for (DispenseDispenseOrderReq.DispenseSheetReq dispenseSheetReq : orderReq.getDispenseSheetReqs()) {
                if (dispenseSheetReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.RECORD_NOT_DISPENSE_STOCK) {
                    throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "不支持的操作");
                }
                dispenseSheetReq.setUnDispense(false);
            }
        }
        return new AbcServiceResponse<>(dispensingServiceV2.dispenseDispensingOrder(dispenseOrderReq));
    }

    /**
     * 查询住院患者的发药退药 item
     */
    @GetMapping("/query-form-item-by-patient-order-id")
    @LogReqAndRsp
    public AbcServiceResponse<AbcListPage<DispensingFormItemView>> queryDispensingFormItemByPatientOrderId(
            @RequestParam("chainId") String chainId,
            @RequestParam("clinicId") String clinicId,
            @RequestParam("patientOrderId") String patientOrderId,
            @RequestParam(value = "offset", required = false, defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", required = false, defaultValue = "100") Integer limit
    ) {
        return new AbcServiceResponse<>(dispensingServiceV2.queryDispensingFormItemByPatientOrderId(chainId, clinicId, patientOrderId, offset, limit));
    }

    /**
     * 获取发药item是否有追溯码
     */
    @PostMapping("/query-sheet-has-traceable-code")
    @LogReqAndRsp
    public AbcServiceResponse<QueryDispensingTraceableCodeRsp> queryDispensingTraceableCode(@RequestBody QueryDispensingTraceableCodeReq req) {
        return new AbcServiceResponse<>(dispensingServiceV2.queryDispensingTraceableCode(req));
    }

    /**
     * 查询锁库药品发药单信息
     */
    @PostMapping("/query-dispensing-locking-info")
    @LogReqAndRsp
    public AbcServiceResponse<AbcListPage<QueryDispensingLockingInfo>> queryDispensingLockingInfo(@RequestBody QueryDispensingLockingInfoReq req) {
        return new AbcServiceResponse<>(dispensingServiceV2.queryDispensingLockingInfo(req));
    }

    /**
     * 根据sourceSheetId查询发药单列表
     *
     * @param sourceSheetId
     * @return
     */
    @GetMapping("/source/query-print-item-list/{sourceSheetId}")
    @LogReqAndRsp
    public AbcServiceResponse<DispensingFormItemPrintListRsp> getDispensingFormItemPrintListRspBySourceSheetId(@PathVariable("sourceSheetId") String sourceSheetId, @RequestParam(value = "printUnDispense", required = false, defaultValue = "false") boolean printUnDispense) {
        DispensingFormItemPrintListRsp rsp = dispensingService.getDispensingFormItemPrintListRspBySourceSheetId(sourceSheetId, printUnDispense);
        return new AbcServiceResponse<>(rsp);
    }
}
