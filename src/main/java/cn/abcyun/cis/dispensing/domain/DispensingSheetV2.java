package cn.abcyun.cis.dispensing.domain;

import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispenseConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsPharmacyView;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.config.LockConfig;
import cn.abcyun.cis.commons.rpc.charge.ChargeSheet;
import cn.abcyun.cis.commons.rpc.charge.Constants;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.commons.util.MathUtils;
import cn.abcyun.cis.dispensing.util.DispensingUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.vladmihalcea.hibernate.type.json.JsonStringType;
import lombok.Data;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * V2版本的发药单，不级联加载Form和FormItem
 */
@Entity
@Table(name = "v2_dispensing_sheet")
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Data
public class DispensingSheetV2 {
    private static final Logger sLogger = LoggerFactory.getLogger(DispensingSheetV2.class);
    @Id
    private String id;
    /**
     * 发药单门店ID
     */
    private String chainId;
    private String clinicId;
    /**
     * 发药单的类型
     * {@link DispenseConst.Type}
     */
    private int type;
    /**
     * patientOrderId
     */
    private String patientOrderId;
    /**
     * 患者ID
     */
    private String patientId;
    /**
     * 医生ID
     */
    private String doctorId;
    /**
     * 关联收费单的Id
     * type = {@link DispenseConst.Type#TYPE_OUTPATIENT}
     * {@link ChargeSheet#getId()}
     */
    private String sourceSheetId;
    /**
     * type = {@link DispenseConst.Type#TYPE_HOSPITAL}
     * 医院发药单时 为医嘱单ID
     */
    private Long adviceId;

    /**
     * 发药单状态
     * {@link DispenseConst.Status}
     * 医院发药单
     * 0 护士站待发药状态 - > 10 申请发药 -> 拒绝发药 3 --> 1 完成发药
     * 1 完成发药 -->  20 申请退药 --> 21 拒绝退药 --> 2 退药
     * 0 待发药状态   -> 1 完成发药  -> 2 退药 --> 3 拒绝发药
     * <p>
     * <p>
     * 门诊发药单
     * 待发药状态   ->  完成发药  ->  退药 -->  拒绝发药
     * CIS        （0）         （1）        （2）        (3）
     * HIS      （0,10）      (1,20,21）     （2）        (3）
     */
    private int status;
    private int chineseMedicineUndispenseType;

    /**
     * 发药单快递类型
     * {@link cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispenseConst.DeliveryType}
     */
    private int deliveryType;

    /**
     * 不级联加载DispenseingForm
     */
    @Transient
    private List<DispensingFormV2> dispensingForms;

    private Instant dispensedTime;

    private Instant orderByDate;

    private String dispensedBy;

    @Type(type = "json")
    @Column(columnDefinition = "json")
    private DispensingSheetInfo dispensingSheetInfo;

    /**
     * 是否是网诊发药单
     * {@link ChargeSheet#getIsOnline()}
     */
    private int isOnline;

    /**
     * 是否 需要煎药的发药单
     * {@link ChargeSheet#getIsDecoction()}
     */
    private int isDecoction;

    /**
     * 发药单 加工类型
     * {@link DispenseConst.ProcessedStatus}
     */
    private int processedStatus;

    /**
     * 发药单 快递状态
     * {@link DispenseConst.DeliveredStatus}
     */
    private int deliveredStatus;

    /**
     * 发药单 审核状态 发药单审核状态是form状态的
     * {@link DispenseConst.ProcessedStatus}
     */
    private int auditedStatus;

    /**
     * 发药单 调配状态
     * {@link DispenseConst.CompoundedStatus}
     */
    private int compoundedStatus;

    /**
     * 来源单据类型
     *
     * @see DispenseConst.SourceSheetType
     */
    private Integer sourceSheetType;


    /**
     * 药房号：默认为0
     */
    private int pharmacyNo;

    /**
     * 药房类型：0：实体药房，1：虚拟药房，2：空中药房
     */
    private int pharmacyType;

    private int printedNumber;

    /**
     * 是否为患者自己支付的
     */
    private int isPatientSelfPay;

    /**
     * 发药方式
     * {@link DispensingSheet.DispenseMethod}
     * <a href="https://www.tapd.cn/47644659/prong/stories/view/1147644659001056600">...</a>
     */
    private int dispensingMethod;

    /**
     * 病区Id
     * 老字段废弃了，现用来表示医院发药单的病区Id
     */
    @Column(name = "v1_id")
    private String v1Id;

    @JsonIgnore
    private int isDeleted;
    @JsonIgnore
    private String createdBy;
    @JsonIgnore
    private Instant created;
    @JsonIgnore
    private String lastModifiedBy;
    @JsonIgnore
    private Instant lastModified;
    @JsonIgnore
    @Version
    private int dataVersion;

    /**
     * 开立科室id，医生是从哪个科室开的医嘱，没有取住院单上的科室id
     */
    private String doctorDepartmentId;

    /**
     * 发药单被打的Tag类型
     * {@link cn.abcyun.cis.dispensing.util.DispensingUtils.DispensingTag}
     */
    private Integer dispensingTag;
    public boolean hasDispensingTag(int tag) {
        if (dispensingTag == null) {
            return false;
        }

        return (dispensingTag & tag) != 0;
    }

    /**
     * 添加发药标签
     */
    public void addDispensingTag(int tag) {
        int newTag = dispensingTag == null ? 0 : dispensingTag;
        newTag |= tag;
        this.dispensingTag = newTag;
    }

    /**
     * 删除标签
     */
    public void deleteDispensingTag(int tag) {
        if (dispensingTag == null) {
            return;
        }

        int newTag = dispensingTag;
        newTag &= ~tag;
        this.dispensingTag = newTag;
    }
    /**
     * 发药标记位，对应GoodsPharmacy的dispenseFlag字段
     * {@link GoodsConst.DispenseFlag}
     */
    private int pharmacyDispenseFlag = GoodsConst.DispenseFlag.DISPENSE_BY_USER;

    /**
     * 医嘱计划执行时间/患者补费用记账时间
     * 护士站领药退药申请筛选
     * https://www.tapd.cn/22044681/prong/stories/view/1122044681001044285
     */
    private Instant planExecuteTime;

    public void initPharmacyDispenseFlag(Map<Integer, GoodsPharmacyView> pharmacyNoToPharmacyView) {
        GoodsPharmacyView pharmacyView = pharmacyNoToPharmacyView.get(pharmacyNo);
        if (pharmacyView != null) {
            pharmacyDispenseFlag = pharmacyView.getDispenseFlag();
        }
    }

    /**
     * 给Sheet打Tag
     * <p>
     * 在新建或修改发药单 最后会执行
     */
    public void tagDispensingSheet() {
        if (CollectionUtils.isEmpty(dispensingForms)) {
            // clear the tag when no form exists.
            if (dispensingTag != null && dispensingTag != 0) {
                dispensingTag = 0;
            }
            return;
        }
        // tag all form
        int newTag = 0;
        for (DispensingFormV2 dispensingForm : dispensingForms) {
            dispensingForm.tagDispensingForm();
            newTag |= dispensingForm.getDispensingTag();
        }
        dispensingTag = newTag;
        return;
    }

    /**
     * 整个发药单的加工状态
     * TODO bug? 有一个Form Waiting  sheet就WAITING,所有SUCCESS才success？
     */
    public void initProcessedStatus() {
        processedStatus = DispensingSheet.ProcessedStatus.NONE;
        if (CollectionUtils.isEmpty(dispensingForms)) {
            return;
        }
        /**
         * 代煎代配 无加工状态直接完成
         * */
        if (pharmacyType == GoodsConst.PharmacyType.VIRTUAL_PHARMACY) {
            processedStatus = DispensingSheet.ProcessedStatus.SUCCEED;
            return;
        }
        if (dispensingForms.stream().anyMatch(dispensingForm -> dispensingForm.getProcessedStatus() == DispensingForm.ProcessedStatus.WAITING)) {
            processedStatus = DispensingSheet.ProcessedStatus.WAITING;
        }
        //TODO 这个逻辑还不敢改，虽然觉得有bug， 不确定dispensingForms里面 是否存在None的场景，如果用allMatch会出问题
        //TODO 前面 if 优先保证了一个waiting  整个sheet waiting
        if (dispensingForms.stream().anyMatch(dispensingForm -> dispensingForm.getProcessedStatus() == DispensingForm.ProcessedStatus.SUCCEED)) {
            processedStatus = DispensingSheet.ProcessedStatus.SUCCEED;
        }
    }

    public void bindProductInfo(Map<String, GoodsItem> dispensingFormItemIdToGoodsItem) {
        for (DispensingFormV2 dispensingForm : dispensingForms) {
            for (DispensingFormItemV2 dispensingFormItem : dispensingForm.getDispensingFormItems()) {
                dispensingFormItem.bindProductInfo(dispensingFormItemIdToGoodsItem);
            }
        }
    }

    /**
     * 通过发药子项的状态的数量 计算 整个发药单的状态
     */
    public int computeDispensingSheetStatusByItemStatusCount() {
        // 所有非套餐 和非 Cancle的发药项
        List<DispensingFormItemV2> validItems = dispensingForms
                .stream()
                .filter(dispensingForm -> dispensingForm.getDispensingFormItems() != null)
                .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream())
                .filter(dispensingFormItem ->
                        dispensingFormItem.getProductType() != Constants.ProductType.COMPOSE_PRODUCT
                                && dispensingFormItem.getStatus() != DispensingFormItem.Status.CANCELED).collect(Collectors.toList());

        Collection<BigDecimal> mergedItemTotalCounts = validItems.stream()
                .collect(
                        Collectors.toMap(
                                item -> {
                                    if (item.getStatus() != DispensingFormItem.Status.UNDISPENSED) {
                                        return item.getId();
                                    } else {
                                        return item.getAssociateFormItemId();
                                    }
                                },
                                item -> {
                                    BigDecimal itemTotalCount = MathUtils.calculateTotalCount(item.getUnitCount(), item.getDoseCount());
                                    if (item.getStatus() != DispensingFormItem.Status.UNDISPENSED) {
                                        return MathUtils.wrapBigDecimal(itemTotalCount, BigDecimal.ZERO);
                                    } else {
                                        return MathUtils.wrapBigDecimal(itemTotalCount, BigDecimal.ZERO).negate();
                                    }
                                },
                                (a, b) -> a.add(b)
                        ))
                .values();

        long waitingDispensingItemCount = validItems.stream().filter(dispensingFormItem -> DispensingFormItem.Status.dispenseable.contains(dispensingFormItem.getStatus())).count();
        long dispensedAndWaitingDispensingItemCount = mergedItemTotalCounts.stream().filter(value -> value.compareTo(BigDecimal.ZERO) > 0).count();
        long dispensedDispensingItemCount = dispensedAndWaitingDispensingItemCount - waitingDispensingItemCount;
        long undispensedDispensingItemCount = mergedItemTotalCounts.stream().filter(value -> value.compareTo(BigDecimal.ZERO) <= 0).count();

        int sheetStatus = DispensingSheet.Status.WAITING;
        if (waitingDispensingItemCount == 0) {
            /**
             * 所有药的发过了
             * */
            if (dispensedDispensingItemCount != 0) {
                sheetStatus = DispensingSheet.Status.DISPENSED;
            }
            if (dispensedDispensingItemCount == 0 && undispensedDispensingItemCount != 0) {
                sheetStatus = DispensingSheet.Status.UNDISPENSED;
            }
        } else { //有未发的药，整个发药单都是待发
            sheetStatus = DispensingSheet.Status.WAITING;
        }
        return sheetStatus;
    }

    @JsonIgnore
    public int getIsPartDispensed() {
        if (CollectionUtils.isEmpty(this.dispensingForms)) {
            return DispensingUtils.SwitchFlag.OFF;
        }
        boolean partDispense = this.status == DispenseConst.Status.WAITING &&
                this.dispensingForms
                        .stream()
                        .flatMap(it -> it.getDispensingFormItems().stream())
                        .anyMatch(dispensingFormItem -> dispensingFormItem.getStatus() == DispenseConst.Status.DISPENSED);
        return partDispense ? DispensingUtils.SwitchFlag.ON : DispensingUtils.SwitchFlag.OFF;
    }

    /**
     * 护士站是否可以发起退药
     * status
     * 1.发药单下面的药全部药发完 发药单才算发完药
     * 2.发药单下面的药全部药退完 发药单才算退完药
     */
    @JsonIgnore
    public boolean canApplyUnDispense() {
        return this.status == DispenseConst.Status.DISPENSED || this.status == DispenseConst.Status.STOCK_NOT_DISPENSE;
    }

    @JsonIgnore
    public boolean canApplyDispense() {
        return this.status == DispenseConst.Status.WAITING;
    }

    /**
     * 发药单下面
     * 发药项目的组成
     * 入库单生成时候 formItem的数量
     * 1. 生成 发药单时候 所有的发药项都是 0 WAITING
     * 2. 只要有一条 WAITING 发药单整体状态就是 WAITING
     * 3. 如果WAITING 还没扣库发药，被取消 直接在原来记录上变成CLOSED
     * 4. 入库单生成时候 formItem的数量 都是 CLOSE 发药单删除 如何判断：没有WAITING&DISPENSED
     * 5. 有发药的DISPENSED 的发药条目
     * 5.1 判断完成退药的数量 == DISPENSED的数量  发药单是退药状态
     * 5.2 判断完成退药的数量 < DISPENSED的数量  发药单是已发药状态
     * <p>
     * 退药记录
     * 4.退一次药生成一条记录
     *
     * @return true状态有变化
     */
    public boolean fixHisSheetStatus() {
        AtomicInteger waitingCount = new AtomicInteger();
        AtomicInteger dispensedCount = new AtomicInteger();
        AtomicInteger dispensedRejectCount = new AtomicInteger();
        AtomicInteger unDispensedCount = new AtomicInteger();
        AtomicInteger closeCount = new AtomicInteger();
        AtomicInteger totalCount = new AtomicInteger();
        AtomicInteger applyUndispenseCount = new AtomicInteger();
        AtomicInteger rejectUndispenseCount = new AtomicInteger();
        // 补记
        AtomicInteger recordNotDispenseCount = new AtomicInteger();
        // 扣库
        AtomicInteger stockNotDispenseCount = new AtomicInteger();
        dispensingForms.stream().flatMap(dispensingFormV21 -> dispensingFormV21.getDispensingFormItems().stream()).forEach(dispensingFormItemV21 -> {
            totalCount.getAndIncrement();
            if (dispensingFormItemV21.getStatus() == DispenseConst.Status.WAITING) {
                waitingCount.getAndIncrement();
            }
            if (dispensingFormItemV21.getStatus() == DispenseConst.Status.DISPENSED) {
                dispensedCount.getAndIncrement();
            }
            if (dispensingFormItemV21.getStatus() == DispenseConst.Status.APPLY_DISPENSE_REJECT) {
                dispensedRejectCount.getAndIncrement();
            }
            if (dispensingFormItemV21.getStatus() == DispenseConst.Status.UNDISPENSED) {
                unDispensedCount.getAndIncrement();
            }
            if (dispensingFormItemV21.getStatus() == DispenseConst.Status.CLOSED) {
                closeCount.getAndIncrement();
            }
            if (dispensingFormItemV21.getStatus() == DispenseConst.Status.APPLY_UNDISPNSE) {
                applyUndispenseCount.getAndIncrement();
            }
            if (dispensingFormItemV21.getStatus() == DispenseConst.Status.APPLY_UNDISPENSE_REJECT) {
                rejectUndispenseCount.getAndIncrement();
            }
            if (dispensingFormItemV21.getStatus() == DispenseConst.Status.RECORD_NOT_DISPENSE) {
                recordNotDispenseCount.getAndIncrement();
            }
            if (dispensingFormItemV21.getStatus() == DispenseConst.Status.STOCK_NOT_DISPENSE) {
                stockNotDispenseCount.getAndIncrement();
            }
            if (dispensingFormItemV21.getStatus() == DispenseConst.Status.REAPPLY_DISPENSE) {
                // 重新申请发药为待发
                waitingCount.getAndIncrement();
            }
            if (dispensingFormItemV21.getStatus() == DispenseConst.Status.REAPPLY_UNDISPENSE) {
                // 重新申请退药为待退
                applyUndispenseCount.getAndIncrement();
            }
        });

        if (totalCount.get() == 0) {
            return false;
        }
        boolean changed = false;
        //还有没发的
        if (waitingCount.get() > 0) {
            changed = this.status != DispenseConst.Status.WAITING;
            this.status = DispenseConst.Status.WAITING;
            return changed;
        }
        // 补记
        if (recordNotDispenseCount.get() > 0) {
            changed = this.status != DispenseConst.Status.RECORD_NOT_DISPENSE;
            this.status = DispenseConst.Status.RECORD_NOT_DISPENSE;
            return changed;
        }
        // 扣库/待退
        if (waitingCount.get() == 0 && stockNotDispenseCount.get() > 0 && applyUndispenseCount.get() > 0) {
            changed = this.status != DispenseConst.Status.STOCK_NOT_DISPENSE;
            this.status = DispenseConst.Status.STOCK_NOT_DISPENSE;
            return changed;
        }
        // 待退或拒退
        if (waitingCount.get() == 0 && dispensedCount.get() > 0 && applyUndispenseCount.get() > 0) {
            changed = this.status != DispenseConst.Status.DISPENSED;
            this.status = DispenseConst.Status.DISPENSED;
            return changed;
        }
        // 没有待发 也没有 发过的  一定是单据被关了
        if (waitingCount.get() == 0
                && dispensedCount.get() == 0
                && stockNotDispenseCount.get() == 0
                && (dispensedRejectCount.get() > 0
                || closeCount.get() > 0)
        ) {
            changed = this.status != DispenseConst.Status.CLOSED;
            this.status = DispenseConst.Status.CLOSED;
            return changed;
        }

        //绝大部分场景没退药，走到这里一定是已经已发
        if (unDispensedCount.get() == 0 && stockNotDispenseCount.get() == 0) {
            changed = this.status != DispenseConst.Status.DISPENSED;
            this.status = DispenseConst.Status.DISPENSED;
            return changed;
        }
        //走到这里一定是发生过退药，要看退药数量。没被退完的药品数量
        long unDispensedToZeroItemCount = 0L;
        // 正常发药
        if (dispensedCount.get() > 0) {
            unDispensedToZeroItemCount = dispensingForms
                    .stream()
                    .filter(dispensingForm -> dispensingForm.getDispensingFormItems() != null)
                    .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream())
                    .filter(dispensingFormItem ->
                            dispensingFormItem.getProductType() != Constants.ProductType.COMPOSE_PRODUCT
                                    && (
                                    dispensingFormItem.getStatus() == DispenseConst.Status.DISPENSED
                                            || dispensingFormItem.getStatus() == DispenseConst.Status.UNDISPENSED
                                            || dispensingFormItem.getStatus() == DispenseConst.Status.APPLY_UNDISPNSE
                            )
                    )
                    .collect(
                            Collectors.toMap(
                                    //  发药和退药记录
                                    dispensingFormItem -> dispensingFormItem.getStatus() == DispenseConst.Status.DISPENSED ? dispensingFormItem.getId() : dispensingFormItem.getAssociateFormItemId(),
                                    //  剩余未退数量
                                    dispensingFormItem -> {
                                        BigDecimal itemTotalCount = MathUtils.calculateTotalCount(dispensingFormItem.getUnitCount(), dispensingFormItem.getDoseCount());
                                        if (dispensingFormItem.getStatus() == DispenseConst.Status.DISPENSED) {
                                            return MathUtils.wrapBigDecimal(itemTotalCount, BigDecimal.ZERO);
                                        } else {
                                            return MathUtils.wrapBigDecimal(itemTotalCount, BigDecimal.ZERO).negate();
                                        }
                                    },
                                    (a, b) -> a.add(b)
                            ))
                    .values()
                    .stream().filter(value -> value.compareTo(BigDecimal.ZERO) > 0)
                    .count();
        }
        // 仅扣库
        long stockUnDispensedToZeroItemCount = 0L;
        if (stockNotDispenseCount.get() > 0) {
            stockUnDispensedToZeroItemCount = dispensingForms
                    .stream()
                    .filter(dispensingForm -> dispensingForm.getDispensingFormItems() != null)
                    .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream())
                    .filter(dispensingFormItem ->
                            dispensingFormItem.getProductType() != Constants.ProductType.COMPOSE_PRODUCT
                                    && (
                                    dispensingFormItem.getStatus() == DispenseConst.Status.STOCK_NOT_DISPENSE
                                            || dispensingFormItem.getStatus() == DispenseConst.Status.UNDISPENSED
                                            || dispensingFormItem.getStatus() == DispenseConst.Status.APPLY_UNDISPNSE
                            )
                    )
                    .collect(
                            Collectors.toMap(
                                    //  发药和退药记录
                                    dispensingFormItem -> dispensingFormItem.getStatus() == DispenseConst.Status.STOCK_NOT_DISPENSE ? dispensingFormItem.getId() : dispensingFormItem.getAssociateFormItemId(),
                                    //  剩余未退数量
                                    dispensingFormItem -> {
                                        BigDecimal itemTotalCount = MathUtils.calculateTotalCount(dispensingFormItem.getUnitCount(), dispensingFormItem.getDoseCount());
                                        if (dispensingFormItem.getStatus() == DispenseConst.Status.STOCK_NOT_DISPENSE) {
                                            return MathUtils.wrapBigDecimal(itemTotalCount, BigDecimal.ZERO);
                                        } else {
                                            return MathUtils.wrapBigDecimal(itemTotalCount, BigDecimal.ZERO).negate();
                                        }
                                    },
                                    BigDecimal::add
                            ))
                    .values()
                    .stream().filter(value -> value.compareTo(BigDecimal.ZERO) > 0)
                    .count();
        }

        if (unDispensedToZeroItemCount > 0) {
            changed = this.status != DispenseConst.Status.DISPENSED;
            this.status = DispenseConst.Status.DISPENSED;
        } else if (stockUnDispensedToZeroItemCount > 0) {
            // 仅扣库未退完
            changed = this.status != DispenseConst.Status.STOCK_NOT_DISPENSE;
            this.status = DispenseConst.Status.STOCK_NOT_DISPENSE;
        } else {
            changed = this.status != DispenseConst.Status.UNDISPENSED;
            this.status = DispenseConst.Status.UNDISPENSED;
        }
        return changed;
    }

    /**
     * 一个发药项不能分多次发，只能一次发完 so 判断 WAITING 没问题
     */
    @JsonIgnore
    public List<DispensingFormItemV2> waitingDispenseItems() {
        if (CollectionUtils.isEmpty(dispensingForms)) {
            return new ArrayList<>();
        }

        return dispensingForms
                .stream()
                .filter(Objects::nonNull)
                .filter(dispensingForm -> !CollectionUtils.isEmpty(dispensingForm.getDispensingFormItems()))
                .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream())
                .filter(dispensingFormItem -> dispensingFormItem.getStatus() == DispenseConst.Status.WAITING || dispensingFormItem.getStatus() == DispenseConst.Status.REAPPLY_DISPENSE)
                .collect(Collectors.toList());
    }

    @JsonIgnore
    public List<DispensingFormItemV2> canUnDispenseItems() {
        if (CollectionUtils.isEmpty(dispensingForms)) {
            return new ArrayList<>();
        }

        return dispensingForms
                .stream()
                .filter(Objects::nonNull)
                .filter(dispensingForm -> !CollectionUtils.isEmpty(dispensingForm.getDispensingFormItems()))
                .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream())
                .filter(dispensingFormItem -> dispensingFormItem.getStatus() == DispenseConst.Status.DISPENSED || dispensingFormItem.getStatus() == DispenseConst.Status.STOCK_NOT_DISPENSE)
                .collect(Collectors.toList());
    }

    @JsonIgnore
    public List<DispensingFormItemV2> allDispenseItems() {
        if (CollectionUtils.isEmpty(dispensingForms)) {
            return new ArrayList<>();
        }

        return dispensingForms
                .stream()
                .filter(Objects::nonNull)
                .filter(dispensingForm -> !CollectionUtils.isEmpty(dispensingForm.getDispensingFormItems()))
                .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream())
                .collect(Collectors.toList());
    }

    /**
     * 效率 提升 为了避免多次拉取
     */
    @Transient
    @JsonIgnore
    private PatientOrder patientOrder;
    @Transient
    @JsonIgnore
    private List<GoodsItem> goodsItemList;

    /**
     * 领药单 只有医院药房这边使用,消息charge需要
     */
    @JsonIgnore
    @Transient
    private DispensingOrder dispensingOrder;

    @JsonIgnore
    public boolean isSelfProvided() {
        return this.status == DispenseConst.Status.SELF_PROVIDED;
    }

    @JsonIgnore
    public boolean isAutoDispensingMethod() {
        return dispensingMethod == DispenseConst.DispensingMethod.AUTO;
    }

    @JsonIgnore
    public boolean noAdviceIdSourceSheetType() {
        return sourceSheetType == DispenseConst.SourceSheetType.USAGE_ASSOCIATION || sourceSheetType == DispenseConst.SourceSheetType.CHARGE_SUPPLEMENT;
    }

    @JsonIgnore
    public boolean hasReNewSheet() {
        if (dispensingTag == null) {
            return false;
        }
        return DispensingUtils.checkFlagOn(dispensingTag, DispensingUtils.DispensingTag.DISPENSING_TAG_HOSPITAL_RENEW_OLD_SHEET_FLAG);
    }

    /**
     * 检查是否需要锁 ： 已经有所锁可能药释放锁& 里面有强锁批次的药品
     */
    @JsonIgnore
    public boolean hasLockBachGoods() {
        if (this.dispensingForms != null) {
            for (DispensingFormV2 form : this.dispensingForms) {
                if (CollectionUtils.isEmpty(form.getDispensingFormItems())) {
                    continue;
                }
                for (DispensingFormItemV2 item : form.getDispensingFormItems()) {
                    if (item.getLockId() != null) {
                        return true;
                    }
                    GoodsItem goodsItem = JsonUtils.readValue(item.getProductInfo(), GoodsItem.class);
                    if (goodsItem == null || goodsItem.getLockBatchOps() == null) {
                        continue;
                    }
                    if (goodsItem.getLockBatchOps() == LockConfig.LockBatchOps.BATCH) {
                        return true;
                    }
                }
            }
        }
        return false;

    }

    @JsonIgnore
    public boolean canDisplay() {
        if (isDeleted == 0) {
            return true;
        }

        return hasDispensingTag(DispensingUtils.DispensingTag.DISPENSING_TAG_HAS_DISPENSE);
    }

}
