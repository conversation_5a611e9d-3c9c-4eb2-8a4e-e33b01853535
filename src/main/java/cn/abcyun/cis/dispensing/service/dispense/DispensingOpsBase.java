package cn.abcyun.cis.dispensing.service.dispense;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsDispenseDataItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.TraceableCode;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.dispensing.base.DispensingConstants;
import cn.abcyun.cis.dispensing.domain.DispensingFormItem;
import cn.abcyun.cis.dispensing.domain.DispensingFormItemExtendData;
import cn.abcyun.cis.dispensing.util.MathUtils;
import cn.abcyun.cis.dispensing.util.TraceCodeUtils;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 发药单操作基类
 *
 * <AUTHOR>
 * @since 2024/11/7 15:19
 **/
@Slf4j
public abstract class DispensingOpsBase {

    protected PatientOrder patientOrder;

    /**
     * @param dispensedItem         发药的FormItem
     * @param clientUnDispenseItem  客户端传入的退药FormItem
     * @param goodsDispenseDataItem 发送给ScGoods的退药申请
     * @param abcIdGenerator
     */
    protected void dealUnDispenseTraceCode(DispensingFormItem dispensedItem, DispensingFormItem clientUnDispenseItem, GoodsDispenseDataItem goodsDispenseDataItem, AbcIdGenerator abcIdGenerator) {
        //指定追溯码退药
        if (clientUnDispenseItem == null || CollectionUtils.isEmpty(clientUnDispenseItem.getTraceableCodeList())) {
            return;
        }
        clientUnDispenseItem.setTraceableCodeList(clientUnDispenseItem.getTraceableCodeList().stream()
                .filter(traceableCode -> Objects.equals(traceableCode.getType(), GoodsConst.DrugIdentificationCodeType.NO_CODE) || !TextUtils.isEmpty(traceableCode.getNo()))
                .collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(clientUnDispenseItem.getTraceableCodeList())) {
            return;
        }
        /**
         * 本次退药标记追溯码可用
         * 如果指定退追溯码，要把退的状态写服务器的状态
         * 产品这里放的很开，后台这里把各种情况都处理了
         * 退药可以不退追溯码，产品逻辑
         * */
        //服务端没extend
        if (dispensedItem.getExtendData() == null) {
            dispensedItem.setExtendData(new DispensingFormItemExtendData());
        }
        //支持之前没有录入追溯码，可以指定追溯码退
        if (dispensedItem.getExtendData().getTraceableCodeList() == null) {
            dispensedItem.getExtendData().setTraceableCodeList(new ArrayList<>());
        }

        // 历史数据兼容，合并相同追溯码
        dispensedItem.getExtendData().setTraceableCodeList(TraceCodeUtils.mergeTraceableCode(dispensedItem.getGoodsItem() != null ? dispensedItem.getGoodsItem().getPieceNum() : null, dispensedItem.getTraceableCodeList()));
        clientUnDispenseItem.getExtendData().setTraceableCodeList(TraceCodeUtils.mergeTraceableCode(dispensedItem.getGoodsItem() != null ? dispensedItem.getGoodsItem().getPieceNum() : null, clientUnDispenseItem.getExtendData().getTraceableCodeList()));

        for (TraceableCode clientTraceableCode : clientUnDispenseItem.getTraceableCodeList()) {
            TraceableCode dispensedTraceableCode = dispensedItem.getTraceableCodeList().stream()
                    .filter(srvTraceableCode -> Objects.equals(srvTraceableCode.getNo(), clientTraceableCode.getNo()) && srvTraceableCode.getUsed() == DispensingConstants.TraceableCodeUsed.DISPENSE)
                    .findFirst().orElse(null);
            // 由于存在重复追溯码，所以先不校验了，找不到就直接不改状态
            if (dispensedTraceableCode == null) {
                continue;
            }

            BigDecimal pieceNum = dispensedItem.getGoodsItem().getPieceNum();
            BigDecimal unDispensedTraceTotalCount = dispensedItem.getTraceableCodeList().stream().map(traceCode -> {
                if (!Objects.equals(traceCode.getNo(), clientTraceableCode.getNo()) || traceCode.getUsed() != DispensingConstants.TraceableCodeUsed.UNDISPENSE) {
                    return BigDecimal.ZERO;
                }
                return MathUtils.wrapBigDecimalAdd(traceCode.getHisPieceCount(), MathUtils.wrapBigDecimalMultiply(traceCode.getHisPackageCount(), pieceNum));
            }).reduce(BigDecimal.ZERO, MathUtils::wrapBigDecimalAdd);

            BigDecimal dispensedTraceTotalCount = MathUtils.wrapBigDecimalAdd(dispensedTraceableCode.getHisPieceCount(), MathUtils.wrapBigDecimal(dispensedTraceableCode.getHisPackageCount(), BigDecimal.ZERO).multiply(pieceNum));
            BigDecimal cliUnDispenseTraceTotalCount = MathUtils.wrapBigDecimalAdd(clientTraceableCode.getHisPieceCount(), MathUtils.wrapBigDecimal(clientTraceableCode.getHisPackageCount(), BigDecimal.ZERO).multiply(pieceNum));
            int compare = MathUtils.wrapBigDecimalCompare(cliUnDispenseTraceTotalCount, MathUtils.wrapBigDecimalSubtract(dispensedTraceTotalCount, unDispensedTraceTotalCount));
            if (compare > 0) {
                // 暂时只做记录，不做抢拦截，因为涉及到发药单的退药，之前没有记录
                log.error("退药追溯码数量大于发药追溯码数量，发药项ID：{}，追溯码：{}，发的数量：{}，已退数量：{}，请求退的数量：{}", dispensedItem.getId(), clientTraceableCode.getNo(), dispensedTraceTotalCount, unDispensedTraceTotalCount, cliUnDispenseTraceTotalCount);
//                throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR);
            } else {
                // 退的数量小于等于发的数量，需要拆分追溯码
                // 创建出一条退的追溯码
                TraceableCode newTraceableCode = new TraceableCode();
                BeanUtils.copyProperties(dispensedTraceableCode, newTraceableCode);
                newTraceableCode.setId(abcIdGenerator.getUID());
                if (cliUnDispenseTraceTotalCount.remainder(pieceNum).compareTo(BigDecimal.ZERO) == 0) {
                    newTraceableCode.setHisPackageCount(cliUnDispenseTraceTotalCount.divide(pieceNum, 2, RoundingMode.DOWN));
                    newTraceableCode.setHisPieceCount(null);
                    newTraceableCode.setDismountingSn(null);
                } else {
                    newTraceableCode.setHisPackageCount(clientTraceableCode.getHisPackageCount());
                    newTraceableCode.setHisPieceCount(clientTraceableCode.getHisPieceCount());
                    newTraceableCode.setDismountingSn(clientTraceableCode.getDismountingSn());
                }
                newTraceableCode.setId(dispensedTraceableCode.getId());
                newTraceableCode.setUsed(DispensingConstants.TraceableCodeUsed.UNDISPENSE);
                dispensedItem.getTraceableCodeList().add(newTraceableCode);
            }

            clientTraceableCode.setUsed(DispensingConstants.TraceableCodeUsed.WAITING);//提上来的都是要用的
        }

        // 最后在进行一次合并，因为多次退的话，可能会有多条退的追溯码
        dispensedItem.setTraceableCodeList(TraceCodeUtils.mergeTraceableCode(dispensedItem.getGoodsItem().getPieceNum(), dispensedItem.getTraceableCodeList()));

        /**
         * 如果是指定追溯码退药，把追溯码加到scGoods的发药请求里面
         * */
        if (goodsDispenseDataItem != null) {
            goodsDispenseDataItem.setTraceableCodeList(clientUnDispenseItem.getTraceableCodeList().stream().filter(it -> it.getUsed() == DispensingConstants.TraceableCodeUsed.WAITING).collect(Collectors.toList()));
            goodsDispenseDataItem.setShebaoDismountingFlag(clientUnDispenseItem.getShebaoDismountingFlag());
        }

    }

}
