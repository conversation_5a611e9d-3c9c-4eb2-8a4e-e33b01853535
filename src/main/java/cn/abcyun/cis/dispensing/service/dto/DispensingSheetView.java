package cn.abcyun.cis.dispensing.service.dto;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Department;
import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispenseConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.bis.rpc.sdk.his.model.advice.AdviceSimpleView;
import cn.abcyun.bis.rpc.sdk.his.model.ward.WardBedView;
import cn.abcyun.cis.commons.model.CisPatientInfo;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.cis.commons.model.ShebaoCardInfo;
import cn.abcyun.cis.commons.rpc.charge.ChargeDeliveryInfoView;
import cn.abcyun.cis.commons.rpc.outpatient.MedicalRecord;
import cn.abcyun.cis.commons.rpc.outpatient.UsageInfo;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.dispensing.api.protocol.config.WardAreaView;
import cn.abcyun.cis.dispensing.domain.*;
import cn.abcyun.cis.dispensing.rpc.model.outpatient.OutpatientSheetInfoDispensing;
import cn.abcyun.cis.dispensing.service.EmployeeService;
import cn.abcyun.cis.dispensing.service.rpc.ScGoodsFeignClient;
import cn.abcyun.cis.dispensing.util.DispensingUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Data
public class DispensingSheetView {
    /**
     * 以下字段是DispensingSheet 也有的字段，直接通过BeanUtils.copy
     */
    @ApiModelProperty("发药单Id")
    private String id;
    @ApiModelProperty("发药单所属patientOrder的Id")
    private String patientOrderId;
    @ApiModelProperty("发药单所属patientOrder的No")
    private String patientOrderNo;
    @ApiModelProperty("病历记录信息")
    private MedicalRecord medicalRecord;
    //就诊时间 for OpenApi
    @ApiModelProperty("就诊日期")
    private Instant diagnosedDate;
    @ApiModelProperty("连锁Id")
    private String chainId;
    //门店ID openApi 叶开泰厚达要使用
    @ApiModelProperty("门店Id")
    private String clinicId;
    @ApiModelProperty("门诊单开方的医生Id")
    private String doctorId;
    @ApiModelProperty("门诊单开方的医生名称")
    private String doctorName;

    /// ///////////////医院发药单新加字段////////////////////
    @ApiModelProperty("发药单类型 0 门诊发药单  10 医院住院发药单")
    private Integer type;
    @ApiModelProperty("医院发药单时，为医嘱ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long adviceId;
    @ApiModelProperty("医院发药单时 为患者住院的病床信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private WardBedView wardBedView;
    @ApiModelProperty("医院发药单时 为患者住院的病区信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private WardAreaView wardAreaView;
    /// ///////////////医院发药单新加字段////////////////////

    @ApiModelProperty("门诊发药单时，对应的收费单的chargeSheetId")
    private String sourceSheetId;
    /**
     * 医生科室 OPENAPI only
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Department department;
    /**
     * 开方医生 OPENAPI only
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private cn.abcyun.bis.rpc.sdk.cis.model.clinic.Employee doctor;
    /**
     * 是否社保支付 openAPI用
     */
    private int isShebaoPay;
    /**
     * 收费单类型
     */
    private Integer sourceSheetType;
    @ApiModelProperty("发药单的状态 0 待发 1 已发 2 已退 3 关闭")
    private int status;

    /**
     * 支付状态
     *
     * @see cn.abcyun.cis.dispensing.domain.DispensingSheet.PayStatus
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @ApiModelProperty(value = "支付状态 10:已支付 20:已退费")
    private Integer payStatus;

    @ApiModelProperty("中药处方退药时的退药类型 0 未退 1 按量退 2 按剂退")
    private int chineseMedicineUndispenseType;
    @ApiModelProperty("发药员Id")
    private String dispensedBy;
    @ApiModelProperty("发药时间")
    private Instant dispensedTime;

    public Instant getUndispensedTime() {
        return this.orderByDate;
    }

    @ApiModelProperty("退药时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Instant undispensedTime;

    @ApiModelProperty("是否是在线支付的发药单 1 是")
    private int isOnline;
    @ApiModelProperty("是否需要煎药 1 是")
    private int isDecoction;
    @ApiModelProperty("是否为患者自己支付的 1 是")
    private int isPatientSelfPay;

    @ApiModelProperty("是否需要快递 1 快递")
    private int deliveryType;
    @ApiModelProperty("快递状态 0 不需要快递 1 快递中 2 快递完成")
    private int deliveredStatus;
    @ApiModelProperty("加工状态 0 不需要加工 1 加工中 2 加工完成")
    private int processedStatus;
    @ApiModelProperty("审方状态 0 不需要审方 1 审方中 2 审方完成")
    private int auditedStatus;
    @ApiModelProperty("调配状态 0 不需要调配 1 调配中 2 调配完成")
    private int compoundedStatus;
    @ApiModelProperty("药房号：默认为0")
    private int pharmacyNo;
    @ApiModelProperty("药房类型：0：实体药房，1：虚拟药房，2：空中药房")
    private int pharmacyType;
    @ApiModelProperty("药房名称")
    private String pharmacyName;
    @ApiModelProperty("药房扩展信息")
    private JsonNode pharmacyExtendInfo;
    @ApiModelProperty("发药单创建时间")
    private Instant created;
    @ApiModelProperty("不知道干啥的")
    private int printedNumber;
    @ApiModelProperty("低开始依次:  0x01中西药处方|0x02中药饮片处方|0x04中药颗粒处方|0x08输注处方|0x10商品材料处方|0x20 标识发药单有进行过发药|0x40标识发药单中存在药品在发药前就已经确定了批次|0x80 标识发药单有进行过退药 |0x100标识发药单存在可退药项|0x200医院发药单重新发药，已退的单据重新生成新的发药单，之前的老发药单已重新生成标识|0x400标记这个发药单是否刷过医保 ")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer dispensingTag;
    /// /////////////////////////// 以下字段DispensingSheet有的，需要通过额外方式初始化
    @ApiModelProperty("患者信息")
    private CisPatientInfo patient;

    //打印需要patientId
    @JsonIgnore
    private String patientId;
    @JsonIgnore
    private Instant orderByDate;

    @ApiModelProperty("patientOrder上的source")
    private int source;
    @ApiModelProperty("从patientOrder上获取的社保信息")
    private ShebaoCardInfo shebaoCardInfo;

    @ApiModelProperty("发药单上收费相关信息")
    private BigDecimal netIncomeFee;
    @ApiModelProperty("收费员的名字")
    private String chargedByName;
    @ApiModelProperty("收费时间")
    private Instant chargedTime;
    @ApiModelProperty("医嘱")
    private String doctorAdvice;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @ApiModelProperty("诊断")
    private String diagnose;
    @ApiModelProperty("快递信息")
    private ChargeDeliveryInfoView deliveryInfo;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @ApiModelProperty("9自煎 10代煎")
    private String medicineStateScopeId;

    /**
     * 发药单的扩展信息
     */
    @ApiModelProperty("发药单状态名字")
    private String statusName;
    @ApiModelProperty("发药人名字")
    private String dispensedByName;
    @ApiModelProperty("多发药人")
    private List<EmployeeView> dispensedByEmployee;
    @ApiModelProperty("是否部分发药")
    private int isPartDispensed;
    @ApiModelProperty("是否可以退药")
    private int canUnDispense;
    @ApiModelProperty("是否可以部分退药")
    private int canPartUnDispense = 1;
    @ApiModelProperty("发药单可打印信息")
    private DispensingSheetPrintable printable;
    @ApiModelProperty("发药FormList")
    private List<DispensingFormView> dispensingForms = new ArrayList<>();


    /**
     * 如果非空为，部分打印的 formIdList
     */
    @JsonIgnore
    private List<String> printDispensingFormIdList;
    @JsonIgnore
    private List<String> printOutpatientFormIdList;

    //门诊单的辅助变量 OPENAPI 发药要把门诊信息带出去
    @JsonIgnore
    private OutpatientSheetInfoDispensing outpatientSheet;

    /**
     * 发药标记位，对应GoodsPharmacy的dispenseFlag字段
     * {@link cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst.DispenseFlag}
     */
    @ApiModelProperty("发药标记位，对应GoodsPharmacy的dispenseFlag字段")
    private int pharmacyDispenseFlag;

    /**
     * 开单人科室id
     */
    @ApiModelProperty("开单人科室id")
    private String sellerDepartmentId;
    /**
     * 开单人科室
     */
    @ApiModelProperty("开单人科室")
    private String sellerDepartmentName;

    /**
     * 开单人id
     */
    @ApiModelProperty("开单人id")
    private String sellerId;

    /**
     * 开单人
     */
    @ApiModelProperty("开单人")
    private String sellerName;

    /**
     * 发药单是否支持重新发药 0:不支持 1:支持
     */
    @ApiModelProperty("发药单是否支持重新发药 0:不支持 1:支持")
    private int supportReDispense;

    /**
     * 是否是重新发药的同时发药
     * 仅在发药事件时有效
     */
    @ApiModelProperty("是否是重新发药的同时发药")
    private int isReDispenseDirectDispense;

    /**
     * 空中药房订单ID
     */
    @ApiModelProperty("空中药房订单ID")
    private String airPharmacyOrderId;

    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private int isDeleted;

    /**
     * 分组 id
     * 医院用，同一组的药品相同 id，使用医嘱groupId+planExecuteTime md5算出来
     */
    @ApiModelProperty(value = "分组id,用于药品关联")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String groupId;
    @ApiModelProperty(value = "医嘱分组id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long adviceGroupId;
    /**
     * 计划执行时间
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Instant planExecuteTime;

    /**
     * 患者当前所在的病区
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String curPatientWardId;

    /**
     * 取药叫号项ID
     */
    @ApiModelProperty("取药叫号项ID（如果不为空说明可以快速叫号）")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dispensingCallingItemId;

    private String v1Id;
    private String departmentId;

    /**
     * 永远返回非null
     * 返回的是半成品版本的 DispensingSheetView ，额外的charge  和patientOrder信息还没填充
     */
    public static DispensingSheetView from(DispensingSheet dispensingSheet) {
        DispensingSheetView dispensingSheetView = new DispensingSheetView();
        BeanUtils.copyProperties(dispensingSheet, dispensingSheetView, "dispensingForms");
        if (dispensingSheet.getDispensingForms() != null) {
            dispensingSheetView.setDispensingForms(dispensingSheet.getDispensingForms().stream().map(DispensingFormView::from).collect(Collectors.toList()));
        } else {
            dispensingSheetView.setDispensingForms(new ArrayList<>());
        }
        if(dispensingSheet.hasDispensingTag(DispensingUtils.DispensingTag.DISPENSING_TAG_SHEBAO_PAY_FLAG)){
            dispensingSheetView.setIsShebaoPay(YesOrNo.YES);
        }
        dispensingSheetView.setIsPartDispensed(DispensingUtils.isPartDispensedSheet(dispensingSheet) ? 1 : 0);
        dispensingSheetView.setCanUnDispense(DispensingUtils.dispensingSheetCanUnDispense(dispensingSheet) ? 1 : 0);

        //判断每个处方是否支持按剂退药
        dispensingSheetView.getDispensingForms().forEach(
                dispensingFormView -> dispensingFormView.updateCanFormUndispenseByDose(dispensingSheetView.getChineseMedicineUndispenseType()));
        return dispensingSheetView;
    }

    /**
     * DispensingSheetV2 不级联加载form和formItem的版本
     */
    public static DispensingSheetView from(DispensingSheetV2 dispensingSheetV2) {
        DispensingSheetView dispensingSheetView = new DispensingSheetView();
        BeanUtils.copyProperties(dispensingSheetV2, dispensingSheetView, "dispensingForms", "isPartDispensed");
        if (!CollectionUtils.isEmpty(dispensingSheetV2.getDispensingForms())) {
            dispensingSheetView.getDispensingForms().addAll(dispensingSheetV2.getDispensingForms().stream().map(DispensingFormView::from).collect(Collectors.toList()));
        }
        if(dispensingSheetV2.hasDispensingTag(DispensingUtils.DispensingTag.DISPENSING_TAG_SHEBAO_PAY_FLAG)){
            dispensingSheetView.setIsShebaoPay(YesOrNo.YES);
        }

        dispensingSheetView.setIsPartDispensed(dispensingSheetV2.getIsPartDispensed());
        dispensingSheetView.setStatusName(transformDispensingSheetStatusName(dispensingSheetV2));
        return dispensingSheetView;
    }

    public static String transformDispensingSheetStatusName(DispensingSheetV2 dispensingSheetV2) {
        String statusName = "";
        if (dispensingSheetV2 == null || CollectionUtils.isEmpty(dispensingSheetV2.getDispensingForms())) {
            return statusName;
        }
        AtomicInteger waitingCount = new AtomicInteger();
        AtomicInteger dispensedCount = new AtomicInteger();
        AtomicInteger dispensedRejectCount = new AtomicInteger();
        AtomicInteger unDispensedRejectCount = new AtomicInteger();
        AtomicInteger unDispensedCount = new AtomicInteger();
        AtomicInteger applyUnDispensedCount = new AtomicInteger();
        AtomicInteger totalCount = new AtomicInteger();
        AtomicInteger notDispenseCount = new AtomicInteger();
        AtomicInteger selfProvidedCount = new AtomicInteger();
        dispensingSheetV2.getDispensingForms().stream().flatMap(item -> item.getDispensingFormItems().stream()).forEach(formItemV2 -> {
            totalCount.getAndIncrement();
            int itemStatus = formItemV2.getStatus();
            if (itemStatus == DispenseConst.Status.WAITING) {
                waitingCount.getAndIncrement();
            }
            if (itemStatus == DispenseConst.Status.DISPENSED) {
                dispensedCount.getAndIncrement();
            }
            if (itemStatus == DispenseConst.Status.APPLY_UNDISPNSE) {
                applyUnDispensedCount.getAndIncrement();
            }
            if (itemStatus == DispenseConst.Status.UNDISPENSED) {
                unDispensedCount.getAndIncrement();
            }
            if (itemStatus == DispenseConst.Status.APPLY_UNDISPENSE_REJECT) {
                unDispensedRejectCount.getAndIncrement();
            }
            if (itemStatus == DispenseConst.Status.APPLY_DISPENSE_REJECT) {
                dispensedRejectCount.getAndIncrement();
            }
            if (itemStatus == DispenseConst.Status.RECORD_NOT_DISPENSE || itemStatus == DispenseConst.Status.STOCK_NOT_DISPENSE) {
                notDispenseCount.getAndIncrement();
            }
            if (itemStatus == DispenseConst.Status.REAPPLY_DISPENSE) {
                waitingCount.getAndIncrement();
            }
            if (itemStatus == DispenseConst.Status.REAPPLY_UNDISPENSE) {
                applyUnDispensedCount.getAndIncrement();
            }
            if (itemStatus == DispenseConst.Status.SELF_PROVIDED) {
                selfProvidedCount.getAndIncrement();
            }
        });
        switch (dispensingSheetV2.getStatus()) {
            case DispenseConst.Status.WAITING:
                statusName = "待发药";
                break;
            case DispenseConst.Status.DISPENSED:
                statusName = "已发药";
                break;
            case DispenseConst.Status.UNDISPENSED:
                statusName = "已退药";
                break;
            case DispenseConst.Status.CLOSED:
                // 关闭，并且有拒绝发药的
                if (dispensedRejectCount.get() > 0) {
                    statusName = "拒绝发药";
                } else {
                    statusName = "已关闭";
                }
                break;
            case DispenseConst.Status.RECORD_NOT_DISPENSE:
            case DispenseConst.Status.STOCK_NOT_DISPENSE:
                statusName = "无需发药";
                break;
            case DispenseConst.Status.SELF_PROVIDED:
                statusName = "自备药";
                break;
        }
        return statusName;
    }

    /**
     * move from dispensingService call if needed
     */
    public void initMaterialRemark() {
        List<DispensingFormView> materialForms = dispensingForms.stream().filter(dispensingFormView -> dispensingFormView.getSourceFormType() == DispensingForm.SourceFormType.MATERIAL).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(materialForms)) {
            return;
        }
        // 在进价加成的需求里面，将上面的代码进行了优化，直接从 dispensingFormItem 里面取 remark 字段写入到 usageInfo 中（保持兼容性）
        // 如果后续有其他需求，和前端沟通后再优化，让前端直接取 dispensingFormItem 的 remark 字段
        materialForms.forEach(materialForm -> {
            if (materialForm == null || CollectionUtils.isEmpty(materialForm.getDispensingFormItems())) {
                return;
            }
            for (DispensingFormItemView dispensingFormItem : materialForm.getDispensingFormItems()) {
                UsageInfo usageInfo = new UsageInfo();
                usageInfo.setSpecialRequirement(dispensingFormItem.getRemark());
                dispensingFormItem.setUsageInfo(JsonUtils.dumpAsJsonNode(usageInfo));
            }
        });
    }

    /***
     * 给 dispensingView 绑定Goods 信息  move from dispensingService
     * call if needed
     * */
    public void refreshProductInfo(ScGoodsFeignClient scGoodsFeignClient, DispensingSheet dispensingSheet) {
        //拉过避免重复拉
        if (dispensingSheet != null && !CollectionUtils.isEmpty(dispensingSheet.getGoodsItemList())) {
            bindProductInfo(ListUtils.toMap(dispensingSheet.getGoodsItemList(), GoodsItem::getKeyId));
            return;
        }

        if (CollectionUtils.isEmpty(dispensingForms)) {
            return;
        }

        Map<String, GoodsItem> dispensingFormItemToGoodsItem = scGoodsFeignClient.queryGoodsItemWitchStockByDispensingSheet(clinicId, chainId, true, 1, dispensingSheet);

        bindProductInfo(dispensingFormItemToGoodsItem);
    }

    public void bindProductInfo(Map<String, GoodsItem> dispensingFormItemIdToGoodsItem) {
        if (CollectionUtils.isEmpty(dispensingFormItemIdToGoodsItem)) {
            return;
        }

        // bind
        dispensingForms.stream()
                .filter(dispensingForm -> dispensingForm.getDispensingFormItems() != null)
                .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream())
                .filter(Objects::nonNull)
                .forEach(dispensingFormItem -> bindProductInfoToFormItemView(dispensingFormItem, dispensingFormItemIdToGoodsItem));

        dispensingForms.forEach(dispensingForm -> dispensingForm.setUsageInfo(JsonUtils.loadAsJsonNode(dispensingForm.getUsageInfoJson())));

        // todo 需要根据item里面的数据进行匹配
        setProductInfos(new ArrayList<>(dispensingFormItemIdToGoodsItem.values()));
    }

    private void bindProductInfoToFormItemView(DispensingFormItemView dispensingFormItem, Map<String, GoodsItem> goodsItemMap) {

        if (dispensingFormItem == null || CollectionUtils.isEmpty(goodsItemMap)) {
            return;
        }

        if (dispensingFormItem.getComposeType() == ComposeType.COMPOSE && !CollectionUtils.isEmpty(dispensingFormItem.getComposeChildren())) {
            dispensingFormItem.getComposeChildren().forEach(child -> bindProductInfoToFormItemView(child, goodsItemMap));
        }

        GoodsItem goodsItem = goodsItemMap.getOrDefault(dispensingFormItem.getId(), null);
        if (goodsItem != null) {
            dispensingFormItem.setStockPackageCount(goodsItem.getStockPackageCount());
            dispensingFormItem.setStockPieceCount(goodsItem.getStockPieceCount());
            // 如果是已发药，需要用快照的信息
            // //快照 规格和标识码
            // 退药也要返回之前的规格
            if (Lists.newArrayList(DispensingFormItem.Status.DISPENSED,DispensingFormItem.Status.UNDISPENSED) .contains(dispensingFormItem.getStatus())&& dispensingFormItem.getProductSnap() != null) {
                goodsItem.setTraceableCodeNoInfoList(dispensingFormItem.getProductSnap().getTraceableCodeNoInfoList());
                if(dispensingFormItem.getProductSnap().getPieceNum()!=null){
                    goodsItem.setPieceNum(dispensingFormItem.getProductSnap().getPieceNum());
                }
                if(!StringUtils.isEmpty(dispensingFormItem.getProductSnap().getPieceUnit()) && !StringUtils.isEmpty(dispensingFormItem.getProductSnap().getPackageUnit())){
                    goodsItem.setPieceUnit(dispensingFormItem.getProductSnap().getPieceUnit());
                    goodsItem.setPackageUnit(dispensingFormItem.getProductSnap().getPackageUnit());
                }
                if(!StringUtils.isEmpty(dispensingFormItem.getProductSnap().getDisplaySpec())){
                    goodsItem.setDisplaySpec(dispensingFormItem.getProductSnap().getDisplaySpec());
                }
            }
            dispensingFormItem.setProductInfo(JsonUtils.dumpAsJsonNode(goodsItem));
        }

        dispensingFormItem.setUsageInfo(JsonUtils.loadAsJsonNode(dispensingFormItem.getUsageInfoJson()));
    }


    /**
     * 绑定多人发药的 人名
     */
    public void bindDispenseUserName(EmployeeService employeeService, List<String> dispensedByIds, String chainId) {
        dispensedByName = employeeService.findEmployeeNameNoExp(dispensedBy, chainId);
        dispensedByEmployee = EmployeeView.from(employeeService.findEmployees(dispensedByIds, chainId));
    }

    /**
     * 绑定多人发药的 人名
     */
    public void bindDispenseUserName(List<EmployeeView> employeeViews, List<String> dispensedByIds) {

        Map<String, String> employeeIdNameMap = employeeViews.stream().collect(Collectors.toMap(EmployeeView::getId, EmployeeView::getName, (a, b) -> a));

        dispensedByName = (StringUtils.isBlank(dispensedBy) || StringUtils.equals("00000000000000000000000000000000", dispensedBy))
                ? ""
                : employeeIdNameMap.getOrDefault(dispensedBy, "");
        dispensedByEmployee = employeeViews.stream().filter(employeeView -> dispensedByIds.contains(employeeView.getId())).collect(Collectors.toList());
        doctorName = StringUtils.isNotEmpty(doctorId) ?
                employeeIdNameMap.getOrDefault(doctorId, "")
                : "";
    }


    @JsonInclude(JsonInclude.Include.NON_NULL)
    private WardBedView beds;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String dispenseOrderId;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private DispensingOrder dispensingOrder;

    /**
     * 开立科室id，医生是从哪个科室开的医嘱，没有取住院单上的科室id
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String doctorDepartmentId;
    @ApiModelProperty(value = "医生科室")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String doctorDepartmentName;

    /**
     * 发药单发药goods信息？为啥要放到这里？？？？？ 看代码 FormItem上已经设置上了对应的GoodsItem
     * 在医院项目上 productInfos 将不会返回
     */
    @ApiModelProperty("进价加成，每个formItem对应的goods信息都不一样了？")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<GoodsItem> productInfos = new ArrayList<>();

    @ApiModelProperty("记账护士id")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String chargeNurseId;
    @ApiModelProperty("记账护士名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String chargeNurseName;
    /**
     * 领药单状态，用于展示未申请状态
     * {@link DispenseConst.DispensingOrderStatus}
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer dispensingOrderStatus;
    /**
     * 发药方式；0：手动，10：自动 {@link DispenseConst.DispenseMethod}
     */
    private int dispensingMethod;

    @ApiModelProperty("医嘱信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private AdviceSimpleView adviceView;

    /**
     * 需要退药标识,住院申请退药时使用
     * 长期医嘱，医嘱任务删除，但发药单已发，在退药时，这个药不好找，所以加一个标识来区分
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer needUnDispenseFlag;

    /**
     * 是否可以重报追溯码
     */
    private int canReReportTraceCode;
}
