package cn.abcyun.cis.dispensing.service;

import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisScGoodsFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.*;
import cn.abcyun.cis.commons.exception.FeignRuntimeException;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.dispensing.base.DispensingConstants;
import cn.abcyun.cis.dispensing.domain.DispensingFormItem;
import cn.abcyun.cis.dispensing.domain.DispensingFormItemAbstract;
import cn.abcyun.cis.dispensing.domain.DispensingSheet;
import cn.abcyun.cis.dispensing.service.rpc.ScGoodsFeignClient;
import cn.abcyun.cis.dispensing.util.DispensingUtils;
import cn.abcyun.cis.dispensing.util.JsonUtils;
import cn.abcyun.cis.dispensing.util.LogUtils;
import cn.abcyun.cis.dispensing.util.MathUtils;
import cn.abcyun.common.model.AbcServiceResponseBody;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Goods RPC迁移，目前只迁移queryGoodsByIds接口
 */
@Service
public class AbcCisScGoodsService {
    private static final Logger sLogger = LoggerFactory.getLogger(AbcCisScGoodsService.class);

    @Autowired
    private AbcCisScGoodsFeignClient abcCisScGoodsFeignClient;

    @Autowired
    private ScGoodsFeignClient scGoodsFeignClient;
    @Autowired
    private DispensingSheetOperationService dispensingSheetOperationService;

    /**
     * 已作废，统一迁移到{@link cn.abcyun.cis.dispensing.service.rpc.ScGoodsFeignClient#queryGoodsInPharmacyByIds(String, String, boolean, int,
     * List)}
     *
     * @param clinicId
     * @param ids
     * @return
     * @throws ServiceInternalException
     */
    @Deprecated
    @Retryable(value = {ServiceInternalException.class}, maxAttempts = 2)
    public List<GoodsItem> findGoodsItemBatch(String clinicId, List<String> ids) throws ServiceInternalException {
        List<GoodsItem> goodsItems = new ArrayList<GoodsItem>();
        if (CollectionUtils.isEmpty(ids)) {
            return goodsItems;
        }
        QueryGoodsByIdsReq goodsListReq = new QueryGoodsByIdsReq();
        goodsListReq.setGoodsIds(ids);
        goodsListReq.setClinicId(clinicId);
        goodsListReq.setWithStock(true);
        goodsListReq.setWithDeleted(1);

        try {
            long startRequestTime = System.currentTimeMillis();
            AbcServiceResponseBody<QueryGoodsByIdsRsp> rspBody = abcCisScGoodsFeignClient.queryGoodsByIds(goodsListReq);
            if (rspBody != null && rspBody.getData() != null && rspBody.getData().getList() != null) {
                LogUtils.infoObjectToJson(sLogger, "AbcCisScGoodsService findGoodsListByIds", rspBody.getData().getList());
                return rspBody.getData().getList().stream().filter(Objects::nonNull).collect(Collectors.toList());
            }
        } catch (Exception e) {
            sLogger.error("AbcCisScGoodsService findGoodsItemBatch error", e);
            throw new ServiceInternalException("findGoodsListByIds error");
        }
        return goodsItems;
    }


    /**
     * move  from OldHisService 发药
     *
     * @param clinicId                                    门店 Id
     * @param operatorId                                  发药员Id
     * @param patientOrderId                              patientOrderId
     * @param dispenseSheetId                             发药单Id
     * @param goodsDispenseDataItems                      发药请求
     * @param dispenseItemIdToDispenseItemAbstract        发药项ID->发药项（非客户端请求）
     * @param dispenseItemIdToHistoryDispenseStockDealIds 发药项ID -> 历史发药 stockDealId 列表
     */
    public List<GoodsDispenseResultItem> dispense(
            String clinicId,
            String chainId,
            String operatorId,
            String patientOrderNo,
            String patientOrderId,
            String dispenseSheetId,
            int pharmacyNo,
            int pharmacyType,
            List<GoodsDispenseDataItem> goodsDispenseDataItems,
            Map<String, DispensingFormItemAbstract> dispenseItemIdToDispenseItemAbstract,
            Map<String, List<String>> dispenseItemIdToHistoryDispenseStockDealIds,
            int scene) throws ServiceInternalException {
        List<GoodsDispenseResultItem> goodsDispenseResultItems = rpcGoodsDispense(clinicId, chainId, operatorId, patientOrderNo, patientOrderId, dispenseSheetId, pharmacyNo, pharmacyType, goodsDispenseDataItems, false, scene);
        filterHistoryDispenseStockDeal(goodsDispenseResultItems, dispenseItemIdToHistoryDispenseStockDealIds);
        mergeGoodsDispenseResultItemsBatchInfoList(goodsDispenseResultItems, dispenseItemIdToDispenseItemAbstract);
        sLogger.info("goodsDispense merged result:{}", JsonUtils.dump(goodsDispenseResultItems));
        return goodsDispenseResultItems;
    }

    /**
     * 过滤掉当前重新发药之前的发药 stockDeal，只保留当次发药单的 stockDeal ，不让后续的操作关注重新发药这个行为
     */
    private void filterHistoryDispenseStockDeal(List<GoodsDispenseResultItem> goodsDispenseResultItems,
                                                Map<String, List<String>> dispenseItemIdToHistoryDispenseStockDealIds) {
        if (CollectionUtils.isEmpty(goodsDispenseResultItems) || CollectionUtils.isEmpty(dispenseItemIdToHistoryDispenseStockDealIds)) {
            return;
        }

        goodsDispenseResultItems.forEach(goodsDispenseResultItem -> {
            if (goodsDispenseResultItem == null) {
                return;
            }
            String dispenseItemId = goodsDispenseResultItem.getDispenseItemId();
            List<String> historyDispenseStockDealIds = dispenseItemIdToHistoryDispenseStockDealIds.get(dispenseItemId);
            if (CollectionUtils.isEmpty(historyDispenseStockDealIds)) {
                return;
            }

            List<GoodsDispenseResultItem.DispenseBatchInfo> dispenseBatchInfoList = goodsDispenseResultItem.getDispenseBatchInfoList();
            if (CollectionUtils.isEmpty(dispenseBatchInfoList)) {
                return;
            }

            List<GoodsDispenseResultItem.DispenseBatchInfo> currentDispenseBatchInfoList = dispenseBatchInfoList.stream()
                    .filter(dispenseBatchInfo -> !historyDispenseStockDealIds.contains(dispenseBatchInfo.getDealId())).collect(Collectors.toList());
            goodsDispenseResultItem.setDispenseBatchInfoList(currentDispenseBatchInfoList);
        });
    }

    private void mergeGoodsDispenseResultItemsBatchInfoList(List<GoodsDispenseResultItem> goodsDispenseResultItems,
                                                            Map<String, DispensingFormItemAbstract> dispenseItemIdToDispenseItemAbstract) {
        if (CollectionUtils.isEmpty(goodsDispenseResultItems)) {
            return;
        }

        goodsDispenseResultItems.forEach(dispenseResultItem -> mergeGoodDispenseResultItemBatchInfoList(dispenseResultItem, dispenseItemIdToDispenseItemAbstract.get(dispenseResultItem.getDispenseItemId())));
    }

    /**
     * 合并发药结果批次信息，根据是否拆零做单位转换
     * 同一个 dealId 可能会有多个，但是 dispensing 处并不关心 goods 批次的概念，所以这里先进行一次合并，把同一个 dealId 的合并为一条
     *
     * @param goodsDispenseResultItem  formItem 发药结果
     * @param dispenseFormItemAbstract 发药项摘要信息
     */
    private void mergeGoodDispenseResultItemBatchInfoList(GoodsDispenseResultItem goodsDispenseResultItem,
                                                          DispensingFormItemAbstract dispenseFormItemAbstract) {
        if (goodsDispenseResultItem == null) {
            return;
        }

        List<GoodsDispenseResultItem.DispenseBatchInfo> allDispenseBatchInfoList = goodsDispenseResultItem.getDispenseBatchInfoList();
        if (CollectionUtils.isEmpty(allDispenseBatchInfoList)) {
            return;
        }
        goodsDispenseResultItem.setOriginalDispenseBatchInfoList(allDispenseBatchInfoList);

        Map<DispenseBatchIdx, List<GoodsDispenseResultItem.DispenseBatchInfo>> dealIdToDispenseBatchInfoList = ListUtils.groupByKey(allDispenseBatchInfoList,
                batchInfo -> new DispenseBatchIdx(batchInfo.getDealId(), batchInfo.getOriginalDealId()));
        List<GoodsDispenseResultItem.DispenseBatchInfo> mergedDispenseBatchInfoList = new ArrayList<>(dealIdToDispenseBatchInfoList.size());
        dealIdToDispenseBatchInfoList.forEach((dispenseBatchIdx, dispenseBatchInfoList) -> {
            if (CollectionUtils.isEmpty(dispenseBatchInfoList)) {
                return;
            }

            // 同一个发药操作各批次发药数量合并
            GoodsDispenseResultItem.DispenseBatchInfo mergedDispenseBatchInfo = new GoodsDispenseResultItem.DispenseBatchInfo();
            GoodsDispenseResultItem.DispenseBatchInfo firstDispenseBatchInfo = dispenseBatchInfoList.get(0);
            BeanUtils.copyProperties(firstDispenseBatchInfo, mergedDispenseBatchInfo);
            dispenseBatchInfoList.stream().skip(1).forEach(dispenseBatchInfo -> {
                if (dispenseBatchInfo.getClientReqOriginFlatPackageCount() != null) {
                    mergedDispenseBatchInfo.setClientReqOriginFlatPackageCount(MathUtils.wrapBigDecimalAdd(mergedDispenseBatchInfo.getClientReqOriginFlatPackageCount(), dispenseBatchInfo.getClientReqOriginFlatPackageCount()));
                }
                if (dispenseBatchInfo.getClientReqOriginFlatPieceCount() != null) {
                    mergedDispenseBatchInfo.setClientReqOriginFlatPieceCount(MathUtils.wrapBigDecimalAdd(mergedDispenseBatchInfo.getClientReqOriginFlatPieceCount(), dispenseBatchInfo.getClientReqOriginFlatPieceCount()));
                }
                mergedDispenseBatchInfo.setPieceCount(MathUtils.wrapBigDecimalAdd(mergedDispenseBatchInfo.getPieceCount(), dispenseBatchInfo.getPieceCount()));
                mergedDispenseBatchInfo.setPackageCount(MathUtils.wrapBigDecimalAdd(mergedDispenseBatchInfo.getPackageCount(), dispenseBatchInfo.getPackageCount()));
                mergedDispenseBatchInfo.setPackageCostPrice(MathUtils.wrapBigDecimalAdd(mergedDispenseBatchInfo.getPackageCostPrice(), dispenseBatchInfo.getPackageCostPrice()));
            });

            // 单位转换
            if (dispenseFormItemAbstract.getUseDismounting() == 1) {
                // 拆零出售
                BigDecimal pieceNum = mergedDispenseBatchInfo.getPieceNum() != null ? new BigDecimal(mergedDispenseBatchInfo.getPieceNum()) : BigDecimal.ZERO;
                // 将发的大单位转为小单位后小单位数量
                BigDecimal packageToPieceCount = cn.abcyun.cis.commons.util.MathUtils.wrapBigDecimalMultiply(mergedDispenseBatchInfo.getPackageCount(), pieceNum);
                mergedDispenseBatchInfo.setPieceCount(MathUtils.wrapBigDecimalAdd(mergedDispenseBatchInfo.getPieceCount(), packageToPieceCount));
                mergedDispenseBatchInfo.setPackageCount(BigDecimal.ZERO);
            } else {
                BigDecimal packageCount = mergedDispenseBatchInfo.getPackageCount();
                BigDecimal pieceCount = mergedDispenseBatchInfo.getPieceCount();
                if (MathUtils.wrapBigDecimalCompare(pieceCount, BigDecimal.ZERO) > 0) {
                    /*
                       如果有发小包装需要将小包装转换为大包装
                       例如发 100片/瓶的商品，发一瓶，然后刚好有一个批次剩下 99 片，另一个批次是 1 片，那么库存那边实际上返回的是 100 片，而不是 1 瓶。
                       虽然是发的 2 盒，但是退药的时候可能会退 1.8 盒，因为后面的同步逻辑需要使用 packageCount，所以必须要把小单位转成大单位，但是可能会存在精度问题
                     */
                    BigDecimal pieceNum = new BigDecimal(mergedDispenseBatchInfo.getPieceNum());
                    packageCount = MathUtils.wrapBigDecimalAdd(packageCount, pieceCount.divide(pieceNum, 4, RoundingMode.DOWN));
                }
                mergedDispenseBatchInfo.setPackageCount(packageCount);
                mergedDispenseBatchInfo.setPieceCount(BigDecimal.ZERO);
            }

            mergedDispenseBatchInfoList.add(mergedDispenseBatchInfo);
        });
        goodsDispenseResultItem.setDispenseBatchInfoList(mergedDispenseBatchInfoList);
    }

    /**
     * move  from OldHisService 退
     *
     * @param clinicId                                 门店 Id
     * @param operatorId                               发药员Id
     * @param patientOrderId                           patientOrderId
     * @param dispenseSheetId                          发药单Id
     * @param pharmacyNo                               要放好
     * @param goodsDispenseDataItems                   发药请求
     * @param dispensingFormItemIdToDispensingFormItem
     */
    public List<GoodsDispenseResultItem> undispense(
            String clinicId,
            String chainId,
            String operatorId,
            String patientOrderNo,
            String patientOrderId,
            String dispenseSheetId,
            int pharmacyNo,
            int pharmacyType,
            List<GoodsDispenseDataItem> goodsDispenseDataItems,
            Map<String, DispensingFormItemAbstract> dispensingFormItemIdToDispensingFormItem,
            int scene) throws ServiceInternalException {
        List<GoodsDispenseResultItem> goodsDispenseResultItems = rpcGoodsDispense(clinicId, chainId, operatorId, patientOrderNo, patientOrderId, dispenseSheetId, pharmacyNo, pharmacyType, goodsDispenseDataItems, true, scene);
        mergeGoodsDispenseResultItemsBatchInfoList(goodsDispenseResultItems, dispensingFormItemIdToDispensingFormItem);
        return goodsDispenseResultItems;
    }

    /**
     * 退回所有追溯码
     */
    public void unUseAllTraceCode(DispensingSheet dispensingSheet, Map<String, List<TraceableCode>> dispensingFormItemIdToCleanTraceableCodeList, String operatorId) {
        if (dispensingSheet == null || CollectionUtils.isEmpty(dispensingFormItemIdToCleanTraceableCodeList)) {
            return;
        }

        List<DispensingFormItem> dispensingFormItems = DispensingUtils.getDispensingFormItems(dispensingSheet);
        Map<String, DispensingFormItem> dispensingFormItemIdToDispensingFormItem = ListUtils.toMap(dispensingFormItems, DispensingFormItem::getId);
        List<GoodsUseTraceCodeReq.GoodsUseTraceCodeItem> goodsUseTraceCodeItemList = new ArrayList<>();
        dispensingFormItemIdToCleanTraceableCodeList.forEach((dispensingFormItemId, traceableCodeList) -> {
            if (CollectionUtils.isEmpty(traceableCodeList)) {
                return;
            }

            DispensingFormItem dispensingFormItem = dispensingFormItemIdToDispensingFormItem.get(dispensingFormItemId);
            if (dispensingFormItem == null) {
                return;
            }


            for (TraceableCode traceableCode : traceableCodeList) {
                if (traceableCode.getType() != null && traceableCode.getType() == GoodsConst.DrugIdentificationCodeType.NO_CODE) {
                    continue;
                }

                GoodsUseTraceCodeReq.GoodsUseTraceCodeItem goodsUseTraceCodeItem = new GoodsUseTraceCodeReq.GoodsUseTraceCodeItem();
                goodsUseTraceCodeItem.setNo(traceableCode.getNo());
                goodsUseTraceCodeItem.setGoodsId(dispensingFormItem.getProductId());
                goodsUseTraceCodeItem.setPharmacyType(dispensingFormItem.getPharmacyType());
                goodsUseTraceCodeItem.setPharmacyNo(dispensingFormItem.getPharmacyNo());
                goodsUseTraceCodeItem.setChangePackageCount(BigDecimal.ZERO);
                goodsUseTraceCodeItem.setChangePieceCount(BigDecimal.ZERO);
                goodsUseTraceCodeItem.setOptType(ScGoodsGoodsLockReq.DEL); // 更新
                goodsUseTraceCodeItem.setKeyId(dispensingFormItemId); // 设置关联ID
                goodsUseTraceCodeItemList.add(goodsUseTraceCodeItem);
            }
        });

        GoodsUseTraceCodeReq goodsUseTraceCodeReq = new GoodsUseTraceCodeReq();
        goodsUseTraceCodeReq.setChainId(dispensingSheet.getChainId());
        goodsUseTraceCodeReq.setClinicId(dispensingSheet.getClinicId());
        goodsUseTraceCodeReq.setEmployeeId(operatorId);
        goodsUseTraceCodeReq.setPatientOrderId(dispensingSheet.getPatientOrderId());
        goodsUseTraceCodeReq.setList(goodsUseTraceCodeItemList);
        scGoodsFeignClient.traceCodeUse(goodsUseTraceCodeReq);
    }

    private List<GoodsDispenseResultItem> rpcGoodsDispense(
            String clinicId,
            String chainId,
            String operatorId,
            String patientOrderNo,
            String patientOrderId,
            String dispenseSheetId,
            int pharmacyNo,
            int pharmacyType,
            List<GoodsDispenseDataItem> goodsDispenseDataItems,
            boolean isUndispense,
            int scene) throws ServiceInternalException {
        GoodsDispenseReq goodsDispenseReq = new GoodsDispenseReq();
        goodsDispenseReq.setDispenseSheetId(dispenseSheetId);
        goodsDispenseReq.setList(goodsDispenseDataItems);
        goodsDispenseReq.setPatientOrderNo(patientOrderNo);
        goodsDispenseReq.setPatientOrderId(patientOrderId);
        goodsDispenseReq.setClinicId(clinicId);
        goodsDispenseReq.setChainId(chainId);
        goodsDispenseReq.setOperatorId(operatorId);
        goodsDispenseReq.setPharmacyNo(pharmacyNo);
        goodsDispenseReq.setPharmacyType(pharmacyType);
        goodsDispenseReq.setScene(scene);

        List<GoodsDispenseResultItem> goodsDispenseResultItems = null;
        try {
            long startRequestTime = System.currentTimeMillis();
            AbcServiceResponseBody<GoodsDispenseRsp> rspBody = isUndispense ? abcCisScGoodsFeignClient.goodsUnDispense(goodsDispenseReq) : abcCisScGoodsFeignClient.goodsDispense(goodsDispenseReq);
            sLogger.info("rpc cost time:goodsDispenseReq: {},{}ms", JsonUtils.dump(goodsDispenseReq), (System.currentTimeMillis() - startRequestTime));
            if (rspBody != null) {
                GoodsDispenseRsp goodsDispenseRsp = rspBody.getData();
                if (goodsDispenseRsp != null && goodsDispenseRsp.getDispenseRstList() != null) {
                    goodsDispenseResultItems = goodsDispenseRsp.getDispenseRstList().stream().filter(Objects::nonNull).collect(Collectors.toList());
                }
            }
        } catch (FeignRuntimeException e) {
            sLogger.error("goodsDispense feign error, req:{}", JsonUtils.dump(goodsDispenseReq), e);
            throw e;
        } catch (Exception e) {
            sLogger.error("goodsDispense error, req:{}", JsonUtils.dump(goodsDispenseReq), e);
            throw new ServiceInternalException("goodsDispense error");
        }
        return goodsDispenseResultItems;
    }

    /**
     * 更新使用追溯码
     *
     * @param dispensingSheets 发药单
     */
    public void updateUseTraceCode(String chainId, String clinicId, List<DispensingSheet> dispensingSheets, String employeeId) {
        if (CollectionUtils.isEmpty(dispensingSheets)) {
            return;
        }


        GoodsUseTraceCodeReq goodsUseTraceCodeReq = new GoodsUseTraceCodeReq();
        goodsUseTraceCodeReq.setChainId(chainId);
        goodsUseTraceCodeReq.setClinicId(clinicId);
        goodsUseTraceCodeReq.setEmployeeId(employeeId);
        goodsUseTraceCodeReq.setPatientOrderId(dispensingSheets.get(0).getPatientOrderId());
        goodsUseTraceCodeReq.setMidstreamSheetId(dispensingSheets.get(0).getSourceSheetId());
        Map<String, GoodsUseTraceCodeReq.GoodsUseTraceCodeItem> keyIdToGoodsUseTraceCodeItem = new HashMap<>();
        DispensingUtils.doWithDispensingItem(dispensingSheets, item -> {
            if (CollectionUtils.isEmpty(item.getTraceableCodeList()) || StringUtils.hasText(item.getAssociateFormItemId())) {
                return;
            }

            for (TraceableCode traceableCode : item.getTraceableCodeList()) {
                if (Objects.equals(traceableCode.getType(), GoodsConst.DrugIdentificationCodeType.NO_CODE) || !StringUtils.hasText(traceableCode.getNo())) {
                    continue;
                }
                String keyId = item.getId() + "_" + traceableCode.getNo();
                GoodsUseTraceCodeReq.GoodsUseTraceCodeItem goodsUseTraceCodeItem = keyIdToGoodsUseTraceCodeItem.computeIfAbsent(keyId, k -> {
                    GoodsUseTraceCodeReq.GoodsUseTraceCodeItem newGoodsUseTraceCodeItem = new GoodsUseTraceCodeReq.GoodsUseTraceCodeItem();
                    newGoodsUseTraceCodeItem.setKeyId(keyId);
                    newGoodsUseTraceCodeItem.setNo(traceableCode.getNo());
                    newGoodsUseTraceCodeItem.setDismountingSn(traceableCode.getDismountingSn());
                    newGoodsUseTraceCodeItem.setGoodsId(item.getProductId());
                    newGoodsUseTraceCodeItem.setPharmacyType(item.getPharmacyType());
                    newGoodsUseTraceCodeItem.setPharmacyNo(item.getPharmacyNo());
                    newGoodsUseTraceCodeItem.setChangePackageCount(BigDecimal.ZERO);
                    newGoodsUseTraceCodeItem.setChangePieceCount(BigDecimal.ZERO);
                    newGoodsUseTraceCodeItem.setDownstreamSheetId(item.getDispensingSheetId());
                    newGoodsUseTraceCodeItem.setDownstreamFormItemId(item.getId());
                    newGoodsUseTraceCodeItem.setMidstreamFormItemId(item.getSourceFormItemId());
                    newGoodsUseTraceCodeItem.setChangePieceCount(BigDecimal.ZERO);
                    newGoodsUseTraceCodeItem.setChangePackageCount(BigDecimal.ZERO);
                    return newGoodsUseTraceCodeItem;
                });

                BigDecimal changePieceCount = goodsUseTraceCodeItem.getChangePieceCount();
                BigDecimal changePackageCount = goodsUseTraceCodeItem.getChangePackageCount();
                if (traceableCode.getUsed() == DispensingConstants.TraceableCodeUsed.UNDISPENSE) {
                    changePieceCount = MathUtils.wrapBigDecimalAdd(changePieceCount, MathUtils.wrapAbs(traceableCode.getHisPieceCount()).negate());
                    changePackageCount = MathUtils.wrapBigDecimalAdd(changePackageCount, MathUtils.wrapAbs(traceableCode.getHisPackageCount()).negate());
                } else {
                    changePieceCount = MathUtils.wrapBigDecimalAdd(changePieceCount, MathUtils.wrapAbs(traceableCode.getHisPieceCount()));
                    changePackageCount = MathUtils.wrapBigDecimalAdd(changePackageCount, MathUtils.wrapAbs(traceableCode.getHisPackageCount()));
                }

                goodsUseTraceCodeItem.setChangePieceCount(changePieceCount);
                goodsUseTraceCodeItem.setChangePackageCount(changePackageCount);
            }
        });
        goodsUseTraceCodeReq.setList(new ArrayList<>(keyIdToGoodsUseTraceCodeItem.values()));
        scGoodsFeignClient.traceCodeUse(goodsUseTraceCodeReq);
    }


    public void updateReReportTraceCode(String chainId, String clinicId, List<DispensingSheet> dispensingSheets, String employeeId,
                                        List<DispensingFormItem> updateItems, Map<String, List<String>> dispenseItemToStockDealIds) {
        if (CollectionUtils.isEmpty(updateItems) || CollectionUtils.isEmpty(dispenseItemToStockDealIds) || CollectionUtils.isEmpty(dispensingSheets)) {
            return;
        }


        List<GoodsReReportTraceCodeReq.GoodsReReportTraceCodeItem> goodsReReportTraceCodeItemList = new ArrayList<>();

        String patientOrderId = dispensingSheets.get(0).getPatientOrderId();
        String chargeSheetId = dispensingSheets.get(0).getSourceSheetId();

        updateItems.forEach(item -> {
            if (StringUtils.hasText(item.getAssociateFormItemId())) {
                return;
            }

            List<String> currentStockDealIds = dispenseItemToStockDealIds.get(item.getId());
            if (CollectionUtils.isEmpty(currentStockDealIds)) {
                return;
            }


            GoodsReReportTraceCodeReq.GoodsReReportTraceCodeItem newGoodsReReportTraceCodeItem = new GoodsReReportTraceCodeReq.GoodsReReportTraceCodeItem();
            newGoodsReReportTraceCodeItem.setDownstreamSheetId(item.getDispensingSheetId());
            newGoodsReReportTraceCodeItem.setDownstreamFormItemId(item.getId());
            newGoodsReReportTraceCodeItem.setMidstreamFormItemId(item.getSourceFormItemId());
            newGoodsReReportTraceCodeItem.setGoodsId(item.getProductId());
            newGoodsReReportTraceCodeItem.setPharmacyType(item.getPharmacyType());
            newGoodsReReportTraceCodeItem.setPharmacyNo(item.getPharmacyNo());
            newGoodsReReportTraceCodeItem.setBatIds(currentStockDealIds);
            newGoodsReReportTraceCodeItem.setShebaoDismountingFlag(item.getShebaoDismountingFlag());
            if (!CollectionUtils.isEmpty(item.getDispensingFormItemBatches())) {
                // 如果不为空就认为是强锁批次
                newGoodsReReportTraceCodeItem.setIsForceLockBatch(1);
            }

            if (!CollectionUtils.isEmpty(item.getTraceableCodeList())) {
                Map<String, GoodsReReportTraceCodeReq.GoodsUseTraceCodeItem> keyIdToGoodsUseTraceCodeItem = new HashMap<>();
                for (TraceableCode traceableCode : item.getTraceableCodeList()) {
                    if (Objects.equals(traceableCode.getType(), GoodsConst.DrugIdentificationCodeType.NO_CODE) || !StringUtils.hasText(traceableCode.getNo())) {
                        // TODO 这里没有传无码，不确定有没有问题
                        continue;
                    }
                    String keyId = item.getId() + "_" + traceableCode.getNo();
                    GoodsReReportTraceCodeReq.GoodsUseTraceCodeItem goodsUseTraceCodeItem = keyIdToGoodsUseTraceCodeItem.computeIfAbsent(keyId, k -> {
                        GoodsReReportTraceCodeReq.GoodsUseTraceCodeItem newGoodsUseTraceCodeItem = new GoodsReReportTraceCodeReq.GoodsUseTraceCodeItem();
                        newGoodsUseTraceCodeItem.setKeyId(keyId);
                        newGoodsUseTraceCodeItem.setNo(traceableCode.getNo());
                        newGoodsUseTraceCodeItem.setDismountingSn(traceableCode.getDismountingSn());
                        newGoodsUseTraceCodeItem.setChangePackageCount(BigDecimal.ZERO);
                        newGoodsUseTraceCodeItem.setChangePieceCount(BigDecimal.ZERO);
                        newGoodsUseTraceCodeItem.setChangePieceCount(BigDecimal.ZERO);
                        newGoodsUseTraceCodeItem.setChangePackageCount(BigDecimal.ZERO);
                        return newGoodsUseTraceCodeItem;
                    });

                    BigDecimal changePieceCount = goodsUseTraceCodeItem.getChangePieceCount();
                    BigDecimal changePackageCount = goodsUseTraceCodeItem.getChangePackageCount();
                    if (traceableCode.getUsed() == DispensingConstants.TraceableCodeUsed.UNDISPENSE) {
                        changePieceCount = MathUtils.wrapBigDecimalAdd(changePieceCount, MathUtils.wrapAbs(traceableCode.getHisPieceCount()).negate());
                        changePackageCount = MathUtils.wrapBigDecimalAdd(changePackageCount, MathUtils.wrapAbs(traceableCode.getHisPackageCount()).negate());
                    } else {
                        changePieceCount = MathUtils.wrapBigDecimalAdd(changePieceCount, MathUtils.wrapAbs(traceableCode.getHisPieceCount()));
                        changePackageCount = MathUtils.wrapBigDecimalAdd(changePackageCount, MathUtils.wrapAbs(traceableCode.getHisPackageCount()));
                    }

                    goodsUseTraceCodeItem.setChangePieceCount(changePieceCount);
                    goodsUseTraceCodeItem.setChangePackageCount(changePackageCount);
                }

                newGoodsReReportTraceCodeItem.setTraceableCodeList(new ArrayList<>(keyIdToGoodsUseTraceCodeItem.values()));
            }

            goodsReReportTraceCodeItemList.add(newGoodsReReportTraceCodeItem);
        });

        if (CollectionUtils.isEmpty(goodsReReportTraceCodeItemList)) {
            sLogger.warn("updateReReportTraceCode goodsReReportTraceCodeItemList is empty");
            return;
        }

        GoodsReReportTraceCodeReq goodsReReportTraceCodeReq = new GoodsReReportTraceCodeReq();
        goodsReReportTraceCodeReq.setChainId(chainId);
        goodsReReportTraceCodeReq.setClinicId(clinicId);
        goodsReReportTraceCodeReq.setEmployeeId(employeeId);
        goodsReReportTraceCodeReq.setPatientOrderId(patientOrderId);
        goodsReReportTraceCodeReq.setMidstreamSheetId(chargeSheetId);
        goodsReReportTraceCodeReq.setList(goodsReReportTraceCodeItemList);

        scGoodsFeignClient.reReportTraceCodeUse(goodsReReportTraceCodeReq);

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class DispenseBatchIdx {
        private String dealId;
        private String originDealId;
    }

}
