package cn.abcyun.cis.dispensing.service;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.UpdateTraceableCodeReq;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.UpdateTraceableCodeRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.*;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.OpsCommonRsp;
import cn.abcyun.bis.rpc.sdk.his.message.advice.*;
import cn.abcyun.bis.rpc.sdk.his.model.advice.AdviceStatus;
import cn.abcyun.bis.rpc.sdk.his.model.charge.HisChargeFormItemView;
import cn.abcyun.bis.rpc.sdk.his.model.charge.HisChargeSheetView;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import cn.abcyun.cis.core.redis.RedisLock;
import cn.abcyun.cis.dispensing.AutoDispenseReq;
import cn.abcyun.cis.dispensing.api.protocol.HospitalDispensingOrderPrescriptionPrintReq;
import cn.abcyun.cis.dispensing.api.protocol.HospitalDispensingOrderPrescriptionPrintRsp;
import cn.abcyun.cis.dispensing.api.protocol.SaveTraceableCodeDraftReq;
import cn.abcyun.cis.dispensing.api.protocol.order.*;
import cn.abcyun.cis.dispensing.api.protocol.print.DispensingPrintLogReq;
import cn.abcyun.cis.dispensing.base.exception.DispenseSheetChangedException;
import cn.abcyun.cis.dispensing.base.exception.DispensingServiceError;
import cn.abcyun.cis.dispensing.base.exception.DispensingServiceException;
import cn.abcyun.cis.dispensing.domain.DispensingOrder;
import cn.abcyun.cis.dispensing.domain.DispensingSheetV2;
import cn.abcyun.cis.dispensing.mybatis.mapper.DispensingMapper;
import cn.abcyun.cis.dispensing.service.dto.DispensingFormItemView;
import cn.abcyun.cis.dispensing.service.dto.DispensingSheetView;
import cn.abcyun.cis.dispensing.service.dto.print.DispensingSheetAstResultView;
import cn.abcyun.common.model.AbcListPage;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * dispensing统一分发service
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-31 20:36:34
 */
@Service
@Slf4j
public class DispensingServiceV2 {

    @Autowired
    private DispensingOrderService dispensingOrderService;
    @Autowired
    private DispensingSheetService dispensingSheetService;
    @Autowired
    private DispensingPrintedService dispensingPrintedService;
    @Autowired
    private DispensingMapper dispensingMapper;

    @Autowired
    private RedissonClient mRedissonClient;

    /**
     * 如果这个时间点的取药单还没生成出来 立即生成
     * 为了防止并发，要加锁
     * 必须快速完成，其他医嘱消息可能还在等着这个发药单
     */
    @Transactional
    @RedisLock(key = "'dispensing.order:' + #clinicId + ':' + #departmentId+ ':' + #wardAreaId+ ':' + #pharmacyNo")
    public DispensingOrder getOrCreateCurrentDispenseOrder(String chainId, String clinicId, String departmentId,
                                                           Long wardAreaId, int pharmacyType, Integer pharmacyNo,
                                                           Instant planExecuteTime) {
        return dispensingOrderService.getOrCreateCurrentDispenseOrder(chainId, clinicId, departmentId, wardAreaId,
                pharmacyType, pharmacyNo, planExecuteTime);
    }

    /**
     * 医院处理医嘱执行消息
     * <p>
     * 为什么要和医嘱规则消息 加同一把Redislock锁？
     * 1.医嘱规则的下达和这个医嘱规则的被执行 理论上存在同时
     * （或者规则先到但是处理时间长，发药单还没落地，导致医嘱执行的消息到达时 发现医嘱规则对应发药单还不存在而重复建发药单的情况）
     * 2.最完美的粒度其实是医嘱RuleId，但是目前为了方便直接锁adviceId
     * 3.如果医嘱执行比医嘱规则(总量发药)先到达先被处理 总量发药不会被记录费用？
     * 4.为什么要优先记录医嘱规则的总量发药？ 比如医嘱规则开的是 执行3次 每次用3片 ,医生开1盒(10片/盒)。总量比总执行用量要多，有浪费，但是要收用户钱
     */
    //重构目标 DispenseService作为服务分发,不做过多业务逻辑，事务和锁注解都加到这个类的方法上，类上的注解在每个方法都有注解后去掉
    @Transactional(rollbackFor = Exception.class)
    @RedisLock(key = "'dispensing.advice:' + #adviceExecuteMessage.clinicId + ':' + #adviceExecuteMessage.adviceId", waitTime = 60)
    public void createDispensingSheetFromAdviceExecuteMessage(AdviceExecuteMessage adviceExecuteMessage,
                                                              DispensingOrder curDispensingOrder,
                                                              List<AdviceRuleItemMessage> ruleItemMessageList,
                                                              boolean supplement) {
        switch (adviceExecuteMessage.getStatus()) {
            //医嘱执行 被核对 建发药单
            case AdviceStatus.CHECKED: {
                dispensingSheetService.createDispensingSheetFromAdviceExecuteMessage(adviceExecuteMessage,
                        curDispensingOrder, ruleItemMessageList, supplement);
                break;
            }
            case AdviceStatus.EXECUTED: {
                if (supplement) {
                    // 补费用医嘱任务执行状态下生成发药单，
                    dispensingSheetService.createDispensingSheetFromAdviceExecuteMessage(adviceExecuteMessage,
                            curDispensingOrder, ruleItemMessageList, true);
                }
                break;
            }
            //停止和撤销医嘱 没发药的删发药单 ，发了药的理论上不会收到这个消息
            case AdviceStatus.STOPPED:
            case AdviceStatus.UNDONE:
                if (supplement) {
                    // 补费用医嘱任务撤销状态下也生成发药单，
                    dispensingSheetService.createDispensingSheetFromAdviceExecuteMessage(adviceExecuteMessage,
                            curDispensingOrder, ruleItemMessageList, true);
                } else {
                    dispensingSheetService.closeDispensingSheetFromAdviceMessage(adviceExecuteMessage, null/*no use*/);
                }
                break;
        }
    }

    /**
     * 医院处理医嘱规则消息
     * <p>
     * 为什么要和医嘱规则消息 加同一把Redislock锁？
     * 1.医嘱规则的下达和这个医嘱规则的被执行 理论上存在同时
     * （或者规则先到但是处理时间长，发药单还没落地，导致医嘱执行的消息到达时 发现医嘱规则对应发药单还不存在而重复建发药单的情况）
     * 2.最完美的粒度其实是医嘱RuleId，但是目前为了方便直接锁adviceId
     * 3.如果医嘱执行比医嘱规则(总量发药)先到达先被处理 总量发药不会被记录费用？
     * 4.为什么要优先记录医嘱规则的总量发药？ 比如医嘱规则开的是 执行3次 每次用3片 ,医生开1盒(10片/盒)。总量比总执行用量要多，有浪费，但是要收用户钱
     */
    //重构目标 DispenseService作为服务分发,不做过多业务逻辑，事务和锁注解都加到这个类的方法上，类上的注解在每个方法都有注解后去掉
    @Transactional(rollbackFor = Exception.class)
    @RedisLock(key = "'dispensing.advice:' + #adviceMessage.clinicId + ':' + #adviceMessage.id", waitTime = 60)
    public void createDispensingSheetFromAdviceMessage(AdviceMessage adviceMessage, DispensingOrder curDispensingOrder) {
        switch (adviceMessage.getStatus()) {
            //核对医嘱 建发药单
            case AdviceStatus.CHECKED: {
                dispensingSheetService.createDispensingSheetFromAdviceMessage(adviceMessage, curDispensingOrder);
                break;
            }
            //停止和撤销医嘱 没发药的删发药单 ，发了药的理论上不会收到这个消息
            case AdviceStatus.STOPPED:
            case AdviceStatus.UNDONE:
                dispensingSheetService.closeDispensingSheetFromAdviceMessage(adviceMessage, null/*no use*/);
                break;
        }
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public DispensingSheetAstResultView getDispensingSheetAstResult(String dispensingSheetId, String clinicId) {
        return dispensingSheetService.getDispensingSheetAstResult(dispensingSheetId, clinicId);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<HospitalDispensingOrderPrescriptionPrintRsp> getHospitalPrescriptionSheetViewList(HospitalDispensingOrderPrescriptionPrintReq clientReq) {
        AbcListPage<HospitalDispensingOrderPrescriptionPrintRsp> result = new AbcListPage<>();
        result.setRows(new ArrayList<>());

        HospitalDispensingOrderPrescriptionBatchPrintReq req = new HospitalDispensingOrderPrescriptionBatchPrintReq();
        req.setTab(clientReq.getTab());
        req.setChainId(clientReq.getChainId());
        req.setClinicId(clientReq.getClinicId());
        req.setDispensingOrderList(Collections.singletonList(new HospitalDispensingOrderBatchBase().setDispensingOrderId(clientReq.getDispensingOrderId()).setDispensingSheetIdList(clientReq.getDispensingSheetIdList())));

        dispensingOrderService.getHospitalPrescriptionSheetViewList(req, result.getRows());
        return result;
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<HospitalDispensingOrderPrescriptionPrintRsp> batchGetHospitalPrescriptionSheetViewList(HospitalDispensingOrderPrescriptionBatchPrintReq clientReq) {
        AbcListPage<HospitalDispensingOrderPrescriptionPrintRsp> result = new AbcListPage<>();
        result.setRows(new ArrayList<>());
        dispensingOrderService.getHospitalPrescriptionSheetViewList(clientReq, result.getRows());
        return result;
    }

    @Async
    public void autoDispenseDispensingSheet(List<DispensingSheetV2> autoDispenseSheetList, String operatorId) {
        if (CollectionUtils.isEmpty(autoDispenseSheetList)) {
            return;
        }
        for (DispensingSheetV2 sheet : autoDispenseSheetList) {
            try {
                log.info("autoDispenseDispensingSheet自动发药单, sheet={}", JsonUtils.dump(sheet));
                dispensingOrderService.autoDispenseSheet(sheet, operatorId);
            } catch (Exception e) {
                log.error("autoDispenseDispensingSheet自动发药单失败", e);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @RedisLock(key = "'dispensing.his.charge:' + #chargeSheet.clinicId + ':' + #chargeSheet.id", waitTime = 60)
    public void createDispensingSheetFromHisChargeSheetMessage(HisChargeSheetView chargeSheet,
                                                               List<HisChargeFormItemView> chargeItems,
                                                               DispensingOrder dispensingOrder,
                                                               String operatorId) {
        dispensingSheetService.createDispensingSheetFromHisChargeSheetMessage(chargeSheet, chargeItems, dispensingOrder, operatorId);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<DispensingSheetView> getAutoDispensingList(GetAutoDispensingListReq clientReq) {
        return dispensingOrderService.getAutoDispensingList(clientReq);
    }

    //@Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp dispenseAutoDispensingSheet(AutoDispenseReq clientReq) {
        dispensingOrderService.dispenseAutoDispensingSheet(clientReq);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp undispenseAutoDispensingSheet(AutoDispenseReq clientReq) {
        dispensingOrderService.undispenseAutoDispensingSheet(clientReq);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<DispensingSheetView> getDispensingSheetViewBySourceType(GetDispenseSheetBySourceTypeReq clientReq) {
        return dispensingOrderService.getDispensingSheetViewBySourceType(clientReq);
    }

    @Transactional(rollbackFor = Exception.class)
    @RedisLock(key = "'dispensing.advice.usage:' + #adviceUsageRule.clinicId + ':' + #adviceUsageRule.adviceUsageRuleId", waitTime = 60)
    public void createDispensingSheetFromAdviceUsageRuleItemAddMessage(AdviceUsageRuleItemsAddMessage adviceUsageRule,
                                                                       List<AdviceUsageRuleItemMessage> ruleItemMessageList,
                                                                       DispensingOrder dispensingOrder,
                                                                       String operatorId) {
        dispensingSheetService.createDispensingSheetFromAdviceUsageRuleItemAddMessage(adviceUsageRule, ruleItemMessageList, dispensingOrder, operatorId);
    }

    @Transactional(rollbackFor = Exception.class)
    @RedisLock(key = "'dispensing.advice.reDispense:' + #adviceNeedReDispenseMessage.adviceMessage.clinicId + ':'" +
            "+ #adviceNeedReDispenseMessage.adviceMessage.id", waitTime = 60)
    public void createDispensingSheetFromAdviceNeedReDispenseMessage(AdviceNeedReDispenseMessage adviceNeedReDispenseMessage,
                                                                     AdviceExecuteNeedReDispenseMessage adviceExecuteNeedReDispenseMessage,
                                                                     List<AdviceRuleItemMessage> ruleItemMesageList,
                                                                     DispensingOrder dispensingOrder,
                                                                     String operatorId,
                                                                     int reDispenseActionType) {
        dispensingSheetService.createDispensingSheetFromAdviceNeedReDispenseMessage(adviceNeedReDispenseMessage,
                adviceExecuteNeedReDispenseMessage, ruleItemMesageList, dispensingOrder, operatorId, reDispenseActionType);
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp undispenseForHisCharge(UnDispenseForHisChargeReq clientReq) {
        dispensingOrderService.undispenseForHisCharge(clientReq);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    /**
     * 重新生成发药单，已退的发药单
     */
    public OpsCommonRsp renewDispensingOrder(SendDispenseOrderToPharmacyReq clientReq) {
        dispensingOrderService.renewDispensingOrder(clientReq);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    /**
     * 药房 发药员 发药
     * 可以指定部分药品发送
     */
    public OpsCommonRsp dispenseDispensingOrder(DispenseDispenseOrderReq dispenseOrderReq) throws ServiceInternalException {
        return dispensingOrderService.dispenseDispensingOrder(dispenseOrderReq);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AutoDispensingCount getAutoDispensingFailCount(GetAutoDispensingListReq req) {
        return dispensingOrderService.getAutoDispensingFailCount(req);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<AutoDispensingFailDailyCount> getAutoDispensingFailDailyCount(GetAutoDispensingListReq req) {
        return dispensingOrderService.getAutoDispensingFailDailyCount(req);
    }

    public OpsCommonRsp dispenseFoAdviceExecute(DispenseForAdviceExecuteReq clientReq) {
        dispensingOrderService.dispenseFoAdviceExecute(clientReq);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    public DispensingSheetView saveTraceableCode(SaveTraceableCodeDraftReq clientReq) {
        try {
            String redisKey = String.format("dispensing.id:%s:%s", clientReq.getClinicId(), clientReq.getDispensingSheetId());
            RLock lock = mRedissonClient.getLock(redisKey);
            // 尝试加锁，最多等待30秒，上锁以后15秒自动解锁
            boolean res = lock.tryLock(3, 15, TimeUnit.SECONDS);
            if (res) {
                log.info("acquired locker:{}, timestamp:{}, threadId:{}", redisKey, Instant.now(), Thread.currentThread().getId());
                try {
                    return dispensingSheetService.saveTraceableCode(clientReq);
                } finally {
                    lock.unlock();
                }
            } else {
                log.error("获取发药单的redis锁失败");
                throw new DispenseSheetChangedException();
            }
        } catch (InterruptedException e) {
            log.error("saveTraceableCode acquire lock failed error:", e);
        }

        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @RedisLock(key = "'dispensing.chargeSheetId:' + #req.clinicId + ':' + #req.chargeSheetId")
    public List<UpdateTraceableCodeRsp.UpdateTraceableCodeItem> updateDispensingSheetTraceableCode(UpdateTraceableCodeReq req) {
        return dispensingSheetService.updateDispensingSheetTraceableCode(req);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<DispensingFormItemView> queryDispensingFormItemByPatientOrderId(
            String chainId, String clinicId, String patientOrderId, Integer offset, Integer limit) {
        if (offset == null) {
            offset = 0;
        }
        if (limit == null) {
            limit = 100;
        }

        return dispensingSheetService.queryDispensingFormItemByPatientOrderId(chainId, clinicId, patientOrderId, offset, limit);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public QueryDispensingTraceableCodeRsp queryDispensingTraceableCode(QueryDispensingTraceableCodeReq req) {
        if (CollectionUtils.isEmpty(req.getFormItemIdList())) {
            return new QueryDispensingTraceableCodeRsp();
        }
        if (req.getFormItemIdList().size() > 200) {
            throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "数量太多");
        }
        return dispensingSheetService.queryDispensingTraceableCode(req);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<QueryDispensingLockingInfo> queryDispensingLockingInfo(QueryDispensingLockingInfoReq req) {
        return dispensingSheetService.queryDispensingLockingInfo(req);
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp logDispensingSheetPrint(DispensingPrintLogReq req) {
        dispensingPrintedService.logDispensingSheetPrint(req);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public List<WaitingApplyOrderDailyCountRsp> getWaitingApplyOrderDailyCount(GetWaitingApplyOrderDailyCountReq req) {
        List<WaitingApplyOrderDailyCountRsp> rsp = dispensingMapper.getWaitingApplyOrderDailyCount(req);
        return rsp;
    }
}
