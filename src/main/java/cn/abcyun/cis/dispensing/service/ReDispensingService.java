package cn.abcyun.cis.dispensing.service;

import cn.abcyun.bis.rpc.sdk.cis.model.goodslog.GoodsStockLogItem;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import cn.abcyun.cis.dispensing.api.protocol.dispense.re.DispensingSheetReDispenseReq;
import cn.abcyun.cis.dispensing.domain.DispensingLogItem;
import cn.abcyun.cis.dispensing.domain.DispensingSheetOperation;
import cn.abcyun.cis.dispensing.repository.DispensingLogRepository;
import cn.abcyun.cis.dispensing.repository.DispensingSheetOperationRepository;
import cn.abcyun.cis.dispensing.service.dispense.ReDispenseCalculateOpt;
import cn.abcyun.cis.dispensing.service.dispense.ReDispenseOpt;
import cn.abcyun.cis.dispensing.service.dispense.ReDispensePreCheckOpt;
import cn.abcyun.cis.dispensing.service.dto.ReDispenseCalculateResult;
import cn.abcyun.cis.dispensing.service.dto.ReDispensePreCheckResult;
import cn.abcyun.cis.dispensing.service.dto.ReDispenseResult;
import cn.abcyun.cis.dispensing.service.dto.operation.OperationRecordForDispense;
import cn.abcyun.cis.dispensing.service.rpc.ScGoodsFeignClient;
import cn.abcyun.cis.dispensing.util.JsonUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 重新发药业务层
 *
 * <AUTHOR>
 * @date 2023/7/6 14:25
 **/
@Service
public class ReDispensingService {

    @Autowired
    private DispensingSheetOperationRepository dispensingSheetOperationRepository;

    @Autowired
    private DispensingLogRepository dispensingItemLogRepository;

    @Autowired
    private ScGoodsFeignClient scGoodsFeignClient;

    /**
     * 获取发药单第一次发药完成的发药批次信息（一般用于开了锁库的情况，需要关注创建发药单的时候锁的哪些批次信息，由于这里是重新发药，所以直接从发药的批次中查询）
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public List<GoodsStockLogItem> getDispensingSheetFirstDispensedBatch(String dispensingSheetId, String chainId, String clinicId) {
        List<DispensingSheetOperation> dispensingSheetDispenseOperation = dispensingSheetOperationRepository.findAllByDispensingSheetIdAndOperationTypeAndIsDeleted(dispensingSheetId, DispensingSheetOperation.OperationType.SEND_MEDICINE, 0);
        if (CollectionUtils.isEmpty(dispensingSheetDispenseOperation)) {
            return new ArrayList<>();
        }

        // 先获取第一次发药完成涉及的所有 operationIds
        List<String> firstDispensedOperationIds = dispensingSheetDispenseOperation.stream().filter(dispenseOperation -> {
            String recordJson = dispenseOperation.getRecordJson();
            OperationRecordForDispense dispenseOperationRecord = JsonUtils.readValue(recordJson, OperationRecordForDispense.class);
            if (dispenseOperationRecord == null) {
                return false;
            }

            // 只需要第一次发药的发药批次信息
            return dispenseOperationRecord.getDispensingSheetReDispenseCount() == 0;
        }).map(DispensingSheetOperation::getId).collect(Collectors.toList());

        // 通过 operationIds 查询发药日志
        List<DispensingLogItem> dispensingItemDispenseLogs = dispensingItemLogRepository.findAllByDispensingSheetIdAndOperationIdIn(dispensingSheetId, firstDispensedOperationIds);
        if (CollectionUtils.isEmpty(dispensingItemDispenseLogs)) {
            return new ArrayList<>();
        }

        // 获取所有发药的 stockDealId，即 goods 那边的 batId
        List<String> stockDealIds = dispensingItemDispenseLogs.stream().map(DispensingLogItem::getStockDealId)
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stockDealIds)) {
            return new ArrayList<>();
        }

        // 通过 batId 查询发药批次信息
        return scGoodsFeignClient.getGoodsStockDispenseLogList(chainId, clinicId, stockDealIds, true);
    }

    /**
     * 重新发药前置检查
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ReDispensePreCheckResult reDispensePreCheck(String dispensingSheetId, String chainId, String clinicId) {
        ReDispensePreCheckOpt.ReDispensePreCheckOptBuilder reDispensePreCheckOptBuilder = ReDispensePreCheckOpt.ReDispensePreCheckOptBuilder.newInstant(dispensingSheetId, chainId, clinicId);
        return reDispensePreCheckOptBuilder.build().preCheck();
    }

    /**
     * 重新发药计算
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ReDispenseCalculateResult reDispenseCalculate(DispensingSheetReDispenseReq req, String dispensingSheetId,
                                                         String chainId, String clinicId) {
        ReDispenseCalculateOpt reDispenseCalculateOpt = ReDispenseCalculateOpt.ReDispenseCalculateOptBuilder.newInstant(dispensingSheetId, chainId, clinicId)
                .setCheckSupportReDispense(1)
                .setDispensingSheetReDispenseReq(req)
                .build();

        return reDispenseCalculateOpt.calculate();
    }

    /**
     * 重新发药
     */
    @Transactional(rollbackFor = Exception.class)
    public ReDispenseResult dispensingSheetReDispense(DispensingSheetReDispenseReq req, String dispensingSheetId, String chainId, String clinicId, String operatorId) {
        ReDispenseOpt reDispenseOpt = ReDispenseOpt.ReDispenseOptBuilder.newInstant(req, dispensingSheetId, chainId, clinicId, operatorId)
                .build();
        return reDispenseOpt.reDispense();
    }
}
