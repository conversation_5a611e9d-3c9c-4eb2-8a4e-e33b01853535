package cn.abcyun.cis.dispensing.service;

import cn.abcyun.cis.dispensing.domain.DispensingSheetOperation;
import cn.abcyun.cis.dispensing.repository.DispensingSheetOperationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

/**
 * 发药单操作记录业务层
 *
 * <AUTHOR>
 * @since 2025/2/7 10:20
 **/
@Service
public class DispensingSheetOperationService {

    @Autowired
    private DispensingSheetOperationRepository dispensingSheetOperationRepository;

    public List<DispensingSheetOperation> findByDispensingSheetIds(List<String> dispensingSheetIds) {
        return dispensingSheetOperationRepository.findAllByDispensingSheetIdInAndIsDeleted(dispensingSheetIds, 0);
    }

    /**
     * 得到当前库存操作ID
     * operation 按照降序排列，只获取发药和退药的 operationId，直到第一次遇到重新发药的操作
     *
     * @param dispensingSheetIds 发药单ID
     * @return 当前库存操作ID
     */
    public List<String> findCurrentStockOperationByDispensingSheetIds(List<String> dispensingSheetIds) {
        if (CollectionUtils.isEmpty(dispensingSheetIds)) {
            return new ArrayList<>();
        }

        List<DispensingSheetOperation> dispensingSheetOperations = dispensingSheetOperationRepository.findAllByDispensingSheetIdInAndOperationTypeInAndIsDeleted(dispensingSheetIds,
                Arrays.asList(DispensingSheetOperation.OperationType.SEND_MEDICINE,
                        DispensingSheetOperation.OperationType.WITHDRAWAL_MEDICINE,
                        DispensingSheetOperation.OperationType.RE_DISPENSE), 0);
        if (CollectionUtils.isEmpty(dispensingSheetOperations)) {
            return new ArrayList<>();
        }

        // 按照创建时间和ID降序排列
        dispensingSheetOperations.sort(Comparator.comparing(DispensingSheetOperation::getCreated, Comparator.reverseOrder())
                .thenComparing(DispensingSheetOperation::getId, Comparator.reverseOrder()));

        // 获取当前库存操作ID，直到遇到第一个重新发药的操作结束
        List<String> operationIds = new ArrayList<>();
        for (DispensingSheetOperation dispensingSheetOperation : dispensingSheetOperations) {
            if (dispensingSheetOperation.getOperationType() == DispensingSheetOperation.OperationType.RE_DISPENSE) {
                break;
            }
            operationIds.add(dispensingSheetOperation.getId());
        }

        return operationIds;
    }
}
