package cn.abcyun.cis.dispensing.service;

import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisSearchFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.message.dispensing.DispensingSheetMessage;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Department;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Employee;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.EmployeeBasic;
import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.*;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.*;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.config.GoodsConfigView;
import cn.abcyun.bis.rpc.sdk.cis.model.patient.GetPatientBasicInfosRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientInfo;
import cn.abcyun.bis.rpc.sdk.cis.model.patientorder.HisPatientOrder;
import cn.abcyun.bis.rpc.sdk.cis.model.patientorder.PatientOrderHospitalVO;
import cn.abcyun.bis.rpc.sdk.cis.model.search.SearchResultRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.shebao.ShebaoSettleInfoRspBody;
import cn.abcyun.bis.rpc.sdk.his.message.advice.AdviceExecuteMessage;
import cn.abcyun.bis.rpc.sdk.his.message.advice.AdviceMessage;
import cn.abcyun.bis.rpc.sdk.his.message.advice.AdviceUsageRuleItemsAddMessage;
import cn.abcyun.bis.rpc.sdk.his.message.charge.HisChargeFormItemDto;
import cn.abcyun.bis.rpc.sdk.his.model.advice.*;
import cn.abcyun.bis.rpc.sdk.his.model.emr.EmrDiagnosisInfo;
import cn.abcyun.bis.rpc.sdk.his.model.ward.WardBedView;
import cn.abcyun.cis.commons.amqp.message.PatientOrderMessage;
import cn.abcyun.cis.commons.exception.CisCustomException;
import cn.abcyun.cis.commons.exception.FeignRuntimeException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.message.SubscribeMessage;
import cn.abcyun.cis.commons.model.CisPatientInfo;
import cn.abcyun.cis.commons.util.*;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.core.redis.RedisLock;
import cn.abcyun.cis.core.util.ExecutorUtils;
import cn.abcyun.cis.core.util.SpringUtils;
import cn.abcyun.cis.dispensing.AutoDispenseReq;
import cn.abcyun.cis.dispensing.amqp.RocketMqProducer;
import cn.abcyun.cis.dispensing.api.protocol.DispensingSheetPatientOrderIdsReq;
import cn.abcyun.cis.dispensing.api.protocol.HospitalDispensingOrderPrescriptionPrintRsp;
import cn.abcyun.cis.dispensing.api.protocol.config.WardAreaView;
import cn.abcyun.cis.dispensing.api.protocol.order.*;
import cn.abcyun.cis.dispensing.api.protocol.order.GetDispenseSheetByAdviceIdReq;
import cn.abcyun.cis.dispensing.base.DispensingConstants;
import cn.abcyun.cis.dispensing.base.exception.*;
import cn.abcyun.cis.dispensing.controller.DTOConverter;
import cn.abcyun.cis.dispensing.domain.*;
import cn.abcyun.cis.dispensing.domain.DispensingForm;
import cn.abcyun.cis.dispensing.domain.DispensingFormItem;
import cn.abcyun.cis.dispensing.domain.DispensingFormItemBatchInfo;
import cn.abcyun.cis.dispensing.domain.DispensingFormItemExtendData;
import cn.abcyun.cis.dispensing.domain.DispensingOrder;
import cn.abcyun.cis.dispensing.listener.DispensingSheetV2Listener;
import cn.abcyun.cis.dispensing.mybatis.mapper.DispensingMapper;
import cn.abcyun.cis.dispensing.repository.*;
import cn.abcyun.cis.dispensing.service.dto.*;
import cn.abcyun.cis.dispensing.service.dto.DispensingFormItemBasicView;
import cn.abcyun.cis.dispensing.service.dto.DispensingFormItemView;
import cn.abcyun.cis.dispensing.service.dto.DispensingFormView;
import cn.abcyun.cis.dispensing.service.dto.DispensingSheetView;
import cn.abcyun.cis.dispensing.service.dto.operation.DispensingFormItemRecordView;
import cn.abcyun.cis.dispensing.service.dto.operation.DispensingFormRecordViewForDispense;
import cn.abcyun.cis.dispensing.service.dto.operation.OperationRecordForDispense;
import cn.abcyun.cis.dispensing.service.operation.SheetOperationBase;
import cn.abcyun.cis.dispensing.service.operation.SheetOperationCreateFactory;
import cn.abcyun.cis.dispensing.service.rpc.*;
import cn.abcyun.cis.dispensing.util.DispensingEventGroupGenerateUtils;
import cn.abcyun.cis.dispensing.util.DispensingUtils;
import cn.abcyun.cis.dispensing.util.JsonUtils;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import cn.abcyun.common.log.marker.AbcLogMarker;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponseBody;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.autoconfigure.condition.ConditionsReportEndpoint;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;

/***
 * 取药单相关
 * */
@Service
@Slf4j
public class DispensingOrderService {
    private static final Logger sLogger = LoggerFactory.getLogger(DispensingOrderService.class);
    @Autowired
    private DispensingMapper dispensingMapper;
    @Autowired
    private CisCrmFeignClient cisCrmFeignClient;
    @Autowired
    private ScGoodsFeignClient scGoodsFeignClient;
    @Autowired
    private CisWardService cisWardService;
    @Autowired
    private AbcIdGenerator abcIdGenerator;
    @Autowired
    private DispensingConfigService dispensingConfigService;
    @Autowired
    private CisScClinicDepartmentService cisScClinicDepartmentService;
    @Autowired
    private DispensingOrderRepository dispensingOrderRepository;
    @Autowired
    private DispensingOrderSheetRelRepository dispensingOrderSheetRelRepository;
    @Autowired
    private DispensingSheetV2Repository dispensingSheetV2Repository;
    @Autowired
    private DispensingFormV2Repository dispensingFormV2Repository;
    @Autowired
    private DispensingFormItemBatchInfoRepository dispensingFormItemBatchInfoRepository;
    @Autowired
    private DispensingFormItemV2Repository dispensingFormItemV2Repository;
    @Autowired
    private DispensingOrderLogRepository dispensingOrderLogRepository;
    @Autowired
    private SheetOperationCreateFactory sheetOperationCreateFactory;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private AbcCisScGoodsService abcCisScGoodsService;
    @Autowired
    private GoodsLockingFeignClient goodsLockingFeignClient;

    @Autowired
    private AbcCisSearchFeignClient searchFeignClient;
    @Autowired
    private UndispensingService undispensingService;
    @Autowired
    private RocketMqProducer rocketMqProducer;
    @Autowired
    private PatientOrderService patientOrderService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private HisAdviceService hisAdviceService;

    @Autowired
    private DispensingLogService dispensingLogService;
    @Autowired
    private DispensingTodoService dispensingTodoService;
    @Autowired
    private ShebaoService shebaoService;
    @Autowired
    private ClinicClient clinicClient;

    @Autowired
    private ObjectProvider<DispensingSheetV2Listener> dispensingSheetV2ListenerProvider;
    private ConditionsReportEndpoint conditionsReportEndpoint;
    @Autowired
    private DispensingSheetEntityService dispensingSheetEntityService;
    @Autowired
    private DispensingLogRepository dispensingLogRepository;
    @Autowired
    private DispensingSheetOperationRepository dispensingSheetOperationRepository;
    @Autowired
    private HisChargeService hisChargeService;
    @Autowired
    private HisEmrService hisEmrService;

    /**
     * QL 拉取取药单列表
     */
    public DispensingOrderAbstractListRsp findDispensingOrderAbstractList(DispenseOrderQLReq clientReq) throws ServiceInternalException {
        Map<Integer, GoodsPharmacyView> pharmacyNoToPharmacy = scGoodsFeignClient.loadPharmacyByClinicMap(clientReq.getChainId(), clientReq.getClinicId());
        List<DispensingOrderAbstract> orderAbstractList = new ArrayList<>();
        DispensingOrderAbstractListRsp clientRsp = new DispensingOrderAbstractListRsp();
        clientRsp.setResult(orderAbstractList);
        clientRsp.setOffset(clientReq.getOffset());
        clientRsp.setLimit(clientReq.getLimit());
        boolean qlFlag = false;
        int totalCount = 0;
        if (clientReq.getFrom() == 1) {
            // 药房需要根据药房权限过滤
            // 设置用户药房权限
            List<Integer> employeeAccessPharmacyNos = scGoodsFeignClient.getEmployeePharmacyNosByEmployeeId(clientReq.getChainId(), clientReq.getClinicId(), clientReq.getHeaderEmployeeId());
            if (CollectionUtils.isEmpty(employeeAccessPharmacyNos)) {
                clientRsp.setResult(new ArrayList<>());
                return clientRsp;
            }
            if (clientReq.getPharmacyNo() != null) {
                if (employeeAccessPharmacyNos.contains(clientReq.getPharmacyNo())) {
                    clientReq.setPharmacyNos(Lists.newArrayList(clientReq.getPharmacyNo()));
                } else {
                    log.warn("employee access no permission pharmacy no");
                    return clientRsp;
                }
            } else {
                clientReq.setPharmacyNos(employeeAccessPharmacyNos);
            }
        }
        /**
         * 产品上是通过搜索患者 找到发药单 然后找到发药单对应的 取药单列表。经过几层转化后 排序分页？
         * */
        if (!StringUtils.isEmpty(clientReq.getKeyword())) {
            List<RpcSearchDispenseRsp.SearchDispenseItem> retList = searchDispensingHospital(clientReq.getKeyword(),
                    null,
                    clientReq.getOffset(),
                    clientReq.getLimit(),
                    clientReq.getChainId(),
                    clientReq.getClinicId(),
                    clientReq.getPharmacyType(),
                    clientReq.getPharmacyNo(),
                    clientReq.getPharmacyNos());
            if (CollectionUtils.isEmpty(retList)) {
                return clientRsp;
            }
            clientReq.setDispenseOrderIdList(retList.stream()
                    .filter(it -> !StringUtils.isEmpty(it.getDispenseOrderId()))
                    .map(it -> Long.parseLong(it.getDispenseOrderId()))
                    .distinct().collect(Collectors.toList())
            );
        }

        if (clientReq.hasDispense()) {
            clientReq.setNeedJoinSheet(1);
        }
        orderAbstractList = dispensingMapper.findDispensingOrderAbstractList(clientReq);

        List<Long> wardAreaIdList = new ArrayList<>();
        List<String> departmentIdList = new ArrayList<>();
        orderAbstractList.forEach(item -> {
            if (!TextUtils.isEmpty(item.getWardAreaId())) {
                Long wardAreaId = Long.parseLong(item.getWardAreaId());
                if (!wardAreaIdList.contains(wardAreaId)) {
                    wardAreaIdList.add(wardAreaId);
                }
            }
            if (!TextUtils.isEmpty(item.getDepartmentId()) && !departmentIdList.contains(item.getDepartmentId())) {
                departmentIdList.add(item.getDepartmentId());
            }
        });
        Map<Long, WardAreaView> wardAreaIdMap = new HashMap<>();
        Map<String, Department> departmentIdMap = new HashMap<>();
        if (wardAreaIdList.size() > 0) {
            wardAreaIdMap = cisWardService.getWardByIdList(clientReq.getChainId(), clientReq.getClinicId(), wardAreaIdList);
        }
        if (departmentIdList.size() > 0) {
            departmentIdMap = cisScClinicDepartmentService.getDepartmentByIdList(departmentIdList);
        }
        for (DispensingOrderAbstract dispensingOrderAbstract : orderAbstractList) {
            dispensingOrderAbstract.setStatusName(DispensingOrder.statusName(dispensingOrderAbstract.getStatus()));
            if (dispensingOrderAbstract.getStatus() == DispensingOrder.Status.DISPENSED_UNDISPENSED
                    || dispensingOrderAbstract.getStatus() == DispensingOrder.Status.DISPENSING_DISPENSED_UNDISPENSED) {
                dispensingOrderAbstract.setStatusName(DispensingOrderView.transferStatusName(clientReq.getTab(), dispensingOrderAbstract.getStatus()));
            } else if (dispensingOrderAbstract.getStatus() == DispensingOrder.Status.UNDISPENSING && clientReq.getTab() == 1) {
                dispensingOrderAbstract.setStatusName("已发");
            }
            dispensingOrderAbstract.setEmergencyStatusName(DispensingOrder.emergencyStatusName(dispensingOrderAbstract.getEmergencyStatus()));
            dispensingOrderAbstract.setWardAreaView(wardAreaIdMap.get(Long.parseLong(dispensingOrderAbstract.getWardAreaId())));
            dispensingOrderAbstract.setDepartment(departmentIdMap.get(dispensingOrderAbstract.getDepartmentId()));
            if (pharmacyNoToPharmacy.containsKey(dispensingOrderAbstract.getPharmacyNo())) {
                dispensingOrderAbstract.setPharmacyName(pharmacyNoToPharmacy.get(dispensingOrderAbstract.getPharmacyNo()).getName());
            }
        }
        clientReq.setNeedJoinSheet(0);
        DispensingOrderListSummary resultSummary = dispensingMapper.countDispensingOrderAbstractList(clientReq);
        if (!qlFlag) {
            totalCount = resultSummary != null ? resultSummary.getNormalTotalCount() : 0;
        }
        clientRsp.setResult(orderAbstractList);
        clientRsp.setTotalCount(totalCount);
        clientRsp.setTabInfo(resultSummary);
        clientRsp.setTotalCount(totalCount);
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "findDispensingOrderAbstractList req = {},rsp={}", clientReq, clientRsp);
        return clientRsp;
    }

    private List<RpcSearchDispenseRsp.SearchDispenseItem> searchDispensingHospital(String keyword,
                                                                                   Integer dispensingSheetFilterTag,
                                                                                   int offset,
                                                                                   int limit,
                                                                                   String chainId,
                                                                                   String clinicId,
                                                                                   Integer pharmacyType,
                                                                                   Integer pharmacyNo,
                                                                                   List<Integer> pharmacyNos) throws ServiceInternalException {
        List<RpcSearchDispenseRsp.SearchDispenseItem> retList = new ArrayList<>();
        try {
            RpcSearchDispenseReq rpcSearchDispenseReq = new RpcSearchDispenseReq();
            rpcSearchDispenseReq.setChainId(chainId);
            rpcSearchDispenseReq.setClinicId(clinicId);
            rpcSearchDispenseReq.setKeyword(keyword);
            rpcSearchDispenseReq.initDispensingTag(dispensingSheetFilterTag);
            rpcSearchDispenseReq.setPharmacyType(pharmacyType);
            rpcSearchDispenseReq.setDispenseSheetType(DispenseConst.Type.TYPE_HOSPITAL);
            rpcSearchDispenseReq.setPharmacyNo(pharmacyNo);
            rpcSearchDispenseReq.setPharmacyNos(pharmacyNos);
            rpcSearchDispenseReq.setOffset(offset);
            rpcSearchDispenseReq.setScoreCreatedGauss(1);
            rpcSearchDispenseReq.setLimit(limit > 100 ? 100 : limit);
            rpcSearchDispenseReq.initDispensingTag(dispensingSheetFilterTag);
            long startRequestTime = System.currentTimeMillis();
            AbcServiceResponseBody<SearchResultRsp> rspBody = searchFeignClient.doCommonSearch(rpcSearchDispenseReq);
            if (rspBody != null && rspBody.getData() != null) {
                if (CollectionUtils.isEmpty(rspBody.getData().getHits())) {
                    return retList;
                }
                for (JsonNode hit : rspBody.getData().getHits()) {
                    RpcSearchDispenseRsp.SearchDispenseItem rspItem = JsonUtils.readValue(hit, RpcSearchDispenseRsp.SearchDispenseItem.class);
                    retList.add(rspItem);
                }
                return retList;
            }
            sLogger.info("rpc cost time:{}ms,rsp={}", (System.currentTimeMillis() - startRequestTime), JsonUtils.dump(rspBody));
        } catch (FeignRuntimeException e) {
            sLogger.error("searchDispensing feign error", e);
            throw e;
        } catch (Exception e) {
            sLogger.error("searchDispensing error", e);
            throw new ServiceInternalException("searchDispensing error");
        }

        return retList;
    }

    /**
     * 批量拉取取药单
     */
    public List<DispensingOrderView> findDispensingOrderViewByIdList(String headerChainId, String headerClinicId, List<Long> dispensingOrderIds, int from) throws ServiceInternalException {

        //加载取药单
        List<DispensingOrder> dispensingOrderList = dispensingOrderRepository.findByChainIdAndClinicIdAndIdIn(headerChainId, headerClinicId, dispensingOrderIds);
        if (CollectionUtils.isEmpty(dispensingOrderList)) {
            return new ArrayList<>();
        }

        dispensingOrderList = dispensingOrderList.stream().filter(order -> order.getIsDeleted() == DispensingUtils.DeleteFlag.NOT_DELETED).collect(toList());


        List<DispensingOrderView> orderViews = dispensingOrderList.stream().map(DispensingOrderView::from).collect(toList());


        List<DispensingSheetView> sheetViewList = genDispenseOrderDispenseSheetViewList(headerChainId, headerClinicId
                , dispensingOrderList, null, from == 1, null, new HashMap<>());
        Map<String, List<DispensingSheetView>> sheetGroupMap = ListUtils.groupByKey(sheetViewList, DispensingSheetView::getDispenseOrderId);

        //查询药房
        Map<Integer, GoodsPharmacyView> noToPharmacyView = scGoodsFeignClient.loadPharmacyByClinicMap(headerChainId, headerClinicId);
        //查询病区
        Map<Long, WardAreaView> wardAreaViewMap = cisWardService.getWardByIdList(headerChainId, headerClinicId, orderViews.stream().map(DispensingOrderView::getWardAreaId).filter(Objects::nonNull).map(Long::parseLong).collect(toList()));

        //查询科室
        Map<String, Department> departmentMap = cisScClinicDepartmentService.getDepartmentByIdList(orderViews.stream().map(DispensingOrderView::getDepartmentId).collect(toList()));

        orderViews.stream().forEach(order -> {
            List<DispensingSheetView> dispensingSheetViews = sheetGroupMap.get(order.getId());

            if (CollectionUtils.isEmpty(dispensingSheetViews)) {
                return;
            }

            GoodsPharmacyView pharmacyView = noToPharmacyView.get(order.getPharmacyNo());
            if (Objects.nonNull(pharmacyView)) {
                order.setPharmacyName(pharmacyView.getName());
            }
            order.setDepartment(departmentMap.get(order.getDepartmentId()));
            order.setWardAreaView(wardAreaViewMap.get(Long.parseLong(order.getWardAreaId())));

            order.getDispensingSheetViewList().addAll(dispensingSheetViews);
        });

        return orderViews;
    }

    /**
     * 拉取单个取药单
     */
    public DispensingOrderView findDispensingOrderViewById(String headerChainId, String headerClinicId, String dispensingOrderId, int from) throws ServiceInternalException {
        DispensingOrderView clientRsp = new DispensingOrderView();
        Long longDispensingOrderId = Long.parseLong(dispensingOrderId);//避免精度损失
        //加载取药单
        DispensingOrder dispensingOrder = dispensingOrderRepository.findByChainIdAndClinicIdAndId(headerChainId, headerClinicId, longDispensingOrderId).orElse(null);
        if (dispensingOrder == null) {
            return clientRsp;
        }

        if (dispensingOrder.getIsDeleted() == DispensingUtils.DeleteFlag.DELETED) {
            return clientRsp;
        }
        DispensingOrderSummary orderSummary = new DispensingOrderSummary();
        clientRsp = DispensingOrderView.from(dispensingOrder);
        //日志组装
        if (from == 2) {
            // 药房才显示日志，护士站不显示
            List<DispensingOrderLog> logList = dispensingOrderLogRepository.findByChainIdAndOrderIdOrderByCreatedDesc(headerChainId, dispensingOrder.getId());
            clientRsp.pathLogList(logList, employeeService, dispensingOrder.getApplyDispenseTime());
            Map<Integer, GoodsPharmacyView> noToPharmacyView = scGoodsFeignClient.loadPharmacyByClinicMap(headerChainId, headerClinicId);
            if (!CollectionUtils.isEmpty(noToPharmacyView)) {
                GoodsPharmacyView goodsPharmacyView = noToPharmacyView.get(dispensingOrder.getPharmacyNo());
                if (goodsPharmacyView != null) {
                    clientRsp.setPharmacyName(goodsPharmacyView.getName());
                }
            }
        }
        List<DispensingSheetView> sheetViewList = genDispenseOrderDispenseSheetViewList(headerChainId, headerClinicId
                , Arrays.asList(dispensingOrder), null, from == 1, null, new HashMap<>());
        clientRsp.getDispensingSheetViewList().addAll(sheetViewList);
        clientRsp.setOrderSummary(orderSummary);
        clientRsp.patchOtherField(cisWardService, cisScClinicDepartmentService);
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "findDispensingOrderViewById rsp={}", clientRsp);
        return clientRsp;
    }

    private Map<String, DispensingSheetV2> loadAssembleDispenseSheet(String headerChainId, String headerClinicId, String patientOrderId, List<String> dispensingSheetIdList, Set<String> goodsIdList) {
        //加载发药单 和发药单下面的条目
        Map<String, DispensingSheetV2> dispensingSheetIdToSheet = new HashMap<>();
        Map<String, List<DispensingFormV2>> dispensingSheetIdToFormList = new HashMap<>();
        Map<String, List<DispensingFormItemV2>> dispensingFormIdToFormItemList = new HashMap<>();
        Map<String, List<DispensingFormItemBatchInfo>> dispensingFormItemIdToBatchInfoList = new HashMap<>();
        if (!CollectionUtils.isEmpty(dispensingSheetIdList)) {
            dispensingSheetIdToSheet.putAll(dispensingSheetV2Repository.findAllByChainIdAndClinicIdAndIdInAndIsDeleted(headerChainId, headerClinicId, dispensingSheetIdList, DispensingUtils.DeleteFlag.NOT_DELETED)
                    .stream()
                    .filter(it -> StringUtils.isEmpty(patientOrderId) || it.getPatientOrderId().compareTo(patientOrderId) == 0)
                    .collect(toMap(DispensingSheetV2::getId, Function.identity(), (a, b) -> a))
            );
            dispensingSheetIdList.clear();
            dispensingSheetIdList.addAll(dispensingSheetIdToSheet.keySet().stream().collect(toList()));
            if (!CollectionUtils.isEmpty(dispensingSheetIdList)) {
                dispensingSheetIdToFormList.putAll(dispensingFormV2Repository.findAllByDispensingSheetIdInAndClinicIdAndIsDeleted(dispensingSheetIdList, headerClinicId, DispensingUtils.DeleteFlag.NOT_DELETED)
                        .stream()
                        .collect(groupingBy(DispensingFormV2::getDispensingSheetId)));
                dispensingFormIdToFormItemList.putAll(dispensingFormItemV2Repository.findAllByChainIdAndClinicIdAndDispensingSheetIdInAndIsDeleted(headerChainId, headerClinicId, dispensingSheetIdList, DispensingUtils.DeleteFlag.NOT_DELETED)
                        .stream()
                        .collect(groupingBy(DispensingFormItemV2::getDispensingFormId))
                );
                dispensingFormItemIdToBatchInfoList.putAll(
                        dispensingFormItemBatchInfoRepository.findAllByChainIdAndDispensingSheetIdInAndIsOldAndIsDeleted(headerChainId, dispensingSheetIdList, 0, DispensingUtils.DeleteFlag.NOT_DELETED)
                                .stream()
                                .collect(groupingBy(DispensingFormItemBatchInfo::getDispensingFormItemId))
                );
            }
        }

        //把发药单下面的Form和formItem组装进去
        dispensingSheetIdToSheet.forEach((dispensingSheetId, sheet) -> {
            List<DispensingFormV2> formV2List = dispensingSheetIdToFormList.get(dispensingSheetId);
            if (CollectionUtils.isEmpty(formV2List)) {
                return;
            }
            sheet.setDispensingForms(formV2List);
            for (DispensingFormV2 dispensingFormV2 : formV2List) {
                List<DispensingFormItemV2> formItemV2List = dispensingFormIdToFormItemList.get(dispensingFormV2.getId());
                if (CollectionUtils.isEmpty(formItemV2List)) {
                    continue;
                }
                dispensingFormV2.setDispensingFormItems(formItemV2List);
                for (DispensingFormItemV2 dispensingFormItemV2 : formItemV2List) {
                    List<DispensingFormItemBatchInfo> batchInfoList = dispensingFormItemIdToBatchInfoList.get(dispensingFormItemV2.getId());
                    if (!CollectionUtils.isEmpty(batchInfoList)) {
                        dispensingFormItemV2.setDispensingFormItemBatches(batchInfoList);
                    }
                }
            }
        });
        if (goodsIdList != null) {
            goodsIdList.addAll(dispensingFormIdToFormItemList.values().stream().flatMap(Collection::stream)
                    .map(DispensingFormItemV2::getProductId)
                    .filter(productId -> !TextUtils.isEmpty(productId))
                    .collect(toSet()));
        }

        return dispensingSheetIdToSheet;
    }

    public DispensingOrderView findDispensingOrderViewByDispensingOrderId(String chainId, String clinicId, String dispenseOrderId, int tab, int from) {
        DispensingOrderView dispensingOrderView = findDispensingOrderViewById(chainId, clinicId, dispenseOrderId, from);
        transDispensingOrderStatus(dispensingOrderView, tab);
        return dispensingOrderView;
    }


    public List<DispensingOrderView> findDispensingOrderViewByDispensingOrderIdList(String chainId, String clinicId, List<Long> dispenseOrderIds, int tab, int from) {
        List<DispensingOrderView> dispensingOrderViewList = findDispensingOrderViewByIdList(chainId, clinicId, dispenseOrderIds, from);

        dispensingOrderViewList.stream().forEach(dispensingOrderView -> {
            transDispensingOrderStatus(dispensingOrderView, tab);
        });
        return dispensingOrderViewList;
    }

    private void transDispensingOrderStatus(DispensingOrderView dispensingOrderView, int tab) {
        if (dispensingOrderView.getStatus() == DispensingOrder.Status.DISPENSED_UNDISPENSED
                || dispensingOrderView.getStatus() == DispensingOrder.Status.DISPENSING_DISPENSED_UNDISPENSED) {
            dispensingOrderView.setStatusName(DispensingOrderView.transferStatusName(tab, dispensingOrderView.getStatus()));
        } else if (dispensingOrderView.getStatus() == DispensingOrder.Status.UNDISPENSING && tab == 1) {
            dispensingOrderView.setStatusName("已发");
        }
        List<DispensingSheetView> resultSheetViewList = new ArrayList<>();
        dispensingOrderView.getDispensingSheetViewList().forEach(sheetView -> {
//            if (tab == 1) {
//                if (sheetView.getStatus() == DispenseConst.Status.UNDISPENSED) {
//                    // 发药tab退药的过滤掉
//                    return;
//                }
//            }
            AtomicBoolean needAddFlag = new AtomicBoolean(false);
            List<DispensingFormView> dispensingForms = sheetView.getDispensingForms();
            dispensingForms.forEach(formItem -> {
                List<DispensingFormItemView> dispensingFormItems = formItem.getDispensingFormItems();
                if (tab == 1) {
                    List<DispensingFormItemView> dispensingItemViewList = dispensingFormItems.stream()
                            .filter(item -> item.getStatus() == DispenseConst.Status.WAITING || item.getStatus() == DispenseConst.Status.DISPENSED
                                    || item.getStatus() == DispenseConst.Status.APPLY_DISPENSE_REJECT
                                    || item.getStatus() == DispenseConst.Status.RECORD_NOT_DISPENSE || item.getStatus() == DispenseConst.Status.STOCK_NOT_DISPENSE
                                    || item.getStatus() == DispenseConst.Status.REAPPLY_DISPENSE).collect(toList());
                    if (!CollectionUtils.isEmpty(dispensingItemViewList)) {
                        needAddFlag.getAndSet(true);
                        formItem.setDispensingFormItems(dispensingItemViewList);
                    }
                } else {
                    List<DispensingFormItemView> undispensingItemViewList = dispensingFormItems.stream()
                            .filter(item -> item.getStatus() == DispenseConst.Status.APPLY_UNDISPNSE || item.getStatus() == DispenseConst.Status.APPLY_UNDISPENSE_REJECT
                                    || item.getStatus() == DispenseConst.Status.UNDISPENSED
                                    || item.getStatus() == DispenseConst.Status.REAPPLY_UNDISPENSE).collect(toList());
                    if (!CollectionUtils.isEmpty(undispensingItemViewList)) {
                        needAddFlag.getAndSet(true);
                        formItem.setDispensingFormItems(undispensingItemViewList);
                    }
                }
            });
            if (needAddFlag.get()) {
                resultSheetViewList.add(sheetView);
            }
        });
        dispensingOrderView.setDispensingSheetViewList(resultSheetViewList);
    }

    /**
     * 返回给前端的协议组装
     */
    private List<DispensingSheetView> genDispenseOrderDispenseSheetViewList(String headerChainId,
                                                                            String headerClinicId,
                                                                            List<DispensingOrder> dispenseOrderList,
                                                                            String patientOrderId, boolean needQueryAdvice,
                                                                            List<String> filterSheetIdList, Map<Long, AdviceExecuteBasicView> executeBasicViewMap) {
        List<DispensingSheetView> sheetViewList = new ArrayList<>();
        //加载取药单下面的发药单ID列表
        List<DispensingOrderSheetRel> orderSheetRelList = dispensingOrderSheetRelRepository.findAllByChainIdAndClinicIdAndOrderIdInAndIsDeleted(headerChainId, headerClinicId,
                dispenseOrderList.stream().map(DispensingOrder::getId).collect(toList()), DispensingUtils.DeleteFlag.NOT_DELETED);
        if (!CollectionUtils.isEmpty(filterSheetIdList)) {
            orderSheetRelList = orderSheetRelList.stream()
                    .filter(it -> filterSheetIdList.contains(it.getDispenseSheetId())).collect(toList());
        }
        if (CollectionUtils.isEmpty(orderSheetRelList)) {
            return sheetViewList;
        }
        List<String> dispensingSheetIdList = orderSheetRelList.stream().map(DispensingOrderSheetRel::getDispenseSheetId).collect(toList());
        DispensingOrder dispensingOrder = dispenseOrderList.get(0);
        //加载发药单 和发药单下面的条目
        Set<String> goodsIdList = new HashSet<>();
        Map<String, DispensingSheetV2> dispensingSheetIdToSheet = loadAssembleDispenseSheet(headerChainId, headerClinicId, patientOrderId, dispensingSheetIdList, goodsIdList);
        CompletableFuture<Map<Long, GoodsBatchInfo>> goodsBatchInfoFuture = getGoodsBatchInfoFuture(headerClinicId, dispensingSheetIdToSheet);

        /**
         * loadpatient
         * */
        Map<String, GoodsItem> goodsIdToGoodsItem = new HashMap<>();
        if (goodsIdList.size() > 0) {
            List<GoodsItem> goodsItems = null;
            try {
                List<QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq> pharmacyGoodsList = new ArrayList<QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq>() {{
                    QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq queryPharmacyGoodsReq = new QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq();
                    queryPharmacyGoodsReq.setPharmacyNo(dispensingOrder != null ? dispensingOrder.getPharmacyNo() : 0);
                    queryPharmacyGoodsReq.setGoodsIds(new ArrayList<>(goodsIdList));
                    add(queryPharmacyGoodsReq);
                }};
                List<QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp> queryPharmacyGoodsRsps = scGoodsFeignClient.queryGoodsInPharmacyByIdsDistinctByGoodsId(headerClinicId, headerChainId, true, 1, pharmacyGoodsList);
                goodsItems = Optional.ofNullable(queryPharmacyGoodsRsps).orElse(new ArrayList<>()).stream()
                        .filter(queryPharmacyGoodsRsp -> !CollectionUtils.isEmpty(queryPharmacyGoodsRsp.getList()))
                        .flatMap(queryPharmacyGoodsRsp -> queryPharmacyGoodsRsp.getList().stream())
                        .collect(toList());
            } catch (Exception e) {
                log.error("load goods error", e);
                goodsItems = new ArrayList<>();
            }

            goodsIdToGoodsItem.putAll(goodsItems.stream().collect(toMap(GoodsItem::getId, Function.identity(), (goodsItem1, goodsItem2) -> goodsItem1)));
        }

        CompletableFuture<Map<String, PatientInfo>> patientInfoFuture = getPatientInfoFuture(headerChainId,
                dispensingSheetIdToSheet.values().stream().map(DispensingSheetV2::getPatientId).collect(toList()));
        CompletableFuture<Map<String, WardBedView>> wardBedViewFuture = getWardBedViewFuture(headerChainId, headerClinicId,
                dispensingSheetIdToSheet.values().stream().map(DispensingSheetV2::getPatientOrderId).distinct().collect(toList()));
        CompletableFuture<Map<Integer, GoodsPharmacyView>> pharmacyViewFuture = getPharmacyViewFuture(headerChainId, headerClinicId);
        CompletableFuture<Map<Long, AdviceDetailView>> adviceDetailViewFuture = getAdviceDetailViewFuture(headerChainId, headerClinicId,
                dispensingSheetIdToSheet.values().stream().map(DispensingSheetV2::getAdviceId).distinct().collect(toList()));

        //Map<Long, AdviceExecuteBasicView> executeBasicViewMap = new HashMap<>();
        if (needQueryAdvice) {
            List<Long> adviceExecuteIdList = dispensingSheetIdToSheet.values().stream().flatMap(item -> item.getDispensingForms().stream())
                    .map(DispensingFormV2::getAdviceExecuteId).filter(Objects::nonNull).distinct().collect(toList());
            CompletableFuture<Map<Long, AdviceExecuteBasicView>> adviceExecuteBasicViewFuture = getAdviceExecuteBasicViewFuture(headerChainId, headerClinicId, adviceExecuteIdList);
            executeBasicViewMap.putAll(adviceExecuteBasicViewFuture.join());
        }

        List<String> patientOrderIdList = dispensingSheetIdToSheet.values().stream().map(DispensingSheetV2::getPatientOrderId).distinct().collect(toList());
        CompletableFuture<Map<String, PatientOrderHospitalVO>> patientOrderHospitalVoFuture = getPatientOrderHospitalVoFuture(headerChainId, headerClinicId, patientOrderIdList);

        Map<String, PatientInfo> patientIdToPatientInfo = patientInfoFuture.join();
        Map<String, WardBedView> patientOrderIdToBedView = wardBedViewFuture.join();
        Map<Integer, GoodsPharmacyView> pharmacyNoToPharmacyView = pharmacyViewFuture.join();
        Map<Long, AdviceDetailView> adviceIdToAdviceDetailView = adviceDetailViewFuture.join();
        Map<Long, GoodsBatchInfo> batchIdToBatchInfo = goodsBatchInfoFuture.join();
        Map<String, PatientOrderHospitalVO> patientOrderHospitalVOMap = patientOrderHospitalVoFuture.join();

        Set<String> addIdSet = new HashSet<>();
        //填充返回数据
        for (DispensingOrderSheetRel dispensingOrderSheetRel : orderSheetRelList) {
            DispensingSheetV2 sheet = dispensingSheetIdToSheet.get(dispensingOrderSheetRel.getDispenseSheetId());
            if (sheet == null) {
                continue;
            }
            if (addIdSet.contains(sheet.getId())) {
                continue;
            }
            addIdSet.add(sheet.getId());
            Map<String, List<DispensingFormItemBatchInfo>> dispensingFormItemIdToBatchInfos = new HashMap<>();
            DispensingUtils.doWithDispensingItem(sheet, item -> {
                if (CollectionUtils.isEmpty(item.getDispensingFormItemBatches())) {
                    return;
                }

                dispensingFormItemIdToBatchInfos.put(item.getId(), item.getDispensingFormItemBatches());
            });

            DispensingSheetView sheetView = DispensingSheetView.from(sheet);
            setSheetViewGroupId(sheetView, adviceIdToAdviceDetailView.get(sheetView.getAdviceId()));
            // 药房名称
            sheetView.setPharmacyName(pharmacyNoToPharmacyView.get(sheet.getPharmacyNo()) != null ? pharmacyNoToPharmacyView.get(sheet.getPharmacyNo()).getName() : "");
            //患者
            if (patientIdToPatientInfo.get(sheetView.getPatientId()) != null) {
                sheetView.setPatient(new CisPatientInfo());
                BeanUtils.copyProperties(patientIdToPatientInfo.get(sheetView.getPatientId()), sheetView.getPatient());
            }

            PatientOrderHospitalVO patientOrderHospitalVO = patientOrderHospitalVOMap.get(sheetView.getPatientOrderId());
            if (patientOrderHospitalVO == null) {
                log.error("patientOrderHospitalVO is null, patientOrderId:{}", sheetView.getPatientOrderId());
            } else {
                sheetView.setCurPatientWardId(patientOrderHospitalVO.getWardId());
            }

            //取药单ID
            sheetView.setDispenseOrderId(dispensingOrderSheetRel.getOrderId().toString());
            //床
            sheetView.setBeds(patientOrderIdToBedView.get(sheetView.getPatientOrderId()));
            //处方
            for (DispensingFormView dispensingForm : sheetView.getDispensingForms()) {
                //发药项里面的Goods 填充的是最新的
                for (DispensingFormItemView dispensingFormItem : dispensingForm.getDispensingFormItems()) {
                    if (goodsIdToGoodsItem.get(dispensingFormItem.getProductId()) != null) {
                        dispensingFormItem.setProductInfo(JsonUtils.dumpAsJsonNode(goodsIdToGoodsItem.get(dispensingFormItem.getProductId())));
                    }

                    if (!CollectionUtils.isEmpty(dispensingFormItem.getDispensingFormItemBatches())) {
                        List<DispensingFormItemBatchInfoView> dispensingFormItemBatches = dispensingFormItem.getDispensingFormItemBatches();
                        for (DispensingFormItemBatchInfoView batchInfo : dispensingFormItemBatches) {
                            GoodsBatchInfo goodsBatchInfo = batchIdToBatchInfo.get(batchInfo.getBatchId());
                            if (goodsBatchInfo != null) {
                                batchInfo.setBatchInfo(goodsBatchInfo);
                            }
                        }
                    }

                    if (dispensingFormItem.getStatus() == DispenseConst.Status.APPLY_UNDISPNSE
                            && dispensingFormItemIdToBatchInfos.containsKey(dispensingFormItem.getAssociateFormItemId())) {
                        dispensingFormItem.setAssociateDispensingFormItemBatches(buildDispensableFormItemBatches(dispensingFormItemIdToBatchInfos.get(dispensingFormItem.getAssociateFormItemId()), batchIdToBatchInfo));
                    }
                }
                if (dispensingForm.getAdviceExecuteId() != null && !CollectionUtils.isEmpty(executeBasicViewMap)) {
                    AdviceExecuteBasicView adviceExecuteBasicView = executeBasicViewMap.get(dispensingForm.getAdviceExecuteId());
                    if (adviceExecuteBasicView != null) {
                        dispensingForm.setPlanExecuteTime(adviceExecuteBasicView.getPlanExecuteTime());
                    }
                }
            }
            sheetViewList.add(sheetView);
        }
        Map<String, List<DispensingSheetView>> patientOrderIdToSheetViewList = sheetViewList.stream().collect(groupingBy(DispensingSheetView::getPatientOrderId));
        TreeMap<Long, List<DispensingSheetView>> bedNoToSheetViewList = new TreeMap<>();
        patientOrderIdToSheetViewList.forEach((pOrderId, sheetViews) -> sortedDispensingSheetViewList(bedNoToSheetViewList, patientOrderIdToBedView.get(pOrderId), sheetViews));
        return bedNoToSheetViewList.values().stream().flatMap(Collection::stream).collect(toList());
    }

    private List<DispensingFormItemBatchInfoView> buildDispensableFormItemBatches(List<DispensingFormItemBatchInfo> dispensingFormItemBatchInfos,
                                                                                  Map<Long, GoodsBatchInfo> batchIdToBatchInfo) {
        if (CollectionUtils.isEmpty(dispensingFormItemBatchInfos)) {
            return Lists.newArrayList();
        }

        return dispensingFormItemBatchInfos.stream()
                .map(dispensingFormItemBatchInfo -> {
                    BigDecimal dispensableUnitCount = MathUtils.wrapBigDecimalSubtract(dispensingFormItemBatchInfo.getDispenseUnitCount(), dispensingFormItemBatchInfo.getUndispenseUnitCount());
                    if (MathUtils.wrapBigDecimalCompare(dispensableUnitCount, BigDecimal.ZERO) <= 0) {
                        return null;
                    }
                    return DispensingFormItemBatchInfoView.from(dispensingFormItemBatchInfo, dispensableUnitCount, batchIdToBatchInfo.get(dispensingFormItemBatchInfo.getBatchId()));
                })
                .filter(Objects::nonNull).collect(Collectors.toList());
    }


    private void sortedDispensingSheetViewList(TreeMap<Long, List<DispensingSheetView>> bedNoToSheetViewList,
                                               WardBedView wardBedView, List<DispensingSheetView> sheetViews) {
        Long bedNo = 0L;
        if (wardBedView != null) {
            bedNo = Long.parseLong(wardBedView.getBedNo());
        }
        // 没有执行时间
        List<DispensingSheetView> noPlanExecuteTimeList = sheetViews.stream().filter(it -> Objects.isNull(it.getPlanExecuteTime())).sorted(Comparator.comparing(DispensingSheetView::getCreated)).collect(toList());
        // 有执行时间，按一组分组且执行时间相同的为一组
        Map<String, List<DispensingSheetView>> groupIdToViewList = sheetViews.stream().filter(it -> Objects.nonNull(it.getPlanExecuteTime())).collect(groupingBy(DispensingSheetView::getGroupId));
        List<DispensingSheetView> firstViewList = new ArrayList<>();
        List<DispensingSheetView> finalFirstViewList = firstViewList;
        groupIdToViewList.forEach((groupId, list) -> finalFirstViewList.add(list.get(0)));
        firstViewList = firstViewList.stream().sorted(Comparator.comparing(DispensingSheetView::getPlanExecuteTime).thenComparing(Comparator.comparing(DispensingSheetView::getCreated))).collect(toList());

        List<DispensingSheetView> sortedSheetViewList = new ArrayList<>();
        firstViewList.forEach(view -> {
            List<DispensingSheetView> dispensingSheetViews = groupIdToViewList.get(view.getGroupId());
            if (dispensingSheetViews == null) {
                return;
            }
            sortedSheetViewList.addAll(dispensingSheetViews);
        });
        List<DispensingSheetView> allSortedSheetList = new ArrayList<>(noPlanExecuteTimeList);
        allSortedSheetList.addAll(sortedSheetViewList);
        List<DispensingSheetView> existList = bedNoToSheetViewList.get(bedNo);
        if (existList != null) {
            existList.addAll(allSortedSheetList);
            bedNoToSheetViewList.put(bedNo, existList);
        } else {
            bedNoToSheetViewList.put(bedNo, allSortedSheetList);
        }
    }

    /**
     * 拉某个患者的所有发药单
     */
    //TODO 差搜索
    public AbcListPage<DispensingSheetView> getDispensingSheetViewByPatientId(DispensingSheetPatientOrderIdsReq clientReq) throws ServiceInternalException {
        String headerChainId = clientReq.getChainId();
        String headerClinicId = clientReq.getClinicId();
        String patientOrderId = clientReq.getPatientOrderIds().get(0);
        int tab = clientReq.getTab();
        AbcListPage<DispensingSheetView> clientRsp = new AbcListPage<>();

        if (!StringUtils.isEmpty(clientReq.getKeyword())) {
            List<String> dispensingSheetList = getDispensingOrderIdBySearchGoodsName(clientReq);
            if (CollectionUtils.isEmpty(dispensingSheetList)) {
                return clientRsp;
            }
            clientReq.setDispensingSheetIdList(dispensingSheetList);
        }
        List<Long> orderIdList = dispensingMapper.findDispensingOrderIdByPatientOrderIdList(clientReq);
        if (CollectionUtils.isEmpty(orderIdList)) {
            return clientRsp;
        }
        List<DispensingOrder> dispensingOrderList = dispensingOrderRepository.findByChainIdAndClinicIdAndIdIn(headerChainId, headerClinicId, orderIdList);
        if (CollectionUtils.isEmpty(dispensingOrderList)) {
            return clientRsp;
        }
        Map<Long, AdviceExecuteBasicView> executeBasicViewMap = new HashMap<>();
        List<DispensingSheetView> sheetViewList = genDispenseOrderDispenseSheetViewList(headerChainId, headerClinicId,
                dispensingOrderList, patientOrderId, true, clientReq.getDispensingSheetIdList(), executeBasicViewMap);

        sheetViewList = filterHasReNewSheetView(sheetViewList, tab);

        sheetViewList = filterDispensingSheetView(sheetViewList, clientReq);

        List<DispensingSheetView> resultSheetViewList = filterDispensingFormItem(sheetViewList, tab, executeBasicViewMap);
        clientRsp.setRows(resultSheetViewList);
        return clientRsp;
    }

    private List<String> getDispensingOrderIdBySearchGoodsName(DispensingSheetPatientOrderIdsReq clientReq) {
        if (CollectionUtils.isEmpty(clientReq.getPatientOrderIds())) {
            return new ArrayList<>();
        }
        int status = 0;
        if (clientReq.getTab() == 2) {
            status = 1;
        } else if (clientReq.getTab() == 3) {
            status = 2;
        }
        List<RpcSearchDispenseRsp.SearchDispenseItem> searchDispenseItemList =
                searchDispensingHospitalNurse(clientReq.getKeyword(), 0, 200, clientReq.getChainId(), clientReq.getClinicId(), status, clientReq.getPatientOrderIds());
        if (CollectionUtils.isEmpty(searchDispenseItemList)) {
            return new ArrayList<>();
        }
        return searchDispenseItemList.stream().filter(it -> !StringUtils.isEmpty(it.getId()))
                .map(RpcSearchDispenseRsp.SearchDispenseItem::getId).distinct().collect(toList());
    }

    private List<DispensingSheetV2> filterHasReNewSheet(List<DispensingSheetV2> sheetV2List, int tab) {
        if (tab != 3) {
            return sheetV2List;
        }
        return sheetV2List.stream().filter(it -> !DispensingUtils.checkFlagOn(it.getDispensingTag(),
                DispensingUtils.DispensingTag.DISPENSING_TAG_HOSPITAL_RENEW_OLD_SHEET_FLAG)).collect(toList());
    }

    private List<DispensingSheetView> filterHasReNewSheetView(List<DispensingSheetView> sheetViewList, int tab) {
        if (tab != 3) {
            return sheetViewList;
        }
        return sheetViewList.stream().filter(it -> !DispensingUtils.checkFlagOn(it.getDispensingTag(),
                DispensingUtils.DispensingTag.DISPENSING_TAG_HOSPITAL_RENEW_OLD_SHEET_FLAG)).collect(toList());
    }

    private List<DispensingSheetView> filterDispensingSheetView(List<DispensingSheetView> resultSheetViewList,
                                                                DispensingSheetPatientOrderIdsReq clientReq) {
        if (CollectionUtils.isEmpty(resultSheetViewList)) {
            return new ArrayList<>();
        }
        if (clientReq.isHasPlanExecuteTime()) {
            // 同一个领药单排除不符合的
            resultSheetViewList = resultSheetViewList.stream().filter(it -> it.getPlanExecuteTime() != null)
                    .filter(it -> it.getPlanExecuteTime().isAfter(clientReq.getPlanExecuteStartTime()) && it.getPlanExecuteTime().isBefore(clientReq.getPlanExecuteEndTime()))
                    .collect(toList());
        }
        if (clientReq.getTab() == 2 && clientReq.isHasDispensedTime()) {
            resultSheetViewList = resultSheetViewList.stream().filter(it -> it.getDispensedTime() != null)
                    .filter(it -> it.getDispensedTime().isAfter(clientReq.getDispensedStartTime()) && it.getDispensedTime().isBefore(clientReq.getDispensedEndTime()))
                    .collect(toList());
        }
        return resultSheetViewList;
    }

    private List<DispensingSheetView> filterDispensingFormItem(List<DispensingSheetView> sheetViewList, int tab,
                                                               Map<Long, AdviceExecuteBasicView> executeBasicViewMap) {
        return filterDispensingFormItem(sheetViewList, tab, executeBasicViewMap, DispenseConst.Status.APPLY_UNDISPNSE, 0);
    }

    /**
     * 过滤器发药formItem
     *
     * @param sheetViewList              发药单列表
     * @param tab                        标签
     * @param executeBasicViewMap        执行基本视图映射
     * @param unDispensedDisplayStatus   未退药展示状态
     * @param includeCanUnDispensingZero 是否包含可退数量为0
     * @return {@link List }<{@link DispensingSheetView }>
     */
    private List<DispensingSheetView> filterDispensingFormItem(List<DispensingSheetView> sheetViewList, int tab,
                                                               Map<Long, AdviceExecuteBasicView> executeBasicViewMap, Integer unDispensedDisplayStatus, int includeCanUnDispensingZero) {
        if (CollectionUtils.isEmpty(sheetViewList)) {
            return new ArrayList<>();
        }
        List<DispensingSheetView> resultSheetView = new ArrayList<>();
        sheetViewList.forEach(sheetView -> {
            AtomicBoolean needAddFlag = new AtomicBoolean(false);
            List<DispensingFormView> dispensingForms = sheetView.getDispensingForms();
            AtomicBoolean needUnDispenseFlag = new AtomicBoolean(false);
            dispensingForms.forEach(formItem -> {
                if (formItem.getAdviceExecuteId() != null && !CollectionUtils.isEmpty(executeBasicViewMap)) {
                    AdviceExecuteBasicView adviceExecuteBasicView = executeBasicViewMap.get(formItem.getAdviceExecuteId());
                    if (adviceExecuteBasicView != null) {
                        formItem.setPlanExecuteTime(adviceExecuteBasicView.getPlanExecuteTime());
                    } else {
                        // 设置标识
                        if (DispensingUtils.isAdviceExecuteSourceSheetType(sheetView.getSourceSheetType()) && tab == 2) {
                            needUnDispenseFlag.set(true);
                        }
                    }
                }
                List<DispensingFormItemView> dispensingFormItems = formItem.getDispensingFormItems();
                if (tab == 1) {
                    // 发药，自备药展示在申请单中
                    List<DispensingFormItemView> waitingItems = dispensingFormItems.stream().filter(item -> item.getStatus() == DispenseConst.Status.WAITING || item.getStatus() == DispenseConst.Status.SELF_PROVIDED).collect(toList());
                    if (!CollectionUtils.isEmpty(waitingItems)) {
                        needAddFlag.getAndSet(true);
                        waitingItems.forEach(item -> {
                            if (item.getProductType() == GoodsConst.GoodsType.MEDICINE && item.getProductSubType() == GoodsConst.GoodsMedicineSubType.CHINESE_MEDICINE) {
                                item.setUseCount(item.getDoseCount());
                            } else {
                                item.setUseCount(item.getUnitCount());
                            }
                        });
                    }
                } else if (tab == 2) {
                    // 发药或扣库
                    List<DispensingFormItemView> dispensedFormItemList = dispensingFormItems.stream().filter(item -> item.getStatus() == DispenseConst.Status.DISPENSED || item.getStatus() == DispenseConst.Status.STOCK_NOT_DISPENSE).collect(toList());
                    Map<String, List<DispensingFormItemView>> formItemIdToUndispensedFormItemList = dispensingFormItems.stream()
                            .filter(item -> !TextUtils.isEmpty(item.getAssociateFormItemId()))
                            .filter(item -> item.getStatus() == DispenseConst.Status.UNDISPENSED || item.getStatus() == DispenseConst.Status.APPLY_UNDISPNSE)
                            .collect(groupingBy(DispensingFormItemBasicView::getAssociateFormItemId));
                    // 计算可退数量函数
                    BiFunction<DispensingFormItemView, List<DispensingFormItemView>, BigDecimal> calculateCount = (formItemView, formItemList) -> {
                        if (formItemView.getProductType() == GoodsConst.GoodsType.MEDICINE && formItemView.getProductSubType() == GoodsConst.GoodsMedicineSubType.CHINESE_MEDICINE) {
                            return formItemList.stream().map(DispensingFormItemBasicView::getDoseCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                        } else {
                            return formItemList.stream().map(DispensingFormItemBasicView::getUnitCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                    };
                    dispensedFormItemList.forEach(item -> {
                        BigDecimal totalCount;
                        if (item.getProductType() == GoodsConst.GoodsType.MEDICINE && item.getProductSubType() == GoodsConst.GoodsMedicineSubType.CHINESE_MEDICINE) {
                            totalCount = item.getDoseCount();
                        } else {
                            totalCount = item.getUnitCount();
                        }
                        List<DispensingFormItemView> dispensedFormItemViews = formItemIdToUndispensedFormItemList.get(item.getId());
                        if (!CollectionUtils.isEmpty(dispensedFormItemViews)) {
                            BigDecimal undispensedCount = calculateCount.apply(dispensedFormItemViews.get(0), dispensedFormItemViews);
                            BigDecimal canUndispensingCount = MathUtils.wrapBigDecimalSubtract(totalCount, undispensedCount);
                            if (includeCanUnDispensingZero == 1 || canUndispensingCount.compareTo(BigDecimal.ZERO) > 0) {
                                needAddFlag.getAndSet(true);
                                item.setUseCount(canUndispensingCount);
                                item.setStatus(DispenseConst.Status.APPLY_UNDISPNSE);
                                item.setStatusName(DispensingFormItemView.statusName(item.getStatus()));
                            }
                        } else {
                            needAddFlag.getAndSet(true);
                            item.setUseCount(totalCount);
                            if (unDispensedDisplayStatus != null) {
                                item.setStatus(unDispensedDisplayStatus);
                            }
                            item.setStatusName(DispensingFormItemView.statusName(item.getStatus()));
                        }
                    });
                    if (needAddFlag.get()) {
                        formItem.setDispensingFormItems(dispensedFormItemList);
                    }
                } else {
                    // 退药的
                    List<DispensingFormItemView> undispensedItems = dispensingFormItems.stream()
                            .filter(item -> item.getStatus() == DispenseConst.Status.UNDISPENSED || item.getStatus() == DispenseConst.Status.APPLY_DISPENSE_REJECT).collect(toList());
                    if (!CollectionUtils.isEmpty(undispensedItems)) {
                        needAddFlag.getAndSet(true);
                        undispensedItems.forEach(item -> {
                            if (DispensingUtils.isChineseMedicine(item.getProductType(), item.getProductSubType())) {
                                item.setUseCount(item.getDoseCount());
                            } else {
                                item.setUseCount(item.getUnitCount());
                            }
                        });
                        formItem.setDispensingFormItems(undispensedItems);
                    }
                }
            });
            if (needAddFlag.get()) {
                if (needUnDispenseFlag.get()) {
                    sheetView.setNeedUnDispenseFlag(1);
                }
                resultSheetView.add(sheetView);
            }
        });
        return resultSheetView;
    }


    /**
     * 通过医嘱IDs 查医嘱下的所有发药单
     * 目前主要返回发药单状态
     */
    public AbcListPage<DispensingSheetView> getDispensingSheetViewByAdvice(GetDispenseSheetByAdviceIdReq clientReq) throws ServiceInternalException {
        AbcListPage<DispensingSheetView> clientRsp = new AbcListPage<>();
        List<DispensingSheetV2> dispensingSheetV2List = null;
        List<Integer> sheetStatusList = clientReq.getSheetStatusList();
        if (!CollectionUtils.isEmpty(sheetStatusList)) {
            dispensingSheetV2List =
                    dispensingSheetV2Repository.findAllByChainIdAndClinicIdAndAdviceIdInAndStatusInAndIsDeleted(clientReq.getChainId(),
                            clientReq.getClinicId(), clientReq.getAdviceIdList(), sheetStatusList, DispensingUtils.DeleteFlag.NOT_DELETED);
        } else {
            dispensingSheetV2List = dispensingSheetV2Repository.findAllByChainIdAndClinicIdAndAdviceIdInAndIsDeleted(clientReq.getChainId(),
                    clientReq.getClinicId(),
                    clientReq.getAdviceIdList(),
                    DispensingUtils.DeleteFlag.NOT_DELETED);
        }
        // 过滤掉重新发药的
        dispensingSheetV2List = dispensingSheetV2List.stream().filter(it -> !DispensingUtils.checkFlagOn(it.getDispensingTag(),
                DispensingUtils.DispensingTag.DISPENSING_TAG_HOSPITAL_RENEW_OLD_SHEET_FLAG)).collect(toList());

        if (CollectionUtils.isEmpty(dispensingSheetV2List)) {
            return clientRsp;
        }
        List<String> dispensingSheetV2IdList = dispensingSheetV2List.stream().map(DispensingSheetV2::getId).collect(toList());
        Map<String, DispensingSheetV2> dispensingSheetV2Map = loadAssembleDispenseSheet(clientReq.getChainId(), clientReq.getClinicId(), null, dispensingSheetV2IdList, null);
        Set<String> filterDispenseSheetIdSet = null;
        if (!CollectionUtils.isEmpty(clientReq.getAdviceExecuteIdList())) {
            filterDispenseSheetIdSet = new HashSet<>();
            Map<Long, List<String>> adviceExecuteIdToSheetIds =
                    dispensingFormV2Repository.findAllByDispensingSheetIdInAndClinicIdAndIsDeleted(
                                    dispensingSheetV2IdList,
                                    clientReq.getClinicId(),
                                    DispensingUtils.DeleteFlag.NOT_DELETED
                            ).stream()
                            .filter(item -> item.getAdviceExecuteId() != null)
                            .collect(Collectors.groupingBy(DispensingFormV2::getAdviceExecuteId,
                                    Collectors.mapping(DispensingFormV2::getDispensingSheetId, Collectors.toList())));
            for (Long adviceExecuteId : clientReq.getAdviceExecuteIdList()) {
                if (adviceExecuteIdToSheetIds.get(adviceExecuteId) != null) {
                    filterDispenseSheetIdSet.addAll(adviceExecuteIdToSheetIds.get(adviceExecuteId));
                }
            }

        }
        List<String> dispensingSheetIds = new ArrayList<>(dispensingSheetV2Map.keySet());
        Map<String, Long> dispensingSheetIdOrderIdMap = dispensingOrderSheetRelRepository.findAllByChainIdAndClinicIdAndDispenseSheetIdInAndIsDeleted(clientReq.getChainId(), clientReq.getClinicId(), dispensingSheetIds, DispensingUtils.DeleteFlag.NOT_DELETED)
                .stream()
                .filter(rel -> rel.getOrderId() != null)
                .collect(toMap(DispensingOrderSheetRel::getDispenseSheetId, DispensingOrderSheetRel::getOrderId, (a, b) -> a));

//        log.info("dispensingSheetV2Value = {}", JsonUtils.dump(dispensingSheetV2Map.values()));
        clientRsp.setRows(new ArrayList<>());
        for (String sheetId : dispensingSheetV2Map.keySet()) {
            if (filterDispenseSheetIdSet != null && !filterDispenseSheetIdSet.contains(sheetId)) {
                continue;
            }
            DispensingSheetV2 dispensingSheetV2 = dispensingSheetV2Map.get(sheetId);
            DispensingSheetView sheetView = DispensingSheetView.from(dispensingSheetV2);
            sheetView.setDispenseOrderId(Objects.toString(dispensingSheetIdOrderIdMap.get(sheetId), null));
            mergeFormItemViewStatus(sheetView, clientReq.getNeedMergeFormItemStatus());
            clientRsp.getRows().add(sheetView);
        }
        // 计算可发/可退数量
        if (clientReq.getTab() != null) {
            clientRsp.setRows(filterDispensingFormItem(clientRsp.getRows(), clientReq.getTab(), null, null, 1));
        }
        return clientRsp;
    }

    private void mergeFormItemViewStatus(DispensingSheetView dispensingSheetView, Boolean needMergeFormItemStatus) {
        if (dispensingSheetView == null) {
            return;
        }
        if (needMergeFormItemStatus == null || !needMergeFormItemStatus) {
            return;
        }
        int sheetStatus = dispensingSheetView.getStatus();
        if (sheetStatus == DispenseConst.Status.WAITING || sheetStatus == DispenseConst.Status.CLOSED) {
            // 待发或拒发不合并
            return;
        }
        dispensingSheetView.getDispensingForms().forEach(form -> {
            List<DispensingFormItemView> formItemListView = form.getDispensingFormItems();
            if (CollectionUtils.isEmpty(formItemListView)) {
                return;
            }
            Map<String, List<DispensingFormItemView>> sourceFormItemIdToItemView = formItemListView.stream().collect(groupingBy(DispensingFormItemView::getSourceFormItemId));
            List<DispensingFormItemView> newFormItemViewList = new ArrayList<>();
            Set<String> sourceFormItemIdSet = new HashSet<>();
            for (DispensingFormItemView itemView : formItemListView) {
                if (sourceFormItemIdSet.contains(itemView.getSourceFormItemId())) {
                    continue;
                }
                sourceFormItemIdSet.add(itemView.getSourceFormItemId());
                List<DispensingFormItemView> viewList = sourceFormItemIdToItemView.get(itemView.getSourceFormItemId());
                if (viewList.size() == 1) {
                    newFormItemViewList.add(itemView);
                } else {
                    List<DispensingFormItemView> filterItemViewList = viewList.stream().filter(it -> it.getStatus() != DispenseConst.Status.APPLY_UNDISPNSE && it.getStatus() != DispenseConst.Status.APPLY_UNDISPENSE_REJECT && it.getStatus() != DispenseConst.Status.REAPPLY_UNDISPENSE).collect(toList());
                    if (sheetStatus == DispenseConst.Status.UNDISPENSED) {
                        DispensingFormItemView dispensingFormItemView = filterItemViewList.stream().filter(it -> it.getStatus() == DispenseConst.Status.DISPENSED).findFirst().orElse(new DispensingFormItemView());
                        dispensingFormItemView.setStatus(DispenseConst.Status.UNDISPENSED);
                        newFormItemViewList.add(dispensingFormItemView);
                    } else {
                        // 非已退药状态下，要用已发-已退数量
                        DispensingFormItemView firstDispensingFormItem = filterItemViewList.get(0);
                        DispensingFormItemView finalDispensingFormItem = new DispensingFormItemView();
                        BeanUtils.copyProperties(firstDispensingFormItem, finalDispensingFormItem);
                        finalDispensingFormItem.setStatus(DispenseConst.Status.DISPENSED);

                        // 重新设置数量和状态
                        Set<Integer> allItemStatusList = new HashSet<>();
                        BigDecimal finalUnitCount = filterItemViewList.stream()
                                .filter(dispensingFormItem -> Objects.nonNull(dispensingFormItem.getUnitCount()))
                                .map(dispensingFormItem -> {
                                    allItemStatusList.add(dispensingFormItem.getStatus());
                                    if (DispensingUtils.isChineseMedicine(dispensingFormItem.getProductType(), dispensingFormItem.getProductSubType())) {
                                        // 中药按剂数退
                                        return dispensingFormItem.getUnitCount();
                                    }
                                    return Objects.equals(dispensingFormItem.getStatus(), DispenseConst.Status.UNDISPENSED) ? dispensingFormItem.getUnitCount().negate() : dispensingFormItem.getUnitCount();
                                })
                                .reduce(BigDecimal::add)
                                .orElse(null);
                        BigDecimal finalDoseCount = filterItemViewList.stream()
                                .filter(dispensingFormItem -> Objects.nonNull(dispensingFormItem.getDoseCount()))
                                .map(dispensingFormItem -> {
                                    if (!DispensingUtils.isChineseMedicine(dispensingFormItem.getProductType(), dispensingFormItem.getProductSubType())) {
                                        return Objects.equals(dispensingFormItem.getStatus(), DispenseConst.Status.UNDISPENSED) ? dispensingFormItem.getUnitCount().negate() : dispensingFormItem.getUnitCount();
                                    }
                                    return dispensingFormItem.getDoseCount();
                                })
                                .reduce(BigDecimal::add)
                                .orElse(BigDecimal.ONE);
                        // 设置数量
                        finalDispensingFormItem.setUnitCount(finalUnitCount);
                        finalDispensingFormItem.setDoseCount(finalDoseCount);
                        finalDispensingFormItem.setTotalCount(MathUtils.wrapBigDecimalMultiply(finalUnitCount, finalDoseCount));
                        if (MathUtils.wrapBigDecimalOrZero(finalDispensingFormItem.getTotalCount()).compareTo(BigDecimal.ZERO) == 0) {
                            // 数量为 0 为已退
                            finalDispensingFormItem.setStatus(DispenseConst.Status.UNDISPENSED);
                        } else {
                            // 设置状态
                            if (allItemStatusList.contains(DispenseConst.Status.DISPENSED)) {
                                finalDispensingFormItem.setStatus(DispenseConst.Status.DISPENSED);
                            } else if (allItemStatusList.contains(DispenseConst.Status.PART_DISPENSE)) {
                                finalDispensingFormItem.setStatus(DispenseConst.Status.PART_DISPENSE);
                            } else if (allItemStatusList.contains(DispenseConst.Status.RECORD_NOT_DISPENSE)) {
                                finalDispensingFormItem.setStatus(DispenseConst.Status.RECORD_NOT_DISPENSE);
                            } else if (allItemStatusList.contains(DispenseConst.Status.STOCK_NOT_DISPENSE)) {
                                finalDispensingFormItem.setStatus(DispenseConst.Status.STOCK_NOT_DISPENSE);
                            } else if (allItemStatusList.contains(DispenseConst.Status.SELF_PROVIDED)) {
                                finalDispensingFormItem.setStatus(DispenseConst.Status.SELF_PROVIDED);
                            }
                        }
                        newFormItemViewList.add(finalDispensingFormItem);
                    }
                }
            }

            form.setDispensingFormItems(newFormItemViewList);
        });
    }

    public void renewDispensingOrder(SendDispenseOrderToPharmacyReq clientReq) {
        List<Long> dispenseOrderIdList = clientReq.getList().stream().map(SendDispenseOrderToPharmacyReqItem::getDispenseOrderId).map(Long::parseLong).distinct().collect(toList());
        Map<Long, DispensingOrder> dispenseOrderIdToDispenseOrder = dispensingOrderRepository.findByChainIdAndClinicIdAndIdIn(
                        clientReq.getHeaderChainId(),
                        clientReq.getHeaderClinicId(),
                        dispenseOrderIdList)
                .stream().collect(toMap(DispensingOrder::getId, Function.identity(), (a, b) -> a));
        List<String> sheetIdList = clientReq.getList().stream().flatMap(it -> it.getDispensingSheetIdList().stream()).distinct().collect(toList());
        Map<String, DispensingSheetV2> sheetIdToSheetV2 = loadAssembleDispenseSheet(clientReq.getHeaderChainId(), clientReq.getHeaderClinicId(), null, sheetIdList, null);
        Map<String, DispensingOrderSheetRel> sheetIdToRel = dispensingOrderSheetRelRepository.findAllByChainIdAndClinicIdAndDispenseSheetIdInAndIsDeletedAndApplyType(clientReq.getHeaderChainId(),
                        clientReq.getHeaderClinicId(), sheetIdList, DispensingUtils.DeleteFlag.NOT_DELETED, DispensingOrder.ApplyType.UN_DISPENSE)
                .stream().collect(toMap(DispensingOrderSheetRel::getDispenseSheetId, Function.identity(), (a, b) -> a));
        // 查患者
        List<String> patientOrderIdList = sheetIdToSheetV2.values().stream().map(DispensingSheetV2::getPatientOrderId).distinct().collect(toList());
        CompletableFuture<Map<String, PatientOrderHospitalVO>> patientOrderHospitalVoFuture =
                getPatientOrderHospitalVoFuture(clientReq.getHeaderChainId(), clientReq.getHeaderClinicId(), patientOrderIdList);
        Map<String, PatientOrderHospitalVO> patientOrderIdToHospitalVo = patientOrderHospitalVoFuture.join();
        checkPatientSameWard(patientOrderIdToHospitalVo.values(), clientReq.getWardAreaId());
        String wardId = patientOrderIdToHospitalVo.values().stream().map(PatientOrderHospitalVO::getWardId).findFirst().orElse(null);

        // 查询医嘱
        Map<Long, AdviceMessage> adviceIdToAdviceMessage = new HashMap<>();
        Map<Long, AdviceExecuteMessage> adviceExecuteIdToAdviceExecuteMessage = new HashMap<>();
        Map<Long, AdviceUsageRuleItemsAddMessage> adviceUsageRuleIdToAddMessage = new HashMap<>();
        doLoadAdviceInfo(clientReq.getHeaderChainId(), clientReq.getHeaderClinicId(), sheetIdToSheetV2.values(),
                adviceIdToAdviceMessage, adviceExecuteIdToAdviceExecuteMessage, adviceUsageRuleIdToAddMessage);
        for (SendDispenseOrderToPharmacyReqItem orderReq : clientReq.getList()) {
            long orderId = Long.parseLong(orderReq.getDispenseOrderId());
            DispensingOrder dispensingOrder = dispenseOrderIdToDispenseOrder.get(orderId);
            if (dispensingOrder == null) {
                throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "未找到取药单");
            }
            for (String sheetId : orderReq.getDispensingSheetIdList()) {
                DispensingSheetV2 dispensingSheetV2 = sheetIdToSheetV2.get(sheetId);
                if (dispensingSheetV2 == null) {
                    throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "未找到发药单");
                }
                if (dispensingSheetV2.getStatus() != DispenseConst.Status.UNDISPENSED) {
                    throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "发药单状态错误,不支持重新发药");
                }
                if (!DispensingUtils.canReNewSheetSourceSheetTypeList.contains(dispensingSheetV2.getSourceSheetType())) {
                    throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR,
                            "患者记账项目暂不支持重新发药，请通过记账重新开出");
                }
                if (dispensingSheetV2.hasReNewSheet()) {
                    throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR,
                            "已重新发药发药单不可再发");
                }
                DispensingOrderSheetRel sheetRel = sheetIdToRel.get(sheetId);
                if (sheetRel == null) {
                    throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "未找到发药单");
                }
                if (!sheetRel.getOrderId().equals(orderId)) {
                    throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "领药单错误");
                }
                PatientOrderHospitalVO patientOrderHospitalVO = patientOrderIdToHospitalVo.get(dispensingSheetV2.getPatientOrderId());
                if (patientOrderHospitalVO == null || patientOrderHospitalVO.getStatus() >= HisPatientOrder.Status.WAIT_SETTLE) {
                    String patientName = "";
                    if (patientOrderHospitalVO != null && patientOrderHospitalVO.getPatient() != null) {
                        patientName = patientOrderHospitalVO.getPatient().getName();
                    }
                    throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR,
                            "【" + patientName + "】已出院，不可申请发药");
                }
                boolean adviceStatusIsInit = false;
                boolean adviceStatusIsUndo = false;
                boolean adviceExecuteNotExist = false;
                if (dispensingSheetV2.getSourceSheetType() == DispenseConst.SourceSheetType.ADVICE) {
                    AdviceMessage adviceMessage = adviceIdToAdviceMessage.get(dispensingSheetV2.getAdviceId());
                    if (adviceMessage == null || adviceMessage.getStatus() == AdviceStatus.INIT) {
                        adviceStatusIsInit = true;
                    } else if (adviceMessage.getStatus() == AdviceStatus.UNDONE || adviceMessage.getStatus() == AdviceStatus.UNDONE_CONFIRM) {
                        adviceStatusIsUndo = true;
                    }
                } else if (dispensingSheetV2.getSourceSheetType() != DispenseConst.SourceSheetType.USAGE_ASSOCIATION) {
                    DispensingFormV2 formV2 = dispensingSheetV2.getDispensingForms().get(0);
                    AdviceExecuteMessage adviceExecuteMessage = adviceExecuteIdToAdviceExecuteMessage.get(formV2.getAdviceExecuteId());
                    if (adviceExecuteMessage == null) {
                        //adviceStatusIsUndo = true;
                        adviceExecuteNotExist = true;
                    } else {
                        AdviceSimpleView adviceSimpleView = adviceExecuteMessage.getAdviceSimpleView();
                        if (adviceSimpleView == null || adviceSimpleView.getStatus() == AdviceStatus.INIT) {
                            adviceStatusIsInit = true;
                        } else if (adviceSimpleView.getStatus() == AdviceStatus.UNDONE || adviceSimpleView.getStatus() == AdviceStatus.UNDONE_CONFIRM) {
                            adviceStatusIsUndo = true;
                        }
                    }
                }
                if (adviceStatusIsInit) {
                    throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR,
                            "下达状态的医嘱请核对后再尝试重新发药");
                } else if (adviceExecuteNotExist) {
                    throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR,
                            "医嘱任务不存在不能重新申请发药");
                } else if (adviceStatusIsUndo) {
                    throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR,
                            "撤销状态的医嘱请重新下达新医嘱后再申请发药");
                }
            }
        }
        Map<Integer, GoodsPharmacyView> pharmacyNoToPharmacyView =
                scGoodsFeignClient.findPharmacyByClinic(clientReq.getHeaderChainId(),
                        clientReq.getHeaderClinicId()).stream().collect(toMap(GoodsPharmacyView::getNo, Function.identity(), (a, b) -> a));
        GoodsConfigView goodsConfig = scGoodsFeignClient.getGoodsConfig(clientReq.getHeaderClinicId());
        // 重新生成一个领药单
        Map<Integer, DispensingOrder> newDispensingOrderMap = new HashMap<>();
        for (SendDispenseOrderToPharmacyReqItem orderReq : clientReq.getList()) {
            DispensingOrderService orderServiceProxy = SpringUtils.getBean(DispensingOrderService.class);
            orderServiceProxy.doReNewDispensingSheet(orderReq, clientReq.getHeaderChainId(),
                    clientReq.getHeaderClinicId(), clientReq.getHeaderEmployeeId(),
                    dispenseOrderIdToDispenseOrder, sheetIdToSheetV2,
                    goodsConfig, pharmacyNoToPharmacyView, adviceIdToAdviceMessage,
                    adviceExecuteIdToAdviceExecuteMessage, clientReq.getIsUrgent(), adviceUsageRuleIdToAddMessage, wardId, newDispensingOrderMap);
        }
        dispensingOrderRepository.saveAll(newDispensingOrderMap.values());
        // 通知
        List<Integer> pharmacyNoList = dispenseOrderIdToDispenseOrder.values().stream().map(DispensingOrder::getPharmacyNo).distinct().collect(toList());
        doNoticeFront(clientReq.getHeaderChainId(), clientReq.getHeaderClinicId(), pharmacyNoList);
    }

    private void doLoadAdviceInfo(String chainId, String clinicId, Collection<DispensingSheetV2> sheetV2List,
                                  Map<Long, AdviceMessage> adviceIdToAdviceMessage,
                                  Map<Long, AdviceExecuteMessage> adviceExecuteIdToAdviceExecuteMessage,
                                  Map<Long, AdviceUsageRuleItemsAddMessage> adviceUsageRuleIdToAddMessage) {
        List<Long> adviceIdList = new ArrayList<>();
        List<Long> adviceExecuteIdList = new ArrayList<>();
        List<Long> adviceUsageRuleIdList = new ArrayList<>();
        sheetV2List.forEach(sheet -> {
            if (sheet.getSourceSheetId() == null) {
                return;
            }
            if (sheet.getSourceSheetType() == DispenseConst.SourceSheetType.ADVICE) {
                if (sheet.getAdviceId() == null) {
                    return;
                }
                adviceIdList.add(sheet.getAdviceId());
            } else if (sheet.getSourceSheetType() == DispenseConst.SourceSheetType.USAGE_ASSOCIATION) {
                if (!StringUtils.isEmpty(sheet.getSourceSheetId())) {
                    adviceUsageRuleIdList.add(Long.parseLong(sheet.getSourceSheetId()));
                }
            } else {
                sheet.getDispensingForms().forEach(form -> {
                    if (form.getAdviceExecuteId() == null) {
                        return;
                    }
                    adviceExecuteIdList.add(form.getAdviceExecuteId());
                });
            }
        });
        CompletableFuture<Map<Long, AdviceMessage>> adviceMessageFuture =
                ExecutorUtils.futureSupplyAsync(() -> hisAdviceService.batchQueryAdviceMessage(chainId, clinicId, adviceIdList));
        CompletableFuture<Map<Long, AdviceExecuteMessage>> adviceExecuteMessageFuture =
                ExecutorUtils.futureSupplyAsync(() -> hisAdviceService.batchQueryAdviceExecuteMessage(chainId, clinicId, adviceExecuteIdList));
        CompletableFuture<Map<Long, AdviceUsageRuleItemsAddMessage>> adviceUsageRuleItemsAddMessageFuture =
                ExecutorUtils.futureSupplyAsync(() -> hisAdviceService.batchQueryAdviceUsageRuleItemAddMessage(chainId, clinicId, adviceUsageRuleIdList));
        adviceIdToAdviceMessage.putAll(adviceMessageFuture.join());
        adviceExecuteIdToAdviceExecuteMessage.putAll(adviceExecuteMessageFuture.join());
        adviceUsageRuleIdToAddMessage.putAll(adviceUsageRuleItemsAddMessageFuture.join());
    }

    @Transactional(rollbackFor = Exception.class)
    public void doReNewDispensingSheet(SendDispenseOrderToPharmacyReqItem orderReq, String chainId,
                                       String clinicId, String employeeId,
                                       Map<Long, DispensingOrder> dispenseOrderIdToDispenseOrder,
                                       Map<String, DispensingSheetV2> sheetIdToSheetV2,
                                       GoodsConfigView goodsConfig,
                                       Map<Integer, GoodsPharmacyView> pharmacyNoToPharmacyView,
                                       Map<Long, AdviceMessage> adviceIdToAdviceMessage,
                                       Map<Long, AdviceExecuteMessage> adviceExecuteIdToAdviceExecuteMessage,
                                       int isUrgent,
                                       Map<Long, AdviceUsageRuleItemsAddMessage> adviceUsageRuleIdToAddMessage,
                                       String wardId,
                                       Map<Integer, DispensingOrder> newDispensingOrderMap
    ) {
        //根据药房号生成发药单，现在生成了多个。只需要生成新的发药单
        long orderId = Long.parseLong(orderReq.getDispenseOrderId());
        DispensingOrder dispensingOrder = dispenseOrderIdToDispenseOrder.get(orderId);
        DispensingOrder newOrder = newDispensingOrderMap.computeIfAbsent(dispensingOrder.getPharmacyNo(),
                k -> createRenewDispensingOrder(dispensingOrder, isUrgent, wardId));
        List<DispensingOrderSheetRel> orderSheetRelList = new ArrayList<>();
        List<DispensingSheetV2> newDispensingSheetList = new ArrayList<>();
        List<DispensingSheetV2> updageOldSheetList = new ArrayList<>();
        for (String sheetId : orderReq.getDispensingSheetIdList()) {
            DispensingSheetV2 dispensingSheetV2 = sheetIdToSheetV2.get(sheetId);
            dispensingSheetV2.setDispensingTag(DispensingUtils.onFlag(dispensingSheetV2.getDispensingTag(), DispensingUtils.DispensingTag.DISPENSING_TAG_HOSPITAL_RENEW_OLD_SHEET_FLAG));
            FillUtils.fillLastModifiedBy(dispensingSheetV2, employeeId);
            updageOldSheetList.add(dispensingSheetV2);
            DispensingSheetV2 newSheet = new DispensingSheetV2();
            BeanUtils.copyProperties(dispensingSheetV2, newSheet);
            newSheet.setId(abcIdGenerator.getUUID());
            newSheet.setStatus(DispenseConst.Status.WAITING);
            newSheet.setDispensingTag(0);
            newSheet.setDispensingSheetInfo(null);
            newSheet.setOrderByDate(Instant.now());
            newSheet.setDispensingForms(new ArrayList<>());
            newSheet.setV1Id(wardId);
            FillUtils.fillCreatedBy(newSheet, employeeId);
            newDispensingSheetList.add(newSheet);
            DispensingOrderSheetRel sheetRel = new DispensingOrderSheetRel();
            sheetRel.setId(abcIdGenerator.getUIDLong());
            sheetRel.setChainId(chainId);
            sheetRel.setClinicId(clinicId);
            sheetRel.setOrderId(newOrder.getId());
            sheetRel.setDispenseSheetId(newSheet.getId());
            sheetRel.setApplyType(DispensingOrder.ApplyType.DISPENSE);
            FillUtils.fillCreatedBy(sheetRel, employeeId);
            orderSheetRelList.add(sheetRel);
            SheetOperationBase operationBase = sheetOperationCreateFactory.createSheetOperation(DispensingSheetOperation.OperationType.CREATED_DISPENSING_SHEET, newSheet.getId(), employeeId, employeeId, newSheet.getChainId(), newSheet.getClinicId());
            operationBase.createAndSaveSheetOperation();
            dispensingSheetV2.getDispensingForms().forEach(form -> {
                DispensingFormV2 formV2 = new DispensingFormV2();
                BeanUtils.copyProperties(form, formV2);
                formV2.setId(abcIdGenerator.getUUID());
                formV2.setDispensingSheetId(newSheet.getId());
                FillUtils.fillCreatedBy(formV2, employeeId);
                formV2.setDispensingFormItems(new ArrayList<>());
                form.getDispensingFormItems().forEach(item -> {
                    if (item.getStatus() != DispenseConst.Status.DISPENSED && item.getStatus() != DispenseConst.Status.APPLY_DISPENSE_REJECT) {
                        return;
                    }
                    DispensingFormItemV2 itemV2 = new DispensingFormItemV2();
                    BeanUtils.copyProperties(item, itemV2);
                    itemV2.setId(abcIdGenerator.getUUID());
                    itemV2.setDispensingFormId(formV2.getId());
                    itemV2.setDispensingSheetId(newSheet.getId());
                    itemV2.setStatus(DispenseConst.Status.WAITING);
                    if (itemV2.isSelfProvidedBySourceItemType()) {
                        // 自备药修改状态，不需发药
                        itemV2.setStatus(DispenseConst.Status.SELF_PROVIDED);
                    }
                    FillUtils.fillCreatedBy(itemV2, employeeId);
                    formV2.getDispensingFormItems().add(itemV2);
                });
                newSheet.getDispensingForms().add(formV2);
            });
        }
        doLoadGoodsAndUpdateSheet(chainId, clinicId, newDispensingSheetList, goodsConfig, pharmacyNoToPharmacyView);
        goodsLockingFeignClient.hospitalTryLockGoodsStock(chainId, clinicId, employeeId, newDispensingSheetList);

        dispensingSheetEntityService.saveAll(newDispensingSheetList);
//        dispensingOrderRepository.save(newOrder);
        if (!CollectionUtils.isEmpty(orderSheetRelList)) {
            dispensingOrderSheetRelRepository.saveAll(orderSheetRelList);
        }
        if (!CollectionUtils.isEmpty(updageOldSheetList)) {
            dispensingSheetEntityService.saveAll(updageOldSheetList);
        }
        newOrder.getDispensingSheetV2List().addAll(newDispensingSheetList);
        // 通知
        doSendDispensingSheetToHisCharge(newDispensingSheetList, adviceIdToAdviceMessage,
                adviceExecuteIdToAdviceExecuteMessage, goodsConfig, employeeId, adviceUsageRuleIdToAddMessage);
        //doNoticeFront(chainId, clinicId);
    }

    private void doSendDispensingSheetToHisCharge(List<DispensingSheetV2> dispensingSheetV2List,
                                                  Map<Long, AdviceMessage> adviceIdToAdviceMessage,
                                                  Map<Long, AdviceExecuteMessage> adviceExecuteIdToAdviceExecuteMessage,
                                                  GoodsConfigView goodsConfigView,
                                                  String operatorId,
                                                  Map<Long, AdviceUsageRuleItemsAddMessage> adviceUsageRuleIdToAddMessage) {
        if (CollectionUtils.isEmpty(dispensingSheetV2List)) {
            return;
        }
        RocketMqProducer.doAfterTransactionCommit(() -> {
            for (DispensingSheetV2 dispensingSheet : dispensingSheetV2List) {
                AdviceMessage adviceMessage = null;
                AdviceExecuteMessage adviceExecuteMessage = null;
                AdviceUsageRuleItemsAddMessage adviceUsageRuleItemsAddMessage = null;
                if (dispensingSheet.getSourceSheetType() == DispenseConst.SourceSheetType.ADVICE) {
                    adviceMessage = adviceIdToAdviceMessage.get(dispensingSheet.getAdviceId());
                } else if (dispensingSheet.getSourceSheetType() == DispenseConst.SourceSheetType.USAGE_ASSOCIATION) {
                    if (!StringUtils.isEmpty(dispensingSheet.getSourceSheetId())) {
                        adviceUsageRuleItemsAddMessage = adviceUsageRuleIdToAddMessage.get(Long.parseLong(dispensingSheet.getSourceSheetId()));
                    }
                } else {
                    if (!CollectionUtils.isEmpty(dispensingSheet.getDispensingForms())) {
                        adviceExecuteMessage = adviceExecuteIdToAdviceExecuteMessage.get(dispensingSheet.getDispensingForms().get(0).getAdviceExecuteId());
                    }
                }
                rocketMqProducer.sendDispensingSheetMessage(dispensingSheet, DispensingSheetMessage.MSG_TYPE_CREATE,
                        operatorId, adviceMessage, adviceExecuteMessage, goodsConfigView, adviceUsageRuleItemsAddMessage);
            }
        });
    }

    private void doNoticeFront(String chainId, String clinicId, List<Integer> pharmacyNoList) {
        List<String> noticeFrontParams = DispensingEventGroupGenerateUtils.generateEventParams(clinicId, SubscribeMessage.EventGroup.DISPENSING_QL);
        messageService.sendSubscribeMessageAfterCommitAsync(noticeFrontParams, MessageService.DispensingQlEvent.NURSE_DISPENSING_ORDER_CHANGE);
        messageService.sendSubscribeMessageAfterCommitAsync(noticeFrontParams, MessageService.DispensingQlEvent.PHARMACY_DISPENSING_ORDER_CHANGE);
        messageService.sendSubscribeMessageOfDispensingOrder(clinicId, MessageService.DispensingQlEvent.PHARMACY_DISPENSING_ORDER_DISPENSE, pharmacyNoList);
        RocketMqProducer.doAfterTransactionCommit(() -> dispensingTodoService.sendHospitalPharmacyTodoCountTask(chainId, clinicId));
    }

    private DispensingOrder createRenewDispensingOrder(DispensingOrder oldOrder, int isUrgent, String wardId) {
        DispensingOrder dispensingOrder = new DispensingOrder();
        dispensingOrder.setId(abcIdGenerator.getUIDLong());
        dispensingOrder.setChainId(oldOrder.getChainId());
        dispensingOrder.setClinicId(oldOrder.getClinicId());
        dispensingOrder.setDepartmentId(oldOrder.getDepartmentId());
        dispensingOrder.setPharmacyNo(oldOrder.getPharmacyNo());
        dispensingOrder.setPharmacyType(oldOrder.getPharmacyType());
        dispensingOrder.setApplyType(DispensingOrder.ApplyType.DISPENSE);
        dispensingOrder.setEmergencyStatus(isUrgent == YesOrNo.YES ? DispensingOrder.EmergencyStatus.EMERGENCY : DispensingOrder.EmergencyStatus.NORMAL);
        dispensingOrder.setStatus(DispensingOrder.Status.DISPENSING);
        dispensingOrder.setApplyDispenseTime(Instant.now());
        dispensingOrder.setApplyUndispenseTime(null);
        String currentName = DateUtils.formatLocalDateTime(LocalDateTime.now(), DateUtils.sFormatterHourMin);
        dispensingOrder.setName(currentName);
        // 新的发药申请单可能来多个不同的申请单
//        dispensingOrder.setSourceOrderId(dispensingOrder.getId());
        dispensingOrder.setIsDeleted(YesOrNo.NO);
        dispensingOrder.setWardAreaId(wardId != null ? Long.parseLong(wardId) : null);
        FillUtils.fillCreatedBy(dispensingOrder, DispensingConstants.ANONYMOUS_OPERATOR_ID);
        return dispensingOrder;
    }

    protected void doLoadGoodsAndUpdateSheet(String chainId, String clinicId,
                                             List<DispensingSheetV2> sheetV2List,
                                             GoodsConfigView goodsConfig,
                                             Map<Integer, GoodsPharmacyView> pharmacyNoToPharmacyView) {
        List<QueryGoodsInPharmacyByIdsAndStockCountReq.QueryPharmacyGoodsReq> queryGoodsReqs =
                sheetV2List.stream().flatMap(it -> it.getDispensingForms().stream())
                        .filter(dispensingForm -> !CollectionUtils.isEmpty(dispensingForm.getDispensingFormItems()))
                        .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream())
                        .filter(it -> it.getStatus() == DispenseConst.Status.WAITING)
                        .collect(Collectors.groupingBy(DispensingFormItemV2::getPharmacyNo,
                                Collectors.mapping(dispensingFormItem -> {
                                    QueryGoodsInPharmacyByIdsAndStockCountReq.QueryGoodsWithStock queryGoodsWithStock = new QueryGoodsInPharmacyByIdsAndStockCountReq.QueryGoodsWithStock();
                                    queryGoodsWithStock.setGoodsId(dispensingFormItem.getProductId());
                                    queryGoodsWithStock.setKeyId(dispensingFormItem.getId()); //每个formItem的返回都不一样
                                    if (goodsConfig.getOpenLock() == 1) {
                                        BigDecimal totalCount = cn.abcyun.cis.dispensing.util.MathUtils.calculateTotalCount(dispensingFormItem.getUnitCount(), dispensingFormItem.getDoseCount());
                                        if (dispensingFormItem.getUseDismounting() == 1) {
                                            queryGoodsWithStock.setPieceCount(totalCount);
                                        } else {
                                            queryGoodsWithStock.setPackageCount(totalCount);
                                        }
                                    }
                                    return queryGoodsWithStock;
                                }, Collectors.toList()))
                        )
                        .entrySet()
                        .stream()
                        .map(pharmacyNoToQueryGoodsWithStockListEntry -> {
                            QueryGoodsInPharmacyByIdsAndStockCountReq.QueryPharmacyGoodsReq req = new QueryGoodsInPharmacyByIdsAndStockCountReq.QueryPharmacyGoodsReq();
                            req.setList(pharmacyNoToQueryGoodsWithStockListEntry.getValue());
                            req.setPharmacyNo(pharmacyNoToQueryGoodsWithStockListEntry.getKey());
                            return req;
                        })
                        .collect(Collectors.toList());

        List<QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp> queryPharmacyGoodsRsps = scGoodsFeignClient.queryGoodsInPharmacyByIds(clinicId, chainId, true, 1, queryGoodsReqs);
        Map<String, GoodsItem> keyIdToGoodsItem = new HashMap<>();
        for (QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp queryPharmacyGoodsRsp : queryPharmacyGoodsRsps) {
            for (GoodsItem goodsItem : queryPharmacyGoodsRsp.getList()) {
                keyIdToGoodsItem.put(goodsItem.getKeyId(), goodsItem);
            }
        }
        Map<String, GoodsItem> formItemIdToGoodsItem = new HashMap<>();
        for (DispensingSheetV2 sheetV2 : sheetV2List) {
            sheetV2.getDispensingForms().forEach(dispensingForm -> {
                dispensingForm.getDispensingFormItems().forEach(dispensingFormItem -> {
                    dispensingFormItem.initPharmacyDispenseFlag(pharmacyNoToPharmacyView);
                    formItemIdToGoodsItem.put(dispensingFormItem.getId(), keyIdToGoodsItem.get(dispensingFormItem.getId()));
                });
            });
            sheetV2.bindProductInfo(formItemIdToGoodsItem);
            sheetV2.tagDispensingSheet();
        }
    }

    /**
     * 护士向药房发起发药申请
     * 药房能看到发药单了
     */
    public OpsCommonRsp sendDispensingOrderToPharmacy(SendDispenseOrderToPharmacyReq pharmacyReq) throws ServiceInternalException {
        OpsCommonRsp clientRsp = new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
        //请求的取药单ID列表
        List<Long> dispenseOrderIdList = pharmacyReq.getList().stream().map(dispenseOrderToPharmacyReqItem -> Long.parseLong(dispenseOrderToPharmacyReqItem.getDispenseOrderId())).collect(toList());
        Map<Long, DispensingOrder> dispenseOrderIdToDispenseOrder = dispensingOrderRepository.findByChainIdAndClinicIdAndIdIn(
                        pharmacyReq.getHeaderChainId(),
                        pharmacyReq.getHeaderClinicId(),
                        dispenseOrderIdList)
                .stream().collect(toMap(DispensingOrder::getId, Function.identity(), (a, b) -> a));

        //每个取药单关联的发药单 输入输出参数
        List<DispensingOrderSheetRel> orderSheetRelList = dispensingOrderSheetRelRepository.findAllByChainIdAndClinicIdAndOrderIdInAndIsDeleted(
                pharmacyReq.getHeaderChainId(),
                pharmacyReq.getHeaderClinicId(),
                dispenseOrderIdList,
                DispensingUtils.DeleteFlag.NOT_DELETED);
        Map<Long, List<DispensingOrderSheetRel>> dispenseOrderIdToRelList = orderSheetRelList.stream()
                .collect(groupingBy(DispensingOrderSheetRel::getOrderId));
        //加载 发药单
        Map<String, DispensingSheetV2> dispensingSheetIdToSheet = loadAssembleDispenseSheet(pharmacyReq.getHeaderChainId(),
                pharmacyReq.getHeaderClinicId(),
                null,
                orderSheetRelList.stream().map(DispensingOrderSheetRel::getDispenseSheetId).collect(toList()),
                null);

        //获取住院单信息
        List<String> patientOrderIds = pharmacyReq.getList().stream()
                .filter(item -> item != null && !CollectionUtils.isEmpty(item.getDispensingSheetIdList()))
                .flatMap(item -> item.getDispensingSheetIdList().stream())
                .filter(dispensingSheetIdToSheet::containsKey)
                .map(item -> dispensingSheetIdToSheet.get(item).getPatientOrderId())
                .distinct()
                .collect(toList());
        Map<String, PatientOrderHospitalVO> patientOrderIdToPatientOrder = patientOrderService.findPatientOrderListHospital(pharmacyReq.getHeaderChainId(),
                pharmacyReq.getHeaderClinicId(), patientOrderIds);
        checkPatientSameWard(patientOrderIdToPatientOrder.values(), pharmacyReq.getWardAreaId());

        //取药单所有的发药单塞进去
        for (DispensingOrderSheetRel rel : orderSheetRelList) {
            DispensingOrder dispensingOrder = dispenseOrderIdToDispenseOrder.get(rel.getOrderId());
            DispensingSheetV2 dispensingSheet = dispensingSheetIdToSheet.get(rel.getDispenseSheetId());
            if (dispensingSheet != null && dispensingOrder != null) {
                dispensingOrder.getDispensingSheetV2List().add(dispensingSheet);
            }
        }

        /**
         * 医嘱任务发药,设置不发药可执行，然后执行医嘱任务，撤销医嘱，因为医嘱任务已执行，不会删除任务，未发药的发药单也不会被删除，导致出院时有
         * 未完结事项，去申请发药，但是医嘱任务是撤销，申请不了，流程就卡住了，所以这里去掉校验，由发药校验，发药的时候拒发
         */
        //List<Long> adviceIdList = dispensingSheetIdToSheet.values().stream().filter(it -> it.getAdviceId() != null).map(DispensingSheetV2::getAdviceId).distinct().collect(toList());
        //CompletableFuture<Map<Long, AdviceDetailView>> adviceDetailViewFuture = getAdviceDetailViewFuture(pharmacyReq.getHeaderChainId(), pharmacyReq.getHeaderClinicId(), adviceIdList);
        //Map<Long, AdviceDetailView> adviceIdToAdviceDetailView = adviceDetailViewFuture.join();
        //
        //for (SendDispenseOrderToPharmacyReqItem pharmacyReqItem : pharmacyReq.getList()) {
        //    //取药单状态检查
        //    DispensingOrder dispensingOrder =
        //            dispenseOrderIdToDispenseOrder.get(Long.parseLong(pharmacyReqItem.getDispenseOrderId()));
        //    if (dispensingOrder == null) {
        //        throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "未找到取药单");
        //    }
        //    if (!dispensingOrder.canApplyDispense()) {
        //        throw new DispensingServiceException(DispensingServiceError.DISPENSING_ORDER_CANNOT_APPLY_UNDISPENSE, "已申请不可再申请");
        //    }
        //    if (CollectionUtils.isEmpty(pharmacyReqItem.getDispensingSheetIdList())) {
        //        continue;
        //    }
        //    for (String sheetId : pharmacyReqItem.getDispensingSheetIdList()) {
        //        DispensingSheetV2 dispensingSheetV2 = dispensingSheetIdToSheet.get(sheetId);
        //        if (dispensingSheetV2 == null) {
        //            continue;
        //        }
        //        if (dispensingSheetV2.isSelfProvided()) {
        //            throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "自备药不可申请发药");
        //        }
        //        if (dispensingSheetV2.getAdviceId() == null) {
        //            continue;
        //        }
        //        AdviceDetailView adviceDetailView = adviceIdToAdviceDetailView.get(dispensingSheetV2.getAdviceId());
        //        if (adviceDetailView != null && DispensingUtils.canNotDispenseAdviceStatusList.contains(adviceDetailView.getStatus())) {
        //            throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR,
        //                    "医嘱【" + adviceDetailView.getName() + "】状态为下达或撤销不可申请发药");
        //        }
        //    }
        //}

        Long wardAreaId = patientOrderIdToPatientOrder.values().stream().map(PatientOrderHospitalVO::getWardId).filter(item -> !StringUtils.isEmpty(item))
                .findFirst().map(Long::parseLong).orElse(null);

        //加急发药单
        DispensingOrder dispensingOrder = null;
        List<DispensingOrderLog> createDispensingOrderLogList = new ArrayList<>();
        List<Integer> pharmacyNoList = new ArrayList<>();
        for (SendDispenseOrderToPharmacyReqItem pharmacyReqItem : pharmacyReq.getList()) {
            //取药单状态检查
            dispensingOrder = dispenseOrderIdToDispenseOrder.get(Long.parseLong(pharmacyReqItem.getDispenseOrderId()));
            if (dispensingOrder == null) {
                throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "未找到取药单");
            }
            if (!dispensingOrder.canApplyDispense()) {
                throw new DispensingServiceException(DispensingServiceError.DISPENSING_ORDER_CANNOT_APPLY_UNDISPENSE, "已申请不可再申请");
            }
            sendOneDispensingOrderToPharmacy(
                    pharmacyReq.getHeaderChainId(),
                    pharmacyReq.getHeaderClinicId(),
                    pharmacyReq.getHeaderEmployeeId(),
                    pharmacyReq.getDispensedById(),
                    dispensingOrder,
                    pharmacyReqItem,
                    dispensingSheetIdToSheet,
                    createDispensingOrderLogList,
                    dispenseOrderIdToRelList,
                    pharmacyReq.getIsUrgent() == DispensingUtils.SwitchFlag.ON,
                    wardAreaId
            );
            pharmacyNoList.add(dispensingOrder.getPharmacyNo());
        }

        // 一起提交 提高效率
        if (!CollectionUtils.isEmpty(createDispensingOrderLogList)) {
            dispensingOrderLogRepository.saveAll(createDispensingOrderLogList);
        }
        if (dispenseOrderIdToRelList.values().stream().flatMap(dispensingOrderSheetRels -> dispensingOrderSheetRels.stream()).count() > 0) {
            dispensingOrderSheetRelRepository.saveAll(dispenseOrderIdToRelList.values().stream().flatMap(dispensingOrderSheetRels -> dispensingOrderSheetRels.stream()).collect(toList()));
        }
        List<String> noticeFrontParams = DispensingEventGroupGenerateUtils.generateEventParams(pharmacyReq.getHeaderClinicId(), SubscribeMessage.EventGroup.DISPENSING_QL);
        messageService.sendSubscribeMessageAfterCommitAsync(noticeFrontParams, MessageService.DispensingQlEvent.NURSE_DISPENSING_ORDER_CHANGE);
        messageService.sendSubscribeMessageAfterCommitAsync(noticeFrontParams, MessageService.DispensingQlEvent.PHARMACY_DISPENSING_ORDER_CHANGE);
        messageService.sendSubscribeMessageOfDispensingOrder(pharmacyReq.getHeaderClinicId(),
                MessageService.DispensingQlEvent.PHARMACY_DISPENSING_ORDER_DISPENSE, pharmacyNoList);
        RocketMqProducer.doAfterTransactionCommit(() -> dispensingTodoService.sendHospitalPharmacyTodoCountTask(pharmacyReq.getHeaderChainId(), pharmacyReq.getHeaderClinicId()));
        return clientRsp;
    }

    /***
     * 护士把取药单 或者 取药单里面的部分发药单 发送到药房准备发药
     * */
    public boolean sendOneDispensingOrderToPharmacy(String chainId,
                                                    String clinicId,
                                                    String employeeId,//当前登录人
                                                    String operatorId, //发药人
                                                    DispensingOrder dispensingOrder,
                                                    SendDispenseOrderToPharmacyReqItem dispenseOrderToPharmacyReqItem,
                                                    Map<String, DispensingSheetV2> dispensingSheetIdToSheet,
                                                    List<DispensingOrderLog> createDispensingOrderLogList, //输出
                                                    Map<Long, List<DispensingOrderSheetRel>> dispenseOrderIdToRelList, // 输入输出
                                                    boolean isUrgent,
                                                    Long wardAreaId
    ) throws ServiceInternalException {
        //没有指定要发送哪些发药单，全部送到药房 正常发药
//        if (dispenseOrderToPharmacyReqItem.isWholeOrderSend()) {
//            dispensingOrder.setStatus(DispensingOrder.Status.DISPENSING);
//            dispensingOrder.setApplyDispenseTime(Instant.now());
//            FillUtils.fillLastModifiedBy(dispensingOrder, employeeId);
//            createDispensingOrderLogList.add(createDispensingOrderLog(chainId, dispensingOrder.getId(), DispensingOrderLog.Action.SEND_TO_PHARMACY, null, employeeId));
//            sLogger.info("取药单 dispenseOrderId={},发送到药房", dispensingOrder.getId());
//            return true;
//        }

        // 走到这里 按紧急发药处理 统一生成一个新的领药单
        // 发药单是否要生成新的领药单，消息生成的时候已经确定了
        DispensingOrder emergencyDispensingOrder = null;

        //输入输出参数
        List<DispensingOrderSheetRel> orderSheetRelList = dispenseOrderIdToRelList.computeIfAbsent(dispensingOrder.getId(), k -> new ArrayList<>());
        int originSheetCount = orderSheetRelList.size();
        Map<String, DispensingOrderSheetRel> dispensingSheetIdToRel = orderSheetRelList.stream().collect(toMap(DispensingOrderSheetRel::getDispenseSheetId, Function.identity(), (a, b) -> a));

        DispensingOrderLog.LogDetail logDetail = null;
        if (dispenseOrderToPharmacyReqItem.getDispensingSheetIdList().size() > 0) {
            logDetail = new DispensingOrderLog.LogDetail();
            logDetail.setSummery(isUrgent ? "加急" : "申请" + dispenseOrderToPharmacyReqItem.getDispensingSheetIdList().size() + "个发药单");
        }
        List<DispensingSheetV2> updateSheets = new ArrayList<>();
        // 申请发药数量
        List<String> delSheetRelIdList = new ArrayList<>();
        //部分加急
        for (String sheetId : dispenseOrderToPharmacyReqItem.getDispensingSheetIdList()) {
            if (!dispensingSheetIdToSheet.containsKey(sheetId)) {
                continue;
            }
            DispensingSheetV2 dispensingSheetV2 = dispensingSheetIdToSheet.get(sheetId);
            if (dispensingSheetV2.isSelfProvided()) {
                throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "自备药不可申请发药");
            }

            if (wardAreaId != null && !org.apache.commons.lang.StringUtils.equals(String.valueOf(wardAreaId), dispensingSheetV2.getV1Id())) {
                dispensingSheetV2.setV1Id(String.valueOf(wardAreaId));
                FillUtils.fillLastModifiedBy(dispensingSheetV2, operatorId);
                updateSheets.add(dispensingSheetV2);
            }

            //先从老的里面移除
            DispensingOrderSheetRel oldSheetRel = dispensingSheetIdToRel.get(sheetId);
            if (oldSheetRel != null) {
                oldSheetRel.setIsDeleted(DispensingUtils.DeleteFlag.DELETED);
                FillUtils.fillLastModifiedBy(oldSheetRel, employeeId);
                delSheetRelIdList.add(sheetId);
            }
            if (emergencyDispensingOrder == null) {
                emergencyDispensingOrder = dispensingConfigService.createEmergencyDispensingOrder(dispensingOrder.getId(), chainId, clinicId, dispensingOrder.getDepartmentId(), wardAreaId, dispensingOrder.getPharmacyType(), dispensingOrder.getPharmacyNo());
                emergencyDispensingOrder.setEmergencyStatus(isUrgent ? DispensingOrder.EmergencyStatus.EMERGENCY : DispensingOrder.EmergencyStatus.NORMAL);
                createDispensingOrderLogList.add(createDispensingOrderLog(chainId, emergencyDispensingOrder.getId(), DispensingOrderLog.Action.CREATE, null, employeeId));
                sLogger.info("新生成取药单 dispenseOrderId={},emergencyDispensingOrderId={}", dispensingOrder.getId(), emergencyDispensingOrder.getId());
            }
            //加入新的 取药单
            DispensingOrderSheetRel newRel = new DispensingOrderSheetRel();
            newRel.setId(abcIdGenerator.getUIDLong());
            newRel.setChainId(chainId);
            newRel.setClinicId(clinicId);
            newRel.setOrderId(emergencyDispensingOrder.getId());
            newRel.setDispenseSheetId(sheetId);
            newRel.setIsDeleted(DispensingUtils.DeleteFlag.NOT_DELETED);
            newRel.setApplyType(DispensingOrder.ApplyType.DISPENSE);
            FillUtils.fillCreatedBy(newRel, employeeId);
            orderSheetRelList.add(newRel);
        }
        if (delSheetRelIdList.size() == originSheetCount
                && (dispensingOrder.getStatus() == DispensingOrder.Status.NORMAL || dispensingOrder.getStatus() == DispensingOrder.Status.NO_ORDER_CONFIG)) {
            log.info("领药单全部申请完，删除原领药单，dispensingOrderId={}", dispensingOrder.getId());
            dispensingOrder.setIsDeleted(DispensingUtils.DeleteFlag.DELETED);
            FillUtils.fillLastModifiedBy(dispensingOrder, employeeId);
        }

        if (!CollectionUtils.isEmpty(updateSheets)) {
            dispensingSheetV2Repository.saveAll(updateSheets);
        }

        if (emergencyDispensingOrder != null) {
            emergencyDispensingOrder.setStatus(DispensingOrder.Status.DISPENSING);
            emergencyDispensingOrder.setApplyDispenseTime(Instant.now());
            sLogger.info("取药单 emergencyDispensingOrder={},发送到药房", emergencyDispensingOrder.getId());
            dispensingOrderRepository.save(emergencyDispensingOrder);
            createDispensingOrderLogList.add(createDispensingOrderLog(chainId, emergencyDispensingOrder.getId(),
                    isUrgent ? DispensingOrderLog.Action.EMERGENCY : DispensingOrderLog.Action.SEND_TO_PHARMACY, JsonUtils.dumpAsJsonNode(logDetail), employeeId));
            //createDispensingOrderLogList.add(createDispensingOrderLog(chainId, dispensingOrder.getId(),
            //        isUrgent ? DispensingOrderLog.Action.EMERGENCY : DispensingOrderLog.Action.SEND_TO_PHARMACY, JsonUtils.dumpAsJsonNode(logDetail), employeeId));
        }
        return true;
    }

    /**
     * 护士向药房发起退药药申请
     * 药房能看到退药单了
     * 这里主要是状态检查 并流转转对应状态
     */
    public OpsCommonRsp sendUnDispensingOrderToPharmacy(DispenseDispenseOrderReq unPharmacyReq) throws ServiceInternalException {
        OpsCommonRsp clientRsp = new OpsCommonRsp(OpsCommonRsp.SUCC, "sucess");
        //加载 取药单
        List<Long> dispenseOrderIdList = unPharmacyReq.getDispenseOrderReqs().stream().map(sendToPharmacyUnDispenseSheet -> Long.parseLong(sendToPharmacyUnDispenseSheet.getId())).distinct().collect(toList());
        Map<Long, DispensingOrder> dispenseOrderIdToDispenseOrder = dispensingOrderRepository.findByChainIdAndClinicIdAndIdIn(
                        unPharmacyReq.getHeaderChainId(),
                        unPharmacyReq.getHeaderClinicId(),
                        dispenseOrderIdList)
                .stream().collect(toMap(DispensingOrder::getId, Function.identity(), (a, b) -> a));
        List<DispensingOrderSheetRel> orderSheetRelList = dispensingOrderSheetRelRepository.findAllByChainIdAndClinicIdAndOrderIdInAndIsDeleted(unPharmacyReq.getHeaderChainId(), unPharmacyReq.getHeaderClinicId(),
                dispenseOrderIdList, DispensingUtils.DeleteFlag.NOT_DELETED);
        if (CollectionUtils.isEmpty(orderSheetRelList)) {
            return clientRsp;
        }
        List<String> dispensingSheetIdList = orderSheetRelList.stream().map(DispensingOrderSheetRel::getDispenseSheetId).collect(toList());
        List<DispensingSheetV2> sndMessageDispenseList = new ArrayList<>();

        //加载领药单关联的所有发药单
        Map<String, DispensingSheetV2> dispensingSheetIdToSheet = loadAssembleDispenseSheet(unPharmacyReq.getHeaderChainId(), unPharmacyReq.getHeaderClinicId(), null, dispensingSheetIdList, null);
        List<String> reqSheetIdList = new ArrayList<>();
        Set<String> patientOrderIdSet = new HashSet<>();
        //if (unPharmacyReq.isFromPharmacy()) {
        reqSheetIdList = unPharmacyReq.getDispenseOrderReqs().stream()
                .flatMap(dispenseOrderReq -> dispenseOrderReq.getDispenseSheetReqs().stream()).map(DispenseDispenseOrderReq.DispenseSheetReq::getId).filter(Objects::nonNull).distinct().collect(toList());
        //}

        //关联领药申请单和发药sheet
        for (DispensingOrderSheetRel rel : orderSheetRelList) {
            DispensingOrder dispensingOrder = dispenseOrderIdToDispenseOrder.get(rel.getOrderId());
            DispensingSheetV2 dispensingSheet = dispensingSheetIdToSheet.get(rel.getDispenseSheetId());
            if (dispensingSheet != null && dispensingOrder != null) {
                dispensingOrder.getDispensingSheetV2List().add(dispensingSheet);
                if (reqSheetIdList.contains(dispensingSheet.getId())) {
                    patientOrderIdSet.add(dispensingSheet.getPatientOrderId());
                }
            }
        }

        //获取住院单信息
        CompletableFuture<Map<String, PatientOrderHospitalVO>> patientOrderHospitalVoFuture = getPatientOrderHospitalVoFuture(unPharmacyReq.getHeaderChainId(), unPharmacyReq.getHeaderClinicId(), new ArrayList<>(patientOrderIdSet));
        Map<String, PatientOrderHospitalVO> patientOrderIdToHospitalVo = patientOrderHospitalVoFuture.join();
        if (!unPharmacyReq.isFromPharmacy() && !CollectionUtils.isEmpty(unPharmacyReq.getDispenseOrderReqs())) {
            checkPatientSameWard(unPharmacyReq.getDispenseOrderReqs().stream()
                    .filter(item -> item != null && !CollectionUtils.isEmpty(item.getDispenseSheetReqs()))
                    .flatMap(item -> item.getDispenseSheetReqs().stream())
                    .map(dispenseSheetReq -> {
                        DispensingSheetV2 dispensingSheetV2 = dispensingSheetIdToSheet.get(dispenseSheetReq.getId());
                        if (dispensingSheetV2 != null) {
                            return dispensingSheetV2.getPatientOrderId();
                        }
                        return null;
                    })
                    .filter(item -> !StringUtils.isEmpty(item))
                    .distinct()
                    .map(patientOrderIdToHospitalVo::get)
                    .filter(Objects::nonNull)
                    .collect(toList()), unPharmacyReq.getWardAreaId());
        }
        checkPatientOutHospital(unPharmacyReq, dispensingSheetIdToSheet, patientOrderIdSet, patientOrderIdToHospitalVo);

        // 发药人
        String employeeName = employeeService.findEmployeeNameNoExp(unPharmacyReq.getHeaderEmployeeId(), unPharmacyReq.getHeaderChainId());

        //病区id
        String wardId = patientOrderIdToHospitalVo.values().stream().map(PatientOrderHospitalVO::getWardId)
                .filter(item -> !StringUtils.isEmpty(item)).findFirst().orElse(null);

        //根据药房号生成退药单
        Map<Integer, DispensingOrder> unDispensingOrderMap = new HashMap<>();

        DispensingOrder dispensingOrder = null;
        DispensingSheetV2 dispensingSheetV2 = null;
        // order sheet form formItem
        List<DispensingOrderLog> dispensingOrderLogList = new ArrayList<>();
        List<Integer> pharmacyNoList = new ArrayList<>();
        boolean needNoticeFront = false;
        for (DispenseDispenseOrderReq.DispenseOrderReq unDispenseOrderReq : unPharmacyReq.getDispenseOrderReqs()) {
            //取药单状态检查
            dispensingOrder = dispenseOrderIdToDispenseOrder.get(Long.parseLong(unDispenseOrderReq.getId()));
            if (dispensingOrder == null) {
                throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "未找到取药单");
            }
            if (!dispensingOrder.canApplyUnDispense()) {
                throw new DispensingServiceException(DispensingServiceError.DISPENSING_ORDER_CANNOT_APPLY_UNDISPENSE, "取药单状态不对,已经不能退药");
            }
            DispensingOrder unDispensingOrder = null;
            if (unPharmacyReq.isApplyUnDispense()) {
                DispensingOrder finalDispensingOrder = dispensingOrder;
                unDispensingOrder = unDispensingOrderMap.computeIfAbsent(dispensingOrder.getPharmacyNo(),
                        k -> createUnDispensingOrder(finalDispensingOrder, unPharmacyReq, wardId));
            } else {
                unDispensingOrder = dispensingOrder;
            }

            List<DispensingFormItemV2> orderLogFormItemList = new ArrayList<>();

            for (DispenseDispenseOrderReq.DispenseSheetReq unDispenseSheetReq : unDispenseOrderReq.getDispenseSheetReqs()) {
                //发药单状态检查
                dispensingSheetV2 = dispensingSheetIdToSheet.get(unDispenseSheetReq.getId());
                if (dispensingSheetV2 == null) {
                    throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "未找到发药单");
                }
                if (!dispensingSheetV2.canApplyUnDispense()) {
                    throw new DispensingServiceException(DispensingServiceError.DISPENE_SHEET_CANNOT_APPLY_UNDISPENSE);
                }

                undispensingService.unDispenseSheetCore(dispensingOrder,
                        patientOrderIdToHospitalVo.get(dispensingSheetV2.getPatientOrderId()) != null? patientOrderIdToHospitalVo.get(dispensingSheetV2.getPatientOrderId()).getNo() : null,
                        dispensingSheetV2,
                        unDispenseSheetReq,
                        sndMessageDispenseList,
                        unPharmacyReq.getHeaderEmployeeId(),
                        orderLogFormItemList,
                        unDispensingOrder);
                dispensingSheetV2.fixHisSheetStatus();
                if (unDispenseSheetReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_CONFIRM
                        && dispensingSheetV2.getStatus() == DispenseConst.Status.UNDISPENSED) {
                    // 退药时间
                    dispensingSheetV2.setOrderByDate(Instant.now());
                }
                needNoticeFront = true;
                if (unDispenseSheetReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_APPLY) {
                    pharmacyNoList.add(dispensingOrder.getPharmacyNo());
                }
            }

            // 日志
            DispensingOrderService orderServiceProxy = SpringUtils.getBean(DispensingOrderService.class);
            orderServiceProxy.handleDispensingOrderLog(unPharmacyReq.getHeaderChainId(), unDispensingOrder.getId(),
                    unPharmacyReq.getHeaderEmployeeId(), employeeName, false, unDispenseOrderReq,
                    dispensingSheetIdToSheet, dispensingOrderLogList, orderLogFormItemList, patientOrderIdToHospitalVo);
        }

        //if (!CollectionUtils.isEmpty(dispensingOrderLogList)) {
        //    dispensingOrderLogRepository.saveAll(dispensingOrderLogList);
        //}

        if (!CollectionUtils.isEmpty(sndMessageDispenseList)) {
            rocketMqProducer.sendDispensingSheetUpdateAfterCommitAsync(PatientOrderMessage.MSG_SUB_TYPE_DISPENGING_UPDATED_UNDISPENSE, sndMessageDispenseList);
        }


        if (!unPharmacyReq.isApplyUnDispense()) {
            // 不再处理退药状态
            for (DispensingOrder order : dispenseOrderIdToDispenseOrder.values()) {
                order.fixOrderStatus();
            }
        }

        //生成新的退药单的场景下，需要保存该退药单
        if (unPharmacyReq.isApplyUnDispense()) {
            List<DispensingOrder> unDispensingOrderList = unDispensingOrderMap.values().stream()
                    .filter(item -> !CollectionUtils.isEmpty(item.getDispensingSheetV2List())).collect(toList());
            if (!CollectionUtils.isEmpty(unDispensingOrderList)) {
                dispensingOrderRepository.saveAll(unDispensingOrderList);
                //加入新的 退药单
                List<DispensingOrderSheetRel> unDispensingOrderSheetRelList = unDispensingOrderList.stream()
                        .flatMap(unDispensingOrder -> unDispensingOrder.getDispensingSheetV2List().stream()
                                .map(sheet -> {
                                    DispensingOrderSheetRel newRel = new DispensingOrderSheetRel();
                                    newRel.setId(abcIdGenerator.getUIDLong());
                                    newRel.setChainId(sheet.getChainId());
                                    newRel.setClinicId(sheet.getClinicId());
                                    newRel.setOrderId(unDispensingOrder.getId());
                                    newRel.setDispenseSheetId(sheet.getId());
                                    newRel.setApplyType(DispensingOrder.ApplyType.UN_DISPENSE);
                                    newRel.setIsDeleted(DispensingUtils.DeleteFlag.NOT_DELETED);
                                    FillUtils.fillCreatedBy(newRel, unPharmacyReq.getHeaderEmployeeId());
                                    return newRel;
                                }).collect(toList()).stream()).collect(toList());
                dispensingOrderSheetRelRepository.saveAll(unDispensingOrderSheetRelList);
            }
        }

        if (needNoticeFront) {
            List<String> noticeFrontParams = DispensingEventGroupGenerateUtils.generateEventParams(unPharmacyReq.getHeaderClinicId(), SubscribeMessage.EventGroup.DISPENSING_QL);
            messageService.sendSubscribeMessageAfterCommitAsync(noticeFrontParams, MessageService.DispensingQlEvent.NURSE_DISPENSING_ORDER_CHANGE);
            messageService.sendSubscribeMessageAfterCommitAsync(noticeFrontParams, MessageService.DispensingQlEvent.PHARMACY_DISPENSING_ORDER_CHANGE);
        }
        if (!pharmacyNoList.isEmpty()) {
            messageService.sendSubscribeMessageOfDispensingOrder(unPharmacyReq.getHeaderClinicId(),
                    MessageService.DispensingQlEvent.PHARMACY_DISPENSING_ORDER_UNDISPENSE, pharmacyNoList);
        }

        return clientRsp;
    }

    private DispensingOrder createUnDispensingOrder(DispensingOrder dispensingOrder, DispenseDispenseOrderReq unPharmacyReq, String wardId) {
        DispensingOrder undispensingOrder = new DispensingOrder();
        undispensingOrder.setId(abcIdGenerator.getUIDLong());
        undispensingOrder.setChainId(unPharmacyReq.getHeaderChainId());
        undispensingOrder.setClinicId(unPharmacyReq.getHeaderClinicId());
        undispensingOrder.setDepartmentId(dispensingOrder.getDepartmentId());
        undispensingOrder.setPharmacyType(dispensingOrder.getPharmacyType());
        undispensingOrder.setPharmacyNo(dispensingOrder.getPharmacyNo());
        undispensingOrder.setStatus(DispensingOrder.Status.UNDISPENSING);
//        undispensingOrder.setApplyDispenseTime(dispensingOrder.getApplyDispenseTime());
        undispensingOrder.setApplyUndispenseTime(Instant.now());
        String orderName = DateUtils.formatLocalDateTime(LocalDateTime.now(), DateUtils.sFormatterHourMin);
        undispensingOrder.setName(orderName);
        undispensingOrder.setIsDeleted(YesOrNo.NO);
        undispensingOrder.setApplyType(DispensingOrder.ApplyType.UN_DISPENSE);
        undispensingOrder.setWardAreaId(Long.parseLong(wardId));
//        undispensingOrder.setSourceOrderId(dispensingOrder.getId());
        FillUtils.fillCreatedBy(undispensingOrder, unPharmacyReq.getHeaderEmployeeId());
        return undispensingOrder;
    }

    /**
     * 护士长重新申请发药
     */
    public OpsCommonRsp sendReDispensingOrderToPharmacy(DispenseDispenseOrderReq reApplyPharmacyReq) {
        OpsCommonRsp clientRsp = new OpsCommonRsp(OpsCommonRsp.SUCC, "sucess");
        //加载 取药单
        List<Long> dispenseOrderIdList = reApplyPharmacyReq.getDispenseOrderReqs().stream().map(sendToPharmacyUnDispenseSheet -> Long.parseLong(sendToPharmacyUnDispenseSheet.getId())).distinct().collect(toList());
        Map<Long, DispensingOrder> dispenseOrderIdToDispenseOrder = dispensingOrderRepository.findByChainIdAndClinicIdAndIdIn(
                        reApplyPharmacyReq.getHeaderChainId(),
                        reApplyPharmacyReq.getHeaderClinicId(),
                        dispenseOrderIdList)
                .stream().collect(toMap(DispensingOrder::getId, Function.identity(), (a, b) -> a));
        List<DispensingOrderSheetRel> orderSheetRelList = dispensingOrderSheetRelRepository.findAllByChainIdAndClinicIdAndOrderIdInAndIsDeleted(reApplyPharmacyReq.getHeaderChainId(), reApplyPharmacyReq.getHeaderClinicId(),
                dispenseOrderIdList, DispensingUtils.DeleteFlag.NOT_DELETED);
        if (CollectionUtils.isEmpty(orderSheetRelList)) {
            return clientRsp;
        }
        List<String> dispensingSheetIdList = orderSheetRelList.stream().map(DispensingOrderSheetRel::getDispenseSheetId).collect(toList());
        List<DispensingSheetV2> sndMessageDispenseList = new ArrayList<>();

        //加载 发药单
        Map<String, DispensingSheetV2> dispensingSheetIdToSheet = loadAssembleDispenseSheet(reApplyPharmacyReq.getHeaderChainId(), reApplyPharmacyReq.getHeaderClinicId(), null, dispensingSheetIdList, null);
        Set<String> patientOrderIdSet = new HashSet<>();
        List<String> reqSheetIdList = reApplyPharmacyReq.getDispenseOrderReqs().stream()
                .flatMap(dispenseOrderReq -> dispenseOrderReq.getDispenseSheetReqs().stream()).map(DispenseDispenseOrderReq.DispenseSheetReq::getId).filter(Objects::nonNull).distinct().collect(toList());

        for (DispensingOrderSheetRel rel : orderSheetRelList) {
            DispensingOrder dispensingOrder = dispenseOrderIdToDispenseOrder.get(rel.getOrderId());
            DispensingSheetV2 dispensingSheet = dispensingSheetIdToSheet.get(rel.getDispenseSheetId());
            if (dispensingSheet != null && dispensingOrder != null) {
                dispensingOrder.getDispensingSheetV2List().add(dispensingSheet);
                if (reqSheetIdList.contains(rel.getDispenseSheetId())) {
                    patientOrderIdSet.add(dispensingSheet.getPatientOrderId());
                }
            }
        }

        CompletableFuture<Map<String, PatientOrderHospitalVO>> patientOrderHospitalVoFuture = getPatientOrderHospitalVoFuture(reApplyPharmacyReq.getHeaderChainId(), reApplyPharmacyReq.getHeaderClinicId(), new ArrayList<>(patientOrderIdSet));
        Map<String, PatientOrderHospitalVO> patientOrderIdToHospitalVo = patientOrderHospitalVoFuture.join();
        // 发药人
        String employeeName = employeeService.findEmployeeNameNoExp(reApplyPharmacyReq.getHeaderEmployeeId(), reApplyPharmacyReq.getHeaderChainId());

        DispensingOrder dispensingOrder = null;
        DispensingSheetV2 dispensingSheetV2 = null;
        // order sheet form formItem
        List<DispensingOrderLog> dispensingOrderLogList = new ArrayList<>();
        List<DispensingSheetV2> needLockSheetList = new ArrayList<>();
        boolean needNoticeFront = false;
        for (DispenseDispenseOrderReq.DispenseOrderReq dispenseOrderReq : reApplyPharmacyReq.getDispenseOrderReqs()) {
            //取药单状态检查
            dispensingOrder = dispenseOrderIdToDispenseOrder.get(Long.parseLong(dispenseOrderReq.getId()));
            if (dispensingOrder == null) {
                throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "未找到取药单");
            }
            List<DispensingFormItemV2> orderLogFormItemList = new ArrayList<>();
            for (DispenseDispenseOrderReq.DispenseSheetReq unDispenseSheetReq : dispenseOrderReq.getDispenseSheetReqs()) {
                //发药单状态检查
                dispensingSheetV2 = dispensingSheetIdToSheet.get(unDispenseSheetReq.getId());
                if (dispensingSheetV2 == null) {
                    throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "未找到发药单");
                }
                if (DispensingUtils.checkFlagOn(dispensingSheetV2.getDispensingTag(),
                        DispensingUtils.DispensingTag.DISPENSING_TAG_HOSPITAL_RENEW_OLD_SHEET_FLAG)) {
                    throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR,
                            "已重新生成过发药单，不可再重新申请发药");
                }
                PatientOrderHospitalVO patientOrderHospitalVO = patientOrderIdToHospitalVo.get(dispensingSheetV2.getPatientOrderId());
                if (patientOrderHospitalVO == null || patientOrderHospitalVO.getStatus() >= HisPatientOrder.Status.WAIT_SETTLE) {
                    String patientName = "";
                    if (patientOrderHospitalVO != null && patientOrderHospitalVO.getPatient() != null) {
                        patientName = patientOrderHospitalVO.getPatient().getName();
                    }
                    throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR,
                            "【" + patientName + "】已出院，不可重新申请发药");
                }

                handleReApplyDispense(dispensingOrder, dispensingSheetV2, unDispenseSheetReq, sndMessageDispenseList,
                        reApplyPharmacyReq.getHeaderEmployeeId(), orderLogFormItemList);
                dispensingSheetV2.fixHisSheetStatus();
                needNoticeFront = true;
                needLockSheetList.add(dispensingSheetV2);
            }
            dispensingOrder.setApplyDispenseTime(Instant.now());

            // 日志
            DispensingOrderService orderServiceProxy = SpringUtils.getBean(DispensingOrderService.class);
            orderServiceProxy.handleDispensingOrderLog(reApplyPharmacyReq.getHeaderChainId(), dispensingOrder.getId(),
                    reApplyPharmacyReq.getHeaderEmployeeId(), employeeName, false, dispenseOrderReq,
                    dispensingSheetIdToSheet, dispensingOrderLogList, orderLogFormItemList, patientOrderIdToHospitalVo);
        }

        //if (!CollectionUtils.isEmpty(dispensingOrderLogList)) {
        //    dispensingOrderLogRepository.saveAll(dispensingOrderLogList);
        //}

        //if(!CollectionUtils.isEmpty(sndMessageDispenseList)){
        //    rocketMqProducer.sendDispensingSheetUpdateAfterCommitAsync(PatientOrderMessage.MSG_SUB_TYPE_DISPENGING_UPDATED_UNDISPENSE,sndMessageDispenseList);
        //}

        boolean needSendTodoCountMessage = false;
        for (DispensingOrder order : dispenseOrderIdToDispenseOrder.values()) {
            order.fixOrderStatus();
            if (order.getStatus() == DispensingOrder.Status.DISPENSING) {
                needSendTodoCountMessage = true;
            }
        }
        if (!CollectionUtils.isEmpty(needLockSheetList)) {
            goodsLockingFeignClient.hospitalTryLockGoodsStock(reApplyPharmacyReq.getHeaderChainId(), reApplyPharmacyReq.getHeaderClinicId(), reApplyPharmacyReq.getHeaderEmployeeId(), needLockSheetList);
        }

        if (needNoticeFront) {
            List<String> noticeFrontParams = DispensingEventGroupGenerateUtils.generateEventParams(reApplyPharmacyReq.getHeaderClinicId(), SubscribeMessage.EventGroup.DISPENSING_QL);
            messageService.sendSubscribeMessageAfterCommitAsync(noticeFrontParams, MessageService.DispensingQlEvent.NURSE_DISPENSING_ORDER_CHANGE);
            messageService.sendSubscribeMessageAfterCommitAsync(noticeFrontParams, MessageService.DispensingQlEvent.PHARMACY_DISPENSING_ORDER_CHANGE);
        }
        if (needSendTodoCountMessage) {
            RocketMqProducer.doAfterTransactionCommit(() -> dispensingTodoService.sendHospitalPharmacyTodoCountTask(reApplyPharmacyReq.getHeaderChainId(), reApplyPharmacyReq.getHeaderClinicId()));
        }

        return clientRsp;
    }

    public void handleReApplyDispense(DispensingOrder dispensingOrder,
                                      DispensingSheetV2 svrDispenseSheet,
                                      DispenseDispenseOrderReq.DispenseSheetReq clientDispenseReq,
                                      List<DispensingSheetV2> sndMessageDispenseList,
                                      String operatorId,
                                      List<DispensingFormItemV2> orderLogFormItemList
    ) {
        List<DispenseDispenseOrderReq.DispenseFormItemReq> clientDispenseFormItemList = clientDispenseReq.getAllDispenseFormItems();
        if (CollectionUtils.isEmpty(clientDispenseFormItemList)) {
            return;
        }
        Map<String, DispensingFormItemV2> formItemIdToFormItemV2 = svrDispenseSheet.getDispensingForms().stream().flatMap(form -> form.getDispensingFormItems().stream()).collect(toMap(DispensingFormItemV2::getId, Function.identity(), (a, b) -> a));
        for (DispenseDispenseOrderReq.DispenseFormItemReq formItemReq : clientDispenseFormItemList) {
            DispensingFormItemV2 svrFormItem = formItemIdToFormItemV2.get(formItemReq.getId());
            if (svrFormItem == null) {
                throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "未找到发药项");
            }
            if (svrFormItem.getStatus() != DispenseConst.Status.APPLY_DISPENSE_REJECT) {
                throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "不能发起重新发药申请操作");
            }
            svrFormItem.setStatus(DispenseConst.Status.REAPPLY_DISPENSE);
            FillUtils.fillLastModifiedBy(svrFormItem, operatorId);
            FillUtils.fillLastModifiedBy(svrDispenseSheet, operatorId);
            orderLogFormItemList.add(svrFormItem);
        }
    }

    /**
     * 把取药单里面的发药单发药
     * 可以指定部分药品发药
     */
    public OpsCommonRsp dispenseDispensingOrder(DispenseDispenseOrderReq dispenseOrderReq) throws ServiceInternalException {
        OpsCommonRsp clientRsp = new OpsCommonRsp();
        List<Long> dispenseOrderIdList = dispenseOrderReq.getDispenseOrderReqs().stream().map(orderReq -> Long.parseLong(orderReq.getId())).collect(toList());
        Map<Long, DispensingOrder> dispenseOrderIdToDispenseOrder = dispensingOrderRepository.findByChainIdAndClinicIdAndIdIn(
                        dispenseOrderReq.getHeaderChainId(),
                        dispenseOrderReq.getHeaderClinicId(),
                        dispenseOrderIdList)
                .stream().collect(toMap(DispensingOrder::getId, Function.identity(), (a, b) -> a));
        List<DispensingOrderSheetRel> orderSheetRelList = dispensingOrderSheetRelRepository.findAllByChainIdAndClinicIdAndOrderIdInAndIsDeleted(dispenseOrderReq.getHeaderChainId(), dispenseOrderReq.getHeaderClinicId(),
                dispenseOrderIdList, DispensingUtils.DeleteFlag.NOT_DELETED);
        if (CollectionUtils.isEmpty(orderSheetRelList)) {
            throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "取药单下没有领药单");
        }
        List<String> dispensingSheetIdList = orderSheetRelList.stream().map(DispensingOrderSheetRel::getDispenseSheetId).collect(toList());
        //加载 发药单 因为要更新取药单的状态，所以要加载所有发药单，这里可能有性能问题
        Map<String, DispensingSheetV2> dispensingSheetIdToSheet = loadAssembleDispenseSheet(dispenseOrderReq.getHeaderChainId(), dispenseOrderReq.getHeaderClinicId(), null, dispensingSheetIdList, null);
        bindGoodsItemIfNeeded(dispenseOrderReq.getHeaderChainId(), dispenseOrderReq.getHeaderClinicId(), dispensingSheetIdToSheet);
        List<String> reqSheetIdList = dispenseOrderReq.getDispenseOrderReqs().stream().flatMap(orderReq -> orderReq.getDispenseSheetReqs().stream())
                .map(DispenseDispenseOrderReq.DispenseSheetReq::getId).filter(Objects::nonNull).distinct().collect(toList());
        Set<String> patientOrderIdSet = new HashSet<>();
        for (DispensingOrderSheetRel rel : orderSheetRelList) {
            DispensingOrder dispensingOrder = dispenseOrderIdToDispenseOrder.get(rel.getOrderId());
            DispensingSheetV2 dispensingSheet = dispensingSheetIdToSheet.get(rel.getDispenseSheetId());
            if (dispensingSheet != null && dispensingOrder != null) {
                dispensingOrder.getDispensingSheetV2List().add(dispensingSheet);
                if (reqSheetIdList.contains(rel.getDispenseSheetId())) {
                    patientOrderIdSet.add(dispensingSheet.getPatientOrderId());
                }
            }
        }
        CompletableFuture<Map<String, PatientOrderHospitalVO>> patientOrderHospitalVoFuture = getPatientOrderHospitalVoFuture(dispenseOrderReq.getHeaderChainId(), dispenseOrderReq.getHeaderClinicId(), new ArrayList<>(patientOrderIdSet));
        Map<String, PatientOrderHospitalVO> patientOrderIdToHospitalVo = patientOrderHospitalVoFuture.join();
        checkPatientOutHospital(dispenseOrderReq, dispensingSheetIdToSheet, patientOrderIdSet, patientOrderIdToHospitalVo);
        // 发药人
        String employeeName = employeeService.findEmployeeNameNoExp(dispenseOrderReq.getHeaderEmployeeId(), dispenseOrderReq.getHeaderChainId());

        List<DispensingSheetV2> unlockDispenseSheetList = new ArrayList<>();
        List<DispensingOrderLog> dispensingOrderLogList = new ArrayList<>();
        //一个领药单一个领药单的处理发药 扣库
        for (DispenseDispenseOrderReq.DispenseOrderReq orderReq : dispenseOrderReq.getDispenseOrderReqs()) {
            for (DispenseDispenseOrderReq.DispenseSheetReq dispenseSheetReq : orderReq.getDispenseSheetReqs()) {
                if (dispenseSheetReq == null) {
                    throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "发药请求不能为空");
                }
                DispensingSheetV2 dispensingSheetV2 = dispensingSheetIdToSheet.get(dispenseSheetReq.getId());
                if (dispensingSheetV2 == null) {
                    throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "未找到发药单");
                }
                if (dispensingSheetV2.isSelfProvided()) {
                    throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "自备药不可发药");
                }
            }
        }

        boolean needNoticeFront = false;
        boolean needSendTodoCountMessage = false;
        for (DispenseDispenseOrderReq.DispenseOrderReq orderReq : dispenseOrderReq.getDispenseOrderReqs()) {
            DispensingOrder dispensingOrder = dispenseOrderIdToDispenseOrder.get(Long.parseLong(orderReq.getId()));
            List<DispensingFormItemV2> orderLogFormItemList = new ArrayList<>();

            DispensingOrderService orderServiceProxy = SpringUtils.getBean(DispensingOrderService.class);

            DispenseDispenseOrderReq.DispenseOrderReq logOrderReq = new DispenseDispenseOrderReq.DispenseOrderReq();
            logOrderReq.setId(orderReq.getId());
            logOrderReq.setDispenseSheetReqs(new ArrayList<>());

            for (DispenseDispenseOrderReq.DispenseSheetReq dispenseSheetReq : orderReq.getDispenseSheetReqs()) {
                if (dispenseSheetReq == null) {
                    continue;
                }
                DispensingSheetV2 dispensingSheetV2 = dispensingSheetIdToSheet.get(dispenseSheetReq.getId());
                DispenseResult dispenseResult = null;
                try {
                    dispenseResult = orderServiceProxy.dispenseSheetHisCore(dispensingSheetV2,
                            patientOrderIdToHospitalVo.get(dispensingSheetV2.getPatientOrderId()) != null? patientOrderIdToHospitalVo.get(dispensingSheetV2.getPatientOrderId()).getNo() : null,
                            dispenseSheetReq, dispenseOrderReq.getHeaderEmployeeId(), orderLogFormItemList, dispensingOrder);
                    logOrderReq.getDispenseSheetReqs().add(dispenseSheetReq);
                    // 发药退药都需要去解锁
                    unlockDispenseSheetList.add(dispensingSheetV2);
                } catch (Exception e) {
                    orderServiceProxy.handleDispensingOrderLog(dispenseOrderReq.getHeaderChainId(), dispensingOrder.getId(),
                            dispenseOrderReq.getHeaderEmployeeId(), employeeName, true, logOrderReq, dispensingSheetIdToSheet,
                            dispensingOrderLogList, orderLogFormItemList, patientOrderIdToHospitalVo);

                    if (!CollectionUtils.isEmpty(unlockDispenseSheetList)) {
                        goodsLockingFeignClient.hospitalTryUnLockGoodsStock(dispenseOrderReq.getHeaderChainId(),
                                dispenseOrderReq.getHeaderClinicId(),
                                dispenseOrderReq.getHeaderEmployeeId(),
                                unlockDispenseSheetList);
                    }
                    throw e;
                }
                //if (dispenseResult != null) {
                //    dispensingSheetV2.setDispensingOrder(dispensingOrder);
                //    sndMessageDispenseSheetList.add(dispensingSheetV2);
                //}
                needNoticeFront = true;
            }
            dispensingOrder.fixOrderStatus();
            if (dispensingOrder.getStatus() > DispensingOrder.Status.DISPENSING) {
                needSendTodoCountMessage = true;
            }
            // 日志
            orderServiceProxy.handleDispensingOrderLog(dispenseOrderReq.getHeaderChainId(), dispensingOrder.getId(),
                    dispenseOrderReq.getHeaderEmployeeId(), employeeName, true, orderReq, dispensingSheetIdToSheet,
                    dispensingOrderLogList, orderLogFormItemList, patientOrderIdToHospitalVo);
        }
        //if (!CollectionUtils.isEmpty(dispensingOrderLogList)) {
        //    dispensingOrderLogRepository.saveAll(dispensingOrderLogList);
        //}
        //if (!CollectionUtils.isEmpty(sndMessageDispenseSheetList)) {
        //    rocketMqProducer.sendDispensingSheetUpdateAfterCommitAsync(PatientOrderMessage.MSG_SUB_TYPE_DISPENGING_UPDATED_DISPENSE, sndMessageDispenseSheetList);
        //}
        if (!CollectionUtils.isEmpty(unlockDispenseSheetList)) {
            goodsLockingFeignClient.hospitalTryUnLockGoodsStock(dispenseOrderReq.getHeaderChainId(),
                    dispenseOrderReq.getHeaderClinicId(),
                    dispenseOrderReq.getHeaderEmployeeId(),
                    unlockDispenseSheetList);
        }
        if (needNoticeFront) {
            List<String> noticeFrontParams = DispensingEventGroupGenerateUtils.generateEventParams(dispenseOrderReq.getHeaderClinicId(), SubscribeMessage.EventGroup.DISPENSING_QL);
            messageService.sendSubscribeMessageAfterCommitAsync(noticeFrontParams, MessageService.DispensingQlEvent.NURSE_DISPENSING_ORDER_CHANGE);
            messageService.sendSubscribeMessageAfterCommitAsync(noticeFrontParams, MessageService.DispensingQlEvent.PHARMACY_DISPENSING_ORDER_CHANGE);
        }
        if (needSendTodoCountMessage) {
            RocketMqProducer.doAfterTransactionCommit(() -> dispensingTodoService.sendHospitalPharmacyTodoCountTask(dispenseOrderReq.getHeaderChainId(), dispenseOrderReq.getHeaderClinicId()));
        }
        return clientRsp;
    }

    private void bindGoodsItemIfNeeded(String chainId, String clinicId, Map<String, DispensingSheetV2> dispensingSheetIdToSheet) {
        if (CollectionUtils.isEmpty(dispensingSheetIdToSheet)) {
            return;
        }

        List<QueryGoodsInPharmacyByIdsAndStockCountReq.QueryPharmacyGoodsReq> queryGoodsReqs = new ArrayList<>();
        Map<String, DispensingFormItemV2> keyIdToDispensingFormItem = new HashMap<>();
        dispensingSheetIdToSheet.values().forEach(dispensingSheet -> {
            List<QueryGoodsInPharmacyByIdsAndStockCountReq.QueryGoodsWithStock> queryGoodsWithStockList = new ArrayList<>();
            DispensingUtils.doWithDispensingItem(dispensingSheet, formItem -> {
                if (!formItem.lockFailOrNeedLock()) {
                    return;
                }

                QueryGoodsInPharmacyByIdsAndStockCountReq.QueryGoodsWithStock queryGoodsWithStock = new QueryGoodsInPharmacyByIdsAndStockCountReq.QueryGoodsWithStock();
                queryGoodsWithStock.setGoodsId(formItem.getProductId());
                BigDecimal totalCount = MathUtils.calculateTotalCount(formItem.getUnitCount(), formItem.getDoseCount());
                if (formItem.getUseDismounting() == 1) {
                    queryGoodsWithStock.setPieceCount(totalCount);
                } else {
                    queryGoodsWithStock.setPackageCount(totalCount);
                }
                queryGoodsWithStock.setKeyId(formItem.getId());
                keyIdToDispensingFormItem.put(formItem.getId(), formItem);
                queryGoodsWithStockList.add(queryGoodsWithStock);
            });

            if (CollectionUtils.isEmpty(queryGoodsWithStockList)) {
                return;
            }

            QueryGoodsInPharmacyByIdsAndStockCountReq.QueryPharmacyGoodsReq queryPharmacyGoodsReq = new QueryGoodsInPharmacyByIdsAndStockCountReq.QueryPharmacyGoodsReq();
            queryPharmacyGoodsReq.setPharmacyNo(dispensingSheet.getPharmacyNo());
            queryPharmacyGoodsReq.setPharmacyType(dispensingSheet.getPharmacyType());
            queryPharmacyGoodsReq.setList(queryGoodsWithStockList);
            queryPharmacyGoodsReq.setSceneType(GoodsConst.SceneType.HOSPITAL);
            queryGoodsReqs.add(queryPharmacyGoodsReq);
        });

        List<QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp> queryPharmacyGoodsRsps = scGoodsFeignClient.queryGoodsInPharmacyByIds(clinicId, chainId, true, 1, queryGoodsReqs);
        for (QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp queryPharmacyGoodsRsp : queryPharmacyGoodsRsps) {
            for (GoodsItem goodsItem : queryPharmacyGoodsRsp.getList()) {
                DispensingFormItemV2 dispensingFormItem = keyIdToDispensingFormItem.get(goodsItem.getKeyId());
                if (dispensingFormItem != null) {
                    dispensingFormItem.setGoodsItem(goodsItem);
                }
            }
        }
    }

    private void checkPatientOutHospital(DispenseDispenseOrderReq dispenseOrderReq,
                                         Map<String, DispensingSheetV2> dispensingSheetIdToSheet,
                                         Set<String> patientOrderIdSet,
                                         Map<String, PatientOrderHospitalVO> patientOrderIdToHospitalVo) {
        if (dispenseOrderReq == null || CollectionUtils.isEmpty(dispensingSheetIdToSheet)
                || CollectionUtils.isEmpty(patientOrderIdSet)) {
            return;
        }
        if (!dispenseOrderReq.isFromPharmacy() && !dispenseOrderReq.isApplyUnDispense()) {
            return;
        }
        //CompletableFuture<Map<String, PatientOrderHospitalVO>> patientOrderHospitalVoFuture = getPatientOrderHospitalVoFuture(dispenseOrderReq.getHeaderChainId(), dispenseOrderReq.getHeaderClinicId(), new ArrayList<>(patientOrderIdSet));
        CompletableFuture<Map<Long, AdviceDetailView>> adviceDetailViewFuture = getAdviceDetailViewFuture(dispenseOrderReq.getHeaderChainId(), dispenseOrderReq.getHeaderClinicId(), dispensingSheetIdToSheet.values().stream().filter(it -> it.getAdviceId() != null)
                .map(DispensingSheetV2::getAdviceId).distinct().collect(toList()));
        //Map<String, PatientOrderHospitalVO> patientOrderIdToHospitalVo = patientOrderHospitalVoFuture.join();
        Map<Long, AdviceDetailView> adviceIdToAdviceView = adviceDetailViewFuture.join();
        if (CollectionUtils.isEmpty(patientOrderIdToHospitalVo) && CollectionUtils.isEmpty(adviceIdToAdviceView)) {
            return;
        }
        List<String> productNameList = new ArrayList<>();
        boolean dispense = true;
        for (DispenseDispenseOrderReq.DispenseOrderReq orderReq : dispenseOrderReq.getDispenseOrderReqs()) {
            for (DispenseDispenseOrderReq.DispenseSheetReq dispenseSheetReq : orderReq.getDispenseSheetReqs()) {
                DispensingSheetV2 sheetV2 = dispensingSheetIdToSheet.get(dispenseSheetReq.getId());
                if (sheetV2 == null) {
                    continue;
                }
                // 医嘱是否可发药
                if (dispenseSheetReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.DISPENSE_CONFIRM
                        || dispenseSheetReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.RECORD_NOT_DISPENSE_STOCK
                        || dispenseSheetReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.STOCK_NOT_DISPENSE) {
                    if (sheetV2.getAdviceId() != null) {
                        AdviceDetailView adviceDetailView = adviceIdToAdviceView.get(sheetV2.getAdviceId());
                        if (adviceDetailView != null && DispensingUtils.canNotDispenseAdviceStatusList.contains(adviceDetailView.getStatus())) {
                            throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR,
                                    "医嘱【" + adviceDetailView.getName() + "】状态为下达或撤销不可发药");
                        }
                    }
                }
                int dispenseType = dispenseSheetReq.getDispenseType();
                if (dispenseType == DispenseDispenseOrderReq.DispenseType.DISPENSE_CONFIRM
                        || dispenseType == DispenseDispenseOrderReq.DispenseType.DISPENSE_REJECT
                        || dispenseType == DispenseDispenseOrderReq.DispenseType.RECORD_NOT_DISPENSE_STOCK
                        || dispenseType == DispenseDispenseOrderReq.DispenseType.STOCK_NOT_DISPENSE) {
                } else {
                    dispense = false;
                }
                PatientOrderHospitalVO patientOrderHospitalVO = patientOrderIdToHospitalVo.get(sheetV2.getPatientOrderId());
                if (patientOrderHospitalVO != null && patientOrderHospitalVO.getStatus() < HisPatientOrder.Status.WAIT_SETTLE) {
                    continue;
                }

                DispensingFormItemV2 dispensingFormItemV2 = sheetV2.getDispensingForms().stream().flatMap(form -> form.getDispensingFormItems().stream()).filter(it -> it.getStatus() == DispenseConst.Status.WAITING || it.getStatus() == DispenseConst.Status.DISPENSED).findFirst().orElse(null);
                //String patientName = patientOrderHospitalVO.getPatient() != null ? patientOrderHospitalVO.getPatient().getName() : "";
                String productName = dispensingFormItemV2 != null ? dispensingFormItemV2.getName() : "";
                productNameList.add(productName);
            }
        }
        if (productNameList.isEmpty()) {
            return;
        }
        throw new DispensingServiceException(dispense ? DispensingServiceError.HOSPITAL_DISPENSE_FAIL : DispensingServiceError.HOSPITAL_UNDISPENSE_FAIL, productNameList);
    }

    private void checkPatientSameWard(Collection<PatientOrderHospitalVO> patientOrderIdToHospitalVos, Long wardAreaId) {
        if (CollectionUtils.isEmpty(patientOrderIdToHospitalVos)) {
            return;
        }
        boolean notSameWard =
                patientOrderIdToHospitalVos.stream().anyMatch(it -> wardAreaId != null && !String.valueOf(wardAreaId).equals(it.getWardId()));
        if (notSameWard) {
            throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "部分患者已不在当前病区");
        }
        //校验一下当前选择的患者是否在同一个病区
        boolean isSeamWardAreaId = patientOrderIdToHospitalVos.stream().map(PatientOrderHospitalVO::getWardId).distinct().count() <= 1;
        if (!isSeamWardAreaId) {
            throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "患者不在同一个病区");
        }
    }


    /**
     * 如果这个时间点的取药单还没生成出来 立即生成
     * 为了防止并发，要加锁
     * 必须快速完成，其他医嘱消息可能还在等着这个发药单
     */
    //@Transactional //
    //@RedisLock(key = "'dispensing.order:' + #clinicId + ':' + #departmentId+ ':' + #wardAreaId+ ':' + #pharmacyNo")
    public DispensingOrder getOrCreateCurrentDispenseOrder(String chainId, String clinicId, String departmentId,
                                                           Long wardAreaId, int pharmacyType, Integer pharmacyNo,
                                                           Instant planExecuteTime) {
        // 领药单生成时间段
        GetDispensingOrderConfigDto currentDispenseOrder = dispensingConfigService.getCurrentDispenseOrder(chainId, clinicId,
                wardAreaId, pharmacyNo, planExecuteTime);
        String curOrderName = currentDispenseOrder.getNextTimeName();
        boolean nextDate = currentDispenseOrder.isNextDay();
        boolean hasOrderConfig = currentDispenseOrder.isHasOrderConfig();
        log.info("领药单时间段：currentDispenseOrder = {}", currentDispenseOrder);

        List<DispensingOrder> dispensingOrderList;
        if (currentDispenseOrder.isNextDay()) {
            // 第二天第一个时间段
            Instant nextBeginOfDay = DateUtils.toInstant(DateUtils.beginOfDay(LocalDate.now().plusDays(1L)));
            dispensingOrderList =
                    dispensingOrderRepository.findAllByChainIdAndClinicIdAndDepartmentIdAndWardAreaIdAndStatusInAndApplyDispenseTimeGreaterThanEqualAndIsDeleted(
                            chainId,
                            clinicId,
                            departmentId,
                            wardAreaId,
                            Lists.newArrayList(DispensingOrder.Status.NORMAL),
                            nextBeginOfDay,
                            DispensingUtils.DeleteFlag.NOT_DELETED);
        } else {
            if (hasOrderConfig) {
                // 昨天所有的
                Instant beginOfDay = DateUtils.toInstant(LocalDateTime.of(LocalDate.now().minusDays(1L), DateUtils.sBeginOfDay));
                dispensingOrderList = dispensingOrderRepository.findAllByChainIdAndClinicIdAndDepartmentIdAndWardAreaIdAndStatusInAndCreatedGreaterThanEqualAndIsDeleted(
                        chainId,
                        clinicId,
                        departmentId,
                        wardAreaId,
                        Lists.newArrayList(DispensingOrder.Status.NORMAL),
                        beginOfDay,
                        DispensingUtils.DeleteFlag.NOT_DELETED);
            } else {
                // 没有配置领药规则，查询当天的领药单
                Instant beginOfDay = DateUtils.toInstant(LocalDateTime.of(LocalDate.now(), DateUtils.sBeginOfDay));
                dispensingOrderList =
                        dispensingOrderRepository.findAllByChainIdAndClinicIdAndDepartmentIdAndWardAreaIdAndStatusInAndCreatedGreaterThanEqualAndIsDeleted(
                                chainId,
                                clinicId,
                                departmentId,
                                wardAreaId,
                                Lists.newArrayList(DispensingOrder.Status.NO_ORDER_CONFIG),
                                beginOfDay,
                                DispensingUtils.DeleteFlag.NOT_DELETED);
            }
        }

        log.info("查询到的领药单，clinicId = {}, orderList = {}", clinicId, dispensingOrderList);

        //有可能当前要加入的取药单不存在，不存在可能是定时时间到了。新建一个取药单
        DispensingOrder curDispensingOrder = dispensingOrderList.stream()
                .filter(dispensingOrder ->
                        dispensingOrder.getEmergencyStatus() == DispensingOrder.EmergencyStatus.NORMAL
                                && dispensingOrder.getPharmacyNo().equals(pharmacyNo)
                                && dispensingOrder.getName().compareTo(curOrderName) == 0)
                .filter(dispensingOrder -> {
                    if (hasOrderConfig) {
                        return dispensingOrder.getStatus() == DispensingOrder.Status.NORMAL;
                    }
                    // 没有领药单配置
                    return dispensingOrder.getStatus() == DispensingOrder.Status.NO_ORDER_CONFIG;
                }).findFirst().orElse(null);

        log.info("curDispensingOrder = {}", curDispensingOrder);

        List<String> noticeFrontParams = DispensingEventGroupGenerateUtils.generateEventParams(clinicId, SubscribeMessage.EventGroup.DISPENSING_QL);
        if (curDispensingOrder != null) {
            String nowDayStr = DateUtils.formatLocalDateTime(LocalDateTime.now(), DateUtils.sFormatterDate);
            String createdDayStr = DateUtils.formatLocalDateTime(DateUtils.toLocalDateTime(curDispensingOrder.getCreated()), DateUtils.sFormatterDate);
            log.info("nowDayStr = {}, createdDayStr = {}", nowDayStr, createdDayStr);
            // 有未发送到药房的直接返回，发药单都放到这个领药单
            return curDispensingOrder;
            //同一天
            //if (nowDayStr.compareTo(createdDayStr) == 0) {
            //    return curDispensingOrder;
            //}
            //else {
            //    if (nextDate) {
            //        // 第二天的领药单
            //        return curDispensingOrder;
            //    }
            //    if (hasOrderConfig) {
            //        curDispensingOrder.setStatus(DispensingOrder.Status.DISPENSING);
            //        curDispensingOrder.setApplyDispenseTime(Instant.now());
            //        dispensingOrderRepository.save(curDispensingOrder);
            //        // 通知前端
            //        messageService.sendSubscribeMessageAfterCommitAsync(noticeFrontParams, MessageService.DispensingQlEvent.PHARMACY_DISPENSING_ORDER_CHANGE);
            //    }
            //}
        }
        //新建一个发药单
        DispensingOrder newCurDispensingOrder = dispensingConfigService.createCurNormalDispensingOrder(chainId,
                clinicId, departmentId, wardAreaId, pharmacyType, pharmacyNo, curOrderName, hasOrderConfig || nextDate);
        long applyDispensingTimestamp = DispensingUtils.getDelayMessageTimestamp(curOrderName, nextDate);
        newCurDispensingOrder.setApplyDispenseTime(Instant.ofEpochMilli(applyDispensingTimestamp));
        dispensingOrderRepository.save(newCurDispensingOrder);
        DispensingOrderLog dispensingOrderLog = createDispensingOrderLog(chainId, newCurDispensingOrder.getId(),
                DispensingOrderLog.Action.CREATE, null, DispensingConstants.ANONYMOUS_PATIENT_ID);
        dispensingOrderLogRepository.save(dispensingOrderLog);
        // 发送mq延时消息
        if (hasOrderConfig || nextDate) {
            RocketMqProducer.doAfterTransactionCommit(() ->
                    rocketMqProducer.sendDispensingOrderStatusChangeDelayMessage(chainId, clinicId, newCurDispensingOrder.getId(),
                            wardAreaId, curOrderName, nextDate));
            messageService.sendSubscribeMessageAfterCommitAsync(noticeFrontParams, MessageService.DispensingQlEvent.NURSE_DISPENSING_ORDER_CHANGE);
        }
        return newCurDispensingOrder;
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleDispensingOrderLog(String chainId, Long orderId, String operatorId, String operatorName,
                                         boolean dispenseFlag, DispenseDispenseOrderReq.DispenseOrderReq orderReq,
                                         Map<String, DispensingSheetV2> dispensingSheetV2Map,
                                         List<DispensingOrderLog> dispensingOrderLogList,
                                         List<DispensingFormItemV2> orderLogFormItemList,
                                         Map<String, PatientOrderHospitalVO> patientOrderIdToHospitalVo
    ) {
//        if (orderId != Long.parseLong(orderReq.getId())) {
//            return;
//        }
        List<DispenseDispenseOrderReq.DispenseSheetReq> dispenseSheetReqs = orderReq.getDispenseSheetReqs().stream().filter(Objects::nonNull).collect(toList());
        if (CollectionUtils.isEmpty(dispenseSheetReqs)) {
            return;
        }
        Map<String, DispensingFormItemV2> itemIdToFormItem = orderLogFormItemList.stream().filter(Objects::nonNull).collect(Collectors.toMap(DispensingFormItemV2::getId, Function.identity(), (a, b) -> a));
        if (CollectionUtils.isEmpty(itemIdToFormItem)) {
            return;
        }
        //DispenseDispenseOrderReq.DispenseSheetReq dispenseSheetReq = dispenseSheetReqs.get(0);
        // 已发药动作展示发药记录
        Map<Integer, List<DispenseDispenseOrderReq.DispenseSheetReq>> dispenseTypeToSheetReqList = dispenseSheetReqs.stream().collect(groupingBy(DispenseDispenseOrderReq.DispenseSheetReq::getDispenseType));
        dispenseTypeToSheetReqList.forEach((dispenseType, sheetReqList) -> {
            int action = 0;
            switch (dispenseType) {
                case DispenseDispenseOrderReq.DispenseType.DISPENSE_CONFIRM:
                    action = DispensingOrderLog.Action.DISPENSE;
                    break;
                case DispenseDispenseOrderReq.DispenseType.DISPENSE_REJECT:
                    action = DispensingOrderLog.Action.REJECT_DISPENSE;
                    break;
                case DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_APPLY:
                    action = DispensingOrderLog.Action.REDRAW_TO_PHARMACY;
                    break;
                case DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_CONFIRM:
                    action = DispensingOrderLog.Action.UNDISPENSE;
                    break;
                case DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_REJECT:
                    action = DispensingOrderLog.Action.REJECT_UNDISPENSE;
                    break;
                case DispenseDispenseOrderReq.DispenseType.RECORD_NOT_DISPENSE_STOCK:
                    action = DispensingOrderLog.Action.RECORD_DISPENSE;
                    break;
                case DispenseDispenseOrderReq.DispenseType.STOCK_NOT_DISPENSE:
                    action = DispensingOrderLog.Action.STOCK_DISPENSE;
                    break;
                case DispenseDispenseOrderReq.DispenseType.RE_APPLY_DISPENSE:
                    action = DispensingOrderLog.Action.RE_APPLY_DISPENSE;
                    break;
                case DispenseDispenseOrderReq.DispenseType.RE_APPLY_UNDISPENSE:
                    action = DispensingOrderLog.Action.RE_APPLY_UNDISPENSE;
                    break;
            }
            if (action == DispensingOrderLog.Action.CREATE) {
                return;
            }
            StringBuilder sb = new StringBuilder();
            AtomicInteger chineseMedicineCount = new AtomicInteger(0);
            AtomicInteger westernMedicineCount = new AtomicInteger(0);
            AtomicInteger materialMedicineCount = new AtomicInteger(0);
            List<OperationRecordForDispense> details = new ArrayList<>();
            List<DispensingOrderLog.DispensingOrderLogNewDetail> newDetails = new ArrayList<>();
            List<String> patientOrderIdList = new ArrayList<>();
            List<DispensingSheetV2> logSheetV2List = new ArrayList<>();
            for (DispenseDispenseOrderReq.DispenseSheetReq sheetReq : sheetReqList) {
                List<String> formItemIds = sheetReq.getAllDispenseFormItems().stream().map(DispenseDispenseOrderReq.DispenseFormItemReq::getId).collect(toList());
                if (CollectionUtils.isEmpty(formItemIds)) {
                    continue;
                }
                DispensingSheetV2 dispensingSheetV2 = dispensingSheetV2Map.get(sheetReq.getId());
                if (dispensingSheetV2 == null) {
                    continue;
                }
                logSheetV2List.add(dispensingSheetV2);
                if (!patientOrderIdList.contains(dispensingSheetV2.getPatientOrderId())) {
                    patientOrderIdList.add(dispensingSheetV2.getPatientOrderId());
                }
                List<DispensingFormV2> dispensingForms = dispensingSheetV2.getDispensingForms();
                if (CollectionUtils.isEmpty(dispensingForms)) {
                    continue;
                }
                List<DispensingFormItemV2> formItemV2List = dispensingForms.stream().filter(item -> !CollectionUtils.isEmpty(item.getDispensingFormItems()))
                        .flatMap(item -> item.getDispensingFormItems().stream()).filter(item -> formItemIds.contains(item.getId())).collect(toList());
                if (CollectionUtils.isEmpty(formItemV2List)) {
                    continue;
                }
                DispensingFormItemV2 firstFormItemV2 = formItemV2List.get(0);
                if (DispensingUtils.isChineseMedicine(firstFormItemV2.getProductType(), firstFormItemV2.getProductSubType())) {
                    chineseMedicineCount.addAndGet(formItemV2List.size());
                } else if (DispensingUtils.isWestMedicine(firstFormItemV2.getProductType(), firstFormItemV2.getProductSubType())) {
                    westernMedicineCount.addAndGet(formItemV2List.size());
                } else if (firstFormItemV2.getProductType() == GoodsConst.GoodsType.MATERIAL) {
                    materialMedicineCount.addAndGet(formItemV2List.size());
                }
                //Map<Integer, List<DispensingFormItemV2>> subTypeMap =
                //        formItemV2List.stream().filter(item -> item.getProductType() == GoodsConst.GoodsType.MEDICINE).collect(groupingBy(DispensingFormItemV2::getProductSubType));
                //if (CollectionUtils.isEmpty(subTypeMap)) {
                //    continue;
                //}
                //addDispensingOrderLog(dispensingSheetV2, operatorName, details, itemIdToFormItem);
                //subTypeMap.forEach((subType, list) -> {
                //    if (subType == GoodsConst.GoodsMedicineSubType.CHINESE_MEDICINE) {
                //        chineseMedicineCount.getAndAdd(list.size());
                //    } else {
                //        westernMedicineCount.getAndAdd(list.size());
                //    }
                //});
            }
            if (westernMedicineCount.get() > 0) {
                sb.append("西药（").append(westernMedicineCount.get()).append("种）；");
            }
            if (chineseMedicineCount.get() > 0) {
                sb.append("中药处方（").append(chineseMedicineCount.get()).append("味）；");
            }
            if (materialMedicineCount.get() > 0) {
                sb.append("耗材商品（").append(materialMedicineCount.get()).append("种）；");
            }
            if (action != DispensingOrderLog.Action.RECORD_DISPENSE && action != DispensingOrderLog.Action.STOCK_DISPENSE) {
                if (!TextUtils.isEmpty(operatorName)) {
                    sb.append("发药人（").append(operatorName).append("）");
                }
            }
            setDispensingOrderLogDetail(newDetails, patientOrderIdList, logSheetV2List, itemIdToFormItem, patientOrderIdToHospitalVo);
            DispensingOrderLog.LogDetail detail = new DispensingOrderLog.LogDetail();
            if (action == DispensingOrderLog.Action.RE_APPLY_DISPENSE) {
                detail.setSummery("重新申请拒发药品");
            } else if (action == DispensingOrderLog.Action.RE_APPLY_UNDISPENSE) {
                detail.setSummery("重新申请拒退药品");
            } else {
                detail.setSummery(sb.toString());
            }
            detail.setDetails(details);
            detail.setNewDetails(newDetails);
            DispensingOrderLog dispensingOrderLog = createDispensingOrderLog(chainId, orderId, action, JsonUtils.dumpAsJsonNode(detail), operatorId);
            dispensingOrderLogList.add(dispensingOrderLog);
        });

        if (!CollectionUtils.isEmpty(dispensingOrderLogList)) {
            dispensingOrderLogRepository.saveAll(dispensingOrderLogList);
        }
        //int action = 0;
        //if (dispenseFlag) {
        //    action = dispenseSheetReq.getUnDispense() ? DispensingOrderLog.Action.REJECT_DISPENSE : DispensingOrderLog.Action.DISPENSE;
        //} else {
        //    if (dispenseSheetReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_APPLY) {
        //        action = DispensingOrderLog.Action.REDRAW_TO_PHARMACY;
        //    } else if (dispenseSheetReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_CONFIRM) {
        //        action = DispensingOrderLog.Action.UNDISPENSE;
        //    } else if (dispenseSheetReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_REJECT) {
        //        action = DispensingOrderLog.Action.REJECT_UNDISPENSE;
        //    } else if (dispenseSheetReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.RECORD_NOT_DISPENSE_STOCK) {
        //        action = DispensingOrderLog.Action.RECORD_DISPENSE;
        //    } else if (dispenseSheetReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.STOCK_NOT_DISPENSE) {
        //        action = DispensingOrderLog.Action.STOCK_DISPENSE;
        //    }
        //}
        //switch (dispenseSheetReq.getDispenseType()) {
        //    case DispenseDispenseOrderReq.DispenseType.DISPENSE_CONFIRM:
        //        action = DispensingOrderLog.Action.DISPENSE;
        //    case DispenseDispenseOrderReq.DispenseType.DISPENSE_REJECT:
        //        action = DispensingOrderLog.Action.REJECT_DISPENSE;
        //    case DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_APPLY:
        //        action = DispensingOrderLog.Action.REDRAW_TO_PHARMACY;
        //    case DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_CONFIRM:
        //        action = DispensingOrderLog.Action.UNDISPENSE;
        //    case DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_REJECT:
        //        action = DispensingOrderLog.Action.REJECT_UNDISPENSE;
        //    case DispenseDispenseOrderReq.DispenseType.RECORD_NOT_DISPENSE_STOCK:
        //        action = DispensingOrderLog.Action.RECORD_DISPENSE;
        //    case DispenseDispenseOrderReq.DispenseType.STOCK_NOT_DISPENSE:
        //        action = DispensingOrderLog.Action.STOCK_DISPENSE;
        //}
        //if (action == DispensingOrderLog.Action.CREATE) {
        //    return;
        //}
        //StringBuilder sb = new StringBuilder();
        //AtomicInteger chineseMedicineCount = new AtomicInteger(0);
        //AtomicInteger westernMedicineCount = new AtomicInteger(0);
        //List<OperationRecordForDispense> details = new ArrayList<>();
        //for (DispenseDispenseOrderReq.DispenseSheetReq sheetReq : dispenseSheetReqs) {
        //    List<String> formItemIds = sheetReq.getAllDispenseFormItems().stream().map(DispenseDispenseOrderReq.DispenseFormItemReq::getId).collect(toList());
        //    if (CollectionUtils.isEmpty(formItemIds)) {
        //        continue;
        //    }
        //    DispensingSheetV2 dispensingSheetV2 = dispensingSheetV2Map.get(sheetReq.getId());
        //    if (dispensingSheetV2 == null) {
        //        continue;
        //    }
        //    List<DispensingFormV2> dispensingForms = dispensingSheetV2.getDispensingForms();
        //    if (CollectionUtils.isEmpty(dispensingForms)) {
        //        continue;
        //    }
        //    List<DispensingFormItemV2> formItemV2List = dispensingForms.stream().filter(item -> !CollectionUtils.isEmpty(item.getDispensingFormItems()))
        //            .flatMap(item -> item.getDispensingFormItems().stream()).filter(item -> formItemIds.contains(item.getId())).collect(toList());
        //    if (CollectionUtils.isEmpty(formItemV2List)) {
        //        continue;
        //    }
        //    Map<Integer, List<DispensingFormItemV2>> subTypeMap =
        //            formItemV2List.stream().filter(item -> item.getProductType() == GoodsConst.GoodsType.MEDICINE).collect(groupingBy(DispensingFormItemV2::getProductSubType));
        //    if (CollectionUtils.isEmpty(subTypeMap)) {
        //        continue;
        //    }
        //    addDispensingOrderLog(dispensingSheetV2, operatorName, details);
        //    subTypeMap.forEach((subType, list) -> {
        //        if (subType == GoodsConst.GoodsMedicineSubType.CHINESE_MEDICINE) {
        //            chineseMedicineCount.getAndAdd(list.size());
        //        } else {
        //            westernMedicineCount.getAndAdd(list.size());
        //        }
        //    });
        //}
        //if (westernMedicineCount.get() > 0) {
        //    sb.append("西药（").append(westernMedicineCount.get()).append("）种；");
        //}
        //if (chineseMedicineCount.get() > 0) {
        //    sb.append("中药处方（").append(chineseMedicineCount.get()).append("）味；");
        //}
        //if (!TextUtils.isEmpty(operatorName)) {
        //    sb.append("发药人（").append(operatorName).append("）");
        //}
        //DispensingOrderLog.LogDetail detail = new DispensingOrderLog.LogDetail();
        //detail.setSummery(sb.toString());
        //detail.setDetails(details);
        //DispensingOrderLog dispensingOrderLog = createDispensingOrderLog(chainId, orderId, action, JsonUtils.dumpAsJsonNode(detail), operatorId);
        //dispensingOrderLogList.add(dispensingOrderLog);
    }

    private void setDispensingOrderLogDetail(List<DispensingOrderLog.DispensingOrderLogNewDetail> newDetails,
                                             List<String> paientOrderIdList,
                                             List<DispensingSheetV2> dispensingSheetV2List,
                                             Map<String, DispensingFormItemV2> formItemIdToItemV2,
                                             Map<String, PatientOrderHospitalVO> patientOrderIdToHospitalVo) {
        Map<String, List<DispensingSheetV2>> patientOrderIdToSheetList = dispensingSheetV2List.stream().collect(groupingBy(DispensingSheetV2::getPatientOrderId));
        Map<String, List<DispensingFormItemV2>> patientOrderIdToItemList = formItemIdToItemV2.values().stream().collect(groupingBy(DispensingFormItemV2::getPatientOrderId));
        //Map<String, List<DispensingFormItemV2>> sheetIdToItemList = formItemIdToItemV2.values().stream().collect(groupingBy(DispensingFormItemV2::getDispensingSheetId));
        for (String patientOrderId : paientOrderIdList) {
            List<DispensingSheetV2> sheetV2List = patientOrderIdToSheetList.get(patientOrderId);
            if (CollectionUtils.isEmpty(sheetV2List)) {
                continue;
            }
            DispensingOrderLog.DispensingOrderLogNewDetail detail = new DispensingOrderLog.DispensingOrderLogNewDetail();
            // 患者信息
            PatientOrderHospitalVO patientOrder = patientOrderIdToHospitalVo.get(patientOrderId);
            if (Objects.nonNull(patientOrder)) {
                DispensingOrderLog.DispensingOrderLogPatient patient = new DispensingOrderLog.DispensingOrderLogPatient();
                patient.setDepartmentName(patientOrder.getDepartmentName());
                patient.setBedNo(patientOrder.getBedNo());
                if (patientOrder.getPatient() != null) {
                    patient.setPatientName(patientOrder.getPatient().getName());
                }
                detail.setPatient(patient);
            }
            newDetails.add(detail);
            // 处方信息，按处方分组，中药、西药、耗材
            List<DispensingFormRecordViewForDispense> formRecordViews = new ArrayList<>();
            List<DispensingFormItemV2> patientOrderItemList = patientOrderIdToItemList.get(patientOrderId);
            if (CollectionUtils.isEmpty(patientOrderItemList)) {
                continue;
            }
            Map<String, List<DispensingFormItemV2>> typeKeyToItemList = patientOrderItemList.stream().collect(groupingBy(it -> {
                if (DispensingUtils.isChineseMedicine(it.getProductType(), it.getProductSubType())) {
                    return "chinese";
                } else if (DispensingUtils.isWestMedicine(it.getProductType(), it.getProductSubType())) {
                    return "western";
                } else {
                    return "material";
                }
            }));
            typeKeyToItemList.forEach((type, itemList) -> {
                DispensingFormRecordViewForDispense formRecordView = new DispensingFormRecordViewForDispense();
                String sourceFromTypeName;
                int sourceFormType;
                switch (type) {
                    case "chinese":
                        sourceFromTypeName = "中药";
                        sourceFormType = DispensingForm.SourceFormType.PRESCRIPTION_CHINESE;
                        break;
                    case "western":
                        sourceFromTypeName = "中西成药";
                        sourceFormType = DispensingForm.SourceFormType.PRESCRIPTION_WESTERN;
                        break;
                    default:
                        sourceFromTypeName = "耗材商品";
                        sourceFormType = DispensingForm.SourceFormType.MATERIAL;
                }
                formRecordView.setSourceFormTypeName(sourceFromTypeName);
                formRecordView.setSourceFormType(sourceFormType);
                List<DispensingFormItemRecordView> operationFormItems = new ArrayList<>();
                itemList.forEach(formItem -> {
                    DispensingFormItemV2 needLogFormItem = formItemIdToItemV2.get(formItem.getId());
                    if (needLogFormItem == null) {
                        return;
                    }
                    DispensingFormItemRecordView formItemRecordView = new DispensingFormItemRecordView();
                    formItemRecordView.setId(formItem.getId());
                    formItemRecordView.setName(formItem.getName());
                    formItemRecordView.setUnit(formItem.getUnit());
                    formItemRecordView.setUnitCount(formItem.getUnitCount());
                    formItemRecordView.setDoseCount(formItem.getDoseCount());
                    operationFormItems.add(formItemRecordView);
                });
                formRecordView.setOperationFormItems(operationFormItems);
                formRecordViews.add(formRecordView);
                detail.setFormRecordViews(formRecordViews);
            });
        }
    }

    /**
     * Order 操作日志
     * 返回批量操作
     */
    public DispensingOrderLog createDispensingOrderLog(String chainId, Long orderId, int action, JsonNode detail, String operator) {
        DispensingOrderLog log = new DispensingOrderLog();
        log.setId(abcIdGenerator.getUIDLong());
        log.setChainId(chainId);
        log.setOrderId(orderId);
        log.setAction(action);
        log.setDetail(detail);
        log.setCreated(Instant.now());
        log.setCreatedBy(operator);
        return log;
    }

    private void addDispensingOrderLog(DispensingSheetV2 dispensingSheet, String operatorName,
                                       List<OperationRecordForDispense> sheets, Map<String, DispensingFormItemV2> itemIdToFormItem) {
        if (dispensingSheet == null) {
            return;
        }
        OperationRecordForDispense recordForDispense = new OperationRecordForDispense();
        recordForDispense.setDispensingName(operatorName);
        List<DispensingFormV2> dispensingForms = dispensingSheet.getDispensingForms();
        if (CollectionUtils.isEmpty(dispensingForms)) {
            return;
        }
        List<DispensingFormRecordViewForDispense> formRecordViews = new ArrayList<>();
        dispensingForms.forEach(form -> {
            DispensingFormRecordViewForDispense formRecordView = new DispensingFormRecordViewForDispense();
            formRecordView.setId(form.getId());
            formRecordView.setSourceFormType(form.getSourceFormType());
            if (form.getSourceFormType() == AdviceRuleType.WESTERN) {
                formRecordView.setSourceFormTypeName("中西成药处方");
            } else if (form.getSourceFormType() == AdviceRuleType.CHINESE_PIECE || form.getSourceFormType() == AdviceRuleType.CHINESE_GRANULE) {
                formRecordView.setSourceFormTypeName("中药处方");
            }
            List<DispensingFormItemRecordView> operationFormItems = new ArrayList<>();
            List<DispensingFormItemV2> dispensingFormItems = form.getDispensingFormItems();
            if (CollectionUtils.isEmpty(dispensingFormItems)) {
                return;
            }
            dispensingFormItems.forEach(formItem -> {
                DispensingFormItemV2 needLogFormItem = itemIdToFormItem.get(formItem.getId());
                if (needLogFormItem == null) {
                    return;
                }
                DispensingFormItemRecordView formItemRecordView = new DispensingFormItemRecordView();
                formItemRecordView.setId(formItem.getId());
                formItemRecordView.setName(formItem.getName());
                formItemRecordView.setUnit(formItem.getUnit());
                formItemRecordView.setUnitCount(formItem.getUnitCount());
                formItemRecordView.setDoseCount(formItem.getDoseCount());
                operationFormItems.add(formItemRecordView);
            });
            formRecordView.setOperationFormItems(operationFormItems);
            formRecordViews.add(formRecordView);
        });
        recordForDispense.setFormRecordViews(formRecordViews);

        sheets.add(recordForDispense);
    }

    /**
     * 发药追溯码处理
     */
    private void dealDispenseTraceCode(DispensingFormItemV2 dispensableDispenseItem, DispenseDispenseOrderReq.DispenseFormItemReq clientReqDispenseItem, GoodsDispenseDataItem goodsDispenseDataItem) {
        clientReqDispenseItem.setTraceableCodeList(Optional.ofNullable(clientReqDispenseItem.getTraceableCodeList()).orElse(new ArrayList<>())
                .stream().filter(traceableCode -> !TextUtils.isEmpty(traceableCode.getNo())).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(clientReqDispenseItem.getTraceableCodeList())
                && (dispensableDispenseItem.getExtendData() == null ||
                CollectionUtils.isEmpty(dispensableDispenseItem.getExtendData().getTraceableCodeList()))) {
            return;
        }
        //服务端没extend
        if (dispensableDispenseItem.getExtendData() == null) {
            dispensableDispenseItem.setExtendData(new DispensingFormItemExtendData());
            dispensableDispenseItem.getExtendData().setTraceableCodeList(new ArrayList<>());
        }
        if (dispensableDispenseItem.getExtendData().getTraceableCodeList() == null) {
            dispensableDispenseItem.getExtendData().setTraceableCodeList(new ArrayList<>());
        }

        List<TraceableCode> thisTimeDispenseTraceableCodeList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(clientReqDispenseItem.getTraceableCodeList())) {
            for (TraceableCode clientTraceableCode : clientReqDispenseItem.getTraceableCodeList()) {
                TraceableCode usedTraceableCode = new TraceableCode();
                usedTraceableCode.setId(clientTraceableCode.getId());
                usedTraceableCode.setNo(clientTraceableCode.getNo());
                dispensableDispenseItem.getExtendData().getTraceableCodeList().add(usedTraceableCode);
                usedTraceableCode.setBatchId(clientTraceableCode.getBatchId());
                usedTraceableCode.setType(clientTraceableCode.getType());
                usedTraceableCode.setUsed(DispensingConstants.TraceableCodeUsed.DISPENSE);//用掉
                if (MathUtils.wrapBigDecimalCompare(clientTraceableCode.getHisPieceCount(), BigDecimal.ZERO) > 0 || MathUtils.wrapBigDecimalCompare(clientTraceableCode.getHisPackageCount(), BigDecimal.ZERO) > 0) {
                    usedTraceableCode.setHisPackageCount(clientTraceableCode.getHisPackageCount());
                    usedTraceableCode.setHisPieceCount(clientTraceableCode.getHisPieceCount());
                } else {
                    usedTraceableCode.setCount(clientTraceableCode.getCount());
                }
                usedTraceableCode.setDismountingSn(clientTraceableCode.getDismountingSn());

                thisTimeDispenseTraceableCodeList.add(usedTraceableCode);
            }
        }
        if (thisTimeDispenseTraceableCodeList.isEmpty()) {
            if (dispensableDispenseItem.getExtendData().isEmpty()) {
                dispensableDispenseItem.setExtendData(null);
            }
            return;
        } else {
            dispensableDispenseItem.getExtendData().setTraceableCodeList(thisTimeDispenseTraceableCodeList);
        }

        /**
         * 如果是指定追溯码发药，把追溯码加到scGoods的发药请求里面
         * */
        if (goodsDispenseDataItem != null) {
            goodsDispenseDataItem.setTraceableCodeList(thisTimeDispenseTraceableCodeList);
            goodsDispenseDataItem.setShebaoDismountingFlag(clientReqDispenseItem.getShebaoDismountingFlag());
        }
    }

    /**
     * 发整个发药单
     *
     * @param operatorId 发药人
     * @note 三条调用路径
     * 1. 药房用户点发药
     * 2. 收费处直接发药
     * 3. 代煎代配药房通过消息过来的自动发药
     * <p>
     * 后续可以base作为中台发药基础代码
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public DispenseResult dispenseSheetHisCore(DispensingSheetV2 adviceDispenseSheet, //JPA发药单
                                               String patientOrderNo,
                                               DispenseDispenseOrderReq.DispenseSheetReq clientDispenseReq,//本次要发的
                                               String operatorId, List<DispensingFormItemV2> orderLogFormItemList,
                                               DispensingOrder dispensingOrder)
            throws ParamRequiredException, CisCustomException, ServiceInternalException {


        if (adviceDispenseSheet.getStatus() == DispenseConst.Status.UNDISPENSED
                || adviceDispenseSheet.getStatus() == DispenseConst.Status.CLOSED) {
            throw new WrongSheetStatusException();
        } else if (adviceDispenseSheet.getStatus() == DispenseConst.Status.DISPENSED) {
            sLogger.info("发药单已发药，id: {}", adviceDispenseSheet.getId());
            throw new DispenseSheetChangedException();
        }

        //用户请求的所有发药项
        List<DispenseDispenseOrderReq.DispenseFormItemReq> clientDispenseList = clientDispenseReq.getAllDispenseFormItems();
        if (CollectionUtils.isEmpty(clientDispenseList)) {
            return null;
        }

        //DB里面的发药单对象 --- 所有还能发的发药条目
        List<DispensingFormItemV2> toDispenseItems = adviceDispenseSheet.waitingDispenseItems();
        if (CollectionUtils.isEmpty(toDispenseItems)) {
            sLogger.warn("to dispense size == 0");
            throw new NoDispenseItemException();
        }

        int dispenseType = clientDispenseReq.getDispenseType();

        // 当前所有发药退药项
        List<DispensingFormItemV2> allDispenseItems = adviceDispenseSheet.allDispenseItems();
        Map<String, DispensingFormItemV2> formItemIdToFormItem = allDispenseItems.stream().collect(toMap(DispensingFormItemV2::getId, Function.identity(), (a, b) -> a));

        //通过formItemId找到Form,拿供应商信息
        Map<String, DispensingFormV2> formItemIdToForm = new HashMap<>();
        Map<String, DispensingFormV2> svrFormIdToForm = new HashMap<>();
        adviceDispenseSheet.getDispensingForms().forEach(dispensingForm -> {
            svrFormIdToForm.put(dispensingForm.getId(), dispensingForm);
            dispensingForm.getDispensingFormItems().forEach(dispensingFormItem -> {
                formItemIdToForm.put(dispensingFormItem.getId(), dispensingForm);
            });
        });
        if (!clientDispenseReq.getUnDispense()) {
            // 发药
            for (DispenseDispenseOrderReq.DispenseFormReq formReq : clientDispenseReq.getDispenseFormReqs()) {
                DispensingFormV2 dispensingFormV2 = svrFormIdToForm.get(formReq.getId());
                if (dispensingFormV2 == null) {
                    throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "未找到发药项");
                }
                // 自备药校验
                Set<String> clientFormItemIdSet = formReq.getDispenseFormItemReqs().stream().map(DispenseDispenseOrderReq.DispenseFormItemReq::getId).collect(toSet());
                dispensingFormV2.getDispensingFormItems().forEach(item -> {
                    if (clientFormItemIdSet.contains(item.getId()) && item.isSelfProvidedByStatus()) {
                        throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "自备药不可发药");
                    }
                });
                DispenseUsageInfo dispenseUsageInfo = JsonUtils.readValue(dispensingFormV2.getUsageInfoJson(), DispenseUsageInfo.class);
                if (dispenseType == DispenseDispenseOrderReq.DispenseType.RECORD_NOT_DISPENSE_STOCK || dispenseType == DispenseDispenseOrderReq.DispenseType.STOCK_NOT_DISPENSE) {
                    if (dispenseUsageInfo == null || CollectionUtils.isEmpty(dispenseUsageInfo.getAdviceTags())) {
                        // 没有补开标签，只能是正常医嘱，正常医嘱只能发药或拒发,不能补记或扣库
                        throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "非补开医嘱不支持仅扣库操作");
                    }
                }
            }
        }
        /**
         * 构造发药请求
         * 1. 本次发药的
         * 2. 发药的数量  要么大 要么小
         * 3. 发药的实收
         * a.toDispenseItems 是本次 发药的goodsId
         * b.一个Goods 只能一次发完，所以这里直接用db里面记录的发药FormItem的发药数量来进行发药
         * c.一个goods在一个处方里面 不能同时开出大小单位
         * */
        List<GoodsDispenseDataItem> goodsDispenseDataItems = new ArrayList<>();
        Map<String, GoodsDispenseDataItem> dispensingFormItemIdToDispenseDataItem = new HashMap<>();
        Map<String, DispensingFormItemV2> formItemIdToSelfFormItem = new HashMap<>();
        Map<String, GoodsDispenseResultItem> formItemIdToResultItem = new HashMap<>();
        if (clientDispenseReq.getUnDispense() == false
                && (dispenseType == DispenseDispenseOrderReq.DispenseType.DISPENSE_CONFIRM || dispenseType == DispenseDispenseOrderReq.DispenseType.STOCK_NOT_DISPENSE)) {
            // 发药、扣库才调用goods
            //取回锁库Id
            for (DispenseDispenseOrderReq.DispenseFormItemReq clientReqDispenseItem : clientDispenseList) {
                DispensingFormItemV2 toDispenseItem = formItemIdToFormItem.get(clientReqDispenseItem.getId());
                if (toDispenseItem == null) {
                    continue;
                }
                if (clientReqDispenseItem.getDispenseType() == DispenseDispenseOrderReq.DispenseType.DISPENSE_REJECT) {
                    // item拒发不发药
                    log.info("item拒发，item: {}", toDispenseItem);
                    continue;
                }

                //自备不发到scGoods扣库
                if (toDispenseItem.isSelfProvidedBySourceItemType()) {
                    formItemIdToSelfFormItem.put(clientReqDispenseItem.getId(), toDispenseItem);
                    continue;
                }
                BigDecimal existedDispensedCount = MathUtils.calculateTotalCount(toDispenseItem.getUnitCount(), toDispenseItem.getDoseCount());
                GoodsDispenseDataItem goodsDispenseDataItem = new GoodsDispenseDataItem();
                goodsDispenseDataItem.setPharmacyNo(toDispenseItem.getPharmacyNo());
                goodsDispenseDataItem.setDispenseItemId(toDispenseItem.getId());
                goodsDispenseDataItem.setGoodsId(toDispenseItem.getProductId());
                if (toDispenseItem.getLockId() != null) {
                    // 优先用发药单自身记录的锁库Id
                    goodsDispenseDataItem.setLockId(toDispenseItem.getLockId());
                }
                goodsDispenseDataItem.setBatchCountList(buildBatchCountList(toDispenseItem.getGoodsItem(), toDispenseItem));
                if (toDispenseItem.lockFailOrNeedLock() && CollectionUtils.isEmpty(goodsDispenseDataItem.getBatchCountList())) {
                    throw new DispensingServiceException(DispensingServiceError.DISPENSE_BATCH_MISS_ERROR);
                }
                if (!CollectionUtils.isEmpty(goodsDispenseDataItem.getBatchCountList())) {
                    goodsDispenseDataItem.setLockBatch(YesOrNo.YES);
                }

                if (toDispenseItem.getUseDismounting() == 1) {
                    goodsDispenseDataItem.setPieceCount(existedDispensedCount);
                    goodsDispenseDataItem.setPackageCount(BigDecimal.ZERO);
                    goodsDispenseDataItem.setTotalDispenseFormItemPieceCount(existedDispensedCount); //医院无多次发
                    goodsDispenseDataItem.setTotalDispenseFormItemPackageCount(BigDecimal.ZERO);
                } else {
                    goodsDispenseDataItem.setPackageCount(existedDispensedCount);
                    goodsDispenseDataItem.setPieceCount(BigDecimal.ZERO);
                    goodsDispenseDataItem.setTotalDispenseFormItemPackageCount(existedDispensedCount);
                    goodsDispenseDataItem.setTotalDispenseFormItemPieceCount(BigDecimal.ZERO);
                }

                /**
                 * 虚拟药房供应商，指定了会强制扣这个供应商的库存  (退药不需要指定)
                 * */
                DispensingFormV2 dispensingForm = formItemIdToForm.get(clientReqDispenseItem.getId());
                if (dispensingForm != null && dispensingForm.getVendorId() != null) {
                    goodsDispenseDataItem.setSupplierShadowId(dispensingForm.getVendorId());
                }

                goodsDispenseDataItem.setTotalPrice(toDispenseItem.getTotalPrice());
                dispensingFormItemIdToDispenseDataItem.put(toDispenseItem.getId(), goodsDispenseDataItem);
                goodsDispenseDataItems.add(goodsDispenseDataItem);
                dealDispenseTraceCode(toDispenseItem, clientReqDispenseItem, goodsDispenseDataItem);
            }
            /***
             * 调用ScGoods发药，并拿到本次发药结果
             */
            if (goodsDispenseDataItems.size() != 0) {
                List<GoodsDispenseResultItem> list = abcCisScGoodsService.dispense(
                        adviceDispenseSheet.getClinicId(),
                        adviceDispenseSheet.getChainId(),
                        operatorId,
                        patientOrderNo,
                        adviceDispenseSheet.getPatientOrderId(),
                        adviceDispenseSheet.getId(),
                        adviceDispenseSheet.getPharmacyNo(),
                        adviceDispenseSheet.getPharmacyType(),
                        goodsDispenseDataItems,
                        DTOConverter.dispensingFormItemV2MapToDispensingFormItemAbstractMap(formItemIdToFormItem),
                        null,
                        10);
                if (CollectionUtils.isEmpty(list) || list.size() != goodsDispenseDataItems.size()) {
                    throw new GoodsDispenseException();
                }
                formItemIdToResultItem.putAll(list.stream().collect(toMap(GoodsDispenseResultItem::getDispenseItemId, Function.identity(), (a, b) -> a)));
            }
        }//end  是否有goods要发
        String operationId = abcIdGenerator.getUID();
        List<DispensingFormItemV2> thisTimeDispensingFormItems = new ArrayList<>();
        List<String> finalDispensedByIds = Arrays.asList(operatorId);
        /**
         * 更新发药单
         * */
        updateDispenseToSheet(operationId,
                clientDispenseReq,
                adviceDispenseSheet,
                clientDispenseList,//用户请求的所有发药项
                dispensingFormItemIdToDispenseDataItem,
                formItemIdToResultItem,
                finalDispensedByIds,
                operatorId,
                thisTimeDispensingFormItems,//输出参数，allDispenseItems里面本次发药项 用于写日志
                formItemIdToFormItem,//发药单上所有发药退药的 map
                formItemIdToSelfFormItem,//输入参数 ，自备药品 没有去scGoods 但是还是要更新 发药Item
                orderLogFormItemList
        );

        /**
         * 发药完成后的处理
         * */
        saveAssembleDispenseSheet(adviceDispenseSheet);
//        dispensingSheetV2Repository.save(adviceDispenseSheet);
        dispensingLogService.logDispensingSheetDispense(adviceDispenseSheet, thisTimeDispensingFormItems, formItemIdToResultItem, operationId, operatorId);

        /**
         * 发药Operation
         * */
        SheetOperationBase operationBase = sheetOperationCreateFactory.createSheetOperation(DispensingSheetOperation.OperationType.SEND_MEDICINE, adviceDispenseSheet.getId(), operatorId, operatorId, adviceDispenseSheet.getChainId(), adviceDispenseSheet.getClinicId());
        operationBase.bindDispensingSheetV2(adviceDispenseSheet)
                .bindDispensingFormItemsV2(thisTimeDispensingFormItems)
                .bindDispensedByIds(finalDispensedByIds)
                .bindRepoId(operationId);
        operationBase.createAndSaveSheetOperation();


        if (clientDispenseReq.getUnDispense() == false
                && ((dispenseType == DispenseDispenseOrderReq.DispenseType.DISPENSE_CONFIRM
                || dispenseType == DispenseDispenseOrderReq.DispenseType.STOCK_NOT_DISPENSE
                || dispenseType == DispenseDispenseOrderReq.DispenseType.RECORD_NOT_DISPENSE_STOCK))) {
            DispenseResult dispenseResult = new DispenseResult();
            dispenseResult.setStatus(adviceDispenseSheet.getStatus());
            dispenseResult.setDispensedCount(goodsDispenseDataItems.size());
            dispenseResult.setId(adviceDispenseSheet.getId());
            /**
             * 发药事件，具体这个发药单是不是全部发完，要看sheet里面的状态
             * */
//        sendDispensingWechatMessage(adviceDispenseSheet, chargeSheet, operatorId);

            // 发送发药消息
            adviceDispenseSheet.setDispensingOrder(dispensingOrder);
            rocketMqProducer.sendDispensingSheetUpdateAfterCommitAsync(PatientOrderMessage.MSG_SUB_TYPE_DISPENGING_UPDATED_DISPENSE, Lists.newArrayList(adviceDispenseSheet));
            return dispenseResult;
        } else {
            return null;
        }
    }

    private List<GoodsDispenseDataItem.BatchCount> buildBatchCountList(GoodsItem goodsItem,
                                                                       DispensingFormItemV2 toDispenseItem) {
        if ((goodsItem == null || CollectionUtils.isEmpty(goodsItem.getGoodsBatchInfoList()))
                && CollectionUtils.isEmpty(toDispenseItem.getDispensingFormItemBatches())) {
            return Lists.newArrayList();
        }

        if (!CollectionUtils.isEmpty(toDispenseItem.getDispensingFormItemBatches())) {
            // 如果发药项记录了批次信息，直接用发药项记录的批次信息
            return toDispenseItem.getDispensingFormItemBatches().stream().map(batch -> {
                GoodsDispenseDataItem.BatchCount batchCount = new GoodsDispenseDataItem.BatchCount();
                batchCount.setBatchId(batch.getBatchId());
                if (toDispenseItem.getUseDismounting() == 1) {
                    batchCount.setPieceCount(batch.getUnitCount());
                } else {
                    batchCount.setPackageCount(batch.getUnitCount());
                }
                return batchCount;
            }).collect(toList());
        } else {
            if ((goodsItem.getShortagePackageCount() != null
                    && goodsItem.getShortagePackageCount().compareTo(BigDecimal.ZERO) != 0)
                    || (goodsItem.getShortagePieceCount() != null
                    && goodsItem.getShortagePieceCount().compareTo(BigDecimal.ZERO) != 0)) {
                return Lists.newArrayList();
            }

            List<GoodsDispenseDataItem.BatchCount> batchCountList = Lists.newArrayList();
            for (GoodsBatchInfo goodsBatchInfo : goodsItem.getGoodsBatchInfoList()) {
                GoodsDispenseDataItem.BatchCount batchCount = new GoodsDispenseDataItem.BatchCount();
                batchCount.setBatchId(goodsBatchInfo.getBatchId());
                if (toDispenseItem.getUseDismounting() == 1) {
                    batchCount.setPieceCount(goodsBatchInfo.getCutPieceCount());
                } else {
                    batchCount.setPackageCount(goodsBatchInfo.getCutPackageCount());
                }
                batchCountList.add(batchCount);
            }
            return batchCountList;
        }
    }

    private void saveAssembleDispenseSheet(DispensingSheetV2 dispenseSheet) {
        dispensingSheetV2Repository.save(dispenseSheet);
        if (!CollectionUtils.isEmpty(dispenseSheet.getDispensingForms())) {
            dispensingFormV2Repository.saveAll(dispenseSheet.getDispensingForms());
        }
        List<DispensingFormItemV2> formItemV2List = dispenseSheet.getDispensingForms().stream().flatMap(dispensingFormV2 -> dispensingFormV2.getDispensingFormItems().stream()).collect(toList());
        if (!CollectionUtils.isEmpty(formItemV2List)) {
            dispensingFormItemV2Repository.saveAll(formItemV2List);
        }
    }

    /**
     * scGood扣库完更新 发药表
     */
    private void updateDispenseToSheet(String dispenseOperatorId,//发药人和操作人可以不是同一个人
                                       DispenseDispenseOrderReq.DispenseSheetReq clientDispenseReq,
                                       DispensingSheetV2 dispenseSheet,//发药单
                                       List<DispenseDispenseOrderReq.DispenseFormItemReq> clientDispenseList,//本次请求发药的项目
                                       Map<String, GoodsDispenseDataItem> dispensingFormItemIdToDispenseDataItem, // 发药请求
                                       Map<String, GoodsDispenseResultItem> formItemIdToResultItem,// 发药结果
                                       List<String> dispensedByIds,//多人发药
                                       String employeeId,//操作人
                                       List<DispensingFormItemV2> thisTimeDispensingFormItems,//输出参数，allDispenseItems里面本次发药项 用于写日志
                                       Map<String, DispensingFormItemV2> formItemIdToFormItem,//发药单上所有发药退药的 map
                                       Map<String, DispensingFormItemV2> formItemIdToSelfFormItem,//输入参数 ，自备药品// 没有去scGoods 但是还是要更新 发药Item
                                       List<DispensingFormItemV2> orderLogFormItemList
    ) {

        DispensingSheetInfo dispensingSheetInfo = new DispensingSheetInfo();
        dispensingSheetInfo.setDispensedByIds(dispensedByIds);
        String dispensedBy = dispensedByIds.get(0);
        int dispenseType = clientDispenseReq.getDispenseType();

        /**
         * 根据scGoods的发药返回结果 ，将发药结果写回到发药单
         * existedDispenseItem 发过药就被记录成了 DispensingFormItem.Status.DISPENSED 状态
         * */
        for (DispenseDispenseOrderReq.DispenseFormItemReq dispenseFormItemReq : clientDispenseList) {
            //对应的发药记录
            DispensingFormItemV2 existedDispenseItem = formItemIdToFormItem.get(dispenseFormItemReq.getId());
            if (existedDispenseItem == null) {
                sLogger.error("严重错误，理论上走不进来");
                continue;
            }
            boolean isChineseMedicine = DispensingUtils.isChineseMedicine(existedDispenseItem.getProductType(), existedDispenseItem.getProductSubType());
            switch (dispenseType) {
                case DispenseDispenseOrderReq.DispenseType.DISPENSE_REJECT:
                case DispenseDispenseOrderReq.DispenseType.RECORD_NOT_DISPENSE_STOCK:
                    if (dispenseType == DispenseDispenseOrderReq.DispenseType.DISPENSE_REJECT) {
                        existedDispenseItem.setStatus(DispenseConst.Status.APPLY_DISPENSE_REJECT);
                    } else {
                        existedDispenseItem.setStatus(DispenseConst.Status.RECORD_NOT_DISPENSE);
                    }
                    FillUtils.fillLastModifiedBy(existedDispenseItem, employeeId);
                    break;
                case DispenseDispenseOrderReq.DispenseType.DISPENSE_CONFIRM:
                case DispenseDispenseOrderReq.DispenseType.STOCK_NOT_DISPENSE:
                    if (isChineseMedicine) {
                        // 拒发
                        if (dispenseFormItemReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.DISPENSE_REJECT) {
                            existedDispenseItem.setStatus(DispenseConst.Status.APPLY_DISPENSE_REJECT);
                            break;
                        }
                    }
                    GoodsDispenseDataItem goodsDispenseDataItem = dispensingFormItemIdToDispenseDataItem.get(existedDispenseItem.getId());
                    GoodsDispenseResultItem resultItem = formItemIdToResultItem.get(existedDispenseItem.getId());
                    if (resultItem != null) {
                        if (dispenseType == DispenseDispenseOrderReq.DispenseType.DISPENSE_CONFIRM) {
                            existedDispenseItem.setStatus(DispenseConst.Status.DISPENSED);
                        } else {
                            existedDispenseItem.setStatus(DispenseConst.Status.STOCK_NOT_DISPENSE);
                        }
                        // 打个标记，标记本次发药
                        existedDispenseItem.setThisTimeDispensed(1);
                        // 存在上次失败，再次发药情况，goods会把前面成功的信息返回取第一个结果
                        existedDispenseItem.setStockDealId(DispensingUtils.getGoodsDispenseResultItemDealId(resultItem, true));//SCGoods返回的key，可以通过这个id和进销存关联起来
                        existedDispenseItem.setTotalCostPrice(MathUtils.wrapBigDecimal(resultItem.getTotalCostPrice(), BigDecimal.ZERO));
                        existedDispenseItem.setOperationId(dispenseOperatorId);//拒发人
                        GoodsItem goodsItem = existedDispenseItem.getGoodsItem();
                        if (!CollectionUtils.isEmpty(existedDispenseItem.getDispensingFormItemBatches()) || existedDispenseItem.lockFailOrNeedLock()) {
                            // 如果有批次信息，或者之前锁库失败，更新批次信息
                            updateDispenseToDispensingFormItemBatch(existedDispenseItem, goodsItem, goodsDispenseDataItem, resultItem, employeeId);
                        }
                        if (!CollectionUtils.isEmpty(existedDispenseItem.getDispensingFormItemBatches())) {
                            // 用批次的价格覆盖发药项的价格
                            existedDispenseItem.setTotalCostPrice(existedDispenseItem.getDispensingFormItemBatches().stream().map(DispensingFormItemBatchInfo::getTotalCostPrice).reduce(BigDecimal.ZERO, MathUtils::wrapBigDecimalAdd));
                            existedDispenseItem.setTotalPrice(existedDispenseItem.getDispensingFormItemBatches().stream().map(DispensingFormItemBatchInfo::getTotalPrice).reduce(BigDecimal.ZERO, MathUtils::wrapBigDecimalAdd));
                        }
                        FillUtils.fillLastModifiedBy(existedDispenseItem, employeeId);
                        thisTimeDispensingFormItems.add(existedDispenseItem);
                    }
                    //resultItem == null  不一定是自备药，可能是发药失败，所以还是用这个逻辑判断自备 更新自备药品发药状态
                    DispensingFormItemV2 selfProvidedToDispenseItem = formItemIdToSelfFormItem.get(existedDispenseItem.getId());
                    if (existedDispenseItem.getSourceItemType() == DispensingFormItem.SourceItemType.SELF_PROVIDED
                            && selfProvidedToDispenseItem != null) {
                        existedDispenseItem.setStatus(DispensingFormItem.Status.DISPENSED);
                        /**
                         * 打个标记，标记本次发药
                         * */
                        existedDispenseItem.setThisTimeDispensed(1);
                        existedDispenseItem.setOperationId(dispenseOperatorId);
                        FillUtils.fillLastModifiedBy(existedDispenseItem, employeeId);
                    }
                    break;
            }
            //设置发药备注
            if (!StringUtils.isEmpty(dispenseFormItemReq.getDispenseRemark())) {
                DispenseUsageInfo usageInfo = DispensingUtils.toDbUsageInfo(existedDispenseItem.getUsageInfoJson());
                if (usageInfo == null) {
                    usageInfo = DispensingSheetServerCreateReqFactory.createUsageInfo();
                }
                usageInfo.setDispenseRemark(dispenseFormItemReq.getDispenseRemark());
                existedDispenseItem.setUsageInfoJson(JsonUtils.dump(usageInfo));
            }
            orderLogFormItemList.add(existedDispenseItem);
        }
        dispenseSheet.fixHisSheetStatus();
        if (dispenseSheet.getStatus() == DispenseConst.Status.DISPENSED) {
            dispenseSheet.setOrderByDate(Instant.now());
        }
        dispenseSheet.setDispensedTime(Instant.now());
        if (TextUtils.isEmpty(dispensedBy)) {
            dispensedBy = employeeId;
        }
        dispenseSheet.setDispensedBy(dispensedBy);
        dispenseSheet.setDispensingSheetInfo(dispensingSheetInfo);
        FillUtils.fillLastModifiedBy(dispenseSheet, employeeId);
        dispensingSheetEntityService.save(dispenseSheet);
    }

    private void updateDispenseToDispensingFormItemBatch(DispensingFormItemV2 existedDispenseItem,
                                                         GoodsItem goodsItem,
                                                         GoodsDispenseDataItem goodsDispenseDataItem,
                                                         GoodsDispenseResultItem resultItem,
                                                         String employeeId) {
        if (existedDispenseItem == null || goodsDispenseDataItem == null
                || CollectionUtils.isEmpty(goodsDispenseDataItem.getBatchCountList())
                || resultItem == null || CollectionUtils.isEmpty(resultItem.getOriginalDispenseBatchInfoList())) {
            return;
        }
        List<GoodsDispenseResultItem.DispenseBatchInfo> thisTimeDispenseBatchInfo = resultItem.getOriginalDispenseBatchInfoList();

        if (existedDispenseItem.lockFailOrNeedLock() && goodsItem != null && !CollectionUtils.isEmpty(goodsItem.getGoodsBatchInfoList())
                && CollectionUtils.isEmpty(existedDispenseItem.getDispensingFormItemBatches())) {
            // 说明是生成发药单时锁库失败了，然后手动发药成功了，写入批次信息
            Map<Long, GoodsBatchInfo> batchIdToGoodsBatchInfo = ListUtils.toMap(goodsItem.getGoodsBatchInfoList(), GoodsBatchInfo::getBatchId);
            List<DispensingFormItemBatchInfo> dispensingFormItemBatches = thisTimeDispenseBatchInfo.stream().map(dispenseBatchInfo -> {
                if (dispenseBatchInfo == null) {
                    return null;
                }
                Long batchId = dispenseBatchInfo.getBatchId();
                GoodsBatchInfo goodsBatchInfo = batchIdToGoodsBatchInfo.get(batchId);
                if (goodsBatchInfo == null) {
                    log.error("goodsBatchInfo is null, batchId:{}", batchId);
                    throw new DispenseSheetChangedException();
                }

                DispensingFormItemBatchInfo dispensingFormItemBatchInfo = new DispensingFormItemBatchInfo();
                dispensingFormItemBatchInfo.setId(abcIdGenerator.getUIDLong());
                dispensingFormItemBatchInfo.setClinicId(existedDispenseItem.getClinicId());
                dispensingFormItemBatchInfo.setChainId(existedDispenseItem.getChainId());
                dispensingFormItemBatchInfo.setPatientOrderId(existedDispenseItem.getPatientOrderId());
                dispensingFormItemBatchInfo.setDispensingSheetId(existedDispenseItem.getDispensingSheetId());
                dispensingFormItemBatchInfo.setDispensingFormId(existedDispenseItem.getDispensingFormId());
                dispensingFormItemBatchInfo.setDispensingFormItemId(existedDispenseItem.getId());
                dispensingFormItemBatchInfo.setTotalPrice(goodsBatchInfo.getTotalSalePrice());
                BigDecimal stockCount = DispensingUtils.calculateStockCount(existedDispenseItem.getUseDismounting() == 1, dispenseBatchInfo.getPackageCount(), goodsItem.getPieceNum(), dispenseBatchInfo.getPieceCount());
                dispensingFormItemBatchInfo.setUnitCount(stockCount);
                int itemCalculateUnitPriceScale = DispensingUtils.getItemCalculateUnitPriceScale(goodsItem.getType(), goodsItem.getSubType());
                MathUtils.CalculateExpectedUnitPriceResult calculateExpectedUnitPriceResult = MathUtils.calculateExpectedUnitPriceBase(dispensingFormItemBatchInfo.getUnitCount(), BigDecimal.ONE, dispensingFormItemBatchInfo.getTotalPrice(), itemCalculateUnitPriceScale, 2);
                dispensingFormItemBatchInfo.setUnitPrice(calculateExpectedUnitPriceResult.expectedUnitPrice);
                dispensingFormItemBatchInfo.setTotalCostPrice(dispenseBatchInfo.getTotalCostPrice());
                MathUtils.CalculateExpectedUnitPriceResult calculateExpectedCostUnitPriceResult = MathUtils.calculateExpectedUnitPriceBase(dispensingFormItemBatchInfo.getUnitCount(), BigDecimal.ONE, dispensingFormItemBatchInfo.getTotalCostPrice(), 4, 4);
                dispensingFormItemBatchInfo.setUnitCostPrice(calculateExpectedCostUnitPriceResult.expectedUnitPrice);
                dispensingFormItemBatchInfo.setSourceUnitPrice(dispensingFormItemBatchInfo.getUnitPrice());
                dispensingFormItemBatchInfo.setSourceTotalPrice(dispensingFormItemBatchInfo.getTotalPrice());
                dispensingFormItemBatchInfo.setUndispenseUnitCount(BigDecimal.ZERO);
                dispensingFormItemBatchInfo.setDispenseUnitCount(dispensingFormItemBatchInfo.getUnitCount());
                dispensingFormItemBatchInfo.setProductId(existedDispenseItem.getProductId());
                dispensingFormItemBatchInfo.setBatchId(dispenseBatchInfo.getBatchId());
                dispensingFormItemBatchInfo.setBatchNo(goodsBatchInfo.getBatchNo());
                FillUtils.fillCreatedBy(dispensingFormItemBatchInfo, employeeId);
                return dispensingFormItemBatchInfo;
            }).collect(toList());
            existedDispenseItem.setDispensingFormItemBatches(dispensingFormItemBatches);
        } else if (!CollectionUtils.isEmpty(existedDispenseItem.getDispensingFormItemBatches())) {
            if (CollectionUtils.isEmpty(thisTimeDispenseBatchInfo)) {
                log.error("thisTimeDispenseBatchInfo is empty, existedDispenseItem:{}", existedDispenseItem);
                throw new DispenseSheetChangedException();
            }
            // 已经有批次信息，则更新批次信息
            List<DispensingFormItemBatchInfo> dispensingFormItemBatches = existedDispenseItem.getDispensingFormItemBatches();
            Map<Long, DispensingFormItemBatchInfo> batchIdToDispensingFormItemBatchInfo = ListUtils.toMap(dispensingFormItemBatches, DispensingFormItemBatchInfo::getBatchId);
            boolean hasError = false;
            for (GoodsDispenseResultItem.DispenseBatchInfo dispenseBatchInfo : thisTimeDispenseBatchInfo) {
                Long batchId = dispenseBatchInfo.getBatchId();
                DispensingFormItemBatchInfo dispensingFormItemBatchInfo = batchIdToDispensingFormItemBatchInfo.get(batchId);
                if (dispensingFormItemBatchInfo == null) {
                    log.error("发了一个没有锁库的批次, batchId:{}", batchId);
                    hasError = true;
                    break;
                }

                BigDecimal stockCount = DispensingUtils.calculateStockCount(existedDispenseItem.getUseDismounting() == 1,
                        dispenseBatchInfo.getPackageCount(), BigDecimal.valueOf(dispenseBatchInfo.getPieceNum()), dispenseBatchInfo.getPieceCount());
                dispensingFormItemBatchInfo.setDispenseUnitCount(MathUtils.wrapBigDecimalAdd(dispensingFormItemBatchInfo.getDispenseUnitCount(), stockCount));
                if (dispenseBatchInfo.getTotalCostPrice() != null) {
                    dispensingFormItemBatchInfo.setTotalCostPrice(dispenseBatchInfo.getTotalCostPrice());
                }
                if (dispensingFormItemBatchInfo.getTotalCostPrice() != null) {
                    MathUtils.CalculateExpectedUnitPriceResult calculateExpectedUnitPriceResult = MathUtils.calculateExpectedUnitPriceBase(stockCount, BigDecimal.ONE, dispensingFormItemBatchInfo.getTotalCostPrice(), 4, 4);
                    dispensingFormItemBatchInfo.setUnitCostPrice(calculateExpectedUnitPriceResult.expectedUnitPrice);
                }
                if (MathUtils.wrapBigDecimalCompare(dispensingFormItemBatchInfo.getDispenseUnitCount(), dispensingFormItemBatchInfo.getUnitCount()) > 0) {
                    log.error("发药数量大于锁库数量, batchId:{}", batchId);
                    hasError = true;
                    break;
                }
            }

            if (hasError) {
                // 如果有失败的话，就删除现有的批次
                existedDispenseItem.getDispensingFormItemBatches().forEach(batch -> {
                    batch.setIsDeleted(1);
                });
                dispensingFormItemBatchInfoRepository.saveAll(existedDispenseItem.getDispensingFormItemBatches());
                existedDispenseItem.getDispensingFormItemBatches().clear();

                // 用实际发的批次覆盖现有的
                List<DispensingFormItemBatchInfo> newDispensingFormItemBatches = thisTimeDispenseBatchInfo.stream().map(dispenseBatchInfo -> {
                    if (dispenseBatchInfo == null) {
                        return null;
                    }
                    DispensingFormItemBatchInfo dispensingFormItemBatchInfo = new DispensingFormItemBatchInfo();
                    dispensingFormItemBatchInfo.setId(abcIdGenerator.getUIDLong());
                    dispensingFormItemBatchInfo.setClinicId(existedDispenseItem.getClinicId());
                    dispensingFormItemBatchInfo.setChainId(existedDispenseItem.getChainId());
                    dispensingFormItemBatchInfo.setPatientOrderId(existedDispenseItem.getPatientOrderId());
                    dispensingFormItemBatchInfo.setDispensingSheetId(existedDispenseItem.getDispensingSheetId());
                    dispensingFormItemBatchInfo.setDispensingFormId(existedDispenseItem.getDispensingFormId());
                    dispensingFormItemBatchInfo.setDispensingFormItemId(existedDispenseItem.getId());
                    BigDecimal stockCount = DispensingUtils.calculateStockCount(existedDispenseItem.getUseDismounting() == 1, dispenseBatchInfo.getPackageCount(), BigDecimal.valueOf(dispenseBatchInfo.getPieceNum()), dispenseBatchInfo.getPieceCount());
                    BigDecimal packagePrice = dispenseBatchInfo.getPackagePrice();
                    BigDecimal totalSellPrice = calculateTotalSellPrice(stockCount, existedDispenseItem.getUseDismounting(), packagePrice, dispenseBatchInfo.getPieceNum());
                    dispensingFormItemBatchInfo.setTotalPrice(totalSellPrice);
                    dispensingFormItemBatchInfo.setUnitCount(stockCount);
                    int itemCalculateUnitPriceScale = DispensingUtils.getItemCalculateUnitPriceScale(existedDispenseItem.getProductType(), existedDispenseItem.getProductSubType());
                    MathUtils.CalculateExpectedUnitPriceResult calculateExpectedUnitPriceResult = MathUtils.calculateExpectedUnitPriceBase(dispensingFormItemBatchInfo.getUnitCount(), BigDecimal.ONE, dispensingFormItemBatchInfo.getTotalPrice(), itemCalculateUnitPriceScale, 2);
                    dispensingFormItemBatchInfo.setUnitPrice(calculateExpectedUnitPriceResult.expectedUnitPrice);
                    dispensingFormItemBatchInfo.setTotalCostPrice(dispenseBatchInfo.getTotalCostPrice());
                    MathUtils.CalculateExpectedUnitPriceResult calculateExpectedCostUnitPriceResult = MathUtils.calculateExpectedUnitPriceBase(dispensingFormItemBatchInfo.getUnitCount(), BigDecimal.ONE, dispensingFormItemBatchInfo.getTotalCostPrice(), 4, 4);
                    dispensingFormItemBatchInfo.setUnitCostPrice(calculateExpectedCostUnitPriceResult.expectedUnitPrice);
                    dispensingFormItemBatchInfo.setSourceUnitPrice(dispensingFormItemBatchInfo.getUnitPrice());
                    dispensingFormItemBatchInfo.setSourceTotalPrice(dispensingFormItemBatchInfo.getTotalPrice());
                    dispensingFormItemBatchInfo.setUndispenseUnitCount(BigDecimal.ZERO);
                    dispensingFormItemBatchInfo.setDispenseUnitCount(dispensingFormItemBatchInfo.getUnitCount());
                    dispensingFormItemBatchInfo.setProductId(existedDispenseItem.getProductId());
                    dispensingFormItemBatchInfo.setBatchId(dispenseBatchInfo.getBatchId());
                    dispensingFormItemBatchInfo.setBatchNo(dispenseBatchInfo.getBatchNo());
                    FillUtils.fillCreatedBy(dispensingFormItemBatchInfo, employeeId);
                    return dispensingFormItemBatchInfo;
                }).collect(toList());
                existedDispenseItem.getDispensingFormItemBatches().addAll(newDispensingFormItemBatches);
            }
        }
    }

    private BigDecimal calculateTotalSellPrice(BigDecimal unitCount, int isDismounting, BigDecimal packagePrice, Integer pieceNum) {
        BigDecimal piecePrice = packagePrice.divide(BigDecimal.valueOf(pieceNum), 2, RoundingMode.DOWN);
        BigDecimal sellTotalPrice;
        if (isDismounting == 1) {
            sellTotalPrice = unitCount.multiply(piecePrice);
        } else {
            sellTotalPrice = unitCount.multiply(packagePrice);
        }

        return sellTotalPrice.setScale(2, RoundingMode.DOWN);
    }

    /**
     * 批量患者获取发药单
     */
    public AbcListPage<DispensingSheetView> listDispensingSheet(DispensingSheetPatientOrderIdsReq clientReq) {
        String chainId = clientReq.getChainId();
        String clinicId = clientReq.getClinicId();
        List<String> patientOrderIds = clientReq.getPatientOrderIds();
        AbcListPage<DispensingSheetView> clientRsp = new AbcListPage<>();
        if (!StringUtils.isEmpty(clientReq.getKeyword())) {
            List<String> dispensingSheetIdList = getDispensingOrderIdBySearchGoodsName(clientReq);
            if (CollectionUtils.isEmpty(dispensingSheetIdList)) {
                return clientRsp;
            }
            clientReq.setDispensingSheetIdList(dispensingSheetIdList);
        }
        //ToDo 一个发药单sheet同时关联领药单和取药单 取最后1次？
        List<Long> dispensingOrderIdList = dispensingMapper.findDispensingOrderIdByPatientOrderIdList(clientReq);
        if (CollectionUtils.isEmpty(dispensingOrderIdList)) {
            return clientRsp;
        }
        List<DispensingOrder> allDispensingOrderList = dispensingOrderRepository.findByChainIdAndClinicIdAndIdIn(chainId, clinicId, dispensingOrderIdList);
        if (CollectionUtils.isEmpty(allDispensingOrderList)) {
            return clientRsp;
        }
        List<DispensingOrderSheetRel> allOrderSheetRelList = dispensingOrderSheetRelRepository.findAllByChainIdAndClinicIdAndOrderIdInAndIsDeleted(chainId, clinicId,
                allDispensingOrderList.stream().map(DispensingOrder::getId).collect(toList()), DispensingUtils.DeleteFlag.NOT_DELETED);
        allOrderSheetRelList =
                allOrderSheetRelList.stream().filter(it -> CollectionUtils.isEmpty(clientReq.getDispensingSheetIdList()) || clientReq.getDispensingSheetIdList().contains(it.getDispenseSheetId()))
                        .collect(toList());
        if (CollectionUtils.isEmpty(allOrderSheetRelList)) {
            return clientRsp;
        }
        // 患者发药单
        List<DispensingSheetV2> allDispensingSheetList = dispensingSheetV2Repository.findAllByChainIdAndClinicIdAndIdInAndIsDeleted(chainId, clinicId,
                allOrderSheetRelList.stream().map(DispensingOrderSheetRel::getDispenseSheetId).collect(toList()), DispensingUtils.DeleteFlag.NOT_DELETED);
        if (CollectionUtils.isEmpty(allDispensingSheetList)) {
            return clientRsp;
        }
        allDispensingSheetList = filterHasReNewSheet(allDispensingSheetList, clientReq.getTab());
        if (CollectionUtils.isEmpty(allDispensingSheetList)) {
            return clientRsp;
        }
        List<String> dispensingSheetIdList = allDispensingSheetList.stream().map(DispensingSheetV2::getId).collect(toList());
        // 领药单id分组
        Map<Long, List<DispensingOrderSheetRel>> orderIdSheetRelMap = allOrderSheetRelList.stream().collect(groupingBy(DispensingOrderSheetRel::getOrderId));

        Map<String, DispensingOrderSheetRel> orderSheetIdRelMap = allOrderSheetRelList.stream().collect(toMap(DispensingOrderSheetRel::getDispenseSheetId, Function.identity(), (a, b) -> a));

        Map<String, List<DispensingSheetV2>> dispensingSheetPatientIdMap = allDispensingSheetList.stream().collect(groupingBy(DispensingSheetV2::getPatientOrderId));
        // 加载sheet下的条目
        List<DispensingFormV2> dispensingSheetFormList = dispensingFormV2Repository.findAllByDispensingSheetIdInAndClinicIdAndIsDeleted(dispensingSheetIdList, clinicId, DispensingUtils.DeleteFlag.NOT_DELETED);
        Map<String, List<DispensingFormV2>> dispensingSheetIdFormMap = dispensingSheetFormList.stream().collect(groupingBy(DispensingFormV2::getDispensingSheetId));
        List<DispensingFormItemV2> allForItemV2List = dispensingFormItemV2Repository.findAllByChainIdAndClinicIdAndDispensingSheetIdInAndIsDeleted(chainId, clinicId, dispensingSheetIdList, DispensingUtils.DeleteFlag.NOT_DELETED);
        Map<String, List<DispensingFormItemV2>> dispensingSheetFormIdItemMap = allForItemV2List.stream().collect(groupingBy(DispensingFormItemV2::getDispensingFormId));
        Map<String, List<DispensingFormItemV2>> dispensingSheetIdFormItemMap = allForItemV2List.stream().collect(groupingBy(DispensingFormItemV2::getDispensingSheetId));
        // 组装发药单的Form和FormItem
        dispensingSheetPatientIdMap.forEach((patientOrderId, sheetList) -> sheetList.forEach(sheet -> {
            List<DispensingFormV2> formV2List = dispensingSheetIdFormMap.get(sheet.getId());
            if (CollectionUtils.isEmpty(formV2List)) {
                return;
            }
            sheet.setDispensingForms(formV2List);
            for (DispensingFormV2 dispensingFormV2 : formV2List) {
                List<DispensingFormItemV2> formItemV2List = dispensingSheetFormIdItemMap.get(dispensingFormV2.getId());
                if (CollectionUtils.isEmpty(formItemV2List)) {
                    continue;
                }
                dispensingFormV2.setDispensingFormItems(formItemV2List);
            }
        }));

        List<String> patientIds = allDispensingSheetList.stream().map(DispensingSheetV2::getPatientId).distinct().collect(toList());

        // 获取goods
        Map<String, GoodsItem> goodsItemMap = new HashMap<>();
        List<QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq> pharmacyGoodsList = new ArrayList<>();
        allDispensingOrderList.forEach(dispensingOrder -> {
            QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq queryPharmacyGoodsReq = new QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq();
            queryPharmacyGoodsReq.setPharmacyNo(dispensingOrder.getPharmacyNo());
            queryPharmacyGoodsReq.setPharmacyType(dispensingOrder.getPharmacyType());
            queryPharmacyGoodsReq.setGoodsIds(new ArrayList<>());
            List<DispensingOrderSheetRel> orderSheetRelList = orderIdSheetRelMap.get(dispensingOrder.getId());
            if (!CollectionUtils.isEmpty(orderSheetRelList)) {
                orderSheetRelList.forEach(orderSheetRel -> {
                    List<DispensingFormItemV2> itemV2List = dispensingSheetIdFormItemMap.get(orderSheetRel.getDispenseSheetId());
                    if (!CollectionUtils.isEmpty(itemV2List)) {
                        List<String> goodsIdList = itemV2List.stream().map(DispensingFormItemV2::getProductId).filter(productId -> !TextUtils.isEmpty(productId)).collect(toList());
                        if (!CollectionUtils.isEmpty(goodsIdList)) {
                            queryPharmacyGoodsReq.getGoodsIds().addAll(goodsIdList);
                        }
                    }
                });
                if (!CollectionUtils.isEmpty(queryPharmacyGoodsReq.getGoodsIds())) {
                    pharmacyGoodsList.add(queryPharmacyGoodsReq);
                }
            }
        });
        if (!CollectionUtils.isEmpty(pharmacyGoodsList)) {
            List<GoodsItem> goodsItems;
            try {
                List<QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp> queryPharmacyGoodsRsps = scGoodsFeignClient.queryGoodsInPharmacyByIdsDistinctByGoodsId(clinicId, chainId, true, 1, pharmacyGoodsList);
                goodsItems = Optional.ofNullable(queryPharmacyGoodsRsps).orElse(new ArrayList<>()).stream()
                        .filter(queryPharmacyGoodsRsp -> !CollectionUtils.isEmpty(queryPharmacyGoodsRsp.getList()))
                        .flatMap(queryPharmacyGoodsRsp -> queryPharmacyGoodsRsp.getList().stream())
                        .collect(toList());
                goodsItemMap.putAll(goodsItems.stream().collect(toMap(GoodsItem::getId, Function.identity(), (a, b) -> a)));
            } catch (Exception e) {
                log.info("获取goods失败，error = ", e);
            }
        }

        // 查询医嘱
        List<Long> allAdviceIdList = allDispensingSheetList.stream().map(DispensingSheetV2::getAdviceId).distinct().collect(toList());
        // 医嘱任务
        List<Long> adviceExecuteIdList = dispensingSheetFormList.stream().map(DispensingFormV2::getAdviceExecuteId).filter(Objects::nonNull).collect(toList());
        // 患者
        CompletableFuture<Map<String, PatientInfo>> patientInfoFuture = getPatientInfoFuture(chainId, patientIds);
        // 获取病区信息
        CompletableFuture<Map<String, WardBedView>> wardBedViewFuture = getWardBedViewFuture(chainId, clinicId, patientOrderIds);
        CompletableFuture<Map<Long, AdviceDetailView>> adviceDetailViewFuture = getAdviceDetailViewFuture(chainId, clinicId, allAdviceIdList);
        CompletableFuture<Map<Long, AdviceExecuteBasicView>> adviceExecuteBasicViewFuture = getAdviceExecuteBasicViewFuture(chainId, clinicId, adviceExecuteIdList);
        CompletableFuture<Map<Integer, GoodsPharmacyView>> pharmacyViewFuture = getPharmacyViewFuture(chainId, clinicId);

        Map<String, PatientInfo> patientInfoMap = patientInfoFuture.join();
        Map<String, WardBedView> patientOrderIdBedViewMap = wardBedViewFuture.join();
        Map<Long, AdviceDetailView> adviceIdToAdviceDetailView = adviceDetailViewFuture.join();
        Map<Long, AdviceExecuteBasicView> executeBasicViewMap = adviceExecuteBasicViewFuture.join();
        Map<Integer, GoodsPharmacyView> pharmacyNoToPharmacyView = pharmacyViewFuture.join();

        TreeMap<Long, List<DispensingSheetView>> bedNoToSheetViewList = new TreeMap<>();
        // 填充返回信息
        //List<DispensingSheetView> sheetViewList = new ArrayList<>();
        for (String patientOrderId : patientOrderIds) {
            List<DispensingSheetV2> sheetV2List = dispensingSheetPatientIdMap.get(patientOrderId);
            if (CollectionUtils.isEmpty(sheetV2List)) {
                continue;
            }
            List<DispensingSheetView> sortedSheetViewList = new ArrayList<>(sheetV2List.size());
            List<DispensingSheetView> finalSortedSheetViewList = sortedSheetViewList;
            sheetV2List.forEach(sheet -> {
                DispensingSheetView sheetViewOrigin = DispensingSheetView.from(sheet);
                List<DispensingSheetView> sheetViews = new ArrayList<DispensingSheetView>() {{
                    add(sheetViewOrigin);
                }};
                List<DispensingSheetView> resultSheetViewList = filterDispensingFormItem(sheetViews, clientReq.getTab(), executeBasicViewMap);
                // 过滤同一个领药单中不符合的发药单
                resultSheetViewList = filterDispensingSheetView(resultSheetViewList, clientReq);
                if (CollectionUtils.isEmpty(resultSheetViewList)) {
                    return;
                }
                DispensingSheetView sheetView = resultSheetViewList.get(0);
                // 药房名称
                sheetView.setPharmacyName(pharmacyNoToPharmacyView.get(sheetView.getPharmacyNo()) != null ? pharmacyNoToPharmacyView.get(sheetView.getPharmacyNo()).getName() : "");
                // 患者
                PatientInfo patientInfo = patientInfoMap.get(sheetView.getPatientId());
                if (Objects.nonNull(patientInfo)) {
                    sheetView.setPatient(new CisPatientInfo());
                    BeanUtils.copyProperties(patientInfo, sheetView.getPatient());
                }
                // 领药单
                sheetView.setDispenseOrderId(orderSheetIdRelMap.get(sheet.getId()).getOrderId().toString());
                // 床
                sheetView.setBeds(patientOrderIdBedViewMap.get(patientOrderId));
                // 处方
                sheetView.getDispensingForms().forEach(formView -> formView.getDispensingFormItems().forEach(formItem -> {
                    if (goodsItemMap.get(formItem.getProductId()) != null) {
                        formItem.setProductInfo(JsonUtils.dumpAsJsonNode(goodsItemMap.get(formItem.getProductId())));
                    }
                }));

                setSheetViewGroupId(sheetView, adviceIdToAdviceDetailView.get(sheet.getAdviceId()));
                finalSortedSheetViewList.add(sheetView);
            });

            sortedDispensingSheetViewList(bedNoToSheetViewList, patientOrderIdBedViewMap.get(patientOrderId), sortedSheetViewList);
        }

        clientRsp.setRows(bedNoToSheetViewList.values().stream().flatMap(Collection::stream).collect(toList()));
        return clientRsp;
    }

    private CompletableFuture<Map<String, PatientInfo>> getPatientInfoFuture(String chainId, List<String> patientIds) {
        return ExecutorUtils.futureSupplyAsync(() -> {
            GetPatientBasicInfosRsp patientBasicInfoRsp = cisCrmFeignClient.getPatientBasicInfos(chainId, patientIds);
            if (patientBasicInfoRsp != null && !CollectionUtils.isEmpty(patientBasicInfoRsp.getPatientInfos())) {
                return patientBasicInfoRsp.getPatientInfos().stream().collect(toMap(PatientInfo::getId, Function.identity(), (a, b) -> a));
            }
            return new HashMap<>(patientIds.size());
        });
    }

    private CompletableFuture<Map<Long, GoodsBatchInfo>> getGoodsBatchInfoFuture(String headerClinicId, Map<String, DispensingSheetV2> dispensingSheetIdToSheet) {
        return ExecutorUtils.futureSupplyAsync(() -> {
            Map<String, GetGoodsStockBatchesReq.GetGoodsStockBatch> goodsIdToGetGoodsStockBatch = new HashMap<>();
            DispensingUtils.doWithV2DispensingItemBatch(dispensingSheetIdToSheet.values(), batchInfo -> {
                GetGoodsStockBatchesReq.GetGoodsStockBatch goodsStockBatch = goodsIdToGetGoodsStockBatch.get(batchInfo.getProductId());
                if (goodsStockBatch == null) {
                    goodsStockBatch = new GetGoodsStockBatchesReq.GetGoodsStockBatch();
                    goodsStockBatch.setGoodsId(batchInfo.getProductId());
                    goodsStockBatch.setBatchIds(new ArrayList<>());
                    goodsIdToGetGoodsStockBatch.put(batchInfo.getProductId(), goodsStockBatch);
                }
                goodsStockBatch.getBatchIds().add(batchInfo.getBatchId());
            });
            GetGoodsStockBatchesRsp goodsStockBatchesRsp = scGoodsFeignClient.getGoodsStockBatches(headerClinicId, new ArrayList<>(goodsIdToGetGoodsStockBatch.values()));
            return Optional.ofNullable(goodsStockBatchesRsp).map(GetGoodsStockBatchesRsp::getList)
                    .orElseGet(Lists::newArrayList).stream()
                    .map(BatchesGoodsStockItem::getList).flatMap(Collection::stream)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(GoodsBatchInfo::getBatchId, Function.identity(), (a, b) -> a));
        });
    }


    private CompletableFuture<Map<String, WardBedView>> getWardBedViewFuture(String chainId, String clinicId,
                                                                             List<String> patientOrderIds) {
        return ExecutorUtils.futureSupplyAsync(() -> cisWardService.listWardBedByPatientOrderIds(chainId, clinicId, patientOrderIds));
    }

    public CompletableFuture<Map<Long, AdviceDetailView>> getAdviceDetailViewFuture(String chainId,
                                                                                    String clinicId,
                                                                                    List<Long> adviceIdList) {
        return ExecutorUtils.futureSupplyAsync(() -> hisAdviceService.listAdviceDetailViewsByIds(chainId, clinicId, adviceIdList)
                .stream().collect(Collectors.toMap(AdviceDetailView::getId, Function.identity(), (a, b) -> a)));
    }

    private CompletableFuture<Map<Long, AdviceExecuteBasicView>> getAdviceExecuteBasicViewFuture(String chainId,
                                                                                                 String clinicId,
                                                                                                 List<Long> adviceExecuteIdList) {
        return ExecutorUtils.futureSupplyAsync(() -> hisAdviceService.getAdviceExecuteBasicViewMap(chainId, clinicId, adviceExecuteIdList));
    }

    private CompletableFuture<Map<Integer, GoodsPharmacyView>> getPharmacyViewFuture(String chainId, String clinicId) {
        return ExecutorUtils.futureSupplyAsync(() -> scGoodsFeignClient.loadPharmacyByClinicMap(chainId, clinicId));
    }

    public AbcListPage<PatientOrderHospitalVO> getDispensingOrderPatientAbstractList(DispensingPatientAbstractListReq clientReq) {
        if (clientReq.getTab() != 1 && clientReq.getTab() != 2 && clientReq.getTab() != 3) {
            throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "参数错误");
        }
        AbcListPage<PatientOrderHospitalVO> clientRsp = new AbcListPage<>();
        clientRsp.setRows(new ArrayList<>());
        clientRsp.setOffset(clientReq.getOffset());
        clientRsp.setLimit(clientReq.getLimit());
        SearchResultRsp searchResultRsp = searchHospitalPatientDispense(clientReq);
        if (searchResultRsp == null || CollectionUtils.isEmpty(searchResultRsp.getHits())) {
            return clientRsp;
        }
        clientRsp.setTotal(searchResultRsp.getTotal());
        List<String> patientOrderIdList = searchResultRsp.getHits().stream().filter(Objects::nonNull)
                .map(it -> it.get("id") != null ? it.get("id").asText() : null)
                .filter(id -> !TextUtils.isEmpty(id)).collect(toList());

        Map<String, PatientOrderHospitalVO> orderListHospital = patientOrderService.findPatientOrderListHospital(clientReq.getChainId(), clientReq.getClinicId(), patientOrderIdList);
        // 过滤出院以及不是同一病区的患者
        // 搜索顺序
        for (String patientOrderId : patientOrderIdList) {
            PatientOrderHospitalVO patientOrderHospitalVO = orderListHospital.get(patientOrderId);
            if (patientOrderHospitalVO == null) {
                continue;
            }
            if (patientOrderHospitalVO.getStatus() >= HisPatientOrder.Status.WAIT_SETTLE
                    || !TextUtils.equals(patientOrderHospitalVO.getWardId(), clientReq.getWardAreaId())) {
                continue;
            }
            clientRsp.getRows().add(patientOrderHospitalVO);
        }
        return clientRsp;
    }

    private SearchResultRsp searchHospitalPatientDispense(DispensingPatientAbstractListReq clientReq) {
        try {
            RpcSearchHospitalPatientDispenseReq searchReq = new RpcSearchHospitalPatientDispenseReq();
            searchReq.setChainId(clientReq.getChainId());
            searchReq.setClinicId(clientReq.getClinicId());
            searchReq.setScoreCreatedGauss(1);
            if (!StringUtils.isEmpty(clientReq.getKeyword())) {
                // 搜索默认100
                searchReq.setLimit(100);
                searchReq.setKeyword(clientReq.getKeyword());
            } else {
                searchReq.setOffset(clientReq.getOffset());
                searchReq.setLimit(clientReq.getLimit());
                // 床号升序
                searchReq.setSortBy("bedNo");
            }
            List<Integer> dispensingStatusList = new ArrayList<>();
            if (clientReq.getTab() == 1) {
                // 发药
                dispensingStatusList.add(DispenseConst.Status.WAITING);
                dispensingStatusList.add(DispenseConst.Status.SELF_PROVIDED);
            } else if (clientReq.getTab() == 2) {
                // 退药
                dispensingStatusList.add(DispenseConst.Status.DISPENSED);
                dispensingStatusList.add(DispenseConst.Status.STOCK_NOT_DISPENSE);
            } else {
                // 已退
                dispensingStatusList.add(DispenseConst.Status.UNDISPENSED);
            }
            searchReq.setDispenseStatus(dispensingStatusList);
            searchReq.setWardId(clientReq.getWardAreaId());
            // 在院的
            searchReq.setStatus(Lists.newArrayList(HisPatientOrder.Status.INPATIENT,
                    HisPatientOrder.Status.TRANSFER_DEPARTMENT_BEGIN,
                    HisPatientOrder.Status.TRANSFER_DEPARTMENT_PROCESS, HisPatientOrder.Status.WAIT_DISCHARGE));
            long startTime = System.currentTimeMillis();
            AbcServiceResponseBody<SearchResultRsp> responseBody = searchFeignClient.doCommonSearch(searchReq);
            log.info("searchHospitalPatientDispense costTime = {}, rsp = {}", (System.currentTimeMillis() - startTime), JsonUtils.dump(responseBody));
            if (responseBody != null && responseBody.getData() != null) {
                return responseBody.getData();
            }
        } catch (Exception e) {
            log.error("searchHospitalPatientDispense error", e);
            throw e;
        }
        return null;
    }

    public void changeDispensingOrderStatusFromDelayMessage(String chainId, String clinicId, Long orderId, Long wardAreaId) {
        DispensingOrder dispensingOrder = dispensingOrderRepository.findByChainIdAndClinicIdAndId(chainId, clinicId, orderId).orElse(null);
        if (Objects.isNull(dispensingOrder) || dispensingOrder.getIsDeleted() != DispensingUtils.DeleteFlag.NOT_DELETED
                || !dispensingOrder.getWardAreaId().equals(wardAreaId)) {
            log.info("未查到领药单, id = {}, wardAreaId = {}", orderId, wardAreaId);
            return;
        }
        if (dispensingOrder.getStatus() >= DispensingOrder.Status.DISPENSING) {
            log.info("状态已更改，无需再修改，id = {}, wardAreaId = {}", orderId, wardAreaId);
            return;
        }
        // 是否有发药单
        List<String> dispensingSheetIdList = dispensingOrderSheetRelRepository.findAllByChainIdAndClinicIdAndOrderIdAndIsDeleted(chainId, clinicId,
                orderId, 0).stream().map(DispensingOrderSheetRel::getDispenseSheetId).collect(toList());
        boolean noDispensingSheet = true;
        if (!CollectionUtils.isEmpty(dispensingSheetIdList)) {
            long dispensingSheetCount = dispensingSheetV2Repository.findAllByChainIdAndClinicIdAndIdInAndIsDeleted(chainId, clinicId, dispensingSheetIdList, 0).size();
            if (dispensingSheetCount > 0) {
                noDispensingSheet = false;
            }
        }
        boolean needSendTodoCountMessage = false;
        if (noDispensingSheet) {
            dispensingOrder.setIsDeleted(DispensingUtils.DeleteFlag.DELETED);
            log.info("该领药单无发药单，无需修改状态让药房看见，直接逻辑删除。orderId = {}", orderId);
        } else {
            // 修改为已发，药房能看见
            dispensingOrder.setStatus(DispensingOrder.Status.DISPENSING);
            dispensingOrder.setApplyDispenseTime(Instant.now());
            needSendTodoCountMessage = true;
        }
        FillUtils.fillLastModifiedBy(dispensingOrder, DispensingConstants.ANONYMOUS_PATIENT_ID);
        dispensingOrderRepository.save(dispensingOrder);
        List<String> noticeFrontParams = DispensingEventGroupGenerateUtils.generateEventParams(clinicId, SubscribeMessage.EventGroup.DISPENSING_QL);
        messageService.sendSubscribeMessageAfterCommitAsync(noticeFrontParams, MessageService.DispensingQlEvent.NURSE_DISPENSING_ORDER_CHANGE);
        messageService.sendSubscribeMessageAfterCommitAsync(noticeFrontParams, MessageService.DispensingQlEvent.PHARMACY_DISPENSING_ORDER_CHANGE);
        messageService.sendSubscribeMessageOfDispensingOrder(dispensingOrder.getClinicId(),
                MessageService.DispensingQlEvent.PHARMACY_DISPENSING_ORDER_DISPENSE, Lists.newArrayList(dispensingOrder.getPharmacyNo()));
        if (needSendTodoCountMessage) {
            RocketMqProducer.doAfterTransactionCommit(() -> dispensingTodoService.sendHospitalPharmacyTodoCountTask(chainId, clinicId));
        }
    }

    public AbcListPage<DispensingSheetUnfinishedView> getUnfinishedDispensingSheetByPatientOrderId(String chainId, String clinicId, String patientOrderId) {
        AbcListPage<DispensingSheetUnfinishedView> abcListPage = new AbcListPage<>();
        abcListPage.setRows(new ArrayList<>());
        if (TextUtils.isEmpty(chainId) || TextUtils.isEmpty(clinicId) || TextUtils.isEmpty(patientOrderId)) {
            return abcListPage;
        }
        // 查询未发或者有未退的发药单
        List<DispensingSheetUnfinishedDto> sheetUnfinishedList = dispensingMapper.getDispensingSheetUnfinishedList(chainId, clinicId, patientOrderId);
        if (CollectionUtils.isEmpty(sheetUnfinishedList)) {
            return abcListPage;
        }

        List<String> sheetIds = sheetUnfinishedList.stream().filter(it -> !DispensingUtils.isAutoDispenseMethod(it.getDispensingMethod()))
                .map(DispensingSheetUnfinishedDto::getId).collect(toList());
        List<DispensingSheetOrderRelDto> dispensingOrderBySheet = new ArrayList<>();
        if (!CollectionUtils.isEmpty(sheetIds)) {
            dispensingOrderBySheet = dispensingMapper.findDispensingOrderBySheet(clinicId, sheetIds);
        }

        //if (CollectionUtils.isEmpty(dispensingOrderBySheet)) {
        //    return abcListPage;
        //}

        Map<String, DispensingSheetOrderRelDto> orderRelDtoMap = ListUtils.toMap(dispensingOrderBySheet, DispensingSheetOrderRelDto::getDispensingSheetId);

        Map<String, List<DispensingSheetUnfinishedDto>> sheetIdToUnfinishedDtoList = sheetUnfinishedList.stream().collect(groupingBy(DispensingSheetUnfinishedDto::getId));
        sheetIdToUnfinishedDtoList.forEach((sheetId, dtoList) -> {
            for (DispensingSheetUnfinishedDto dto : dtoList) {
                DispensingSheetUnfinishedView unfinishedView = new DispensingSheetUnfinishedView();

                DispensingSheetOrderRelDto dispensingSheetOrderRelDto = orderRelDtoMap.get(sheetId);
                if (!DispensingUtils.isAutoDispenseMethod(dto.getDispensingMethod()) && Objects.isNull(dispensingSheetOrderRelDto)) {
                    continue;
                }
                // 自动发药的领药单为空，需要判断
                if (Objects.nonNull(dispensingSheetOrderRelDto)) {
                    unfinishedView.setDispensingOrderStatus(dispensingSheetOrderRelDto.getDispensingOrderStatus());
                }
                unfinishedView.setChainId(dto.getChainId());
                unfinishedView.setClinicId(dto.getClinicId());
                unfinishedView.setPatientId(dto.getPatientId());
                unfinishedView.setPatientOrderId(dto.getPatientOrderId());
                unfinishedView.setId(dto.getId());
                unfinishedView.setCreated(dto.getCreated());
                unfinishedView.setAdviceId(dto.getAdviceId());
                unfinishedView.setDispensingMethod(dto.getDispensingMethod());
                unfinishedView.setSourceSheetId(dto.getSourceSheetId());
                unfinishedView.setSourceSheetType(dto.getSourceSheetType());
                if (dto.getStatus() == DispenseConst.Status.WAITING) {

                    if (DispensingUtils.isAutoDispenseMethod(dto.getDispensingMethod())) {
                        unfinishedView.setUnfinishedStatus(DispensingSheetUnfinishedView.Status.NOT_DISPENSING);
                    } else {
                        if (dispensingSheetOrderRelDto.getDispensingOrderStatus() < DispensingOrder.Status.DISPENSING) {
                            unfinishedView.setUnfinishedStatus(DispensingSheetUnfinishedView.Status.NOT_APPLY);
                        } else {
                            unfinishedView.setUnfinishedStatus(DispensingSheetUnfinishedView.Status.NOT_DISPENSING);
                        }
                    }
                    // 未发药
                    unfinishedView.setApplyTime(dto.getApplyTime());
                    abcListPage.getRows().add(unfinishedView);
                    break;
                } else if (dto.getStatus() == DispenseConst.Status.APPLY_UNDISPNSE) {
                    // 申请退药未退
                    unfinishedView.setUnfinishedStatus(DispensingSheetUnfinishedView.Status.NOT_UNDISPENSING);
                    unfinishedView.setApplyTime(dto.getApplyTime());
                    abcListPage.getRows().add(unfinishedView);
                    break;
                }
            }
        });
        return abcListPage;
    }

    public void deleteDispensingOrderFromAdviceMessage(AdviceMessage adviceMessage) {
        if (adviceMessage.getDiagnosisTreatmentType() != DiagnosisTreatmentType.MEDICINE && adviceMessage.getDiagnosisTreatmentType() != DiagnosisTreatmentType.MATERIALS) {
            return;
        }
        if (!(adviceMessage.getStatus() == AdviceStatus.INIT
                || adviceMessage.getStatus() == AdviceStatus.UNDONE || adviceMessage.getStatus() == AdviceStatus.STOPPED)) {
            return;
        }
        String chainId = adviceMessage.getChainId();
        String clinicId = adviceMessage.getClinicId();
        Long messageId = adviceMessage.getId();
        String operatorId = adviceMessage.getOperatorId();
        log.info("撤销核对/停止医嘱消息：医嘱id = {}", messageId);
        List<DispensingSheetV2> dispensingSheetV2List = dispensingSheetV2Repository.findAllByChainIdAndClinicIdAndAdviceIdInAndIsDeleted(chainId, clinicId,
                Collections.singletonList(messageId), DispensingUtils.DeleteFlag.NOT_DELETED);
        if (CollectionUtils.isEmpty(dispensingSheetV2List)) {
            log.info("未找到发药单：医嘱id = {}", messageId);
            return;
        }
        // 只处理医嘱的发药单
        List<DispensingSheetV2> manualSheetList = dispensingSheetV2List.stream()
                .filter(it -> it.getDispensingMethod() == DispenseConst.DispensingMethod.MANUAL && DispensingUtils.isAdviceSourceSheetType(it.getSourceSheetType())).collect(toList());
        List<DispensingSheetV2> autoSheetList = dispensingSheetV2List.stream()
                .filter(it -> it.getDispensingMethod() == DispenseConst.DispensingMethod.AUTO && DispensingUtils.isAdviceSourceSheetType(it.getSourceSheetType())).collect(toList());
        if (!CollectionUtils.isEmpty(autoSheetList)) {
            doDeleteAutoDispenseSheet(chainId, clinicId, operatorId, autoSheetList);
        }
        if (CollectionUtils.isEmpty(manualSheetList)) {
            return;
        }
        List<String> dispensingSheetIdList = ListUtils.extractUniqueProperty(manualSheetList, DispensingSheetV2::getId);
        List<DispensingOrderSheetRel> orderSheetRelList =
                dispensingOrderSheetRelRepository.findAllByChainIdAndClinicIdAndDispenseSheetIdInAndIsDeletedAndApplyType(chainId, clinicId, dispensingSheetIdList, DispensingUtils.DeleteFlag.NOT_DELETED, DispensingOrder.ApplyType.DISPENSE);
        if (CollectionUtils.isEmpty(orderSheetRelList)) {
            log.info("未找到领药单：医嘱id = {}", messageId);
            return;
        }
        List<Long> dispensingOrderIds = ListUtils.extractUniqueProperty(orderSheetRelList, DispensingOrderSheetRel::getOrderId);
        List<DispensingOrder> dispensingOrderList = dispensingOrderRepository.findByChainIdAndClinicIdAndIdInAndIsDeleted(chainId, clinicId, dispensingOrderIds, DispensingUtils.DeleteFlag.NOT_DELETED);
        if (CollectionUtils.isEmpty(dispensingOrderList)) {
            log.info("未找到领药单：医嘱id = {}", messageId);
            return;
        }

        Map<String, DispensingSheetV2> dispensingSheetV2Map = loadAssembleDispenseSheet(chainId, clinicId, null, dispensingSheetIdList, null);
        // 删除未发药的相关信息
        List<String> needDeleteSheetIdList = new ArrayList<>();
        List<DispensingSheetV2> needUnLockGoodsStockList = new ArrayList<>();
        dispensingSheetV2Map.values().forEach(sheetV2 -> {
            List<DispensingFormV2> dispensingForms = sheetV2.getDispensingForms();
            // 都未发
            boolean allWaiting = dispensingForms.stream().flatMap(item -> item.getDispensingFormItems().stream()).allMatch(item -> item.getStatus() == DispenseConst.Status.WAITING);
            // 自备也要删除
            boolean allSelf = dispensingForms.stream().flatMap(item -> item.getDispensingFormItems().stream()).allMatch(item -> item.getStatus() == DispenseConst.Status.SELF_PROVIDED);
            if (allWaiting || allSelf) {
                log.info("删除发药单：医嘱id = {}, sheet = {}", messageId, JsonUtils.dump(sheetV2));
                sheetV2.setIsDeleted(DispensingUtils.DeleteFlag.DELETED);
                FillUtils.fillLastModifiedBy(sheetV2, operatorId);
                dispensingForms.forEach(form -> {
                    form.setIsDeleted(DispensingUtils.DeleteFlag.DELETED);
                    FillUtils.fillLastModifiedBy(form, operatorId);
                    form.getDispensingFormItems().forEach(item -> {
                        item.setIsDeleted(DispensingUtils.DeleteFlag.DELETED);
                        FillUtils.fillLastModifiedBy(item, operatorId);
                    });
                });
                needDeleteSheetIdList.add(sheetV2.getId());
                if (allWaiting) {
                    needUnLockGoodsStockList.add(sheetV2);
                }
            }
        });

        if (CollectionUtils.isEmpty(needDeleteSheetIdList)) {
            return;
        }

        // 删除关联关系
        orderSheetRelList.forEach(sheetRel -> {
            if (needDeleteSheetIdList.contains(sheetRel.getDispenseSheetId())) {
                sheetRel.setIsDeleted(DispensingUtils.DeleteFlag.DELETED);
                FillUtils.fillLastModifiedBy(sheetRel, operatorId);
            }
        });
        // 查询领药单中其他发药单是否都发药，都发药要更改领药单状态
        Map<Long, DispensingOrder> orderIdToOrder = dispensingOrderList.stream().collect(Collectors.toMap(DispensingOrder::getId, Function.identity(), (a, b) -> a));
        Map<Long, List<DispensingOrderSheetRel>> orderIdToSheetRelList = dispensingOrderSheetRelRepository.findAllByChainIdAndClinicIdAndOrderIdInAndIsDeleted(chainId, clinicId,
                dispensingOrderList.stream().map(DispensingOrder::getId).distinct().collect(toList()), DispensingUtils.DeleteFlag.NOT_DELETED).stream().collect(groupingBy(DispensingOrderSheetRel::getOrderId));
        List<String> allSheetIdList = orderIdToSheetRelList.values().stream().flatMap(Collection::stream).map(DispensingOrderSheetRel::getDispenseSheetId).distinct().collect(toList());
        Map<String, DispensingSheetV2> sheetIdToSheetV2 = dispensingSheetV2Repository.findAllByChainIdAndClinicIdAndIdInAndIsDeleted(chainId, clinicId, allSheetIdList, DispensingUtils.DeleteFlag.NOT_DELETED)
                .stream().collect(toMap(DispensingSheetV2::getId, Function.identity(), (a, b) -> a));
        orderIdToSheetRelList.forEach((orderId, sheetRelList) -> {
            List<DispensingSheetV2> otherSheetList = new ArrayList<>();
            sheetRelList.forEach(rel -> {
                if (needDeleteSheetIdList.contains(rel.getDispenseSheetId())) {
                    return;
                }
                DispensingSheetV2 dispensingSheetV2 = sheetIdToSheetV2.get(rel.getDispenseSheetId());
                if (dispensingSheetV2 == null) {
                    return;
                }
                otherSheetList.add(dispensingSheetV2);
            });
            boolean allDispensed = otherSheetList.stream().allMatch(it -> it.getStatus() >= DispenseConst.Status.DISPENSED);
            if (!allDispensed) {
                return;
            }
            DispensingOrder dispensingOrder = orderIdToOrder.get(orderId);
            if (dispensingOrder != null && dispensingOrder.getStatus() == DispensingOrder.Status.DISPENSING) {
                log.info("撤销医嘱删除发药单，其他发药单已发，更改领药单为已发，adviceId ={}, clinicId = {}, order = {}", messageId, clinicId, JsonUtils.dump(dispensingOrder));
                dispensingOrder.setStatus(DispensingOrder.Status.DISPENSED);
                FillUtils.fillLastModifiedBy(dispensingOrder, operatorId);
            }
        });
        // 锁库解锁
        goodsLockingFeignClient.hospitalTryUnLockGoodsStock(chainId, clinicId, operatorId, needUnLockGoodsStockList);

        List<String> noticeFrontParams = DispensingEventGroupGenerateUtils.generateEventParams(clinicId, SubscribeMessage.EventGroup.DISPENSING_QL);
        messageService.sendSubscribeMessageAfterCommitAsync(noticeFrontParams, MessageService.DispensingQlEvent.NURSE_DISPENSING_ORDER_CHANGE);
        messageService.sendSubscribeMessageAfterCommitAsync(noticeFrontParams, MessageService.DispensingQlEvent.PHARMACY_DISPENSING_ORDER_CHANGE);

        DispensingSheetV2Listener.Context context = new DispensingSheetV2Listener.Context();
        context.setAdviceMessage(adviceMessage);
        RocketMqProducer.doAfterTransactionCommit(() -> {
            dispensingSheetV2ListenerProvider.forEach(listener -> {
                needUnLockGoodsStockList.forEach(dispensingSheetV2 -> {
                    listener.onDispensingSheetDeletedAfterCommit(chainId, clinicId, dispensingSheetV2, operatorId, context);
                });
            });
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @RedisLock(key = "'dispensing.advice.execute-deleted:' + #adviceExecuteMessage.clinicId + ':' + #adviceExecuteMessage.adviceId", waitTime = 60)
    public void deleteDispensingSheetFromAdviceExecuteMessage(AdviceExecuteMessage adviceExecuteMessage) {
        if (!DispensingUtils.adviceRuleTypeNeedCreateDispenseSheet(adviceExecuteMessage.getType())) {
            return;
        }
        // 医嘱执行任务删除，删除对应的发药单
        String chainId = adviceExecuteMessage.getChainId();
        String clinicId = adviceExecuteMessage.getClinicId();
        Long executeId = adviceExecuteMessage.getId();
        Long adviceId = adviceExecuteMessage.getAdviceId();
        String operatorId = adviceExecuteMessage.getOperatorId();
        log.info("医嘱执行任务删除：医嘱执行id = {}", executeId);
        // 查找对应的 sheet
        List<DispensingSheetV2> adviceSheetList = dispensingSheetV2Repository.findAllByChainIdAndClinicIdAndAdviceIdAndIsDeleted(chainId, clinicId, adviceId,
                DispensingUtils.DeleteFlag.NOT_DELETED);
        if (CollectionUtils.isEmpty(adviceSheetList)) {
            log.info("未找到发药单：医嘱执行id = {}", executeId);
            return;
        }
        Map<String, DispensingSheetV2> sheetIdToSheet = adviceSheetList.stream().collect(toMap(DispensingSheetV2::getId, Function.identity(), (a, b) -> a));
        List<String> sheetIdList = adviceSheetList.stream().map(DispensingSheetV2::getId).distinct().collect(toList());
        List<DispensingFormV2> adviceFormList = dispensingFormV2Repository.findAllByDispensingSheetIdInAndClinicIdAndIsDeleted(sheetIdList, clinicId,
                DispensingUtils.DeleteFlag.NOT_DELETED);
        List<DispensingFormV2> executeFormList = adviceFormList.stream().filter(it -> it.getAdviceExecuteId() != null && it.getAdviceExecuteId().equals(executeId)).collect(toList());
        if (CollectionUtils.isEmpty(executeFormList)) {
            log.info("未找到发药单：医嘱执行id = {}", executeId);
            return;
        }
        List<String> idList = executeFormList.stream().map(DispensingFormV2::getDispensingSheetId).distinct().collect(toList());
        List<DispensingSheetV2> dispensingSheetV2List = idList.stream().map(sheetIdToSheet::get).filter(Objects::nonNull).collect(toList());
        //
        Map<Integer, List<DispensingSheetV2>> dispensingMethodToSheetList = dispensingSheetV2List.stream().collect(groupingBy(DispensingSheetV2::getDispensingMethod));
        dispensingMethodToSheetList.forEach((dispensingMethod, sheetList) -> {
            if (dispensingMethod == DispenseConst.DispensingMethod.MANUAL) {
                doDeleteSheetAndUpdateOrder(chainId, clinicId, operatorId, executeId, dispensingSheetV2List);
            } else {
                doDeleteAutoDispenseSheet(chainId, clinicId, operatorId, sheetList);
            }
        });
    }

    private void doDeleteAutoDispenseSheet(String chainId, String clinicId, String employeeId, List<DispensingSheetV2> dispensingSheetV2List) {
        if (CollectionUtils.isEmpty(dispensingSheetV2List)) {
            return;
        }
        List<DispensingSheetV2> waitingSheetList = dispensingSheetV2List.stream().filter(it -> it.getStatus() == DispenseConst.Status.WAITING && it.getDispensingMethod() == DispenseConst.DispensingMethod.AUTO).collect(toList());
        List<DispensingSheetV2> unDispensedList = dispensingSheetV2List.stream().filter(it -> it.getStatus() == DispenseConst.Status.DISPENSED && it.getDispensingMethod() == DispenseConst.DispensingMethod.AUTO).collect(toList());
        doDeleteAutoWaitDispenseSheet(chainId, clinicId, employeeId, waitingSheetList);
        doUnDispenseAutoDispenseSheetList(chainId, clinicId, employeeId, unDispensedList);
    }

    private void doDeleteAutoWaitDispenseSheet(String chainId, String clinicId, String employeeId, List<DispensingSheetV2> dispensingSheetV2List) {
        if (CollectionUtils.isEmpty(dispensingSheetV2List)) {
            return;
        }
        List<String> sheetIdList = dispensingSheetV2List.stream().map(DispensingSheetV2::getId).collect(toList());
        Map<String, DispensingSheetV2> sheetIdToSheet = loadAssembleDispenseSheet(chainId, clinicId, null, sheetIdList, null);
        if (CollectionUtils.isEmpty(sheetIdToSheet)) {
            return;
        }
        log.info("删除自动发药未发的发药单，list={}", JsonUtils.dump(dispensingSheetV2List));
        sheetIdToSheet.forEach((sheetId, sheetV2) -> {
            sheetV2.setIsDeleted(1);
            FillUtils.fillLastModifiedBy(sheetV2, employeeId);
            sheetV2.getDispensingForms().forEach(form -> {
                form.setIsDeleted(1);
                FillUtils.fillLastModifiedBy(form, employeeId);
                form.getDispensingFormItems().forEach(item -> {
                    item.setIsDeleted(1);
                    FillUtils.fillLastModifiedBy(item, employeeId);
                });
            });
        });
        goodsLockingFeignClient.hospitalTryUnLockGoodsStock(chainId, clinicId, employeeId, dispensingSheetV2List);
    }

    private void doUnDispenseAutoDispenseSheetList(String chainId, String clinicId, String employeeId, List<DispensingSheetV2> dispensingSheetV2List) {
        if (CollectionUtils.isEmpty(dispensingSheetV2List)) {
            return;
        }
        List<String> sheetIdList = dispensingSheetV2List.stream().map(DispensingSheetV2::getId).collect(toList());
        Map<String, DispensingSheetV2> sheetIdToSheet = loadAssembleDispenseSheet(chainId, clinicId, null, sheetIdList, null);
        if (CollectionUtils.isEmpty(sheetIdToSheet)) {
            return;
        }
        List<String> patientOrderIdList = sheetIdToSheet.values().stream().filter(it -> it.getPatientOrderId() != null).map(DispensingSheetV2::getPatientOrderId).distinct().collect(toList());
        CompletableFuture<Map<String, PatientOrderHospitalVO>> patientOrderHospitalVoFuture = getPatientOrderHospitalVoFuture(chainId, clinicId, patientOrderIdList);
        Map<String, PatientOrderHospitalVO> patientOrderIdToHospitalVo = patientOrderHospitalVoFuture.join();
        log.info("自动发药的发药单退药，list={}", JsonUtils.dump(dispensingSheetV2List));
        AutoDispenseReq autoDispenseReq = new AutoDispenseReq();
        autoDispenseReq.setDispenseSheets(new ArrayList<>());
        autoDispenseReq.setHeaderChainId(chainId);
        autoDispenseReq.setHeaderClinicId(clinicId);
        autoDispenseReq.setHeaderEmployeeId(employeeId);
        autoDispenseReq.setDispenseType(AutoDispenseReq.DISPENSE_TYPE_UNDISPENSE);
        for (DispensingSheetV2 sheetV2 : dispensingSheetV2List) {
            AutoDispenseReq.AutoDispenseSheetReq sheetReq = new AutoDispenseReq.AutoDispenseSheetReq();
            sheetReq.setId(sheetV2.getId());
            sheetReq.setDispensingForms(new ArrayList<>());
            sheetV2.getDispensingForms().forEach(form -> {
                AutoDispenseReq.AutoDispenseFormReq formReq = new AutoDispenseReq.AutoDispenseFormReq();
                formReq.setId(form.getId());
                formReq.setDispenseFormItems(new ArrayList<>());
                form.getDispensingFormItems().forEach(item -> {
                    if (item.getStatus() == DispenseConst.Status.APPLY_UNDISPNSE) {
                        return;
                    }
                    AutoDispenseReq.AutoDispenseFormItemReq itemReq = new AutoDispenseReq.AutoDispenseFormItemReq();
                    itemReq.setId(item.getId());
                    itemReq.setDoseCount(item.getDoseCount());
                    itemReq.setUnitCount(item.getUnitCount());
                    formReq.getDispenseFormItems().add(itemReq);
                });
                sheetReq.getDispensingForms().add(formReq);
            });
            autoDispenseReq.getDispenseSheets().add(sheetReq);
        }
        doUnDispenseAutoDispenseSheet(autoDispenseReq,patientOrderIdToHospitalVo, sheetIdToSheet);
    }

    private void doDeleteSheetAndUpdateOrder(String chainId, String clinicId, String operatorId, Long messageId,
                                             List<DispensingSheetV2> dispensingSheetV2List) {
        List<String> dispensingSheetIdList = dispensingSheetV2List.stream().map(DispensingSheetV2::getId).collect(toList());
        List<DispensingOrderSheetRel> orderSheetRelList =
                dispensingOrderSheetRelRepository.findAllByChainIdAndClinicIdAndDispenseSheetIdInAndIsDeletedAndApplyType(chainId, clinicId, dispensingSheetIdList, DispensingUtils.DeleteFlag.NOT_DELETED, DispensingOrder.ApplyType.DISPENSE);
        if (CollectionUtils.isEmpty(orderSheetRelList)) {
            log.info("未找到领药单：医嘱id = {}", messageId);
            return;
        }
        List<DispensingOrder> dispensingOrderList = dispensingOrderRepository.findByChainIdAndClinicIdAndIdInAndIsDeleted(chainId, clinicId,
                orderSheetRelList.stream().map(DispensingOrderSheetRel::getOrderId).distinct().collect(toList()), DispensingUtils.DeleteFlag.NOT_DELETED);
        if (CollectionUtils.isEmpty(dispensingOrderList)) {
            log.info("未找到领药单：医嘱id = {}", messageId);
            return;
        }

        Map<String, DispensingSheetV2> dispensingSheetV2Map = loadAssembleDispenseSheet(chainId, clinicId, null, dispensingSheetIdList, null);
        // 删除未发药的相关信息
        List<String> needDeleteSheetIdList = new ArrayList<>();
        List<DispensingSheetV2> needUnLockGoodsStockList = new ArrayList<>();
        dispensingSheetV2Map.values().forEach(sheetV2 -> {
            List<DispensingFormV2> dispensingForms = sheetV2.getDispensingForms();
            // 都未发
            boolean allWaiting = dispensingForms.stream().flatMap(item -> item.getDispensingFormItems().stream()).allMatch(item -> item.getStatus() == DispenseConst.Status.WAITING);
            boolean allSelf = dispensingForms.stream().flatMap(item -> item.getDispensingFormItems().stream()).allMatch(item -> item.getStatus() == DispenseConst.Status.SELF_PROVIDED);
            if (allWaiting || allSelf) {
                log.info("删除发药单：医嘱id = {}", messageId);
                sheetV2.setIsDeleted(DispensingUtils.DeleteFlag.DELETED);
                FillUtils.fillLastModifiedBy(sheetV2, operatorId);
                dispensingForms.forEach(form -> {
                    form.setIsDeleted(DispensingUtils.DeleteFlag.DELETED);
                    FillUtils.fillLastModifiedBy(form, operatorId);
                    form.getDispensingFormItems().forEach(item -> {
                        item.setIsDeleted(DispensingUtils.DeleteFlag.DELETED);
                        FillUtils.fillLastModifiedBy(item, operatorId);
                    });
                });
                needDeleteSheetIdList.add(sheetV2.getId());
                if (allWaiting) {
                    needUnLockGoodsStockList.add(sheetV2);
                }
            }
        });
        if (needDeleteSheetIdList.size() > 0) {
            // 删除关联关系
            orderSheetRelList.forEach(sheetRel -> {
                if (needDeleteSheetIdList.contains(sheetRel.getDispenseSheetId())) {
                    sheetRel.setIsDeleted(DispensingUtils.DeleteFlag.DELETED);
                    FillUtils.fillLastModifiedBy(sheetRel, operatorId);
                }
            });
            // 查询领药单中其他发药单是否都发药，都发药要更改领药单状态
            Map<Long, DispensingOrder> orderIdToOrder = dispensingOrderList.stream().collect(Collectors.toMap(DispensingOrder::getId, Function.identity(), (a, b) -> a));
            //Map<Long, List<DispensingOrderSheetRel>> orderIdToSheetRelList = dispensingOrderSheetRelRepository.findAllByChainIdAndClinicIdAndOrderIdInAndIsDeleted(chainId, clinicId,
            //        dispensingOrderList.stream().map(DispensingOrder::getId).distinct().collect(toList()), DispensingUtils.DeleteFlag.NOT_DELETED).stream().collect(groupingBy(DispensingOrderSheetRel::getOrderId));
            //List<String> allSheetIdList = orderIdToSheetRelList.values().stream().flatMap(Collection::stream).map(DispensingOrderSheetRel::getDispenseSheetId).distinct().collect(toList());
            //Map<String, DispensingSheetV2> sheetIdToSheetV2 = dispensingSheetV2Repository.findAllByChainIdAndClinicIdAndIdInAndIsDeleted(chainId, clinicId, allSheetIdList, DispensingUtils.DeleteFlag.NOT_DELETED)
            //        .stream().collect(toMap(DispensingSheetV2::getId, Function.identity(), (a, b) -> a));
            //orderIdToSheetRelList.forEach((orderId, sheetRelList) -> {
            //    List<DispensingSheetV2> otherSheetList = new ArrayList<>();
            //    sheetRelList.forEach(rel -> {
            //        if (needDeleteSheetIdList.contains(rel.getDispenseSheetId())) {
            //            return;
            //        }
            //        DispensingSheetV2 dispensingSheetV2 = sheetIdToSheetV2.get(rel.getDispenseSheetId());
            //        if (dispensingSheetV2 == null) {
            //            return;
            //        }
            //        otherSheetList.add(dispensingSheetV2);
            //    });
            //    boolean allDispensed = otherSheetList.stream().allMatch(it -> it.getStatus() >= DispenseConst.Status.DISPENSED);
            //    if (!allDispensed) {
            //        return;
            //    }
            //    DispensingOrder dispensingOrder = orderIdToOrder.get(orderId);
            //    if (dispensingOrder != null && dispensingOrder.getStatus() == DispensingOrder.Status.DISPENSING) {
            //        log.info("撤销医嘱删除发药单，其他发药单已发，更改领药单为已发，adviceId ={}, clinicId = {}, order {}", messageId, clinicId, JsonUtils.dump(dispensingOrder));
            //        dispensingOrder.setStatus(DispensingOrder.Status.DISPENSED);
            //        FillUtils.fillLastModifiedBy(dispensingOrder, operatorId);
            //    }
            //});
            // 锁库解锁
            goodsLockingFeignClient.hospitalTryUnLockGoodsStock(chainId, clinicId, operatorId, needUnLockGoodsStockList);

            List<String> noticeFrontParams = DispensingEventGroupGenerateUtils.generateEventParams(clinicId, SubscribeMessage.EventGroup.DISPENSING_QL);
            messageService.sendSubscribeMessageAfterCommitAsync(noticeFrontParams, MessageService.DispensingQlEvent.NURSE_DISPENSING_ORDER_CHANGE);
            messageService.sendSubscribeMessageAfterCommitAsync(noticeFrontParams, MessageService.DispensingQlEvent.PHARMACY_DISPENSING_ORDER_CHANGE);
            RocketMqProducer.doAfterTransactionCommit(() -> {
                DispensingOrderService proxy = SpringUtils.getBean(DispensingOrderService.class);
                proxy.doUpdateDispensingOrderStatusAfterCommit(chainId, clinicId, operatorId, new ArrayList<>(orderIdToOrder.keySet()));
            });
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void doUpdateDispensingOrderStatusAfterCommit(String chainId, String clinicId, String employeeId,
                                                         List<Long> dispensingOrderIdList) {
        if (CollectionUtils.isEmpty(dispensingOrderIdList)) {
            return;
        }
        log.info("doUpdateDispensingOrderStatusAfterCommit, clinicId={}, orderIdList={}", clinicId, dispensingOrderIdList);
        List<DispensingOrder> dispensingOrderList = dispensingOrderRepository.findByChainIdAndClinicIdAndIdInAndIsDeleted(chainId, clinicId,
                dispensingOrderIdList, DispensingUtils.DeleteFlag.NOT_DELETED);
        if (CollectionUtils.isEmpty(dispensingOrderList)) {
            log.info("not found dispensing order, orderIdList={}", dispensingOrderIdList);
            return;
        }
        List<DispensingOrderSheetRel> relSheetList = dispensingOrderSheetRelRepository.findAllByChainIdAndClinicIdAndOrderIdInAndIsDeleted(chainId, clinicId, dispensingOrderIdList, DispensingUtils.DeleteFlag.NOT_DELETED);
        if (CollectionUtils.isEmpty(relSheetList)) {
            log.info("not found sheet rel, orderIdList={}", dispensingOrderIdList);
            return;
        }
        List<String> sheetIdList = relSheetList.stream().map(DispensingOrderSheetRel::getDispenseSheetId).distinct().collect(toList());
        List<DispensingSheetV2> sheetV2List = dispensingSheetV2Repository.findAllByChainIdAndClinicIdAndIdInAndIsDeleted(chainId, clinicId, sheetIdList,
                DispensingUtils.DeleteFlag.NOT_DELETED);
        if (CollectionUtils.isEmpty(sheetV2List)) {
            log.info("not found sheet, sheetIdList={}", sheetIdList);
            return;
        }
        Map<Long, DispensingOrder> orderIdToOrder = ListUtils.toMap(dispensingOrderList, DispensingOrder::getId);
        Map<Long, List<DispensingOrderSheetRel>> orderIdToRelList = relSheetList.stream().collect(groupingBy(DispensingOrderSheetRel::getOrderId));
        Map<String, DispensingSheetV2> idToSheetV2 = ListUtils.toMap(sheetV2List, DispensingSheetV2::getId);
        orderIdToRelList.forEach((orderId, relList) -> {
            DispensingOrder dispensingOrder = orderIdToOrder.get(orderId);
            if (dispensingOrder == null) {
                return;
            }
            List<DispensingSheetV2> sheetList = new ArrayList<>();
            relSheetList.forEach(rel -> {
                DispensingSheetV2 sheet = idToSheetV2.get(rel.getDispenseSheetId());
                if (sheet == null) {
                    return;
                }
                sheetList.add(sheet);
            });
            if (sheetList.isEmpty()) {
                return;
            }
            // 都发药
            boolean allDispensed = sheetList.stream().allMatch(it -> it.getStatus() >= DispenseConst.Status.DISPENSED);
            if (!allDispensed) {
                log.info("有未药发的发药单，clinicId = {}, orderId={}, status={}", clinicId, dispensingOrder.getId(), dispensingOrder.getStatus());
                return;
            }
            if (dispensingOrder.getStatus() == DispensingOrder.Status.DISPENSING) {
                log.info("撤销医嘱删除发药单，其他发药单已发，更改领药单为已发，clinicId = {}, order {}", clinicId, JsonUtils.dump(dispensingOrder));
                dispensingOrder.setStatus(DispensingOrder.Status.DISPENSED);
                FillUtils.fillLastModifiedBy(dispensingOrder, employeeId);
            }
        });
    }

    public void getHospitalPrescriptionSheetViewList(HospitalDispensingOrderPrescriptionBatchPrintReq clientReq,
                                                     List<HospitalDispensingOrderPrescriptionPrintRsp> resultList) {
        if (CollectionUtils.isEmpty(clientReq.getDispensingOrderList())) {
            return;
        }

        List<Long> dispensingOrderIds = clientReq.getDispensingOrderList().stream().map(HospitalDispensingOrderBatchBase::getDispensingOrderId).collect(toList());
        List<String> dispensingSheetIdReqList = clientReq.getDispensingOrderList().stream().map(HospitalDispensingOrderBatchBase::getDispensingSheetIdList)
                .flatMap(List::stream).filter(Objects::nonNull).map(String::valueOf).collect(toList());
        if (CollectionUtils.isEmpty(dispensingOrderIds) || CollectionUtils.isEmpty(dispensingSheetIdReqList)) {
            return;
        }
        String chainId = clientReq.getChainId();
        String clinicId = clientReq.getClinicId();
        // 查询领药单
        List<DispensingOrder> dispensingOrderList = dispensingOrderRepository.findByChainIdAndClinicIdAndIdInAndIsDeleted(chainId, clinicId, dispensingOrderIds, DispensingUtils.DeleteFlag.NOT_DELETED);
        if (CollectionUtils.isEmpty(dispensingOrderList)) {
            return;
        }
        Map<Long, DispensingOrder> dispensingOrderMap = ListUtils.toMap(dispensingOrderList, DispensingOrder::getId);
        //   DispensingOrder dispensingOrder = dispensingOrderList.get(0);
        List<DispensingOrderSheetRel> dispensingOrderSheetRelList = dispensingOrderSheetRelRepository.findAllByChainIdAndClinicIdAndOrderIdInAndIsDeleted(chainId, clinicId, dispensingOrderIds, DispensingUtils.DeleteFlag.NOT_DELETED);
        Map<String, DispensingOrderSheetRel> orderSheetRelMap = ListUtils.toMap(dispensingOrderSheetRelList, DispensingOrderSheetRel::getDispenseSheetId);
        List<String> dispensingSheetIdList = dispensingOrderSheetRelList
                .stream().filter(it -> dispensingSheetIdReqList.contains(it.getDispenseSheetId())).map(DispensingOrderSheetRel::getDispenseSheetId).collect(toList());
        if (CollectionUtils.isEmpty(dispensingSheetIdList)) {
            return;
        }
        if (resultList == null) {
            resultList = Lists.newArrayList();
        }
        // 获取发药单相关信息
        Set<String> goodsIdSet = new HashSet<>();
        Map<String, GoodsItem> goodsIdToGoodsItem = new HashMap<>();
        Map<String, DispensingSheetV2> sheetIdToSheetV2 = loadAssembleDispenseSheet(chainId, clinicId, null, dispensingSheetIdList, goodsIdSet);
        sheetIdToSheetV2.forEach((k, v) -> {
            DispensingOrderSheetRel orderSheetRel = orderSheetRelMap.get(k);

            if (Objects.isNull(orderSheetRel)) {
                return;
            }

            DispensingOrder dispensingOrder = dispensingOrderMap.get(orderSheetRel.getOrderId());
            dispensingOrder.getDispensingSheetV2List().add(v);

        });
        // 获取 goodsItem
        getGoodsItem(chainId, clinicId, dispensingOrderList, goodsIdToGoodsItem);
        List<String> doctorDepartmentIdList = sheetIdToSheetV2.values().stream().filter(it -> it.getDoctorDepartmentId() != null).map(DispensingSheetV2::getDoctorDepartmentId).distinct().collect(toList());
        // 发药人与医生
        List<String> dispensedByList = sheetIdToSheetV2.values().stream().filter(it -> !StringUtils.isEmpty(it.getDispensedBy())).map(DispensingSheetV2::getDispensedBy).distinct().collect(toList());
        dispensedByList.addAll(sheetIdToSheetV2.values().stream().filter(it -> it.getDoctorId() != null).map(DispensingSheetV2::getDoctorId).distinct().collect(toList()));
        // 按患者分组
        Map<String, List<DispensingSheetV2>> patientOrderIdToSheetV2List = sheetIdToSheetV2.values().stream().collect(groupingBy(DispensingSheetV2::getPatientOrderId));
        List<String> patientOrderIdList = new ArrayList<>(patientOrderIdToSheetV2List.keySet());
        CompletableFuture<Map<String, PatientOrderHospitalVO>> patientOrderHospitalVoFuture = getPatientOrderHospitalVoFuture(chainId, clinicId, patientOrderIdList);
        CompletableFuture<Map<String, ShebaoSettleInfoRspBody.ShebaoSettleInfo>> shebaoSettleInfoFuture = getShebaoSettleInfoFuture(chainId, clinicId, patientOrderIdList);
        CompletableFuture<Map<String, Employee>> employeeFuture = getEmployeeFuture(dispensedByList, chainId);
        CompletableFuture<Map<String, Department>> departmentFuture = getDepartmentFuture(doctorDepartmentIdList);
        CompletableFuture<Map<Long, AdviceDetailView>> adviceDetailViewFuture = getAdviceDetailViewFuture(chainId, clinicId, sheetIdToSheetV2.values().stream().map(DispensingSheetV2::getAdviceId).distinct().collect(toList()));
        CompletableFuture<Map<String, List<EmrDiagnosisInfo>>> listPatientDiagnosisFuture = getListPatientDiagnosisFuture(chainId, clinicId, patientOrderIdList);

        Map<String, Employee> employeeIdToEmployee = employeeFuture.join();
        Map<String, PatientOrderHospitalVO> patientOrderIdToHospitalVo = patientOrderHospitalVoFuture.join();
        Map<String, ShebaoSettleInfoRspBody.ShebaoSettleInfo> patientOrderIdToShebaoSettle = shebaoSettleInfoFuture.join();
        Map<String, Department> departmentIdToDepartment = departmentFuture.join();
        Map<Long, AdviceDetailView> adviceIdToAdviceDetailView = adviceDetailViewFuture.join();
        Map<String, List<EmrDiagnosisInfo>> emrDiagnosisInfoMap = listPatientDiagnosisFuture.join();


        List<HospitalDispensingOrderPrescriptionPrintRsp> finalResultList = resultList;
        patientOrderIdToSheetV2List.forEach((patientOrderId, sheetV2List) -> {
            List<DispensingSheetView> sheetViewList = new ArrayList<>();
            sheetV2List.forEach(sheetV2 -> {
                DispensingSheetView sheetView = DispensingSheetView.from(sheetV2);
                setSheetViewGroupId(sheetView, adviceIdToAdviceDetailView.get(sheetView.getAdviceId()));
                AtomicBoolean needAddFlag = new AtomicBoolean(false);
                List<DispensingFormView> dispensingForms = sheetView.getDispensingForms();
                dispensingForms.forEach(formItem -> {
                    List<DispensingFormItemView> dispensingFormItems = formItem.getDispensingFormItems();
                    if (clientReq.getTab() == 1) {
                        List<DispensingFormItemView> dispensingItemViewList = dispensingFormItems.stream()
                                .filter(item -> DispensingUtils.HOSPITAL_DISPENSE_DISPLAY_STATUS.contains(item.getStatus())).collect(toList());
                        if (!CollectionUtils.isEmpty(dispensingItemViewList)) {
                            needAddFlag.getAndSet(true);
                            formItem.setDispensingFormItems(dispensingItemViewList);
                        }
                    } else {
                        List<DispensingFormItemView> undispensingItemViewList = dispensingFormItems.stream()
                                .filter(item -> DispensingUtils.HOSPITAL_UNDISPENSE_DISPLAY_STATUS.contains(item.getStatus())).collect(toList());
                        if (!CollectionUtils.isEmpty(undispensingItemViewList)) {
                            needAddFlag.getAndSet(true);
                            formItem.setDispensingFormItems(undispensingItemViewList);
                        }
                    }
                });
                if (!needAddFlag.get()) {
                    return;
                }
                Employee employee = employeeIdToEmployee.get(sheetV2.getDispensedBy());
                if (employee != null) {
                    sheetView.setDispensedByName(employee.getName());
                }
                Employee doctor = employeeIdToEmployee.get(sheetV2.getDoctorId());
                if (doctor != null) {
                    sheetView.setDoctorName(doctor.getName());
                }
                Department department = departmentIdToDepartment.get(sheetV2.getDoctorDepartmentId());
                if (department != null) {
                    sheetView.setDoctorDepartmentName(department.getName());
                }
                sheetView.setDispenseOrderId(Optional.ofNullable(orderSheetRelMap.get(sheetView.getId())).map(DispensingOrderSheetRel::getOrderId).map(String::valueOf).orElse(null));
                sheetView.getDispensingForms().forEach(formView -> formView.getDispensingFormItems()
                        .forEach(formItemView -> formItemView.setProductInfo(JsonUtils.dumpAsJsonNode(goodsIdToGoodsItem.get(formItemView.getProductId())))));

                sheetViewList.add(sheetView);
            });

            HospitalDispensingOrderPrescriptionPrintRsp rsp = new HospitalDispensingOrderPrescriptionPrintRsp();
            rsp.setPatientOrderId(patientOrderId);
            rsp.setPatientOrderHospitalVO(patientOrderIdToHospitalVo.get(patientOrderId));
            rsp.setDispensingSheetList(sheetViewList);
            rsp.setShebaoSettle(patientOrderIdToShebaoSettle.get(patientOrderId));
            rsp.setDiagnoses(emrDiagnosisInfoMap.get(patientOrderId));
            finalResultList.add(rsp);
        });
    }

    private void setSheetViewGroupId(DispensingSheetView sheetView, AdviceDetailView adviceDetailView) {
        if (sheetView == null) {
            return;
        }
        if (adviceDetailView != null) {
            sheetView.setAdviceView(adviceDetailView);
            if (sheetView.getPlanExecuteTime() != null) {
                sheetView.setGroupId(DispensingUtils.generateSheetAdviceGroupId(adviceDetailView.getGroupId(), sheetView.getPlanExecuteTime(), null));
            } else {
                sheetView.setGroupId(DispensingUtils.generateSheetAdviceGroupId(sheetView.getAdviceId(), null, sheetView.getId()));
            }
            sheetView.setAdviceGroupId(adviceDetailView.getGroupId());
        } else {
            sheetView.setGroupId(DispensingUtils.generateSheetAdviceGroupId(sheetView.getAdviceId(), null, sheetView.getId()));
        }
    }

    private CompletableFuture<Map<String, PatientOrderHospitalVO>> getPatientOrderHospitalVoFuture(String chainId, String clinicId,
                                                                                                   List<String> patientOrderIdList) {
        return ExecutorUtils.futureSupplyAsync(() -> patientOrderService.findPatientOrderListHospital(chainId, clinicId, patientOrderIdList));
    }

    private CompletableFuture<Map<String, ShebaoSettleInfoRspBody.ShebaoSettleInfo>> getShebaoSettleInfoFuture(String chainId, String clinicId,
                                                                                                               List<String> patientOrderIdList) {
        return ExecutorUtils.futureSupplyAsync(() -> shebaoService.getShebaoSettleInfoMap(chainId, clinicId, patientOrderIdList));
    }


    private CompletableFuture<Map<String, List<EmrDiagnosisInfo>>> getListPatientDiagnosisFuture(String chainId, String clinicId, List<String> patientOrderIds) {
        return ExecutorUtils.futureSupplyAsync(() -> hisEmrService.listPatientDiagnosis(chainId, clinicId, patientOrderIds).stream().collect(groupingBy(EmrDiagnosisInfo::getPatientOrderId)));
    }

    private CompletableFuture<Map<String, Employee>> getEmployeeFuture(List<String> employeeIdList, String chainId) {
        return ExecutorUtils.futureSupplyAsync(() -> employeeService.findEmployees(employeeIdList, chainId).stream().collect(toMap(EmployeeBasic::getId, Function.identity(), (a, b) -> a)));
    }

    private CompletableFuture<Map<String, Department>> getDepartmentFuture(List<String> departmentIdList) {
        return ExecutorUtils.futureSupplyAsync(() -> clinicClient.queryDepartmentsByIds(departmentIdList).stream().collect(toMap(Department::getId, Function.identity(), (a, b) -> a)));
    }

    private void getGoodsItem(String chainId, String clinicId, List<DispensingOrder> dispensingOrderList, Map<String, GoodsItem> goodsIdToGoodsItem) {
        if (CollectionUtils.isEmpty(dispensingOrderList)) {
            return;
        }
        if (goodsIdToGoodsItem == null) {
            goodsIdToGoodsItem = new HashMap<>();
        }
        List<GoodsItem> goodsItems = new ArrayList<>();
        try {
            List<QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq> pharmacyGoodsList = new ArrayList<>();
            dispensingOrderList.stream().forEach(dispensingOrder -> {
                QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq queryPharmacyGoodsReq = new QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq();
                queryPharmacyGoodsReq.setPharmacyNo(dispensingOrder != null ? dispensingOrder.getPharmacyNo() : 0);
                queryPharmacyGoodsReq.setQueryGoodsList(dispensingOrder.getDispensingSheetV2List().stream().map(DispensingSheetV2::getDispensingForms).flatMap(List::stream)
                        .map(DispensingFormV2::getDispensingFormItems).flatMap(List::stream).map(item -> {
                            QueryGoodsInPharmacyByIdsReq.QueryGoodsItem queryGoodsItem = new QueryGoodsInPharmacyByIdsReq.QueryGoodsItem();
                            queryGoodsItem.setGoodsId(item.getProductId());
                            return queryGoodsItem;
                        }).collect(toList()));
                pharmacyGoodsList.add(queryPharmacyGoodsReq);
            });
            List<QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp> queryPharmacyGoodsRspList = scGoodsFeignClient.queryGoodsInPharmacyByIdsDistinctByGoodsId(clinicId, chainId, true, 1, pharmacyGoodsList);
            goodsItems.addAll(Optional.ofNullable(queryPharmacyGoodsRspList).orElse(new ArrayList<>()).stream()
                    .filter(goodsRsp -> !CollectionUtils.isEmpty(goodsRsp.getList()))
                    .flatMap(goodsRsp -> goodsRsp.getList().stream())
                    .collect(toList()));
        } catch (ServiceInternalException e) {
            log.error("queryGoodsInPharmacyByIds error, e = ", e);
        }
        goodsIdToGoodsItem.putAll(goodsItems.stream().collect(toMap(GoodsItem::getId, Function.identity(), (a, b) -> a)));
    }

    @Component
    public class InnerDispensingSheetV2Listener implements DispensingSheetV2Listener {

        @Autowired
        private RocketMqProducer rocketMqProducer;

        @Override
        public void onDispensingSheetDeletedAfterCommit(String chainId, String clinicId, DispensingSheetV2 dispensingSheet, String operatorId, Context context) {
            rocketMqProducer.sendDispensingSheetMessage(dispensingSheet, DispensingSheetMessage.MSG_TYPE_DELETE,
                    operatorId, context.getAdviceMessage(), context.getAdviceExecuteMessage(),
                    context.getGoodsConfigView(), context.getAdviceUsageRuleItemsAddMessage());
        }

        @Override
        public void onDispensingSheetClosedAfterCommit(String chainId, String clinicId, DispensingSheetV2 dispensingSheet, String operatorId, Context context) {
//            rocketMqProducer.sendDispensingSheetMessage(dispensingSheet, DispensingSheetMessage.MSG_TYPE_CLOSE, operatorId);
        }

        @Override
        public void onDispensingSheetCreatedAfterCommit(String chainId, String clinicId, DispensingSheetV2 dispensingSheet, String operatorId, Context context) {
            rocketMqProducer.sendDispensingSheetMessage(dispensingSheet, DispensingSheetMessage.MSG_TYPE_CREATE,
                    operatorId, context.getAdviceMessage(), context.getAdviceExecuteMessage(),
                    context.getGoodsConfigView(), context.getAdviceUsageRuleItemsAddMessage());
        }
    }

    public AbcListPage<DispensingSheetView> getAutoDispensingList(GetAutoDispensingListReq clientReq) {
        AbcListPage<DispensingSheetView> abcListPage = new AbcListPage<>();
        abcListPage.setRows(new ArrayList<>());
        if (!StringUtils.isEmpty(clientReq.getKeyword())) {
            List<String> patientOrderIdList = searchAutoDispensingPatientOrder(clientReq);
            if (CollectionUtils.isEmpty(patientOrderIdList)) {
                return abcListPage;
            }
            clientReq.setPatientOrderIds(patientOrderIdList);
        } else {
            int totalCount = dispensingMapper.countAutoDispensingSheetIdList(clientReq);
            abcListPage.setTotal(totalCount);
            if (totalCount <= 0) {
                return abcListPage;
            }
        }
        String chainId = clientReq.getChainId();
        String clinicId = clientReq.getClinicId();
        // 获取发药单id
        List<String> dispensingShhetIdList = dispensingMapper.getAutoDispensingSheetIdList(clientReq);
        if (CollectionUtils.isEmpty(dispensingShhetIdList)) {
            return abcListPage;
        }
        List<String> queriedSheetIdList = new ArrayList<>(dispensingShhetIdList);
        Map<String, DispensingSheetV2> sheetIdToSheet = loadAssembleDispenseSheet(chainId, clinicId, null, dispensingShhetIdList, null);
        CompletableFuture<Map<String, GoodsItem>> goodsItemFuture = getGoodsItemFuture(new ArrayList<>(sheetIdToSheet.values()), chainId, clinicId);
        CompletableFuture<Map<String, PatientInfo>> patientInfoFuture = getPatientInfoFuture(chainId,
                sheetIdToSheet.values().stream().map(DispensingSheetV2::getPatientId).collect(toList()));
        CompletableFuture<Map<String, WardBedView>> wardBedViewFuture = getWardBedViewFuture(chainId, clinicId,
                sheetIdToSheet.values().stream().map(DispensingSheetV2::getPatientOrderId).distinct().collect(toList()));
        Map<Long, WardAreaView> wardAreaIdToWardArea = new HashMap<>();
        if (clientReq.getWardAreaId() != null && clientReq.getWardAreaId() > 0) {
            CompletableFuture<Map<Long, WardAreaView>> wardAreaFuture = getWardAreaFuture(chainId, clinicId, sheetIdToSheet.values()
                    .stream().filter(it -> !StringUtils.isEmpty(it.getV1Id())).map(it -> Long.parseLong(it.getV1Id())).distinct().collect(toList()));
            wardAreaIdToWardArea = wardAreaFuture.join();
        }
        // 记账护士
        List<String> chargeCreatedByIdList = sheetIdToSheet.values().stream().filter(it -> it.getSourceSheetType() == DispenseConst.SourceSheetType.CHARGE_SUPPLEMENT).map(DispensingSheetV2::getCreatedBy).distinct().collect(toList());
        Map<String, Employee> employeeIdToEmployeeId = new HashMap<>();
        if (!CollectionUtils.isEmpty(chargeCreatedByIdList)) {
            CompletableFuture<Map<String, Employee>> employeeFuture = getEmployeeFuture(chargeCreatedByIdList, chainId);
            employeeIdToEmployeeId = employeeFuture.join();
        }

        Map<String, GoodsItem> goodsIdToGoodsItem = goodsItemFuture.join();
        Map<String, PatientInfo> patientIdToPatientInfo = patientInfoFuture.join();
        Map<String, WardBedView> patientOrderIdToBedView = wardBedViewFuture.join();

        for (String sheetId : queriedSheetIdList) {
            DispensingSheetV2 dispensingSheetV2 = sheetIdToSheet.get(sheetId);
            if (dispensingSheetV2 == null) {
                continue;
            }
            //Map<String, List<DispensingFormItemBatchInfo>> dispensingFormItemIdToBatchInfos = new HashMap<>();
            //DispensingUtils.doWithDispensingItem(dispensingSheetV2, item -> {
            //    if (CollectionUtils.isEmpty(item.getDispensingFormItemBatches())) {
            //        return;
            //    }
            //    dispensingFormItemIdToBatchInfos.put(item.getId(), item.getDispensingFormItemBatches());
            //});
            DispensingSheetView sheetView = DispensingSheetView.from(dispensingSheetV2);
            if (patientIdToPatientInfo.get(sheetView.getPatientId()) != null) {
                sheetView.setPatient(new CisPatientInfo());
                BeanUtils.copyProperties(patientIdToPatientInfo.get(sheetView.getPatientId()), sheetView.getPatient());
            }
            if (dispensingSheetV2.getV1Id() != null) {
                sheetView.setWardAreaView(wardAreaIdToWardArea.get(Long.parseLong(dispensingSheetV2.getV1Id())));
            }
            sheetView.setBeds(patientOrderIdToBedView.get(sheetView.getPatientOrderId()));
            if (sheetView.getSourceSheetType() == DispenseConst.SourceSheetType.CHARGE_SUPPLEMENT) {
                sheetView.setChargeNurseId(dispensingSheetV2.getCreatedBy());
                Employee employee = employeeIdToEmployeeId.get(dispensingSheetV2.getCreatedBy());
                sheetView.setChargeNurseName(employee != null ? employee.getName() : "");
            }
            for (DispensingFormView dispensingForm : sheetView.getDispensingForms()) {
                //发药项里面的Goods 填充的是最新的
                for (DispensingFormItemView dispensingFormItem : dispensingForm.getDispensingFormItems()) {
                    if (goodsIdToGoodsItem.get(dispensingFormItem.getProductId()) != null) {
                        dispensingFormItem.setProductInfo(JsonUtils.dumpAsJsonNode(goodsIdToGoodsItem.get(dispensingFormItem.getProductId())));
                    }
                    //if (!CollectionUtils.isEmpty(dispensingFormItem.getDispensingFormItemBatches())) {
                    //    List<DispensingFormItemBatchInfoView> dispensingFormItemBatches = dispensingFormItem.getDispensingFormItemBatches();
                    //    for (DispensingFormItemBatchInfoView batchInfo : dispensingFormItemBatches) {
                    //        GoodsBatchInfo goodsBatchInfo = batchIdToBatchInfo.get(batchInfo.getBatchId());
                    //        if (goodsBatchInfo != null) {
                    //            batchInfo.setBatchInfo(goodsBatchInfo);
                    //        }
                    //    }
                    //}
                    //if (dispensingFormItem.getStatus() == DispenseConst.Status.APPLY_UNDISPNSE
                    //        && dispensingFormItemIdToBatchInfos.containsKey(dispensingFormItem.getAssociateFormItemId())) {
                    //    dispensingFormItem.setAssociateDispensingFormItemBatches(buildDispensableFormItemBatches(dispensingFormItemIdToBatchInfos.get(dispensingFormItem.getAssociateFormItemId()), batchIdToBatchInfo));
                    //}
                }
                // 过滤待退的项
                List<DispensingFormItemView> dispensingFormItems = dispensingForm.getDispensingFormItems().stream()
                        .filter(it -> it.getStatus() != DispenseConst.Status.APPLY_UNDISPNSE).collect(toList());
                if (dispensingSheetV2.getStatus() == DispenseConst.Status.UNDISPENSED) {
                    dispensingFormItems = dispensingFormItems.stream().filter(it -> it.getStatus() == DispenseConst.Status.UNDISPENSED).collect(toList());
                }
                dispensingForm.setDispensingFormItems(dispensingFormItems);
            }

            abcListPage.getRows().add(sheetView);
        }

        return abcListPage;
    }

    private CompletableFuture<Map<Long, WardAreaView>> getWardAreaFuture(String chainId, String clinicId, List<Long> wardAreaIdList) {
        return ExecutorUtils.futureSupplyAsync(() -> cisWardService.getWardByIdList(chainId, clinicId, wardAreaIdList));
    }

    private CompletableFuture<Map<String, GoodsItem>> getGoodsItemFuture(List<DispensingSheetV2> dispensingSheetV2List,
                                                                         String chainId, String clinicId) {
        return ExecutorUtils.futureSupplyAsync(() -> {
            Map<Integer, List<DispensingSheetV2>> pharmacyNoToSheetList = dispensingSheetV2List.stream().collect(groupingBy(DispensingSheetV2::getPharmacyNo));
            List<QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq> pharmacyGoodsList = new ArrayList<>();
            pharmacyNoToSheetList.forEach((pharmacyNo, sheetList) -> {
                List<String> goodsIdList = sheetList.stream().flatMap(it -> it.getDispensingForms().stream())
                        .flatMap(it -> it.getDispensingFormItems().stream()).map(DispensingFormItemV2::getProductId)
                        .distinct().collect(toList());
                QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq queryPharmacyGoodsReq = new QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq();
                queryPharmacyGoodsReq.setPharmacyNo(pharmacyNo);
                List<QueryGoodsInPharmacyByIdsReq.QueryGoodsItem> queryGoodsItemList = new ArrayList<>();
                for (String goodsId : goodsIdList) {
                    QueryGoodsInPharmacyByIdsReq.QueryGoodsItem queryGoodsItem = new QueryGoodsInPharmacyByIdsReq.QueryGoodsItem();
                    queryGoodsItem.setGoodsId(goodsId);
                    queryGoodsItemList.add(queryGoodsItem);
                }
                queryPharmacyGoodsReq.setQueryGoodsList(queryGoodsItemList);
                pharmacyGoodsList.add(queryPharmacyGoodsReq);
            });
            List<QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp> queryPharmacyGoodsRspList =
                    scGoodsFeignClient.queryGoodsInPharmacyByIdsDistinctByGoodsId(clinicId, chainId, true, 1, pharmacyGoodsList);
            List<GoodsItem> goodsItems = Optional.ofNullable(queryPharmacyGoodsRspList).orElse(new ArrayList<>())
                    .stream().filter(queryPharmacyGoodsRsp -> !CollectionUtils.isEmpty(queryPharmacyGoodsRsp.getList()))
                    .flatMap(queryPharmacyGoodsRsp -> queryPharmacyGoodsRsp.getList().stream())
                    .collect(toList());
            return goodsItems.stream().collect(toMap(GoodsItem::getId, Function.identity(), (a, b) -> a));
        });
    }

    /**
     * 自动发药清单搜索，结果为patientOrderId
     */
    private List<String> searchAutoDispensingPatientOrder(GetAutoDispensingListReq clientReq) {
        try {
            RpcSearchHospitalPatientDispenseReq searchReq = new RpcSearchHospitalPatientDispenseReq();
            searchReq.setChainId(clientReq.getChainId());
            searchReq.setClinicId(clientReq.getClinicId());
            searchReq.setScoreCreatedGauss(1);
            searchReq.setKeyword(clientReq.getKeyword());
            searchReq.setOffset(0);
            searchReq.setLimit(100);
            if (clientReq.getWardAreaId() != null) {
                searchReq.setWardId(clientReq.getWardAreaId().toString());
            }
            long startTime = System.currentTimeMillis();
            AbcServiceResponseBody<SearchResultRsp> responseBody = searchFeignClient.doCommonSearch(searchReq);
            log.info("searchAutoDispensing costTime = {}, rsp = {}", (System.currentTimeMillis() - startTime), JsonUtils.dump(responseBody));
            if (responseBody != null && responseBody.getData() != null && !CollectionUtils.isEmpty(responseBody.getData().getHits())) {
                return responseBody.getData().getHits().stream().filter(Objects::nonNull)
                        .filter(it -> it.get("id") != null).map(it -> it.get("id").asText()).collect(toList());
            }
        } catch (Exception e) {
            log.error("searchAutoDispensing error, e = ", e);
        }
        return new ArrayList<>();
    }

    @Transactional(rollbackFor = Exception.class)
    public void autoDispenseSheet(DispensingSheetV2 sheetV2, String operatorId) {
        Map<String, DispensingSheetV2> sheetIdToSheetV2 = loadAssembleDispenseSheet(sheetV2.getChainId(), sheetV2.getClinicId(),
                null, Lists.newArrayList(sheetV2.getId()), null);
        DispensingSheetV2 dispensingSheetV2 = sheetIdToSheetV2.get(sheetV2.getId());
        if (dispensingSheetV2 == null) {
            return;
        }
        List<DispensingFormItemV2> waitingDispenseItems = dispensingSheetV2.waitingDispenseItems();
        if (CollectionUtils.isEmpty(waitingDispenseItems)) {
            log.info("自动发药，发药单无待发药项目，sheetId = {}", dispensingSheetV2.getId());
            return;
        }
        PatientOrder patientOrder = patientOrderService.findPatientOrder(dispensingSheetV2.getPatientOrderId());
        Map<String, DispensingFormItemV2> formItemIdToItem = waitingDispenseItems.stream().collect(toMap(DispensingFormItemV2::getId, Function.identity(), (a, b) -> a));
        bindGoodsItemIfNeeded(dispensingSheetV2.getChainId(), dispensingSheetV2.getClinicId(), sheetIdToSheetV2);
        List<GoodsDispenseDataItem> goodsDispenseDataItems = new ArrayList<>();
        List<DispensingFormItemV2> dispensedDispenseFormItemList = new ArrayList<>();
        Map<String, GoodsDispenseDataItem> formItemIdToDataItem = new HashMap<>();
        Map<String, GoodsDispenseResultItem> formItemIdToResultItem = new HashMap<>();
        for (DispensingFormItemV2 formItemV2 : waitingDispenseItems) {
            if (formItemV2.isSelfProvidedBySourceItemType()) {
                log.info("自备药，不自动发药, item={}", JsonUtils.dump(formItemV2));
                continue;
            }
            BigDecimal totalCount = MathUtils.calculateTotalCount(formItemV2.getUnitCount(), formItemV2.getDoseCount());
            GoodsDispenseDataItem goodsDispenseDataItem = new GoodsDispenseDataItem();
            goodsDispenseDataItem.setPharmacyNo(formItemV2.getPharmacyNo());
            goodsDispenseDataItem.setDispenseItemId(formItemV2.getId());
            goodsDispenseDataItem.setGoodsId(formItemV2.getProductId());
            if (formItemV2.getLockId() != null) {
                goodsDispenseDataItem.setLockId(formItemV2.getLockId());
            }
            goodsDispenseDataItem.setBatchCountList(buildBatchCountList(formItemV2.getGoodsItem(), formItemV2));
            if (!CollectionUtils.isEmpty(goodsDispenseDataItem.getBatchCountList())) {
                goodsDispenseDataItem.setLockBatch(YesOrNo.YES);
            }

            if (formItemV2.getUseDismounting() == 1) {
                goodsDispenseDataItem.setPieceCount(totalCount);
                goodsDispenseDataItem.setPackageCount(BigDecimal.ZERO);
                goodsDispenseDataItem.setTotalDispenseFormItemPieceCount(totalCount); //医院无多次发
                goodsDispenseDataItem.setTotalDispenseFormItemPackageCount(BigDecimal.ZERO);
            } else {
                goodsDispenseDataItem.setPackageCount(totalCount);
                goodsDispenseDataItem.setPieceCount(BigDecimal.ZERO);
                goodsDispenseDataItem.setTotalDispenseFormItemPieceCount(BigDecimal.ZERO); //医院无多次发
                goodsDispenseDataItem.setTotalDispenseFormItemPackageCount(totalCount);
            }
            goodsDispenseDataItem.setTotalPrice(formItemV2.getTotalPrice());
            formItemIdToDataItem.put(formItemV2.getId(), goodsDispenseDataItem);
            goodsDispenseDataItems.add(goodsDispenseDataItem);
            dispensedDispenseFormItemList.add(formItemV2);
        }
        if (!goodsDispenseDataItems.isEmpty()) {
            List<GoodsDispenseResultItem> list = abcCisScGoodsService.dispense(
                    dispensingSheetV2.getClinicId(),
                    dispensingSheetV2.getChainId(),
                    operatorId,
                    patientOrder != null ? patientOrder.getNo() + "": null,
                    dispensingSheetV2.getPatientOrderId(),
                    dispensingSheetV2.getId(),
                    dispensingSheetV2.getPharmacyNo(),
                    dispensingSheetV2.getPharmacyType(),
                    goodsDispenseDataItems,
                    DTOConverter.dispensingFormItemV2MapToDispensingFormItemAbstractMap(formItemIdToItem),
                    null,
                    10);
            if (CollectionUtils.isEmpty(list) || list.size() != goodsDispenseDataItems.size()) {
                throw new GoodsDispenseException();
            }
            formItemIdToResultItem.putAll(list.stream().collect(toMap(GoodsDispenseResultItem::getDispenseItemId, Function.identity(), (a, b) -> a)));
        }
        String operationId = abcIdGenerator.getUID();
        List<DispensingFormItemV2> thisTimeDispensingFormItems = new ArrayList<>();
        List<String> finalDispensedByIds = Arrays.asList(operatorId);

        updateDispenseSheet(dispensingSheetV2, operationId, operatorId, finalDispensedByIds, dispensedDispenseFormItemList,
                formItemIdToDataItem, formItemIdToResultItem, thisTimeDispensingFormItems);

        if (thisTimeDispensingFormItems.isEmpty()) {
            return;
        }

        dispensingLogService.logDispensingSheetDispense(dispensingSheetV2, thisTimeDispensingFormItems, formItemIdToResultItem, operationId, operatorId);

        /**
         * 发药Operation
         * */
        SheetOperationBase operationBase =
                sheetOperationCreateFactory.createSheetOperation(DispensingSheetOperation.OperationType.SEND_MEDICINE,
                        dispensingSheetV2.getId(), operatorId, operatorId, dispensingSheetV2.getChainId(), dispensingSheetV2.getClinicId());
        operationBase.bindDispensingSheetV2(dispensingSheetV2)
                .bindDispensingFormItemsV2(thisTimeDispensingFormItems)
                .bindDispensedByIds(finalDispensedByIds)
                .bindRepoId(operationId);
        operationBase.createAndSaveSheetOperation();

        rocketMqProducer.sendDispensingSheetUpdateAfterCommitAsync(PatientOrderMessage.MSG_SUB_TYPE_DISPENGING_UPDATED_DISPENSE, Arrays.asList(dispensingSheetV2));
    }

    private void updateDispenseSheet(DispensingSheetV2 dispensingSheetV2,
                                     String operationId,
                                     String employeeId,
                                     List<String> dispensedByIds,
                                     List<DispensingFormItemV2> dispensedDispenseItems,
                                     Map<String, GoodsDispenseDataItem> formItemIdToDataItem,
                                     Map<String, GoodsDispenseResultItem> formItemIdToResultItem,
                                     List<DispensingFormItemV2> thisTimeDispensingFormItems) {
        DispensingSheetInfo dispensingSheetInfo = new DispensingSheetInfo();
        dispensingSheetInfo.setDispensedByIds(dispensedByIds);
        String dispensedBy = dispensedByIds.get(0);

        for (DispensingFormItemV2 formItemV2 : dispensedDispenseItems) {
            formItemV2.setStatus(DispenseConst.Status.DISPENSED);
            GoodsDispenseDataItem goodsDispenseDataItem = formItemIdToDataItem.get(formItemV2.getId());
            GoodsDispenseResultItem dispenseResultItem = formItemIdToResultItem.get(formItemV2.getId());
            if (dispenseResultItem != null) {
                formItemV2.setThisTimeDispensed(1);
                formItemV2.setStockDealId(DispensingUtils.getGoodsDispenseResultItemDealId(dispenseResultItem, true));
                formItemV2.setTotalCostPrice(MathUtils.wrapBigDecimal(dispenseResultItem.getTotalCostPrice(), BigDecimal.ZERO));
                formItemV2.setOperationId(operationId);
                GoodsItem goodsItem = formItemV2.getGoodsItem();
                if (!CollectionUtils.isEmpty(formItemV2.getDispensingFormItemBatches()) || formItemV2.lockFailOrNeedLock()) {
                    // 如果有批次信息，或者之前锁库失败，更新批次信息
                    updateDispenseToDispensingFormItemBatch(formItemV2, goodsItem, goodsDispenseDataItem, dispenseResultItem, employeeId);
                }
                if (!CollectionUtils.isEmpty(formItemV2.getDispensingFormItemBatches())) {
                    // 用批次的价格覆盖发药项的价格
                    formItemV2.setTotalCostPrice(formItemV2.getDispensingFormItemBatches().stream().map(DispensingFormItemBatchInfo::getTotalCostPrice).reduce(BigDecimal.ZERO, MathUtils::wrapBigDecimalAdd));
                    formItemV2.setTotalPrice(formItemV2.getDispensingFormItemBatches().stream().map(DispensingFormItemBatchInfo::getTotalPrice).reduce(BigDecimal.ZERO, MathUtils::wrapBigDecimalAdd));
                }
                FillUtils.fillLastModifiedBy(formItemV2, employeeId);
                thisTimeDispensingFormItems.add(formItemV2);

            }
        }
        dispensingSheetV2.fixHisSheetStatus();
        if (dispensingSheetV2.getStatus() == DispenseConst.Status.DISPENSED) {
            dispensingSheetV2.setOrderByDate(Instant.now());
        }
        dispensingSheetV2.setDispensedTime(Instant.now());

        dispensingSheetV2.setDispensedBy(dispensedBy);
        dispensingSheetV2.setDispensingSheetInfo(dispensingSheetInfo);
        FillUtils.fillLastModifiedBy(dispensingSheetV2, employeeId);
        dispensingSheetEntityService.save(dispensingSheetV2);
    }

    public void dispenseAutoDispensingSheet(AutoDispenseReq clientReq) {
        String chainId = clientReq.getHeaderChainId();
        String clinicId = clientReq.getHeaderClinicId();
        String employeeId = clientReq.getHeaderEmployeeId();
        List<String> dispensingSheetIdList = clientReq.getDispenseSheets().stream()
                .filter(it -> !StringUtils.isEmpty(it.getId())).map(AutoDispenseReq.AutoDispenseSheetReq::getId).distinct().collect(toList());
        Map<String, DispensingSheetV2> sheetIdToSheet = loadAssembleDispenseSheet(chainId, clinicId, null, dispensingSheetIdList, null);

        Map<String, PatientOrderHospitalVO>  patientOrderHospitalVOMap = checkPatientOutHospitalOfAutoDispense(clientReq, sheetIdToSheet);

        Map<String, DispensingSheetV2> needAutoDispenseSheetIdToSheet = new HashMap<>();
        List<DispensingSheetV2> needAutoReNewSheetList = new ArrayList<>();
        for (AutoDispenseReq.AutoDispenseSheetReq sheetReq : clientReq.getDispenseSheets()) {
            sheetReq.checkParam();
            DispensingSheetV2 dispensingSheetV2 = sheetIdToSheet.get(sheetReq.getId());
            if (dispensingSheetV2 == null) {
                throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "未找到发药单");
            }
            if (dispensingSheetV2.isSelfProvided()) {
                throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "自备药不可发药");
            }
            if (!dispensingSheetV2.isAutoDispensingMethod()) {
                throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "非自动发药不支持发药");
            }
            if (dispensingSheetV2.getStatus() == DispenseConst.Status.CLOSED || dispensingSheetV2.getStatus() == DispenseConst.Status.DISPENSED) {
                sLogger.info("发药单已发药，id: {}", dispensingSheetV2.getId());
                throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "已发发药单不可在发");
            } else if (dispensingSheetV2.getStatus() == DispenseConst.Status.UNDISPENSED) {
                // 退药
                if (!DispensingUtils.canReNewSheetSourceSheetTypeList.contains(dispensingSheetV2.getSourceSheetType())) {
                    throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR,
                            "患者记账项目暂不支持重新发药，请通过记账重新开出");
                }
                if (dispensingSheetV2.hasReNewSheet()) {
                    throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "已重新发药发药单不可再发");
                }
                needAutoReNewSheetList.add(dispensingSheetV2);
            } else {
                needAutoDispenseSheetIdToSheet.put(dispensingSheetV2.getId(), dispensingSheetV2);
            }
            if (dispensingSheetV2.getStatus() == DispenseConst.Status.WAITING) {
                List<DispensingFormItemV2> waitingDispenseItems = dispensingSheetV2.waitingDispenseItems();
                if (CollectionUtils.isEmpty(waitingDispenseItems)) {
                    log.info("发药单无待发药项目，sheetId = {}", dispensingSheetV2.getId());
                    throw new NoDispenseItemException();
                }
            }
        }

        DispensingOrderService orderServiceProxy = SpringUtils.getBean(DispensingOrderService.class);
        orderServiceProxy.doDispenseAutoDispensingSheet(chainId, clinicId, employeeId, needAutoDispenseSheetIdToSheet,patientOrderHospitalVOMap, clientReq);
        // 重新生成发药单
        doReNewAndDispenseAutoSheet(chainId, clinicId, employeeId, needAutoReNewSheetList,patientOrderHospitalVOMap, orderServiceProxy);
    }

    private void doReNewAndDispenseAutoSheet(String chainId, String clinicId, String employeeId,
                                             List<DispensingSheetV2> needAutoReNewSheetList,
                                             Map<String, PatientOrderHospitalVO>  patientOrderHospitalVOMap,
                                             DispensingOrderService dispensingOrderService) {
        if (CollectionUtils.isEmpty(needAutoReNewSheetList)) {
            return;
        }
        Map<Integer, GoodsPharmacyView> pharmacyNoToPharmacyView = scGoodsFeignClient.findPharmacyByClinic(chainId, clinicId)
                .stream().collect(toMap(GoodsPharmacyView::getNo, Function.identity(), (a, b) -> a));
        GoodsConfigView goodsConfig = scGoodsFeignClient.getGoodsConfig(clinicId);
        // 查询医嘱
        Map<Long, AdviceMessage> adviceIdToAdviceMessage = new HashMap<>();
        Map<Long, AdviceExecuteMessage> adviceExecuteIdToAdviceExecuteMessage = new HashMap<>();
        Map<Long, AdviceUsageRuleItemsAddMessage> adviceUsageRuleIdToAddMessage = new HashMap<>();
        doLoadAdviceInfo(chainId, clinicId, needAutoReNewSheetList, adviceIdToAdviceMessage,
                adviceExecuteIdToAdviceExecuteMessage, adviceUsageRuleIdToAddMessage);
        // 生成发药单
        List<DispensingSheetV2> newSheetList = dispensingOrderService.doReNewAutoDispensingSheet(chainId, clinicId, employeeId, needAutoReNewSheetList,
                goodsConfig, pharmacyNoToPharmacyView, adviceIdToAdviceMessage, adviceExecuteIdToAdviceExecuteMessage, adviceUsageRuleIdToAddMessage);
        // 发药
        if (newSheetList.isEmpty()) {
            return;
        }
        Map<String, DispensingSheetV2> sheetIdToSheet = newSheetList.stream().collect(toMap(DispensingSheetV2::getId, Function.identity(), (a, b) -> a));
        AutoDispenseReq autoDispenseReq = buildAutoDispenseReq(chainId, clinicId, employeeId, sheetIdToSheet);
        dispensingOrderService.doDispenseAutoDispensingSheet(chainId, clinicId, employeeId, sheetIdToSheet, patientOrderHospitalVOMap,autoDispenseReq);
    }

    private AutoDispenseReq buildAutoDispenseReq(String chainId, String clinicId, String employeeId,
                                                 Map<String, DispensingSheetV2> sheetIdToSheet) {
        AutoDispenseReq autoDispenseReq = new AutoDispenseReq();
        autoDispenseReq.setHeaderChainId(chainId);
        autoDispenseReq.setHeaderClinicId(clinicId);
        autoDispenseReq.setHeaderEmployeeId(employeeId);
        autoDispenseReq.setDispenseSheets(new ArrayList<>());
        sheetIdToSheet.forEach((sheetId, sheet) -> {
            AutoDispenseReq.AutoDispenseSheetReq sheetReq = new AutoDispenseReq.AutoDispenseSheetReq();
            sheetReq.setId(sheetId);
            sheetReq.setDispensingForms(new ArrayList<>());
            sheet.getDispensingForms().forEach(form -> {
                AutoDispenseReq.AutoDispenseFormReq formReq = new AutoDispenseReq.AutoDispenseFormReq();
                formReq.setId(form.getId());
                formReq.setDispenseFormItems(new ArrayList<>());
                form.getDispensingFormItems().forEach(formItem -> {
                    if (formItem.getStatus() != DispenseConst.Status.WAITING) {
                        return;
                    }
                    AutoDispenseReq.AutoDispenseFormItemReq formItemReq = new AutoDispenseReq.AutoDispenseFormItemReq();
                    formItemReq.setId(formItem.getId());
                    formItemReq.setUnitCount(formItem.getUnitCount());
                    formItemReq.setDoseCount(formItem.getDoseCount());
                    formReq.getDispenseFormItems().add(formItemReq);
                });
                if (!formReq.getDispenseFormItems().isEmpty()) {
                    sheetReq.getDispensingForms().add(formReq);
                }
            });
            if (!sheetReq.getDispensingForms().isEmpty()) {
                autoDispenseReq.getDispenseSheets().add(sheetReq);
            }
        });
        return autoDispenseReq;
    }

    private  Map<String, PatientOrderHospitalVO> checkPatientOutHospitalOfAutoDispense(AutoDispenseReq clientReq,
                                                       Map<String, DispensingSheetV2> dispensingSheetIdToSheet) {
        if (clientReq == null || CollectionUtils.isEmpty(dispensingSheetIdToSheet)) {
            return null;
        }
        if (!clientReq.isNeedCheckPatientOrder()) {
            return null;
        }
        List<String> patientOrderIdList = dispensingSheetIdToSheet.values().stream().filter(it -> it.getPatientOrderId() != null).map(DispensingSheetV2::getPatientOrderId).distinct().collect(toList());
        CompletableFuture<Map<String, PatientOrderHospitalVO>> patientOrderHospitalVoFuture = getPatientOrderHospitalVoFuture(clientReq.getHeaderChainId(), clientReq.getHeaderClinicId(), patientOrderIdList);
        Map<String, PatientOrderHospitalVO> patientOrderIdToHospitalVo = patientOrderHospitalVoFuture.join();
        if (CollectionUtils.isEmpty(patientOrderIdToHospitalVo)) {
            return null;
        }
        List<String> productNameList = new ArrayList<>();
        boolean dispense = clientReq.getDispenseType() == AutoDispenseReq.DISPENSE_TYPE_DISPENSE;
        for (AutoDispenseReq.AutoDispenseSheetReq sheetReq : clientReq.getDispenseSheets()) {
            DispensingSheetV2 sheetV2 = dispensingSheetIdToSheet.get(sheetReq.getId());
            if (sheetV2 == null) {
                continue;
            }
            PatientOrderHospitalVO patientOrderHospitalVO = patientOrderIdToHospitalVo.get(sheetV2.getPatientOrderId());
            if (patientOrderHospitalVO != null && patientOrderHospitalVO.getStatus() < HisPatientOrder.Status.WAIT_SETTLE) {
                continue;
            }
            DispensingFormItemV2 dispensingFormItemV2 = sheetV2.getDispensingForms().stream().flatMap(form -> form.getDispensingFormItems()
                            .stream()).filter(it -> it.getStatus() == DispenseConst.Status.WAITING || it.getStatus() == DispenseConst.Status.DISPENSED)
                    .findFirst().orElse(null);
            //String patientName = patientOrderHospitalVO.getPatient() != null ? patientOrderHospitalVO.getPatient().getName() : "";
            String productName = dispensingFormItemV2 != null ? dispensingFormItemV2.getName() : "";
            productNameList.add(productName);
        }
        if (productNameList.isEmpty()) {
            return patientOrderIdToHospitalVo;
        }
        throw new DispensingServiceException(dispense ? DispensingServiceError.HOSPITAL_DISPENSE_FAIL : DispensingServiceError.HOSPITAL_UNDISPENSE_FAIL, productNameList);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void doDispenseAutoDispensingSheet(String chainId, String clinicId, String employeeId,
                                              Map<String, DispensingSheetV2> sheetIdToSheet,
                                              Map<String, PatientOrderHospitalVO>  patientOrderHospitalVOMap,
                                              AutoDispenseReq clientReq) {
        if (CollectionUtils.isEmpty(sheetIdToSheet)) {
            return;
        }
        bindGoodsItemIfNeeded(chainId, clinicId, sheetIdToSheet);
        List<DispensingSheetV2> sendMessageSheetList = new ArrayList<>();
        for (AutoDispenseReq.AutoDispenseSheetReq sheetReq : clientReq.getDispenseSheets()) {
            DispensingSheetV2 dispensingSheetV2 = sheetIdToSheet.get(sheetReq.getId());
            List<AutoDispenseReq.AutoDispenseFormItemReq> clientFormItemList = sheetReq.getAllDispenseFormItems();
            if (CollectionUtils.isEmpty(clientFormItemList)) {
                log.info("请求无发药项目，sheetId = {}", sheetReq.getId());
                continue;
            }
            List<GoodsDispenseDataItem> goodsDispenseDataItems = new ArrayList<>();
            Map<String, GoodsDispenseDataItem> dispensingFormItemIdToDispenseDataItem = new HashMap<>();
            Map<String, GoodsDispenseResultItem> formItemIdToResultItem = new HashMap<>();
            List<DispensingFormItemV2> srvFormItemList = dispensingSheetV2.allDispenseItems();
            Map<String, DispensingFormItemV2> formItemIdToFormItem =
                    srvFormItemList.stream().collect(toMap(DispensingFormItemV2::getId, Function.identity(), (a, b) -> a));
            // 锁库id
            for (AutoDispenseReq.AutoDispenseFormItemReq clientFormItem : clientFormItemList) {
                DispensingFormItemV2 toDispenseItem = formItemIdToFormItem.get(clientFormItem.getId());
                if (toDispenseItem == null) {
                    continue;
                }
                if (toDispenseItem.isSelfProvidedBySourceItemType()) {
                    log.info("自备药，不自动发药, item={}", JsonUtils.dump(toDispenseItem));
                    continue;
                }
                BigDecimal existedDispensedCount = MathUtils.calculateTotalCount(toDispenseItem.getUnitCount(), toDispenseItem.getDoseCount());
                GoodsDispenseDataItem goodsDispenseDataItem = new GoodsDispenseDataItem();
                goodsDispenseDataItem.setPharmacyNo(toDispenseItem.getPharmacyNo());
                goodsDispenseDataItem.setDispenseItemId(toDispenseItem.getId());
                goodsDispenseDataItem.setGoodsId(toDispenseItem.getProductId());
                if (toDispenseItem.getLockId() != null) {
                    // 优先用发药单自身记录的锁库Id
                    goodsDispenseDataItem.setLockId(toDispenseItem.getLockId());
                }
                goodsDispenseDataItem.setBatchCountList(buildBatchCountList(toDispenseItem.getGoodsItem(), toDispenseItem));
                if (!CollectionUtils.isEmpty(goodsDispenseDataItem.getBatchCountList())) {
                    goodsDispenseDataItem.setLockBatch(YesOrNo.YES);
                }


                if (toDispenseItem.getUseDismounting() == 1) {
                    goodsDispenseDataItem.setPieceCount(existedDispensedCount);
                    goodsDispenseDataItem.setPackageCount(BigDecimal.ZERO);
                    goodsDispenseDataItem.setTotalDispenseFormItemPieceCount(existedDispensedCount); //医院无多次发
                    goodsDispenseDataItem.setTotalDispenseFormItemPackageCount(BigDecimal.ZERO);

                } else {
                    goodsDispenseDataItem.setPackageCount(existedDispensedCount);
                    goodsDispenseDataItem.setPieceCount(BigDecimal.ZERO);
                    goodsDispenseDataItem.setTotalDispenseFormItemPieceCount(BigDecimal.ZERO); //医院无多次发
                    goodsDispenseDataItem.setTotalDispenseFormItemPackageCount(existedDispensedCount);
                }

                goodsDispenseDataItem.setTotalPrice(toDispenseItem.getTotalPrice());
                dispensingFormItemIdToDispenseDataItem.put(toDispenseItem.getId(), goodsDispenseDataItem);
                goodsDispenseDataItems.add(goodsDispenseDataItem);
            }
            if (!goodsDispenseDataItems.isEmpty()) {
                List<GoodsDispenseResultItem> list = abcCisScGoodsService.dispense(
                        dispensingSheetV2.getClinicId(),
                        dispensingSheetV2.getChainId(),
                        employeeId,
                        patientOrderHospitalVOMap != null && patientOrderHospitalVOMap.containsKey(dispensingSheetV2.getPatientOrderId())? patientOrderHospitalVOMap.get(dispensingSheetV2.getPatientOrderId()).getNo() : null,
                        dispensingSheetV2.getPatientOrderId(),
                        dispensingSheetV2.getId(),
                        dispensingSheetV2.getPharmacyNo(),
                        dispensingSheetV2.getPharmacyType(),
                        goodsDispenseDataItems,
                        DTOConverter.dispensingFormItemV2MapToDispensingFormItemAbstractMap(formItemIdToFormItem),
                        null,
                        10);
                if (CollectionUtils.isEmpty(list) || list.size() != goodsDispenseDataItems.size()) {
                    throw new GoodsDispenseException();
                }
                formItemIdToResultItem.putAll(list.stream().collect(toMap(GoodsDispenseResultItem::getDispenseItemId, Function.identity(), (a, b) -> a)));
            }
            String operationId = abcIdGenerator.getUID();
            List<DispensingFormItemV2> thisTimeDispensingFormItems = new ArrayList<>();
            List<String> finalDispensedByIds = Lists.newArrayList(employeeId);

            updateDispenseSheetOfAuto(dispensingSheetV2, operationId, employeeId, finalDispensedByIds,
                    clientFormItemList, thisTimeDispensingFormItems, dispensingFormItemIdToDispenseDataItem,
                    formItemIdToResultItem, formItemIdToFormItem);

            dispensingLogService.logDispensingSheetDispense(dispensingSheetV2, thisTimeDispensingFormItems,
                    formItemIdToResultItem, operationId, employeeId);
            SheetOperationBase operationBase =
                    sheetOperationCreateFactory.createSheetOperation(DispensingSheetOperation.OperationType.SEND_MEDICINE,
                            dispensingSheetV2.getId(), employeeId, employeeId, dispensingSheetV2.getChainId(), dispensingSheetV2.getClinicId());
            operationBase.bindDispensingSheetV2(dispensingSheetV2)
                    .bindDispensingFormItemsV2(thisTimeDispensingFormItems)
                    .bindDispensedByIds(finalDispensedByIds)
                    .bindRepoId(operationId);
            operationBase.createAndSaveSheetOperation();

            sendMessageSheetList.add(dispensingSheetV2);
        }

        if (!CollectionUtils.isEmpty(sendMessageSheetList)) {
            rocketMqProducer.sendDispensingSheetUpdateAfterCommitAsync(PatientOrderMessage.MSG_SUB_TYPE_DISPENGING_UPDATED_DISPENSE, sendMessageSheetList);
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public List<DispensingSheetV2> doReNewAutoDispensingSheet(String chainId, String clinicId, String employeeId,
                                                              List<DispensingSheetV2> dispensingSheetV2List,
                                                              GoodsConfigView goodsConfig,
                                                              Map<Integer, GoodsPharmacyView> pharmacyNoToPharmacyView,
                                                              Map<Long, AdviceMessage> adviceIdToAdviceMessage,
                                                              Map<Long, AdviceExecuteMessage> adviceExecuteIdToAdviceExecuteMessage,
                                                              Map<Long, AdviceUsageRuleItemsAddMessage> adviceUsageRuleIdToAddMessage
    ) {
        if (CollectionUtils.isEmpty(dispensingSheetV2List)) {
            return new ArrayList<>();
        }
        List<DispensingSheetV2> newDispensingSheetList = new ArrayList<>();
        List<DispensingSheetV2> updageOldSheetList = new ArrayList<>();
        for (DispensingSheetV2 dispensingSheetV2 : dispensingSheetV2List) {
            dispensingSheetV2.setDispensingTag(DispensingUtils.onFlag(dispensingSheetV2.getDispensingTag(), DispensingUtils.DispensingTag.DISPENSING_TAG_HOSPITAL_RENEW_OLD_SHEET_FLAG));
            FillUtils.fillLastModifiedBy(dispensingSheetV2, employeeId);
            updageOldSheetList.add(dispensingSheetV2);
            DispensingSheetV2 newSheet = new DispensingSheetV2();
            BeanUtils.copyProperties(dispensingSheetV2, newSheet);
            newSheet.setId(abcIdGenerator.getUUID());
            newSheet.setStatus(DispenseConst.Status.WAITING);
            newSheet.setDispensingTag(0);
            newSheet.setDispensingSheetInfo(null);
            newSheet.setOrderByDate(Instant.now());
            newSheet.setDispensingForms(new ArrayList<>());
            FillUtils.fillCreatedBy(newSheet, employeeId);
            newDispensingSheetList.add(newSheet);

            SheetOperationBase operationBase = sheetOperationCreateFactory.createSheetOperation(DispensingSheetOperation.OperationType.CREATED_DISPENSING_SHEET, newSheet.getId(), employeeId, employeeId, newSheet.getChainId(), newSheet.getClinicId());
            operationBase.createAndSaveSheetOperation();
            dispensingSheetV2.getDispensingForms().forEach(form -> {
                DispensingFormV2 formV2 = new DispensingFormV2();
                BeanUtils.copyProperties(form, formV2);
                formV2.setId(abcIdGenerator.getUUID());
                formV2.setDispensingSheetId(newSheet.getId());
                FillUtils.fillCreatedBy(formV2, employeeId);
                formV2.setDispensingFormItems(new ArrayList<>());
                form.getDispensingFormItems().forEach(item -> {
                    if (item.getStatus() != DispenseConst.Status.DISPENSED && item.getStatus() != DispenseConst.Status.APPLY_DISPENSE_REJECT) {
                        return;
                    }
                    DispensingFormItemV2 itemV2 = new DispensingFormItemV2();
                    BeanUtils.copyProperties(item, itemV2);
                    itemV2.setId(abcIdGenerator.getUUID());
                    itemV2.setDispensingFormId(formV2.getId());
                    itemV2.setDispensingSheetId(newSheet.getId());
                    itemV2.setStatus(DispenseConst.Status.WAITING);
                    if (itemV2.isSelfProvidedBySourceItemType()) {
                        // 自备药修改状态，不需发药
                        itemV2.setStatus(DispenseConst.Status.SELF_PROVIDED);
                    }
                    FillUtils.fillCreatedBy(itemV2, employeeId);
                    formV2.getDispensingFormItems().add(itemV2);
                });
                newSheet.getDispensingForms().add(formV2);
            });
        }
        doLoadGoodsAndUpdateSheet(chainId, clinicId, newDispensingSheetList, goodsConfig, pharmacyNoToPharmacyView);
        goodsLockingFeignClient.hospitalTryLockGoodsStock(chainId, clinicId, employeeId, newDispensingSheetList);

        dispensingSheetEntityService.saveAll(newDispensingSheetList);
        if (!CollectionUtils.isEmpty(updageOldSheetList)) {
            dispensingSheetEntityService.saveAll(updageOldSheetList);
        }
        // 通知
        doSendDispensingSheetToHisCharge(newDispensingSheetList, adviceIdToAdviceMessage, adviceExecuteIdToAdviceExecuteMessage,
                goodsConfig, employeeId, adviceUsageRuleIdToAddMessage);
        return newDispensingSheetList;
    }

    private void updateDispenseSheetOfAuto(
            DispensingSheetV2 dispensingSheetV2,
            String operationId,
            String employeeId,
            List<String> dispensedByIds,
            List<AutoDispenseReq.AutoDispenseFormItemReq> clientFormItemList,
            List<DispensingFormItemV2> thisTimeDispensingFormItems,
            Map<String, GoodsDispenseDataItem> dispensingFormItemIdToDispenseDataItem,
            Map<String, GoodsDispenseResultItem> formItemIdToResultItem,
            Map<String, DispensingFormItemV2> formItemIdToFormItem
    ) {
        DispensingSheetInfo dispensingSheetInfo = new DispensingSheetInfo();
        dispensingSheetInfo.setDispensedByIds(dispensedByIds);
        String dispensedBy = dispensedByIds.get(0);
        for (AutoDispenseReq.AutoDispenseFormItemReq formItemReq : clientFormItemList) {
            DispensingFormItemV2 formItemV2 = formItemIdToFormItem.get(formItemReq.getId());
            if (formItemV2 == null) {
                log.error("updateDispenseSheetOfAuto未找到发药项，id = {}", formItemReq.getId());
                continue;
            }
            GoodsDispenseDataItem goodsDispenseDataItem = dispensingFormItemIdToDispenseDataItem.get(formItemV2.getId());
            GoodsDispenseResultItem resultItem = formItemIdToResultItem.get(formItemV2.getId());
            if (resultItem != null) {
                formItemV2.setStatus(DispenseConst.Status.DISPENSED);
                // 打个标记，标记本次发药
                formItemV2.setThisTimeDispensed(1);
                // 存在上次失败，再次发药情况，goods会把前面成功的信息返回取第一个结果
                formItemV2.setStockDealId(DispensingUtils.getGoodsDispenseResultItemDealId(resultItem, true));
                formItemV2.setTotalCostPrice(MathUtils.wrapBigDecimal(resultItem.getTotalCostPrice(), BigDecimal.ZERO));
                formItemV2.setOperationId(operationId);
                Integer lockStatus = formItemV2.getExtendData() != null ? formItemV2.getExtendData().getLockStatus() : null;
                GoodsItem goodsItem = formItemV2.getGoodsItem();
                if (!CollectionUtils.isEmpty(formItemV2.getDispensingFormItemBatches()) || formItemV2.lockFailOrNeedLock()) {
                    // 如果有批次信息，或者之前锁库失败，更新批次信息
                    updateDispenseToDispensingFormItemBatch(formItemV2, goodsItem, goodsDispenseDataItem, resultItem, employeeId);
                }
                if (!CollectionUtils.isEmpty(formItemV2.getDispensingFormItemBatches())) {
                    // 用批次的价格覆盖发药项的价格
                    formItemV2.setTotalCostPrice(formItemV2.getDispensingFormItemBatches().stream().map(DispensingFormItemBatchInfo::getTotalCostPrice).reduce(BigDecimal.ZERO, MathUtils::wrapBigDecimalAdd));
                    formItemV2.setTotalPrice(formItemV2.getDispensingFormItemBatches().stream().map(DispensingFormItemBatchInfo::getTotalPrice).reduce(BigDecimal.ZERO, MathUtils::wrapBigDecimalAdd));
                }
                FillUtils.fillLastModifiedBy(formItemV2, employeeId);
                thisTimeDispensingFormItems.add(formItemV2);
            }
        }
        dispensingSheetV2.fixHisSheetStatus();
        if (dispensingSheetV2.getStatus() == DispenseConst.Status.DISPENSED) {
            dispensingSheetV2.setOrderByDate(Instant.now());
            dispensingSheetV2.setDispensedTime(Instant.now());
        }
        if (TextUtils.isEmpty(dispensedBy)) {
            dispensedBy = employeeId;
        }
        dispensingSheetV2.setDispensedBy(dispensedBy);
        dispensingSheetV2.setDispensingSheetInfo(dispensingSheetInfo);
        FillUtils.fillLastModifiedBy(dispensingSheetV2, employeeId);
        dispensingSheetEntityService.save(dispensingSheetV2);
    }

    public void undispenseAutoDispensingSheet(AutoDispenseReq clientReq) {
        String chainId = clientReq.getHeaderChainId();
        String clinicId = clientReq.getHeaderClinicId();
        List<String> dispensingSheetIdList = clientReq.getDispenseSheets().stream()
                .filter(it -> !StringUtils.isEmpty(it.getId())).map(AutoDispenseReq.AutoDispenseSheetReq::getId).distinct().collect(toList());
        Map<String, DispensingSheetV2> sheetIdToSheet = loadAssembleDispenseSheet(chainId, clinicId, null, dispensingSheetIdList, null);

        Map<String, PatientOrderHospitalVO> patientOrderIdToHospitalVo = checkPatientOutHospitalOfAutoDispense(clientReq, sheetIdToSheet);

        doUnDispenseAutoDispenseSheet(clientReq,patientOrderIdToHospitalVo, sheetIdToSheet);
    }

    private void doUnDispenseAutoDispenseSheet(
            AutoDispenseReq clientReq,
            Map<String,PatientOrderHospitalVO> patientOrderIdToPatientOrder,
            Map<String, DispensingSheetV2> sheetIdToSheet
    ) {
        for (AutoDispenseReq.AutoDispenseSheetReq sheetReq : clientReq.getDispenseSheets()) {
            sheetReq.checkParam();
            DispensingSheetV2 dispensingSheetV2 = sheetIdToSheet.get(sheetReq.getId());
            if (dispensingSheetV2 == null) {
                throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "未找到发药单");
            }
            if (!dispensingSheetV2.isAutoDispensingMethod()) {
                throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "非自动发药不支持退药");
            }
            if (!dispensingSheetV2.canApplyUnDispense()) {
                throw new DispensingServiceException(DispensingServiceError.DISPENE_SHEET_CANNOT_APPLY_UNDISPENSE, "已退药不可再申请退药");
            }
            List<DispensingFormItemV2> canDispensedItemList = dispensingSheetV2.canUnDispenseItems();
            if (canDispensedItemList.isEmpty()) {
                throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "无药品可退");
            }
        }
        String employeeId = clientReq.getHeaderEmployeeId();
        List<DispensingSheetV2> sendMsgSheetList = new ArrayList<>();
        for (AutoDispenseReq.AutoDispenseSheetReq sheetReq : clientReq.getDispenseSheets()) {
            //sheetReq.checkParam();
            DispensingSheetV2 dispensingSheetV2 = sheetIdToSheet.get(sheetReq.getId());
            List<DispensingFormItemV2> svrAllFormItemList = dispensingSheetV2.allDispenseItems();
            Map<String, DispensingFormItemV2> formItemIdToFormItem = svrAllFormItemList.stream()
                    .collect(toMap(DispensingFormItemV2::getId, Function.identity(), (a, b) -> a));
            List<AutoDispenseReq.AutoDispenseFormItemReq> clientFormItemList = sheetReq.getAllDispenseFormItems();
            //发药FormItemId -> 已退UnitCount
            Map<String, BigDecimal> dispenseFormItemIdToUnDispensedUnitCount = new HashMap<>();
            //发药FormItemId -> 已退DoseCount
            Map<String, BigDecimal> dispenseFormItemIdToUnDispensedDoseCount = new HashMap<>();
            // 发药 formItemId -> (批次ID -> 批次可退数量)
            Map<String, Map<Long, BigDecimal>> dispenseFormItemIdToBatchIdToUndispensableUnitCount = new HashMap<>();
            //计算已经退的数量 包括 已退和可退的
            undispensingService.calUnDispensedCount(svrAllFormItemList, dispenseFormItemIdToUnDispensedUnitCount,
                    dispenseFormItemIdToUnDispensedDoseCount, dispenseFormItemIdToBatchIdToUndispensableUnitCount);

            List<DispensingFormItemV2> unDispensedFormItemList = new ArrayList<>();
            //发送退药请求到scGoods 处理库存
            Map<String, GoodsDispenseResultItem> dispenseFormItemIdToResultItem =
                    sendGoodsUnDispenseDataItemsToScGoods(dispensingSheetV2,
                            patientOrderIdToPatientOrder !=null&&patientOrderIdToPatientOrder.get(dispensingSheetV2.getPatientOrderId())!=null?patientOrderIdToPatientOrder.get(dispensingSheetV2.getPatientOrderId()):null,
                            clientFormItemList,
                            formItemIdToFormItem,
                            unDispensedFormItemList,
                            employeeId,
                            dispenseFormItemIdToUnDispensedUnitCount,
                            dispenseFormItemIdToUnDispensedDoseCount,
                            dispenseFormItemIdToBatchIdToUndispensableUnitCount);
            String operationId = abcIdGenerator.getUID();
            List<DispensingFormItemV2> logUndispensingFormItems = new ArrayList<>();
            updateUnDispenseToSheet(
                    dispensingSheetV2,
                    operationId,
                    employeeId,
                    clientFormItemList,
                    formItemIdToFormItem,
                    dispenseFormItemIdToResultItem,
                    unDispensedFormItemList, logUndispensingFormItems, sendMsgSheetList);
            saveAssembleDispenseSheet(dispensingSheetV2);
            dispensingLogService.logDispensingSheetUndispense(dispensingSheetV2, logUndispensingFormItems, operationId, employeeId);

            String dispensingSheetId = dispensingSheetV2.getId();
            SheetOperationBase operationBase =
                    sheetOperationCreateFactory.createSheetOperation(DispensingSheetOperation.OperationType.WITHDRAWAL_MEDICINE,
                            dispensingSheetId, employeeId, employeeId, dispensingSheetV2.getChainId(), dispensingSheetV2.getClinicId());

            List<DispensingSheetOperation> dispenseOperations = dispensingSheetOperationRepository.findAllByDispensingSheetIdAndIsDeletedOrderByCreatedDescOperationTypeDesc(dispensingSheetId, 0);
            ArrayList<GoodsDispenseResultItem> goodsDispenseResultItems = dispenseFormItemIdToResultItem != null ? new ArrayList<>(dispenseFormItemIdToResultItem.values()) : new ArrayList<>();
            List<DispensingLogItem> dispenseItemLogs = dispensingLogRepository.findAllByDispensingSheetIdAndType(dispensingSheetId, DispensingLogItem.Type.DISPENSE);
            operationBase.bindDispensingSheetV2(dispensingSheetV2)
                    .bindDispensingFormItemsV2(logUndispensingFormItems)
                    .bindDispenseOperations(dispenseOperations)
                    .bindGoodsDispenseResultItems(goodsDispenseResultItems)
                    .bindDispenseItemLogs(dispenseItemLogs)
                    .bindRepoId(operationId);
            operationBase.createAndSaveSheetOperation();
        }
        if (!sendMsgSheetList.isEmpty()) {
            rocketMqProducer.sendDispensingSheetUpdateAfterCommitAsync(PatientOrderMessage.MSG_SUB_TYPE_DISPENGING_UPDATED_UNDISPENSE, sendMsgSheetList);
        }
    }

    private Map<String, GoodsDispenseResultItem> sendGoodsUnDispenseDataItemsToScGoods(
            DispensingSheetV2 unDispenseSheet,
            PatientOrderHospitalVO patientOrderHospitalVO,
            List<AutoDispenseReq.AutoDispenseFormItemReq> clientFormItemList,
            Map<String, DispensingFormItemV2> formItemIdToFormItem,
            List<DispensingFormItemV2> unDispensedFormItemList,
            String operatorId,
            Map<String, BigDecimal> dispenseFormItemIdToUnDispensedUnitCount,
            Map<String, BigDecimal> dispenseFormItemIdToUnDispensedDoseCount,
            Map<String, Map<Long, BigDecimal>> dispenseFormItemIdToBatchIdToUndispensableUnitCount) throws DispenseSheetChangedException {
        //入参检查
        if (CollectionUtils.isEmpty(unDispenseSheet.getDispensingForms())) {
            return new HashMap<>();
        }
        // 预先已生成的退药项
        DispensingFormItemV2 unDispenseFormItem = unDispenseSheet.getDispensingForms().stream().flatMap(it -> it.getDispensingFormItems().stream())
                .filter(it -> it.getStatus() == DispenseConst.Status.APPLY_UNDISPNSE).findFirst().orElse(null);
        Map<String, DispensingFormV2> formIdToForm = unDispenseSheet.getDispensingForms().stream().collect(toMap(DispensingFormV2::getId,
                Function.identity(), (a, b) -> a));

        List<GoodsDispenseDataItem> goodsDispenseDataItems = new ArrayList<>();
        for (AutoDispenseReq.AutoDispenseFormItemReq clientUnFromReqItem : clientFormItemList) {
            //要处理退药的发药ITEM
            DispensingFormItemV2 dispenseItem = formItemIdToFormItem.get(clientUnFromReqItem.getId());
            if (dispenseItem == null) {
                continue;
            }
            DispensingFormV2 dispensingFormV2 = formIdToForm.get(dispenseItem.getDispensingFormId());
            if (dispensingFormV2 == null) {
                continue;
            }
            if (dispenseItem.getSourceItemType() == DispensingFormItem.SourceItemType.SELF_PROVIDED) {
                continue;
            }
            // 申请退的
            BigDecimal toUndispenseTotalCount;
            if (unDispenseFormItem != null) {
                toUndispenseTotalCount = MathUtils.calculateTotalCount(unDispenseFormItem.getUnitCount(), unDispenseFormItem.getDoseCount());
            } else {
                unDispenseFormItem = new DispensingFormItemV2();
                BeanUtils.copyProperties(dispenseItem, unDispenseFormItem);
                FillUtils.fillCreatedBy(unDispenseFormItem, operatorId);
                unDispenseFormItem.setId(abcIdGenerator.getUUID());
                unDispenseFormItem.setAssociateFormItemId(dispenseItem.getId());
                unDispenseFormItem.setUnitCount(clientUnFromReqItem.getUnitCount());
                unDispenseFormItem.setDoseCount(clientUnFromReqItem.getDoseCount());
                unDispenseFormItem.setStatus(DispenseConst.Status.APPLY_UNDISPNSE);
                unDispenseFormItem.setStockDealId(unDispenseFormItem.getId());
                if (DispensingUtils.isChineseMedicine(dispenseItem.getProductType(), dispenseItem.getProductSubType())) {
                    unDispenseFormItem.setUndispenseType(DispensingFormItem.UndispenseType.BY_DOSE);
                } else {
                    unDispenseFormItem.setUndispenseType(DispensingFormItem.UndispenseType.BY_UNIT);
                }
                DispensingOrderService proxyOrderService = SpringUtils.getBean(DispensingOrderService.class);
                proxyOrderService.saveUnDispenseFormItem(unDispenseFormItem);
                toUndispenseTotalCount = MathUtils.calculateTotalCount(clientUnFromReqItem.getUnitCount(), clientUnFromReqItem.getDoseCount());
                dispensingFormV2.getDispensingFormItems().add(unDispenseFormItem);
            }
            unDispensedFormItemList.add(unDispenseFormItem);

            GoodsDispenseDataItem goodsDispenseDataItem = new GoodsDispenseDataItem();
            goodsDispenseDataItem.setPharmacyNo(dispenseItem.getPharmacyNo());
            goodsDispenseDataItem.setDispenseItemId(dispenseItem.getId());
            goodsDispenseDataItem.setUnDispenseItemId(unDispenseFormItem.getId());
            goodsDispenseDataItem.setDealId(unDispenseFormItem.getId());
            goodsDispenseDataItem.setGoodsId(dispenseItem.getProductId());
            if (dispenseItem.getUseDismounting() == 1) {
                goodsDispenseDataItem.setPieceCount(toUndispenseTotalCount);
                goodsDispenseDataItem.setPackageCount(BigDecimal.ZERO);
                goodsDispenseDataItem.setTotalDispenseFormItemPieceCount(toUndispenseTotalCount);
                goodsDispenseDataItem.setTotalDispenseFormItemPackageCount(BigDecimal.ZERO);
            } else {
                goodsDispenseDataItem.setPackageCount(toUndispenseTotalCount);
                goodsDispenseDataItem.setPieceCount(BigDecimal.ZERO);
                goodsDispenseDataItem.setTotalDispenseFormItemPackageCount(toUndispenseTotalCount);
                goodsDispenseDataItem.setTotalDispenseFormItemPieceCount(BigDecimal.ZERO);
            }
            //TODO 成本
            BigDecimal totalPrice = null;
            BigDecimal unitPrice = dispenseItem.getUnitPrice();
            if (unitPrice != null) {
                totalPrice = unitPrice.multiply(toUndispenseTotalCount).setScale(4, RoundingMode.HALF_UP);
            }
            goodsDispenseDataItem.setTotalPrice(totalPrice);
            goodsDispenseDataItems.add(goodsDispenseDataItem);
        }
        List<GoodsDispenseResultItem> goodsDispenseResultItems = new ArrayList<>();
        if (!goodsDispenseDataItems.isEmpty()) {
            goodsDispenseResultItems = abcCisScGoodsService.undispense(
                    unDispenseSheet.getClinicId(),
                    unDispenseSheet.getChainId(),
                    operatorId,
                    patientOrderHospitalVO!=null?patientOrderHospitalVO.getNo():null,
                    unDispenseSheet.getPatientOrderId(),
                    unDispenseSheet.getId(),
                    unDispenseSheet.getPharmacyNo(),
                    unDispenseSheet.getPharmacyType(),
                    goodsDispenseDataItems,
                    DTOConverter.dispensingFormItemV2MapToDispensingFormItemAbstractMap(formItemIdToFormItem),
                    10);
            if (goodsDispenseResultItems == null || goodsDispenseResultItems.isEmpty() || goodsDispenseResultItems.size() != goodsDispenseDataItems.size()) {
                throw new GoodsUndispenseException();
            }
        }
        return goodsDispenseResultItems.stream()
                .collect(Collectors.toMap(GoodsDispenseResultItem::getDispenseItemId, Function.identity(), (item1, item2) -> item1));
    }

    public void updateUnDispenseToSheet(
            DispensingSheetV2 dispensingSheetV2,
            String operationId,
            String employeeId,
            List<AutoDispenseReq.AutoDispenseFormItemReq> clientFormItemList,
            Map<String, DispensingFormItemV2> formItemIdToFormItem,
            Map<String, GoodsDispenseResultItem> dispenseFormItemIdToResultItem,
            List<DispensingFormItemV2> unDispensedFormItemList,
            List<DispensingFormItemV2> logUndispensingFormItems,
            List<DispensingSheetV2> sendMsgSheetList
    ) {
        if (CollectionUtils.isEmpty(unDispensedFormItemList)) {
            return;
        }
        boolean needUpdate = false;
        Map<String, DispensingFormV2> formIdToForm = dispensingSheetV2.getDispensingForms().stream().collect(toMap(DispensingFormV2::getId,
                Function.identity(), (a, b) -> a));
        for (AutoDispenseReq.AutoDispenseFormItemReq formItemReq : clientFormItemList) {
            DispensingFormItemV2 dispenseFormItem = formItemIdToFormItem.get(formItemReq.getId());
            if (dispenseFormItem == null) {
                continue;
            }
            DispensingFormV2 dispensingFormV2 = formIdToForm.get(dispenseFormItem.getDispensingFormId());
            if (dispensingFormV2 == null) {
                continue;
            }
            DispensingFormItemV2 unDispensedFormItemV2 = unDispensedFormItemList.get(0);
            GoodsDispenseResultItem resultItem = dispenseFormItemIdToResultItem.get(unDispensedFormItemV2.getAssociateFormItemId());
            if (resultItem == null) {
                continue;
            }
            unDispensedFormItemV2.setOperationId(operationId);
            unDispensedFormItemV2.setStatus(DispenseConst.Status.UNDISPENSED);
            unDispensedFormItemV2.setThisTimeDispensed(1);
            unDispensedFormItemV2.setStockDealId(DispensingUtils.getGoodsDispenseResultItemDealId(resultItem, false));
            unDispensedFormItemV2.setTotalCostPrice(MathUtils.wrapBigDecimal(resultItem.getTotalCostPrice(), BigDecimal.ZERO));
            undispensingService.updateUndispenseToDispensingFormItemBatch(unDispensedFormItemV2, dispenseFormItem, resultItem, employeeId);
            //dispensingFormV2.getDispensingFormItems().add(unDispensedFormItemV2);
            logUndispensingFormItems.add(unDispensedFormItemV2);
            needUpdate = true;
        }
        if (needUpdate) {
            dispensingSheetV2.fixHisSheetStatus();
            if (dispensingSheetV2.getStatus() == DispenseConst.Status.UNDISPENSED) {
                dispensingSheetV2.setOrderByDate(Instant.now());
            }
            sendMsgSheetList.add(dispensingSheetV2);
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveUnDispenseFormItem(DispensingFormItemV2 dispensingFormItemV2) {
        dispensingFormItemV2Repository.save(dispensingFormItemV2);
    }

    public AbcListPage<DispensingSheetView> getDispensingSheetViewBySourceType(GetDispenseSheetBySourceTypeReq clientReq) {
        AbcListPage<DispensingSheetView> result = new AbcListPage<>();
        result.setRows(new ArrayList<>());
        List<Integer> sourceSheetTypeList = clientReq.getSourceSheetTypeList();
        List<String> sourceSheetIdList = clientReq.getSourceSheetIdList();
        if (CollectionUtils.isEmpty(sourceSheetIdList)) {
            return result;
        }
        String chainId = clientReq.getChainId();
        String clinicId = clientReq.getClinicId();
        List<DispensingSheetV2> sheetList =
                dispensingSheetV2Repository.findAllByChainIdAndClinicIdAndSourceSheetIdInAndIsDeleted(chainId, clinicId, sourceSheetIdList, YesOrNo.NO);
        // 过滤掉重新发药的
        sheetList = sheetList.stream().filter(it -> !DispensingUtils.checkFlagOn(it.getDispensingTag(),
                DispensingUtils.DispensingTag.DISPENSING_TAG_HOSPITAL_RENEW_OLD_SHEET_FLAG)).collect(toList());
        if (CollectionUtils.isEmpty(sheetList)) {
            return result;
        }
        if (!CollectionUtils.isEmpty(sourceSheetTypeList)) {
            sheetList = sheetList.stream().filter(it -> sourceSheetTypeList.contains(it.getSourceSheetType())).collect(toList());
        }
        if (sheetList.isEmpty()) {
            return result;
        }
        List<String> allSheetIdList = sheetList.stream().map(DispensingSheetV2::getId).distinct().collect(toList());
        Map<String, DispensingSheetV2> sheetIdToSheet = loadAssembleDispenseSheet(chainId, clinicId, null, allSheetIdList, null);
        // 是否存在手动未发药
        List<DispensingSheetV2> manualSheetList = sheetList.stream().filter(it -> it.getStatus() == DispenseConst.Status.WAITING && it.getDispensingMethod() == DispenseConst.DispensingMethod.MANUAL).collect(toList());
        Map<String, Integer> sheetIdToOrderStatus = new HashMap<>();
        if (!manualSheetList.isEmpty()) {
            // 查询领药单状态，是否申请过发药
            List<String> waitingSheetIdList = manualSheetList.stream().map(DispensingSheetV2::getId).distinct().collect(toList());
            Map<Long, List<DispensingOrderSheetRel>> orderIdToSheetRelList = dispensingOrderSheetRelRepository.findAllByChainIdAndClinicIdAndDispenseSheetIdInAndIsDeleted(chainId,
                    clinicId, waitingSheetIdList, YesOrNo.NO).stream().collect(groupingBy(DispensingOrderSheetRel::getOrderId));
            List<Long> orderIdList = new ArrayList<>(orderIdToSheetRelList.keySet());
            if (!orderIdList.isEmpty()) {
                List<DispensingOrder> orderList = dispensingOrderRepository.findByChainIdAndClinicIdAndIdInAndIsDeleted(chainId, clinicId, orderIdList, YesOrNo.NO);
                orderList.sort(Comparator.comparingInt(DispensingOrder::getApplyType));
                orderList.forEach(order -> {
                    List<DispensingOrderSheetRel> sheetRelList = orderIdToSheetRelList.get(order.getId());
                    if (CollectionUtils.isEmpty(sheetRelList)) {
                        return;
                    }
                    sheetRelList.forEach(rel -> {
                        sheetIdToOrderStatus.put(rel.getDispenseSheetId(), order.getStatus());
                    });
                });
            }
        }

        for (String sheetId : sheetIdToSheet.keySet()) {
            DispensingSheetV2 dispensingSheetV2 = sheetIdToSheet.get(sheetId);
            DispensingSheetView sheetView = DispensingSheetView.from(dispensingSheetV2);
            if (dispensingSheetV2.getDispensingMethod() == DispenseConst.DispensingMethod.MANUAL
                    && dispensingSheetV2.getStatus() == DispenseConst.Status.WAITING) {
                Integer orderStatus = sheetIdToOrderStatus.get(sheetId);
                sheetView.setDispensingOrderStatus(orderStatus);
            }
            mergeFormItemViewStatus(sheetView, clientReq.getNeedMergeFormItemStatus());
            result.getRows().add(sheetView);
        }
        return result;
    }

    public void undispenseForHisCharge(UnDispenseForHisChargeReq clientReq) {
        List<UnDispenseForHisChargeReq.UnDispenseItem> items = clientReq.getItems();
        if (CollectionUtils.isEmpty(items)) {
            throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "退药项不能为空");
        }
        String chainId = clientReq.getChainId();
        String clinicId = clientReq.getClinicId();
        String employeeId = clientReq.getEmployeeId();
        List<String> formItemIdList = items.stream().map(UnDispenseForHisChargeReq.UnDispenseItem::getId).filter(Objects::nonNull).distinct().collect(toList());
        List<DispensingFormItemV2> formItemList = dispensingFormItemV2Repository.findAllByChainIdAndClinicIdAndIdInAndIsDeleted(chainId, clinicId, formItemIdList, YesOrNo.NO);
        if (CollectionUtils.isEmpty(formItemList)) {
            throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "未找到发药项");
        }
        List<String> sheetIdList = formItemList.stream().map(DispensingFormItemV2::getDispensingSheetId).distinct().collect(toList());
        Map<String, DispensingSheetV2> sheetIdToSheet = loadAssembleDispenseSheet(chainId, clinicId, null, sheetIdList, null);
        List<String> patientOrderIdList = sheetIdToSheet.values().stream().filter(it -> it.getPatientOrderId() != null).map(DispensingSheetV2::getPatientOrderId).distinct().collect(toList());
        CompletableFuture<Map<String, PatientOrderHospitalVO>> patientOrderHospitalVoFuture = getPatientOrderHospitalVoFuture(chainId, clinicId, patientOrderIdList);
        Map<String, PatientOrderHospitalVO> patientOrderIdToHospitalVo = patientOrderHospitalVoFuture.join();

        Map<String, DispensingFormItemV2> formItemIdToFormItem = sheetIdToSheet.values().stream().flatMap(it -> it.allDispenseItems().stream())
                .collect(toMap(DispensingFormItemV2::getId, Function.identity(), (a, b) -> a));
        // 组装请求
        AutoDispenseReq autoDispenseReq = new AutoDispenseReq();
        autoDispenseReq.setDispenseSheets(new ArrayList<>());
        autoDispenseReq.setDispenseType(AutoDispenseReq.DISPENSE_TYPE_UNDISPENSE);
        autoDispenseReq.setHeaderChainId(chainId);
        autoDispenseReq.setHeaderClinicId(clinicId);
        autoDispenseReq.setHeaderEmployeeId(employeeId);
        for (UnDispenseForHisChargeReq.UnDispenseItem unDispenseItemReq : items) {
            DispensingFormItemV2 formItemV2 = formItemIdToFormItem.get(unDispenseItemReq.getId());
            if (formItemV2 == null) {
                throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "发药项不存在");
            }
            DispensingSheetV2 dispensingSheetV2 = sheetIdToSheet.get(formItemV2.getDispensingSheetId());
            if (dispensingSheetV2 == null) {
                throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "发药单不存在");
            }
            if (!dispensingSheetV2.noAdviceIdSourceSheetType()) {
                throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "发药单不支持退药");
            }
            AutoDispenseReq.AutoDispenseSheetReq autoDispenseSheetReq = new AutoDispenseReq.AutoDispenseSheetReq();
            autoDispenseSheetReq.setId(dispensingSheetV2.getId());
            autoDispenseSheetReq.setDispensingForms(new ArrayList<>());
            dispensingSheetV2.getDispensingForms().forEach(form -> {
                AutoDispenseReq.AutoDispenseFormReq autoDispenseFormReq = new AutoDispenseReq.AutoDispenseFormReq();
                autoDispenseFormReq.setId(form.getId());
                autoDispenseFormReq.setDispenseFormItems(new ArrayList<>());
                form.getDispensingFormItems().forEach(item -> {
                    if (item.getStatus() == DispenseConst.Status.APPLY_UNDISPNSE) {
                        return;
                    }
                    AutoDispenseReq.AutoDispenseFormItemReq autoDispenseFormItemReq = new AutoDispenseReq.AutoDispenseFormItemReq();
                    autoDispenseFormItemReq.setId(item.getId());
                    autoDispenseFormItemReq.setUnitCount(item.getUnitCount());
                    autoDispenseFormItemReq.setDoseCount(item.getDoseCount());
                    autoDispenseFormReq.getDispenseFormItems().add(autoDispenseFormItemReq);
                });
                autoDispenseSheetReq.getDispensingForms().add(autoDispenseFormReq);
            });
            autoDispenseReq.getDispenseSheets().add(autoDispenseSheetReq);
        }

        doUnDispenseAutoDispenseSheet(autoDispenseReq,patientOrderIdToHospitalVo, sheetIdToSheet);
    }

    @Transactional(rollbackFor = Exception.class)
    @RedisLock(key = "'dispensing.advice.execute-undo:' + #adviceExecuteMessage.clinicId + ':' + #adviceExecuteMessage.adviceId", waitTime = 60)
    public void handleAdviceExecuteUndoMessage(AdviceExecuteMessage adviceExecuteMessage) {
        if (adviceExecuteMessage == null || CollectionUtils.isEmpty(adviceExecuteMessage.getExecuteItems())) {
            return;
        }
        if (adviceExecuteMessage.getStatus() != AdviceStatus.UNDONE) {
            sLogger.info("非撤销消息，不处理，adviceExecuteId={}", adviceExecuteMessage.getId());
        }
        String chainId = adviceExecuteMessage.getChainId();
        String clinicId = adviceExecuteMessage.getClinicId();
        Long executeId = adviceExecuteMessage.getId();
        Long adviceId = adviceExecuteMessage.getAdviceId();
        String operatorId = adviceExecuteMessage.getOperatorId();
        List<DispensingSheetV2> autoDipensingSheetList = dispensingSheetV2Repository.findAllByChainIdAndClinicIdAndAdviceIdAndDispensingMethodAndIsDeleted(chainId,
                clinicId, adviceId, DispenseConst.DispensingMethod.AUTO, YesOrNo.NO);
        // 医嘱任务的才退药
        autoDipensingSheetList = autoDipensingSheetList.stream().filter(it -> DispensingUtils.isAdviceExecuteSourceSheetType(it.getSourceSheetType())).collect(toList());
        if (CollectionUtils.isEmpty(autoDipensingSheetList)) {
            sLogger.info("未找到自动发药单，不处理，adviceExecuteId={}", executeId);
            return;
        }
        List<String> sheetIdList = autoDipensingSheetList.stream().map(DispensingSheetV2::getId).distinct().collect(toList());
        List<DispensingFormV2> formList = dispensingFormV2Repository.findAllByDispensingSheetIdInAndClinicIdAndIsDeleted(sheetIdList, clinicId, YesOrNo.NO);
        List<String> needUnDispenseSheetIdList = formList.stream().filter(it -> it.getAdviceExecuteId() != null && it.getAdviceExecuteId().equals(executeId))
                .map(DispensingFormV2::getDispensingSheetId).distinct().collect(toList());
        List<DispensingSheetV2> needUnDispenseSheetList = autoDipensingSheetList.stream().filter(it -> needUnDispenseSheetIdList.contains(it.getId())).collect(toList());
        if (CollectionUtils.isEmpty(needUnDispenseSheetList)) {
            sLogger.info("未找到需要自动退药的发药单，不处理，adviceExecuteId={}", executeId);
            return;
        }
        doDeleteAutoDispenseSheet(chainId, clinicId, operatorId, needUnDispenseSheetList);
    }

    public AutoDispensingCount getAutoDispensingFailCount(GetAutoDispensingListReq req) {
        AutoDispensingCount result = new AutoDispensingCount();
        req.setIsWaiting(YesOrNo.YES);
        int failCount = dispensingMapper.countAutoDispensingSheetFail(req);
        result.setAutoDispensingFailCount(failCount);
        return result;
    }

    public AbcListPage<AutoDispensingFailDailyCount> getAutoDispensingFailDailyCount(GetAutoDispensingListReq req) {
        AbcListPage<AutoDispensingFailDailyCount> result = new AbcListPage<>();
        result.setRows(dispensingMapper.getAutoDispensingFailDailyCount(req));
        return result;
    }

    @Async
    //@Transactional(rollbackFor = Exception.class)
    public void dispenseFoAdviceExecute(DispenseForAdviceExecuteReq clientReq) {
        if (CollectionUtils.isEmpty(clientReq.getAdvices())) {
            log.info("没有待发的医嘱,clinicId={}", clientReq.getClinicId());
            return;
        }
        List<Long> adviceExecuteIds = new ArrayList<>();
        List<Long> adviceIds = new ArrayList<>();
        clientReq.getAdvices().forEach(advice -> {
            adviceIds.add(advice.getAdviceId());
            if (!CollectionUtils.isEmpty(advice.getAdviceExecuteIds())) {
                adviceExecuteIds.addAll(advice.getAdviceExecuteIds());
            }
        });
        List<String> autoSheetIdList = dispensingMapper.getAutoDispensingSheetIdForAdvice(clientReq.getChainId(),
                clientReq.getClinicId(), adviceIds);
        if (!CollectionUtils.isEmpty(adviceExecuteIds)) {
            autoSheetIdList.addAll(dispensingMapper.getAutoDispensingSheetIdForAdviceExecute(clientReq.getChainId(),
                    clientReq.getClinicId(), adviceIds, adviceExecuteIds));
        }
        autoSheetIdList = autoSheetIdList.stream().distinct().collect(toList());
        if (CollectionUtils.isEmpty(autoSheetIdList)) {
            log.info("未找到医嘱任务自动发药单，不处理，clinicId={}", clientReq.getClinicId());
            return;
        }
        Map<String, DispensingSheetV2> sheetIdToSheet = loadAssembleDispenseSheet(clientReq.getChainId(), clientReq.getClinicId(), null, autoSheetIdList, null);
        AutoDispenseReq autoDispenseReq = new AutoDispenseReq();
        autoDispenseReq.setHeaderChainId(clientReq.getChainId());
        autoDispenseReq.setHeaderClinicId(clientReq.getClinicId());
        autoDispenseReq.setHeaderEmployeeId(clientReq.getOperatorId());
        autoDispenseReq.setDispenseSheets(new ArrayList<>());
        sheetIdToSheet.forEach((sheetId, sheet) -> {
            AutoDispenseReq.AutoDispenseSheetReq sheetReq = new AutoDispenseReq.AutoDispenseSheetReq();
            sheetReq.setId(sheetId);
            sheetReq.setDispensingForms(new ArrayList<>());
            sheet.getDispensingForms().forEach(form -> {
                AutoDispenseReq.AutoDispenseFormReq formReq = new AutoDispenseReq.AutoDispenseFormReq();
                formReq.setId(form.getId());
                formReq.setDispenseFormItems(new ArrayList<>());
                form.getDispensingFormItems().forEach(formItem -> {
                    if (formItem.getStatus() != DispenseConst.Status.WAITING) {
                        return;
                    }
                    AutoDispenseReq.AutoDispenseFormItemReq formItemReq = new AutoDispenseReq.AutoDispenseFormItemReq();
                    formItemReq.setId(formItem.getId());
                    formItemReq.setUnitCount(formItem.getUnitCount());
                    formItemReq.setDoseCount(formItem.getDoseCount());
                    formReq.getDispenseFormItems().add(formItemReq);
                });
                if (!formReq.getDispenseFormItems().isEmpty()) {
                    sheetReq.getDispensingForms().add(formReq);
                }
            });
            if (!sheetReq.getDispensingForms().isEmpty()) {
                autoDispenseReq.getDispenseSheets().add(sheetReq);
            }
        });

        if (autoDispenseReq.getDispenseSheets().isEmpty()) {
            return;
        }
        List<String> patientOrderIdList = sheetIdToSheet.values().stream().filter(it -> it.getPatientOrderId() != null).map(DispensingSheetV2::getPatientOrderId).distinct().collect(toList());
        CompletableFuture<Map<String, PatientOrderHospitalVO>> patientOrderHospitalVoFuture = getPatientOrderHospitalVoFuture(clientReq.getChainId(), clientReq.getClinicId(), patientOrderIdList);
        Map<String, PatientOrderHospitalVO> patientOrderIdToHospitalVo = patientOrderHospitalVoFuture.join();

        DispensingOrderService orderServiceProxy = SpringUtils.getBean(DispensingOrderService.class);
        orderServiceProxy.doDispenseAutoDispensingSheet(clientReq.getChainId(), clientReq.getClinicId(),
                clientReq.getOperatorId(), sheetIdToSheet,patientOrderIdToHospitalVo, autoDispenseReq);
    }

    private List<RpcSearchDispenseRsp.SearchDispenseItem> searchDispensingHospitalNurse(
            String keyword, int offset, int limit,
            String chainId, String clinicId, int dispensingStatus,
            List<String> patientOrderIdList) throws ServiceInternalException {
        List<RpcSearchDispenseRsp.SearchDispenseItem> retList = new ArrayList<>();
        try {
            RpcSearchDispenseReq rpcSearchDispenseReq = new RpcSearchDispenseReq();
            rpcSearchDispenseReq.setChainId(chainId);
            rpcSearchDispenseReq.setClinicId(clinicId);
            rpcSearchDispenseReq.setKeyword(keyword);
            rpcSearchDispenseReq.setPharmacyType(0);
            rpcSearchDispenseReq.setDispenseSheetType(DispenseConst.Type.TYPE_HOSPITAL);
            rpcSearchDispenseReq.setOffset(offset);
            rpcSearchDispenseReq.setScoreCreatedGauss(1);
            rpcSearchDispenseReq.setLimit(limit);
            rpcSearchDispenseReq.setDispensingStatus(dispensingStatus);
            rpcSearchDispenseReq.setPatientOrderIdList(patientOrderIdList);
            long startRequestTime = System.currentTimeMillis();
            AbcServiceResponseBody<SearchResultRsp> rspBody = searchFeignClient.doCommonSearch(rpcSearchDispenseReq);
            if (rspBody != null && rspBody.getData() != null) {
                if (CollectionUtils.isEmpty(rspBody.getData().getHits())) {
                    return retList;
                }
                for (JsonNode hit : rspBody.getData().getHits()) {
                    RpcSearchDispenseRsp.SearchDispenseItem rspItem = JsonUtils.readValue(hit, RpcSearchDispenseRsp.SearchDispenseItem.class);
                    retList.add(rspItem);
                }
                return retList;
            }
            sLogger.info("rpc cost time:{}ms,rsp={}", (System.currentTimeMillis() - startRequestTime), JsonUtils.dump(rspBody));
        } catch (FeignRuntimeException e) {
            sLogger.error("searchDispensingHospitalNurse feign error", e);
            throw e;
        } catch (Exception e) {
            sLogger.error("searchDispensingHospitalNurse error", e);
            throw new ServiceInternalException("searchDispensing error");
        }

        return retList;
    }

    public CompletableFuture<Map<Long, AdviceUsageRuleItemsAddMessage>> getAdviceUsageRuleFuture(String chainId,
                                                                                                 String clinicId,
                                                                                                 List<Long> adviceUsageRuleIdList) {
        return ExecutorUtils.futureSupplyAsync(() -> hisAdviceService.batchQueryAdviceUsageRuleItemAddMessage(chainId, clinicId, adviceUsageRuleIdList));
    }

    public CompletableFuture<Map<String, HisChargeFormItemDto>> getHisChargeFormItemFuture(String chainId,
                                                                                           String clinicId,
                                                                                           List<String> chargeFormItemIdList) {
        return ExecutorUtils.futureSupplyAsync(() -> hisChargeService.listHisChargeFormItemMap(chainId, clinicId, chargeFormItemIdList));
    }

}
