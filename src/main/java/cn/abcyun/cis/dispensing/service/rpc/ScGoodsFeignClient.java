package cn.abcyun.cis.dispensing.service.rpc;

import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisGoodsLogFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisScGoodsFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.*;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.config.GoodsConfigView;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.config.LockConfig;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslog.GetGoodsStockLogReq;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslog.GoodsStockLogItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslog.QueryByStockIdsReq;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.core.util.FeignClientRpcTemplate;
import cn.abcyun.cis.dispensing.api.protocol.dispense.DispensingFormItemBatchReq;
import cn.abcyun.cis.dispensing.api.protocol.dispense.re.DispensingFormItemReDispenseReq;
import cn.abcyun.cis.dispensing.api.protocol.dispense.re.DispensingFormReDispenseReq;
import cn.abcyun.cis.dispensing.api.protocol.dispense.re.DispensingSheetReDispenseReq;
import cn.abcyun.cis.dispensing.domain.DispensingFormItem;
import cn.abcyun.cis.dispensing.domain.DispensingSheet;
import cn.abcyun.cis.dispensing.util.DispensingUtils;
import cn.abcyun.cis.dispensing.util.MathUtils;
import cn.abcyun.cis.dispensing.util.PharmacyPermissionUtils;
import cn.abcyun.common.model.AbcListPage;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.Lists;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ScGoodsFeignClient {

    @Autowired
    private AbcCisScGoodsFeignClient client;

    @Autowired
    private AbcCisGoodsLogFeignClient abcCisGoodsLogFeignClient;

    private Cache<String, GoodsConfigView> clinicIdToGoodsConfigView = Caffeine.newBuilder()
            .maximumSize(500)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build();


    private Cache<String, GoodsSupplierView> clinicIdToGoodsCloudDecoctionView = Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build();

    /**
     * 查询发药的批次
     */
    public List<GoodsUseTraceCodeRsp.GoodsUseTraceCodeItem> traceCodeUse(GoodsUseTraceCodeReq req) {
        if (req == null) {
            return null;
        }
        try {
            GoodsUseTraceCodeRsp rsp = FeignClientRpcTemplate.dealRpcClientMethod("tracecodeUseLogV2", true, () -> client.tracecodeUseLogV2(req), req);
            if (rsp != null && rsp.getList() != null) {
                return rsp.getList();
            }
        } catch (Exception exp) {

        }
        return null;
    }

    /**
     * 补录追溯码
     */
    public void reReportTraceCodeUse(GoodsReReportTraceCodeReq req) {
        if (req == null) {
            return;
        }
        FeignClientRpcTemplate.dealRpcClientMethod("reReportTraceCode", true, () -> client.reReportTraceCode(req), req);
    }


    /**
     * 查询发药的批次
     */
    public List<GoodsStockLogItem> getGoodsStockDispenseLogList(String chainId, String clinicId, List<String> batIds, boolean withBatchId) {
        return getGoodsStockLogList(chainId, clinicId, batIds, withBatchId, GoodsConst.StockLogAction.ACTION_DISPENSE);
    }

    /**
     * 查询发药的批次
     */
    public List<GoodsStockLogItem> getGoodsStockUndispenseLogList(String chainId, String clinicId, List<String> batIds, boolean withBatchId) {
        return getGoodsStockLogList(chainId, clinicId, batIds, withBatchId, GoodsConst.StockLogAction.ACTION_UN_DISPENSE);
    }

    /**
     * 查询发药的批次
     */
    public List<GoodsStockLogItem> getGoodsStockLogList(String chainId, String clinicId, List<String> batIds, boolean withBatchId, String action) {
        if (CollectionUtils.isEmpty(batIds)) {
            return new ArrayList<>();
        }

        GetGoodsStockLogReq getGoodsStockLogReq = new GetGoodsStockLogReq();
        getGoodsStockLogReq.setChainId(chainId);
        getGoodsStockLogReq.setClinicId(clinicId);
        getGoodsStockLogReq.setAction(action);
        getGoodsStockLogReq.setBatIdList(batIds);
        AbcListPage<GoodsStockLogItem> listPage = FeignClientRpcTemplate.dealRpcClientMethod("getGoodsStockLogList",
                () -> abcCisGoodsLogFeignClient.getGoodsStockLogList(getGoodsStockLogReq), getGoodsStockLogReq);
        if (listPage == null || CollectionUtils.isEmpty(listPage.getRows())) {
            return new ArrayList<>();
        }

        List<GoodsStockLogItem> goodsStockLogs = listPage.getRows();
        if (withBatchId) {
            // 虽然实体类中定义的了 batchId ，但是在 v2_goods_stock_log 中并没有保存 batchId，如果需要再调用一次 rpc 绑定
            List<Long> stockIds = goodsStockLogs.stream().map(GoodsStockLogItem::getStockId).collect(Collectors.toList());
            List<GoodsStockInfo> goodsStockInfoList = queryGoodsStockInfoByIds(chainId, clinicId, stockIds);
            bindBatchId(goodsStockLogs, goodsStockInfoList);
        }
        return goodsStockLogs;
    }

    private void bindBatchId(List<GoodsStockLogItem> goodsStockLogs, List<GoodsStockInfo> goodsStockInfoList) {
        if (CollectionUtils.isEmpty(goodsStockInfoList) || CollectionUtils.isEmpty(goodsStockLogs)) {
            return;
        }

        Map<Long, Long> stockIdToBatchId = goodsStockInfoList.stream().collect(Collectors.toMap(GoodsStockInfo::getStockId, GoodsStockInfo::getBatchId, (a, b) -> a));
        goodsStockLogs.forEach(goodsStockLog -> {
            goodsStockLog.setBatchId(stockIdToBatchId.get(goodsStockLog.getStockId()));
        });
    }

    private List<GoodsStockInfo> queryGoodsStockInfoByIds(String chainId, String clinicId, List<Long> stockIds) {
        if (CollectionUtils.isEmpty(stockIds)) {
            return new ArrayList<>();
        }

        QueryByStockIdsReq req = new QueryByStockIdsReq();
        req.setChainId(chainId);
        req.setClinicId(clinicId);
        req.setStockIds(stockIds);

        AbcListPage<GoodsStockInfo> listPage = FeignClientRpcTemplate.dealRpcClientMethod("queryStockInfoByGoodsIds", true,
                () -> client.queryStockInfoByGoodsIds(req), req);

        if (listPage == null || CollectionUtils.isEmpty(listPage.getRows())) {
            return new ArrayList<>();
        }

        return listPage.getRows();
    }

    /**
     * 查询商品的库存信息
     */
    public GetGoodsStockBatchesRsp getGoodsStockBatches(String clinicId, List<String> goodsIds, int pharmacyType) {
        GetGoodsStockBatchesReq getGoodsStockBatchesReq = new GetGoodsStockBatchesReq();
        getGoodsStockBatchesReq.setPharmacyType(pharmacyType);
        getGoodsStockBatchesReq.setClinicId(clinicId);
        getGoodsStockBatchesReq.setGoodsIds(goodsIds);
        return FeignClientRpcTemplate.dealRpcClientMethod("getGoodsStockBatches", true,
                () -> client.getGoodsStockBatches(getGoodsStockBatchesReq), getGoodsStockBatchesReq);
    }

    /**
     * 查询商品的批次信息（主要用于批次信息的展示）
     */
    public GetGoodsStockBatchesRsp getGoodsStockBatches(String clinicId, List<GetGoodsStockBatchesReq.GetGoodsStockBatch> goodsList) {
        if (CollectionUtils.isEmpty(goodsList)) {
            return null;
        }

        GetGoodsStockBatchesReq getGoodsStockBatchesReq = new GetGoodsStockBatchesReq();
        getGoodsStockBatchesReq.setClinicId(clinicId);
        getGoodsStockBatchesReq.setGoodsList(goodsList);
        try {
            return FeignClientRpcTemplate.dealRpcClientMethod("getGoodsStockBatches", true,
                    () -> client.getGoodsStockBatches(getGoodsStockBatchesReq), getGoodsStockBatchesReq);
        } catch (Exception e) {
            return null;
        }
    }


    /**
     * 查询发药单的商品信息（对商品的价格和库存敏感的调用这个）
     *
     * @return key->{@link DispensingFormItem#getId()} value->{@link GoodsItem}
     */
    public Map<String, GoodsItem> queryGoodsItemWitchStockByDispensingSheet(String clinicId,
                                                                            String chainId,
                                                                            boolean isWithStock,
                                                                            int notQuerySheBaoInfo,
                                                                            DispensingSheet dispensingSheet) {
        if (dispensingSheet == null) {
            return new HashMap<>();
        }

        List<QueryGoodsInPharmacyByIdsAndStockCountReq.QueryGoodsWithStock> queryGoodsWithStockList = new ArrayList<>();
        DispensingUtils.doWithDispensingItem(dispensingSheet, formItem -> {
            if (formItem.getComposeType() == ComposeType.COMPOSE) {
                return;
            }

            queryGoodsWithStockList.add(fromDispensingFormItem(formItem));
        });

        if (CollectionUtils.isEmpty(queryGoodsWithStockList)) {
            return new HashMap<>();
        }

        QueryGoodsInPharmacyByIdsAndStockCountReq.QueryPharmacyGoodsReq queryPharmacyGoodsReq = new QueryGoodsInPharmacyByIdsAndStockCountReq.QueryPharmacyGoodsReq();
        queryPharmacyGoodsReq.setPharmacyNo(dispensingSheet.getPharmacyNo());
        queryPharmacyGoodsReq.setPharmacyType(dispensingSheet.getPharmacyType());
        queryPharmacyGoodsReq.setList(queryGoodsWithStockList);

        List<QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp> queryPharmacyGoodsRsps = queryGoodsInPharmacyByIds(clinicId, chainId, isWithStock, notQuerySheBaoInfo, Lists.newArrayList(queryPharmacyGoodsReq));
        if (CollectionUtils.isEmpty(queryPharmacyGoodsRsps) || queryPharmacyGoodsRsps.get(0) == null) {
            return new HashMap<>();
        }

        QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp queryPharmacyGoodsRsp = queryPharmacyGoodsRsps.get(0);
        List<GoodsItem> goodsItems = queryPharmacyGoodsRsp.getList();
        return ListUtils.toMap(goodsItems, GoodsItem::getKeyId);
    }

    private static QueryGoodsInPharmacyByIdsAndStockCountReq.QueryGoodsWithStock fromDispensingFormItem(DispensingFormItem dispensingFormItem) {
        if (dispensingFormItem == null) {
            return null;
        }

        QueryGoodsInPharmacyByIdsAndStockCountReq.QueryGoodsWithStock queryGoodsWithStock = new QueryGoodsInPharmacyByIdsAndStockCountReq.QueryGoodsWithStock();
        queryGoodsWithStock.setGoodsId(dispensingFormItem.getProductId());
        if (dispensingFormItem.isDispensable()) {
            queryGoodsWithStock.setLockId(dispensingFormItem.getLockId()); //如果没有lockId拉回来的也就是现在的Goods
        }
        queryGoodsWithStock.setKeyId(dispensingFormItem.getId()); //每个formItem的返回都不一样
        return queryGoodsWithStock;
    }

    @Retryable(value = {RetryableException.class})
    public List<QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp> queryGoodsInPharmacyByIds(String clinicId,
                                                                                              String chainId,
                                                                                              boolean isWithStock,
                                                                                              int notQuerySheBaoInfo,
                                                                                              List<QueryGoodsInPharmacyByIdsAndStockCountReq.QueryPharmacyGoodsReq> pharmacyGoodsList) {
        if (CollectionUtils.isEmpty(pharmacyGoodsList) || StringUtils.isEmpty(clinicId) || StringUtils.isEmpty(chainId)) {
            return new ArrayList<>();
        }
        QueryGoodsInPharmacyByIdsAndStockCountReq rpcQueryGoodsReq = new QueryGoodsInPharmacyByIdsAndStockCountReq();
        rpcQueryGoodsReq.setChainId(chainId);

        rpcQueryGoodsReq.setQueryPharmacyGoodsList(pharmacyGoodsList);
        rpcQueryGoodsReq.setClinicId(clinicId);
        rpcQueryGoodsReq.setLoadRepoPrice(1);
        rpcQueryGoodsReq.setWithStock(isWithStock);
        rpcQueryGoodsReq.setWithDeleted(1);
        rpcQueryGoodsReq.setNotQuerySheBaoInfo(notQuerySheBaoInfo);
        QueryGoodsInPharmacyByIdsRsp rsp = FeignClientRpcTemplate.dealRpcClientMethod("queryGoodsInPharmacyByIdsAndStockCountV3", true,
                () -> client.queryGoodsInPharmacyByIdsAndStockCount(rpcQueryGoodsReq), rpcQueryGoodsReq);

        return Optional.ofNullable(rsp).map(QueryGoodsInPharmacyByIdsRsp::getList).orElse(new ArrayList<>());
    }

    /**
     * 根据不同药房查询goods信息
     *
     * @param clinicId
     * @param chainId
     * @param isWithStock
     * @param notQuerySheBaoInfo
     * @param pharmacyGoodsList
     * @return
     */
    @Retryable(value = {ServiceInternalException.class}, maxAttempts = 2)
    public List<QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp> queryGoodsInPharmacyByIdsDistinctByGoodsId(String clinicId, String chainId, boolean isWithStock, int notQuerySheBaoInfo, List<QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq> pharmacyGoodsList) {
        if (CollectionUtils.isEmpty(pharmacyGoodsList) || StringUtils.isEmpty(clinicId) || StringUtils.isEmpty(chainId)) {
            return new ArrayList<>();
        }

        QueryGoodsInPharmacyByIdsReq req = new QueryGoodsInPharmacyByIdsReq();
        req.setClinicId(clinicId);
        req.setChainId(chainId);
        req.setWithStock(isWithStock);
        req.setWithDeleted(1);
        req.setNotQuerySheBaoInfo(notQuerySheBaoInfo);
        req.setQueryPharmacyGoodsList(pharmacyGoodsList);

        QueryGoodsInPharmacyByIdsRsp rsp = FeignClientRpcTemplate.dealRpcClientMethod("queryGoodsInPharmacyByIds", true,
                () -> client.queryGoodsInPharmacyByIds(req), req);

        return Optional.ofNullable(rsp).map(QueryGoodsInPharmacyByIdsRsp::getList).orElse(new ArrayList<>());
    }


    /**
     * 拉取门店的药房集合
     *
     * @param chainId  连锁id
     * @param clinicId 门店id
     * @return 结果
     */
    @Retryable(value = {Exception.class}, maxAttempts = 2)
    public List<GoodsPharmacyView> findPharmacyByClinic(String chainId,
                                                        String clinicId) {
        if (StringUtils.isBlank(chainId)) {
            throw new ParamRequiredException("chainId");
        }
        if (StringUtils.isBlank(clinicId)) {
            throw new ParamRequiredException("clinicId");
        }

        return Optional
                .ofNullable(
                        FeignClientRpcTemplate.dealRpcClientMethod("findPharmacyByClinic", true,
                                () -> client.findPharmacyByClinic(chainId, clinicId),
                                chainId, clinicId)
                ).map(AbcListPage::getRows)
                .orElse(Lists.newArrayList());
    }

    /**
     * 查询指定药房信息
     */
    public GoodsPharmacyView getPharmacyInfo(String chainId, String clinicId, int pharmacyNo) {
        Map<Integer, GoodsPharmacyView> pharmacyNoToPharmacy = loadPharmacyByClinicMap(chainId, clinicId);
        return pharmacyNoToPharmacy.get(pharmacyNo);
    }

    public Map<Integer, GoodsPharmacyView> loadPharmacyByClinicMap(String chainId,
                                                                   String clinicId) {
        if (StringUtils.isBlank(chainId)) {
            throw new ParamRequiredException("chainId");
        }
        if (StringUtils.isBlank(clinicId)) {
            throw new ParamRequiredException("clinicId");
        }

        GoodsConfigView goodsConfigView = getGoodsConfig(clinicId);
        if (goodsConfigView == null || CollectionUtils.isEmpty(goodsConfigView.getPharmacyList())) {
            return new HashMap<>();
        }
        return goodsConfigView.getPharmacyList().stream().collect(Collectors.toMap(GoodsPharmacyView::getNo, Function.identity(), (a, b) -> a));

    }

    /**
     * GoodsRpc接口 获取诊所配置
     *
     * @param clinicId 门店id
     * @return 响应结果
     */
    @Retryable(value = {RetryableException.class})
    public GoodsConfigView getGoodsConfig(String clinicId) {
        if (StringUtils.isBlank(clinicId)) {
            throw new ParamRequiredException("clinicId is empty");
        }
        return clinicIdToGoodsConfigView.get(clinicId,
                (key) -> FeignClientRpcTemplate.dealRpcClientMethod("getGoodsConfig", true,
                        () -> client.getGoodsConfig(key),
                        key));
    }

    public List<GoodsStockInfo> queryStockInfoByGoodsIds(String chainId, String clinicId, List<Long> stockIds) {
        if (CollectionUtils.isEmpty(stockIds)) {
            return new ArrayList<>();
        }
        QueryByStockIdsReq queryByStockIdsReq = new QueryByStockIdsReq();
        queryByStockIdsReq.setChainId(chainId);
        queryByStockIdsReq.setClinicId(clinicId);
        queryByStockIdsReq.setStockIds(stockIds);

        return Optional.ofNullable(
                FeignClientRpcTemplate.dealRpcClientMethod(
                        "queryStockInfoByGoodsIds",
                        true,
                        () -> client.queryStockInfoByGoodsIds(queryByStockIdsReq),
                        queryByStockIdsReq
                )
        ).map(AbcListPage::getRows).orElseGet(ArrayList::new);
    }

    public boolean isLockByBatch(GoodsConfigView getGoodsConfig) {
        if (CollectionUtils.isEmpty(getGoodsConfig.getLockConfigs())) {
            return false;
        }
        LockConfig findLockConfig = null;
        for (LockConfig lockConfig : getGoodsConfig.getLockConfigs()) {
            if (lockConfig.getSceneType() == LockConfig.SceneType.OUTPATIENT) {
                findLockConfig = lockConfig;
                break;
            }
        }

        if (findLockConfig != null && findLockConfig.getLockFlag() == LockConfig.LockFlag.OUTPATIENT && findLockConfig.getLockBatch() == LockConfig.LockBatch.YES) {
            return true;
        }

        return false;
    }

    /**
     * 获取员工可访问的药房号
     */
    public List<Integer> getEmployeePharmacyNosByEmployeeId(String chainId, String clinicId, String employeeId) {
        return getEmployeePharmacyNoMapByEmployeeIds(chainId, clinicId, Collections.singletonList(employeeId)).getOrDefault(employeeId, new ArrayList<>());
    }

    /**
     * 获取员工可访问的药房号
     */
    public Map<String, List<Integer>> getEmployeePharmacyNoMapByEmployeeIds(String chainId, String clinicId, List<String> employeeIds) {
        if (CollectionUtils.isEmpty(employeeIds)) {
            return new HashMap<>();
        }

        Map<Integer, GoodsPharmacyView> pharmacyNoToPharmacyView = loadPharmacyByClinicMap(chainId, clinicId);
        if (CollectionUtils.isEmpty(pharmacyNoToPharmacyView)) {
            log.info("no pharmacy info, clinicId={}", clinicId);
            return new HashMap<>();
        }

        return PharmacyPermissionUtils.buildEmployeePharmacyNoMap(pharmacyNoToPharmacyView, employeeIds);
    }

    public Map<String, GoodsItem> queryGoodsItemWitchStockByDispensingSheetReDispenseReq(DispensingSheetReDispenseReq dispensingSheetReDispenseReq,
                                                                                         DispensingSheet dispensingSheet,
                                                                                         Map<String, DispensingFormItem> dispensingFormItemToDispensingFormItem) {
        List<DispensingFormItemReDispenseReq> redisenseItemReqList = Optional.ofNullable(dispensingSheetReDispenseReq).map(DispensingSheetReDispenseReq::getDispensingForms).orElseGet(Collections::emptyList)
                .stream().filter(Objects::nonNull).map(DispensingFormReDispenseReq::getDispensingFormItems).filter(Objects::nonNull).flatMap(Collection::stream).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, DispensingFormItemReDispenseReq> dispensingItemIdToItemRedispenseItemReq = ListUtils.toMap(redisenseItemReqList, DispensingFormItemReDispenseReq::getId);

        List<QueryGoodsInPharmacyByIdsAndStockCountReq.QueryGoodsWithStock> queryGoodsWithStockList = new ArrayList<>();
        dispensingFormItemToDispensingFormItem.forEach((dispensingFormItemId, dispensingFormItem) -> {
            DispensingFormItemReDispenseReq reDispenseItemReq = dispensingItemIdToItemRedispenseItemReq.get(dispensingFormItemId);

            QueryGoodsInPharmacyByIdsAndStockCountReq.QueryGoodsWithStock queryGoodsWithStock = new QueryGoodsInPharmacyByIdsAndStockCountReq.QueryGoodsWithStock();
            queryGoodsWithStock.setKeyId(dispensingFormItem.getId());
            queryGoodsWithStock.setGoodsId(dispensingFormItem.getProductId());

            if (reDispenseItemReq != null && !CollectionUtils.isEmpty(reDispenseItemReq.getDispenseBatches())) {
                List<QueryGoodsInPharmacyByIdsAndStockCountReq.PretendBatchCutItem> pretendBatchCutItemList = new ArrayList<>();
                BigDecimal totalPieceCount = BigDecimal.ZERO;
                BigDecimal totalPackageCount = BigDecimal.ZERO;
                for (DispensingFormItemBatchReq dispenseBatch : reDispenseItemReq.getDispenseBatches()) {
                    if (dispenseBatch == null) {
                        continue;
                    }

                    QueryGoodsInPharmacyByIdsAndStockCountReq.PretendBatchCutItem pretendBatchCutItem = new QueryGoodsInPharmacyByIdsAndStockCountReq.PretendBatchCutItem();
                    pretendBatchCutItem.setBatchId(dispenseBatch.getBatchId());
                    if (dispensingFormItem.getUseDismounting() == 1) {
                        pretendBatchCutItem.setPieceCount(dispenseBatch.getUnitCount());
                        totalPieceCount = MathUtils.wrapBigDecimalAdd(totalPieceCount, dispenseBatch.getUnitCount());
                    } else {
                        pretendBatchCutItem.setPackageCount(dispenseBatch.getUnitCount());
                        totalPackageCount = MathUtils.wrapBigDecimalAdd(totalPackageCount, dispenseBatch.getUnitCount());
                    }
                    pretendBatchCutItemList.add(pretendBatchCutItem);
                }

                queryGoodsWithStock.setPieceCount(totalPieceCount);
                queryGoodsWithStock.setPackageCount(totalPackageCount);
                queryGoodsWithStock.setPretendBatchCutItemList(pretendBatchCutItemList);
            }

            queryGoodsWithStockList.add(queryGoodsWithStock);
        });

        if (CollectionUtils.isEmpty(queryGoodsWithStockList)) {
            return new HashMap<>();
        }

        QueryGoodsInPharmacyByIdsAndStockCountReq.QueryPharmacyGoodsReq queryPharmacyGoodsReq = new QueryGoodsInPharmacyByIdsAndStockCountReq.QueryPharmacyGoodsReq();
        queryPharmacyGoodsReq.setPharmacyNo(dispensingSheet.getPharmacyNo());
        queryPharmacyGoodsReq.setPharmacyType(dispensingSheet.getPharmacyType());
        queryPharmacyGoodsReq.setList(queryGoodsWithStockList);

        List<QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp> queryPharmacyGoodsRsps = queryGoodsInPharmacyByIds(dispensingSheet.getClinicId(), dispensingSheet.getChainId(), true, 1, Lists.newArrayList(queryPharmacyGoodsReq));
        if (CollectionUtils.isEmpty(queryPharmacyGoodsRsps) || queryPharmacyGoodsRsps.get(0) == null || CollectionUtils.isEmpty(queryPharmacyGoodsRsps.get(0).getList())) {
            return new HashMap<>();
        }

        QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp queryPharmacyGoodsRsp = queryPharmacyGoodsRsps.get(0);
        List<GoodsItem> goodsItems = queryPharmacyGoodsRsp.getList();
        return ListUtils.toMap(goodsItems, GoodsItem::getKeyId);
    }

    /**
     * 查询药房供应商的配置
     */
    public GoodsSupplierView getCloudDecoctionSupplier(String chainId, String clinicId) {
        GoodsSupplierView goodsSupplierView = clinicIdToGoodsCloudDecoctionView.get(clinicId, (clinicIdKey) -> {
            AbcListPage<GoodsSupplierView> goodsSupplierRsp = FeignClientRpcTemplate.dealRpcClientMethod("getGoodsSuppliers",
                    () -> client.getGoodsSuppliers(chainId, clinicIdKey, GoodsConst.PharmacyType.VIRTUAL_PHARMACY, GoodsConst.GoodsSupplierType.NORMAL, 0, 1), clinicIdKey);
            if (goodsSupplierRsp == null || goodsSupplierRsp.getRows() == null) {
                return null;
            }
            return goodsSupplierRsp.getRows().stream()
                    .filter(item -> item.getSubType() == GoodsSupplierView.SupplierSubType.CLOUD_SUPPLIER && StringUtils.equals(item.getClinicId(), clinicIdKey))
                    .findFirst().orElse(null);
        });

        if (goodsSupplierView != null && goodsSupplierView.getExtendInfo() != null && goodsSupplierView.getExtendInfo().getCloudDecoctionSupplierInfo() != null) {
            return goodsSupplierView;
        }

        return null;
    }
}
