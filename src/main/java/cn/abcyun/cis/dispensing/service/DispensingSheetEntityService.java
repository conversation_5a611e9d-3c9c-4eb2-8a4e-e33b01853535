package cn.abcyun.cis.dispensing.service;

import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.dispensing.domain.*;
import cn.abcyun.cis.dispensing.repository.*;
import cn.abcyun.cis.dispensing.util.DispensingUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Nullable;
import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 发药单服务，相比于 {@link DispensingSheetService} 更加轻量
 *
 * <AUTHOR>
 * @since 2024-03-20 13:02:58
 **/
@Slf4j
@Service
public class DispensingSheetEntityService {

    @Autowired
    private DispensingSheetRepository dispensingSheetRepository;

    @Autowired
    private DispensingSheetV2Repository dispensingSheetV2Repository;

    @Autowired
    private DispensingFormV2Repository dispensingFormV2Repository;

    @Autowired
    private DispensingFormItemV2Repository dispensingFormItemV2Repository;

    @Autowired
    private DispensingFormItemBatchInfoRepository dispensingFormItemBatchInfoRepository;

    @Autowired
    private DispensingFormAdditionalRepository dispensingFormAdditionalRepository;

    public void save(DispensingSheet dispensingSheet) {
        if (dispensingSheet == null) {
            return;
        }
        log.info(AbcLogMarker.MARKER_MESSAGE_INDEX, "dispensingSheet {} dateVersion:{}", dispensingSheet.getId(), dispensingSheet.getDataVersion());
        dispensingSheetRepository.save(dispensingSheet);
        List<DispensingFormItemBatchInfo> dispensingFormItemBatches = dispensingSheet.getAllDispensingFormItemBatches();
        if (!CollectionUtils.isEmpty(dispensingFormItemBatches)) {
            dispensingFormItemBatchInfoRepository.saveAll(dispensingFormItemBatches);
        }
        List<DispensingForm> dispensingForms = dispensingSheet.getDispensingForms();
        if (!CollectionUtils.isEmpty(dispensingForms)) {
            List<DispensingFormAdditional> additionalList = dispensingForms.stream().map(DispensingForm::getAdditional).filter(Objects::nonNull).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(additionalList)) {
                dispensingFormAdditionalRepository.saveAll(additionalList);
            }
        }
    }

    public void save(DispensingSheetV2 dispensingSheet) {
        if (dispensingSheet == null) {
            return;
        }

        saveAll(Lists.newArrayList(dispensingSheet));
    }

    public void saveAll(List<DispensingSheetV2> dispensingSheetV2List) {
        if (CollectionUtils.isEmpty(dispensingSheetV2List)) {
            return;
        }

        List<DispensingFormV2> allDispensingForms = new ArrayList<>();
        List<DispensingFormItemV2> allDispensingFormItems = new ArrayList<>();
        List<DispensingFormItemBatchInfo> allDispensingFormItemBatches = new ArrayList<>();
        for (DispensingSheetV2 dispensingSheet : dispensingSheetV2List) {
            if (dispensingSheet == null) {
                continue;
            }

            List<DispensingFormV2> dispensingForms = dispensingSheet.getDispensingForms();
            if (CollectionUtils.isEmpty(dispensingForms)) {
                continue;
            }

            allDispensingForms.addAll(dispensingForms);
            for (DispensingFormV2 dispensingForm : dispensingForms) {
                if (dispensingForm == null) {
                    continue;
                }

                List<DispensingFormItemV2> formItems = dispensingForm.getDispensingFormItems();
                if (CollectionUtils.isEmpty(formItems)) {
                    continue;
                }

                allDispensingFormItems.addAll(formItems);
                for (DispensingFormItemV2 formItem : formItems) {
                    if (formItem == null) {
                        continue;
                    }

                    List<DispensingFormItemBatchInfo> dispensingFormItemBatches = formItem.getDispensingFormItemBatches();
                    if (CollectionUtils.isEmpty(dispensingFormItemBatches)) {
                        continue;
                    }

                    allDispensingFormItemBatches.addAll(dispensingFormItemBatches);
                }
            }
        }

        if (!CollectionUtils.isEmpty(allDispensingForms)) {
            dispensingFormV2Repository.saveAll(allDispensingForms);
        }

        if (!CollectionUtils.isEmpty(allDispensingFormItems)) {
            dispensingFormItemV2Repository.saveAll(allDispensingFormItems);
        }

        if (!CollectionUtils.isEmpty(allDispensingFormItemBatches)) {
            dispensingFormItemBatchInfoRepository.saveAll(allDispensingFormItemBatches);
        }

        dispensingSheetV2Repository.saveAll(dispensingSheetV2List);
    }

    public DispensingSheet findByIdAndChainIdAndNoDeleted(String id, String chainId) {
        DispensingSheet dispensingSheet = dispensingSheetRepository.findByIdAndChainIdAndIsDeleted(id, chainId, DispensingUtils.DeleteFlag.NOT_DELETED).orElse(null);
        if (dispensingSheet == null) {
            return null;
        }
        this.appendInfo(Lists.newArrayList(dispensingSheet));
        return dispensingSheet;
    }

    public DispensingSheet findByIdAndClinicId(String id, String clinicId) {
        DispensingSheet dispensingSheet = dispensingSheetRepository.findByIdAndClinicIdAndIsDeleted(id, clinicId, 0).orElse(null);
        if (dispensingSheet == null || !dispensingSheet.canDisplay()) {
            return null;
        }

        appendInfo(Lists.newArrayList(dispensingSheet));
        return dispensingSheet;
    }

    /**
     * 通过来源单据ID查询发药单（包含已删除但是有库存操作的单据）
     * 为什么要把已删除的也查询出来就是因为，具体显不显示还要看这个发药单有没有操作过发药，如果操作过发药的话，还是要把这个发药单显示出来
     */
    public List<DispensingSheet> findAllBySourceSheetIdAndChainIdForDisplay(String sourceSheetId, String chainId) {
        List<DispensingSheet> dispensingSheets = dispensingSheetRepository.findAllBySourceSheetIdAndChainId(sourceSheetId, chainId);
        if (CollectionUtils.isEmpty(dispensingSheets)) {
            return Lists.newArrayList();
        }

        List<DispensingSheet> canDisplayDispensingSheets = dispensingSheets.stream().filter(DispensingSheet::canDisplay).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(canDisplayDispensingSheets)) {
            return Lists.newArrayList();
        }

        appendInfo(canDisplayDispensingSheets);
        return canDisplayDispensingSheets;
    }

    public List<DispensingSheet> findAllBySourceSheetId(String chargeSheetId) {
        return this.findAllBySourceSheetId(null, chargeSheetId, 0, 0);
    }

    public List<DispensingSheet> findAllByChainIdSourceSheetId(String chainId, String chargeSheetId) {
        return this.findAllBySourceSheetId(chainId, chargeSheetId, 0, 0);
    }

    /**
     * 通过收费单查询发药单
     * <p>
     * 默认只查询正常的发药单，即未删除的，并且未退费的
     *
     * @param chainId       连锁ID
     * @param chargeSheetId 收费单ID
     * @param withRefunded  是否包含已退费的发药单
     * @return 发药单列表
     */
    public List<DispensingSheet> findAllBySourceSheetId(@Nullable String chainId, String chargeSheetId, int withDeleted, int withRefunded) {
        return findAllBySourceSheetId(chainId, chargeSheetId, withDeleted, withRefunded, null);
    }

    /**
     * 通过收费单查询发药单
     * <p>
     * 默认只查询正常的发药单，即未删除的，并且未退费的。封装这个方法，而不是直接调用 Repository 是因为在 Repository 没办法实现太复杂的查询逻辑（主要是要过滤掉已退费的）
     *
     * @param chainId       连锁ID
     * @param chargeSheetId 收费单ID
     * @param withRefunded  是否包含已退费的发药单
     * @param pharmacyType  药房类型
     * @return 发药单列表
     */
    public List<DispensingSheet> findAllBySourceSheetId(@Nullable String chainId, String chargeSheetId, int withDeleted, int withRefunded, Integer pharmacyType) {
        Specification<DispensingSheet> specification = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (StringUtils.hasText(chainId)) {
                predicates.add(cb.equal(root.get(DispensingSheet.Fields.chainId), chainId));
            }
            predicates.add(cb.equal(root.get(DispensingSheet.Fields.sourceSheetId), chargeSheetId));

            if (pharmacyType != null) {
                predicates.add(cb.equal(root.get(DispensingSheet.Fields.pharmacyType), pharmacyType));
            }

            if (withDeleted == 0) {
                predicates.add(cb.equal(root.get(DispensingSheet.Fields.isDeleted), 0));
            }

            if (withRefunded == 0) {
                Predicate payStatusIsNull = cb.isNull(root.get(DispensingSheet.Fields.payStatus));
                Predicate payStatusIsPaid = cb.equal(root.get(DispensingSheet.Fields.payStatus), DispensingSheet.PayStatus.PAID);
                predicates.add(cb.or(payStatusIsNull, payStatusIsPaid));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
        List<DispensingSheet> dispensingSheets = dispensingSheetRepository.findAll(specification);
        this.appendInfo(dispensingSheets);
        return dispensingSheets;
    }

    public DispensingSheet findById(String dispensingSheetId) {
        return findById(dispensingSheetId, false);
    }

    public List<DispensingSheet> findAllSameSourceSheetById(String chainId, String clinicId, String dispensingSheetId) {
        DispensingSheetV2 dispensingSheet = dispensingSheetV2Repository.findAllByChainIdAndClinicIdAndIdAndIsDeleted(chainId, clinicId, dispensingSheetId, 0);
        if (dispensingSheet == null) {
            return null;
        }

        List<DispensingSheet> dispensingSheets = this.findAllBySourceSheetId(dispensingSheet.getSourceSheetId());
        appendInfo(dispensingSheets);
        return dispensingSheets;
    }

    public DispensingSheet findById(String dispensingSheetId, boolean withDeleted) {
        DispensingSheet dispensingSheet;
        if (withDeleted) {
            dispensingSheet = dispensingSheetRepository.findById(dispensingSheetId).orElse(null);
        } else {
            dispensingSheet = dispensingSheetRepository.findByIdAndIsDeleted(dispensingSheetId, 0).orElse(null);
        }

        if (dispensingSheet == null) {
            return null;
        }

        appendInfo(Lists.newArrayList(dispensingSheet));
        return dispensingSheet;
    }

    private void appendInfo(List<DispensingSheet> dispensingSheets) {
        if (CollectionUtils.isEmpty(dispensingSheets)) {
            return;
        }

        String chainId = dispensingSheets.get(0).getChainId();
        List<String> dispensingSheetIds = ListUtils.extractUniqueProperty(dispensingSheets, DispensingSheet::getId);
        List<DispensingFormItemBatchInfo> allDispensingFormItemBatches = dispensingFormItemBatchInfoRepository.findAllByChainIdAndDispensingSheetIdInAndIsOldAndIsDeleted(chainId, dispensingSheetIds, 0, 0);
        if (!CollectionUtils.isEmpty(allDispensingFormItemBatches)) {
            Map<String, List<DispensingFormItemBatchInfo>> sheetIdToBatches = ListUtils.groupByKey(allDispensingFormItemBatches, DispensingFormItemBatchInfo::getDispensingSheetId);
            dispensingSheets.forEach(dispensingSheet -> {
                dispensingSheet.bindDispensingFormItemBatches(sheetIdToBatches.get(dispensingSheet.getId()));
            });
        }
        // 添加form扩展信息
        this.appendDispensingFormInfo(dispensingSheets);
    }

    private void appendDispensingFormInfo(List<DispensingSheet> dispensingSheetList) {
        if (CollectionUtils.isEmpty(dispensingSheetList)) {
            return;
        }
        List<String> dispensingFormIdSet = dispensingSheetList.stream()
                .map(DispensingSheet::getDispensingForms)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .map(DispensingForm::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dispensingFormIdSet)) {
            return;
        }
        List<DispensingFormAdditional> additionalList = dispensingFormAdditionalRepository.findAllById(dispensingFormIdSet);
        if (CollectionUtils.isEmpty(additionalList)) {
            return;
        }
        Map<String, DispensingFormAdditional> additionalMap = ListUtils.toMap(additionalList, DispensingFormAdditional::getId);
        dispensingSheetList.stream()
                .map(DispensingSheet::getDispensingForms)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .forEach(dispensingForm -> {
                    DispensingFormAdditional additional = additionalMap.get(dispensingForm.getId());
                    if (additional != null) {
                        dispensingForm.setAdditional(additional);
                    }
                });
    }

    public void saveAllV1(List<DispensingSheet> sheetList) {
        if (CollectionUtils.isEmpty(sheetList)) {
            return;
        }

        dispensingSheetRepository.saveAll(sheetList);

        sheetList.forEach(sheet -> {
            List<DispensingFormItemBatchInfo> dispensingFormItemBatches = sheet.getAllDispensingFormItemBatches();
            if (!CollectionUtils.isEmpty(dispensingFormItemBatches)) {
                dispensingFormItemBatchInfoRepository.saveAll(dispensingFormItemBatches);
            }
        });
    }
}
