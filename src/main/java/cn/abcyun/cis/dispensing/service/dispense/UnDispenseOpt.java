package cn.abcyun.cis.dispensing.service.dispense;

import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsDispenseDataItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsDispenseResultItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.config.GoodsConfigView;
import cn.abcyun.bis.rpc.sdk.property.model.Dispensing;
import cn.abcyun.cis.commons.amqp.message.PatientOrderMessage;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.cis.commons.rpc.charge.ChargeForm;
import cn.abcyun.cis.commons.rpc.charge.ChargeFormItem;
import cn.abcyun.cis.commons.rpc.charge.ChargeSheet;
import cn.abcyun.cis.commons.rpc.charge.Constants;
import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.core.util.SpringUtils;
import cn.abcyun.cis.dispensing.amqp.HAMQProducer;
import cn.abcyun.cis.dispensing.amqp.RocketMqProducer;
import cn.abcyun.cis.dispensing.base.exception.*;
import cn.abcyun.cis.dispensing.controller.DTOConverter;
import cn.abcyun.cis.dispensing.controller.StatusNameTranslator;
import cn.abcyun.cis.dispensing.domain.*;
import cn.abcyun.cis.dispensing.repository.DispensingLogRepository;
import cn.abcyun.cis.dispensing.repository.DispensingSheetOperationRepository;
import cn.abcyun.cis.dispensing.service.*;
import cn.abcyun.cis.dispensing.service.dto.UndispenseResult;
import cn.abcyun.cis.dispensing.service.operation.SheetOperationBase;
import cn.abcyun.cis.dispensing.service.operation.SheetOperationCreateFactory;
import cn.abcyun.cis.dispensing.service.rpc.ScGoodsFeignClient;
import cn.abcyun.cis.dispensing.util.DispenseHelper;
import cn.abcyun.cis.dispensing.util.DispensingUtils;
import cn.abcyun.cis.dispensing.util.FillUtils;
import cn.abcyun.cis.dispensing.util.MathUtils;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 退药操作
 *
 * <AUTHOR>
 * @date 2023/6/21 16:20
 **/
@Slf4j
@Component
@Scope("prototype")
public class UnDispenseOpt extends DispensingOpsBase {

    //********************* Bean-START ****************************

    @Autowired
    private DispensingSheetEntityService dispensingSheetEntityService;

    @Autowired
    private AbcIdGenerator abcIdGenerator;

    @Autowired
    private ChargeService chargeService;

    @Autowired
    private UndispensingService unDispensingService;

    @Autowired
    private DispensingSheetOperationRepository dispensingSheetOperationRepository;

    @Autowired
    private DispensingLogRepository dispensingLogRepository;

    @Autowired
    private AbcCisScGoodsService abcCisScGoodsService;

    @Autowired
    private DispensingLogService dispensingLogService;

    @Autowired
    private SheetOperationCreateFactory sheetOperationCreateFactory;

    @Autowired
    private RocketMqProducer rocketMqProducer;

    @Autowired
    private HAMQProducer hamqProducer;

    @Autowired
    private DispensingTodoService dispensingTodoService;

    @Autowired
    private ScGoodsFeignClient scGoodsFeignClient;

    @Autowired
    private PatientOrderService patientOrderService;

    @Autowired
    private DispensingService dispensingService;

    //********************* Bean-END ****************************

    /**
     * 操作ID
     */
    private String operationId;

    /**
     * 发药单ID
     */
    @NotNull
    private String dispensingSheetId;

    /**
     * 客户端传入的退药单
     */
    @NotNull
    private DispensingSheet clientUnDispenseSheet;

    /**
     * 客户端退药项请求列表
     */
    @NotNull
    private List<DispensingFormItem> clientUnDispenseItems;

    /**
     * 处方ID —> 客户端退处方请求
     */
    private Map<String, DispensingForm> dispensingFormIdToClientUnDispensingForm;

    /**
     * 发药项ID -> 客户端退药项请求
     */
    @NotNull
    private Map<String, DispensingFormItem> dispenseItemIdToClientUnDispenseItem;

    /**
     * 诊所ID
     */
    @NotNull
    private String clinicId;

    /**
     * 操作人ID
     */
    @NotNull
    private String operatorId;

    private List<DispensingFormItem> logUnDispensingFormItems = new ArrayList<>();

    /**
     * 数据库中的发药单
     */
    private DispensingSheet dispensingSheet;


    /**
     * 初始发药单状态
     */
    private DispensingTodoService.DispensingSheetStatus beforeDispensingSheetStatus;

    /**
     * 发药单对应的收费单
     */
    private ChargeSheet chargeSheet;

    /**
     * 收费单的收费项
     */
    private Map<String, ChargeFormItem> chargeItemIdToChargeItem;

    /**
     * 发药单所有的项
     */
    private List<DispensingFormItem> dispensingItems;

    /**
     * formItemId -> formItem
     */
    private Map<String, DispensingFormItem> dispensingItemIdToDispensingItem;

    /**
     * 发药的 formItemId -> Array[0]退药的单位数量 Array[1]退药的剂数
     */
    private Map<String, BigDecimal[]> dispenseItemIdToUnDispenseUnitCountAndDoseCount;

    /**
     * 发药单操作记录列表
     */
    private List<DispensingSheetOperation> dispenseOperations;

    /**
     * 发药的发药项操作记录列表
     */
    private List<DispensingLogItem> dispenseItemLogs;

    /**
     * goods 配置
     */
    private GoodsConfigView goodsConfig;

    /**
     * 发药的配置信息
     */
    private Dispensing dispensingConfig;

    public UndispenseResult unDispense() {
        // 数据加载
        load();

        // 退药前置处理
        doUnDispensePreProcess();

        // 退药
        UndispenseResult undispenseResult = doUnDispense();

        // 推送发送结果
        pushUnDispenseResult();

        return undispenseResult;
    }

    private void pushUnDispenseResult() {
        /***
         * 具体是不是整个发药单全退了，要检查sheet里面状态
         * */
        hamqProducer.sendDispensingSheetUpdate(PatientOrderMessage.MSG_SUB_TYPE_DISPENGING_UPDATED_UNDISPENSE, dispensingSheet, operatorId);
        rocketMqProducer.broadcastDispensingSheetStatusChange(dispensingSheet, false, operatorId);

        // 推送待办统计
        if (dispensingSheet.getStatus() == DispensingSheet.Status.UNDISPENSED) {
            List<String> subKeys = new ArrayList<>();
            boolean sendTodoMessage = dispensingTodoService.getWaitingTodoSubKey(beforeDispensingSheetStatus.getOrderByDate(),
                    dispensingSheet.getIsOnline(), dispensingSheet.getDeliveredStatus(),
                    dispensingSheet.getProcessedStatus(), subKeys, DispensingTodoService.Action.UNDISPENSE,
                    beforeDispensingSheetStatus, DispensingTodoService.DispensingSheetStatus.of(dispensingSheet));
            RocketMqProducer.doAfterTransactionCommit(() -> {
                dispensingTodoService.addDecPharmacyTodoCountTask(dispensingSheet.getChainId(), clinicId,
                        dispensingSheet.getPharmacyNo(), operatorId, subKeys, sendTodoMessage);
            });
        }
    }

    private UndispenseResult doUnDispense() {
        // 返还库存
        List<GoodsDispenseResultItem> goodsDispenseResultItems = returnStock();

        // 更新发药单
        updateUndispenseToSheet(goodsDispenseResultItems);
        dispensingSheetEntityService.save(dispensingSheet);

        // 记录日志
        log(goodsDispenseResultItems);

        // 智能发药机-退药结果处理
        //整单--标记取消状态，同时提交取消接口
        RocketMqProducer.doAfterTransactionCommit(() -> {
            // 无脑直接查询然后更新追溯码使用量
            List<DispensingSheet> dispensingSheets = dispensingSheetEntityService.findAllByChainIdSourceSheetId(dispensingSheet.getChainId(), dispensingSheet.getSourceSheetId());
            abcCisScGoodsService.updateUseTraceCode(dispensingSheet.getChainId(), dispensingSheet.getClinicId(), dispensingSheets, operatorId);
            dispensingService.smartUnDispense(dispensingSheet, operatorId);
        });

        UndispenseResult undispenseResult = new UndispenseResult();
        undispenseResult.setUndispensedCount(goodsDispenseResultItems.size());
        undispenseResult.setStatus(dispensingSheet.getStatus());
        undispenseResult.setStatusName(StatusNameTranslator.translateDispensingSheetStatus(dispensingSheet.getStatus()));
        undispenseResult.setId(dispensingSheetId);
        return undispenseResult;

    }


    private void log(List<GoodsDispenseResultItem> goodsDispenseResultItems) {
        // 记录发药项日志
        dispensingLogService.logDispensingSheetUndispense(dispensingSheet, logUnDispensingFormItems, operationId, operatorId);

        // 记录操作日志
        SheetOperationBase operationBase = sheetOperationCreateFactory.createSheetOperation(DispensingSheetOperation.OperationType.WITHDRAWAL_MEDICINE, dispensingSheetId, operatorId, operatorId, dispensingSheet.getChainId(), dispensingSheet.getClinicId());
        operationBase.bindDispensingSheet(dispensingSheet)
                .bindRepoId(operationId)
                .bindDispensingFormItems(logUnDispensingFormItems)
                .bindDispenseOperations(dispenseOperations)
                .bindGoodsDispenseResultItems(goodsDispenseResultItems)
                .bindDispenseItemLogs(dispenseItemLogs);
        operationBase.createAndSaveSheetOperation();
    }

    private void updateUndispenseToSheet(List<GoodsDispenseResultItem> goodsDispenseResultItems) {
        Map<String, GoodsDispenseResultItem> dispenseItemIdToGoodsDispenseResultItem = ListUtils.toMap(goodsDispenseResultItems, GoodsDispenseResultItem::getDispenseItemId);

        // 更新处方信息
        dispensingSheet.getDispensingForms().forEach(dispensingForm -> {
            DispensingForm clientUnDispensingForm = dispensingFormIdToClientUnDispensingForm.get(dispensingForm.getId());
            if (clientUnDispensingForm == null) {
                return;
            }
            updateUndispenseToForm(clientUnDispensingForm, dispensingForm, dispenseItemIdToGoodsDispenseResultItem);
        });

        // 统计未退完的发药项数量，主要是判断总共要发的数量和已退的数量是不是相等
        long dispensedAndWaitingCount = dispensingSheet.getDispensingForms()
                .stream()
                .filter(dispensingForm -> dispensingForm.getDispensingFormItems() != null)
                .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream())
                .filter(dispensingFormItem ->
                        dispensingFormItem.getProductType() != Constants.ProductType.COMPOSE_PRODUCT
                                && dispensingFormItem.getStatus() != DispensingFormItem.Status.CANCELED
                                // 同时还不能是历史的退药项
                                && dispensingFormItem.getIsHistoryItem() == 0)
                .collect(
                        Collectors.toMap(
                                item -> {
                                    if (item.getStatus() != DispensingFormItem.Status.UNDISPENSED) {
                                        return item.getId();
                                    } else {
                                        return item.getAssociateFormItemId();
                                    }
                                },
                                item -> {
                                    BigDecimal itemTotalCount = MathUtils.calculateTotalCount(item.getUnitCount(), item.getDoseCount());
                                    if (item.getStatus() != DispensingFormItem.Status.UNDISPENSED) {
                                        return MathUtils.wrapBigDecimal(itemTotalCount, BigDecimal.ZERO);
                                    } else {
                                        return MathUtils.wrapBigDecimal(itemTotalCount, BigDecimal.ZERO).negate();
                                    }
                                },
                                (a, b) -> a.add(b)
                        ))
                .values()
                .stream().filter(value -> value.compareTo(BigDecimal.ZERO) > 0)
                .count();
        // 统计未退完的发药项数量，主要是判断发的数量和已退的数量是不是相等
        long undispensableCount = DispensingUtils.getUndispensableItemCount(dispensingSheet);
        if (dispensedAndWaitingCount == 0) {
            /**
             * cis 逻辑全部退完才算退完
             * */
            dispensingSheet.setStatus(DispensingSheet.Status.UNDISPENSED);
            dispensingSheet.setOrderByDate(Instant.now());
        }
        if (undispensableCount == 0) {
            dispensingSheet.deleteDispensingTag(DispensingUtils.DispensingTag.DISPENSING_TAG_HAS_UNDISPENSABLE);
        }
        dispensingSheet.addDispensingTag(DispensingUtils.DispensingTag.DISPENSING_TAG_HAS_UNDISPENSE);

        dispensingSheet.setChineseMedicineUndispenseType(DispensingUtils.getSheetChineseMedicineUndispenseType(dispensingSheet));
    }

    private DispensingForm updateUndispenseToForm(DispensingForm clientUnDispensingForm, DispensingForm dispensingForm,
                                                  Map<String, GoodsDispenseResultItem> dispenseItemIdToGoodsDispenseResultItem) {
        if (dispensingForm == null || dispensingForm.getDispensingFormItems() == null
                || clientUnDispensingForm == null || clientUnDispensingForm.getDispensingFormItems() == null) {
            return dispensingForm;
        }

        List<DispensingFormItem> addedItems = new ArrayList<>();
        List<DispensingFormItem> addedSelfProvidedItems = new ArrayList<>();

        Map<String, DispensingFormItem> dispenseItemIdToClientUnDispenseFormItem = ListUtils.toMap(clientUnDispensingForm.getDispensingFormItems(), DispensingFormItem::getId);
        dispensingForm.getDispensingFormItems().stream()
                .filter(existedFormItem -> DispensingFormItem.Status.unDispenseable.contains(existedFormItem.getStatus()))
                .forEach(dispenseItem -> {
                    DispensingFormItem clientUnDispenseItem = dispenseItemIdToClientUnDispenseFormItem.get(dispenseItem.getId());
                    if (clientUnDispenseItem == null) {
                        return;
                    }

                    BigDecimal[] undispensedUnitCountAndDoseCount = dispenseItemIdToUnDispenseUnitCountAndDoseCount.get(clientUnDispenseItem.getId());
                    if (undispensedUnitCountAndDoseCount == null) {
                        return;
                    }
                    BigDecimal toUndispenseUnitCount = undispensedUnitCountAndDoseCount[0];
                    BigDecimal toUndispenseDoseCount = undispensedUnitCountAndDoseCount[1];

                    DispensingFormItem undispensedFormItem = new DispensingFormItem();
                    BeanUtils.copyProperties(dispenseItem, undispensedFormItem);
                    FillUtils.fillCreatedBy(undispensedFormItem, operatorId);
                    undispensedFormItem.setOperationId(operationId);
                    undispensedFormItem.setId(clientUnDispenseItem.getAssociateFormItemId());
                    undispensedFormItem.setStatus(DispensingFormItem.Status.UNDISPENSED);
                    /***
                     * 打上内存标记位，标记本次退药
                     * */
                    undispensedFormItem.setThisTimeDispensed(1);
                    undispensedFormItem.setAssociateFormItemId(dispenseItem.getId());
                    GoodsDispenseResultItem goodsDispenseResultItem = dispenseItemIdToGoodsDispenseResultItem.get(dispenseItem.getId());
                    if (goodsDispenseResultItem != null) {
                        undispensedFormItem.setStockDealId(goodsDispenseResultItem.getDealId());
                        undispensedFormItem.setTotalCostPrice(MathUtils.wrapBigDecimalOrZero(goodsDispenseResultItem.getTotalCostPrice()));
                    }
                    undispensedFormItem.setUnitCount(toUndispenseUnitCount);
                    undispensedFormItem.setDoseCount(toUndispenseDoseCount);
                    //设置UndispenseType
                    if (dispenseItem.getProductType() == Constants.ProductType.MEDICINE && dispenseItem.getProductSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE) {
                        undispensedFormItem.setUndispenseType(DispensingFormItem.UndispenseType.BY_DOSE);
                    } else {
                        undispensedFormItem.setUndispenseType(DispensingFormItem.UndispenseType.BY_UNIT);
                    }
                    //自备药品不写入DispensingLog表
                    if (dispenseItem.getSourceItemType() == DispensingFormItem.SourceItemType.SELF_PROVIDED) {
                        addedSelfProvidedItems.add(undispensedFormItem);
                    } else {
                        addedItems.add(undispensedFormItem);
                    }

                    updateUndispenseToDispensingFormItemBatch(undispensedFormItem, dispenseItem, goodsDispenseResultItem);
                });
        dispensingForm.getDispensingFormItems().addAll(addedItems);
        dispensingForm.getDispensingFormItems().addAll(addedSelfProvidedItems);
        logUnDispensingFormItems.addAll(addedItems);
        //退费退药 保留审核和调配状态 前端通过 chargeSheet的stats进行管理
//        updateFormAuditedAndCompoundedStatusWhenUndispensed(existedForm);
        return dispensingForm;
    }

    private void updateUndispenseToDispensingFormItemBatch(DispensingFormItem undispenseItem,
                                                           DispensingFormItem dispenseItem,
                                                           GoodsDispenseResultItem goodsDispenseResultItem) {
        if (dispenseItem == null || CollectionUtils.isEmpty(dispenseItem.getDispensingFormItemBatches())
                || goodsDispenseResultItem == null || CollectionUtils.isEmpty(goodsDispenseResultItem.getDispenseBatchInfoList())) {
            return;
        }

        List<DispensingFormItemBatchInfo> dispensingFormItemBatches = dispenseItem.getDispensingFormItemBatches();
        List<GoodsDispenseResultItem.DispenseBatchInfo> dispenseBatchInfoList = goodsDispenseResultItem.getOriginalDispenseBatchInfoList();
        Map<Long, DispensingFormItemBatchInfo> batchIdToDispensingFormItemBatch = ListUtils.toMap(dispensingFormItemBatches, DispensingFormItemBatchInfo::getBatchId);
        dispenseBatchInfoList.forEach(dispenseBatchResult -> {
            DispensingFormItemBatchInfo dispensingFormItemBatch = batchIdToDispensingFormItemBatch.get(dispenseBatchResult.getBatchId());
            if (dispensingFormItemBatch == null) {
                log.error("退了不存在的批次信息，发药项ID：{}，批次ID：{}", dispenseItem.getId(), dispenseBatchResult.getBatchId());
                throw new DispensingServiceException(DispensingServiceError.DISPENSING_SHEET_CHANGE);
            }

            BigDecimal stockCount = calculateStockCount(dispenseItem, dispenseBatchResult, dispenseItem.getUseDismounting() == 1);
            dispensingFormItemBatch.setUndispenseUnitCount(MathUtils.wrapBigDecimalAdd(dispensingFormItemBatch.getUndispenseUnitCount(), stockCount));
            DispensingFormItemBatchInfo dispensingFormItemBatchInfo = buildUndispenseFormItemBatchInfo(undispenseItem, dispensingFormItemBatch, stockCount, operatorId);
            if (undispenseItem.getDispensingFormItemBatches() == null) {
                undispenseItem.setDispensingFormItemBatches(new ArrayList<>());
            }
            if (dispensingFormItemBatchInfo != null) {
                undispenseItem.getDispensingFormItemBatches().add(dispensingFormItemBatchInfo);
            }
        });
    }

    private DispensingFormItemBatchInfo buildUndispenseFormItemBatchInfo(DispensingFormItem undispenseFormItemItem,
                                                                         DispensingFormItemBatchInfo dispensingFormItemBatchInfo,
                                                                         BigDecimal undispenseUnitCount,
                                                                         String operator) {
        if (dispensingFormItemBatchInfo == null) {
            return null;
        }

        DispensingFormItemBatchInfo undispenseFormItemBatchInfo = new DispensingFormItemBatchInfo();
        BeanUtils.copyProperties(dispensingFormItemBatchInfo, undispenseFormItemBatchInfo);
        undispenseFormItemBatchInfo.setId(abcIdGenerator.getUIDLong());
        undispenseFormItemBatchInfo.setDispensingFormItemId(undispenseFormItemItem.getId());
        undispenseFormItemBatchInfo.setAssociateFormItemBatchInfoId(dispensingFormItemBatchInfo.getId());
        undispenseFormItemBatchInfo.setUnitCount(undispenseUnitCount);
        undispenseFormItemBatchInfo.setUndispenseUnitCount(undispenseUnitCount);
        undispenseFormItemBatchInfo.setDispenseUnitCount(undispenseUnitCount);
        BigDecimal totalPrice = MathUtils.wrapBigDecimalMultiply(undispenseFormItemBatchInfo.getUnitCount(), dispensingFormItemBatchInfo.getUnitPrice());
        undispenseFormItemBatchInfo.setTotalPrice(totalPrice);
        BigDecimal sourceTotalPrice = MathUtils.wrapBigDecimalMultiply(undispenseFormItemBatchInfo.getUnitCount(), dispensingFormItemBatchInfo.getSourceUnitPrice());
        undispenseFormItemBatchInfo.setSourceTotalPrice(sourceTotalPrice);
        FillUtils.fillLastModifiedBy(undispenseFormItemBatchInfo, operator);
        return undispenseFormItemBatchInfo;
    }

    private static BigDecimal calculateStockCount(DispensingFormItem dispensableItem,
                                                  GoodsDispenseResultItem.DispenseBatchInfo dispenseBatchInfoResult,
                                                  boolean isDismounting) {
        Integer pieceNum = dispenseBatchInfoResult.getPieceNum();
        BigDecimal formattedPieceNum = formatPieceNum(pieceNum);
        BigDecimal packageCount = dispenseBatchInfoResult.getPackageCount();
        BigDecimal pieceCount = dispenseBatchInfoResult.getPieceCount();
        if (isDismounting) {
            if (dispenseBatchInfoResult.getClientReqOriginFlatPieceCount() != null) {
                return dispenseBatchInfoResult.getClientReqOriginFlatPieceCount();
            }
            return MathUtils.wrapBigDecimalAdd(MathUtils.wrapBigDecimalMultiply(packageCount, formattedPieceNum), pieceCount);
        } else {
            if (dispenseBatchInfoResult.getClientReqOriginFlatPackageCount() != null) {
                return dispenseBatchInfoResult.getClientReqOriginFlatPackageCount();
            }
            BigDecimal piecePackageCount = BigDecimal.ZERO;
            if (MathUtils.wrapBigDecimalCompare(pieceCount, BigDecimal.ZERO) > 0) {
                // 如果除了以后有余数则报错
                // 由于用户开单就有可能开的是小数的大包装，和界面上支持退的时候退小数的大包装，所以这里只能放开校验
//                boolean checkPieceCount = dispensableItem.getUseDismounting() == 0
//                        && MathUtils.wrapBigDecimalCompare(dispensableItem.getUnitCount().remainder(BigDecimal.ONE), BigDecimal.ZERO) <= 0;
//                if (checkPieceCount && MathUtils.wrapBigDecimalCompare(pieceCount.remainder(formattedPieceNum), BigDecimal.ZERO) > 0) {
//                    log.error("发药项 {} 本次发药批次 {} 的数量 {} 不是整数，dispenseItemId:{}", dispensableItem.getName(), dispenseBatchInfoResult.getBatchId(), JsonUtils.dump(dispenseBatchInfoResult), dispensableItem.getId());
//                    throw new DispensingServiceException(DispensingServiceError.DISPENSE_QUANTITY_ERROR);
//                }
                //几个月过后可以把这个分支删了，这个分支是2025-01之前的发药单
                piecePackageCount = pieceCount.divide(formattedPieceNum, 2, RoundingMode.DOWN);
            }
            return MathUtils.wrapBigDecimalAdd(packageCount, piecePackageCount);
        }
    }

    private static BigDecimal formatPieceNum(Integer pieceNum) {
        if (pieceNum == null || pieceNum <= 0) {
            return BigDecimal.ONE;
        }
        return BigDecimal.valueOf(pieceNum);
    }

    private List<GoodsDispenseResultItem> returnStock() {
        // 构建返还库存请求
        List<GoodsDispenseDataItem> goodsDispenseDataItems = buildGoodsDispenseDataItems();

        // 只有普通发药项才需要返还库存
        goodsDispenseDataItems = goodsDispenseDataItems.stream().filter(goodsitem -> {
            DispensingFormItem dispensedItem = dispensingItemIdToDispensingItem.get(goodsitem.getDispenseItemId());
            return dispensedItem.getSourceItemType() == DispensingFormItem.SourceItemType.NORMAL;
        }).collect(Collectors.toList());

        // 调用 goods 退还库存
        return rpcReturnStock(goodsDispenseDataItems);
    }

    private List<GoodsDispenseResultItem> rpcReturnStock(List<GoodsDispenseDataItem> goodsDispenseDataItems) {
        List<GoodsDispenseResultItem> goodsDispenseResultItems = new ArrayList<>();
        String dispensingSheetId = dispensingSheet.getId();
        if (goodsDispenseDataItems.size() != 0) {
            goodsDispenseResultItems = abcCisScGoodsService.undispense(
                    dispensingSheet.getClinicId(),
                    dispensingSheet.getChainId(),
                    operatorId,
                    patientOrder != null ? patientOrder.getNo() + "" : null,
                    dispensingSheet.getPatientOrderId(),
                    dispensingSheetId,
                    dispensingSheet.getPharmacyNo(),
                    dispensingSheet.getPharmacyType(),
                    goodsDispenseDataItems,
                    DTOConverter.dispensingFormItemMapToDispensingFormItemAbstractMap(dispensingItemIdToDispensingItem),
                    0);
            if (goodsDispenseResultItems == null || goodsDispenseResultItems.size() == 0 || goodsDispenseResultItems.size() != goodsDispenseDataItems.size()) {
                throw new GoodsUndispenseException();
            }
        }
        return goodsDispenseResultItems;
    }

    private List<GoodsDispenseDataItem> buildGoodsDispenseDataItems() {
        // 发药单上的可退药的 item
        List<DispensingFormItem> undispenseableItems = DTOConverter.collectDispensingSheetItemsWithStatus(dispensingSheet, DispensingFormItem.Status.unDispenseable);
        if (undispenseableItems.size() == 0) {
            return new ArrayList<>();
        }

        Map<String, DispenseHelper.DispensingItemDispensePrice> dispensingItemIdToUndispensePrice = DispenseHelper.calculateItemCurrentUndispensePrice(undispenseableItems, dispenseItemIdToClientUnDispenseItem, chargeItemIdToChargeItem);

        List<GoodsDispenseDataItem> goodsDispenseDataItems = undispenseableItems.stream()
                .map(dispensedItem -> {
                    DispensingFormItem clientUnDispenseItem = dispenseItemIdToClientUnDispenseItem.get(dispensedItem.getId());
                    DispenseHelper.DispensingItemDispensePrice undispensePrice = dispensingItemIdToUndispensePrice.get(dispensedItem.getId());
                    if (dispensedItem == null || clientUnDispenseItem == null) {
                        return null;
                    }

                    BigDecimal[] clientUnDispenseUnitCountAndDoseCount = dispenseItemIdToUnDispenseUnitCountAndDoseCount.get(dispensedItem.getId());
                    GoodsDispenseDataItem goodsDispenseDataItem = buildGoodsDispenseDataItem(dispensedItem, clientUnDispenseItem, undispensePrice, clientUnDispenseUnitCountAndDoseCount);
                    dealUnDispenseTraceCode(dispensedItem, clientUnDispenseItem, goodsDispenseDataItem, abcIdGenerator);
                    return goodsDispenseDataItem;
                })
                .filter(Objects::nonNull).collect(Collectors.toList());

        if (goodsDispenseDataItems.size() != clientUnDispenseItems.size()) {
            throw new DispenseSheetChangedException();
        }

        return goodsDispenseDataItems;
    }


    private GoodsDispenseDataItem buildGoodsDispenseDataItem(DispensingFormItem dispensedItem,
                                                             DispensingFormItem clientUnDispenseItem,
                                                             DispenseHelper.DispensingItemDispensePrice undispensePrice,
                                                             BigDecimal[] clientUnDispenseUnitCountAndDoseCount) {
        if (dispensedItem == null || clientUnDispenseItem == null || clientUnDispenseUnitCountAndDoseCount == null) {
            return null;
        }
        Optional<DispenseHelper.DispensingItemDispensePrice> undispensePriceOpt = Optional.ofNullable(undispensePrice);

        BigDecimal toUndispenseUnitCount = clientUnDispenseUnitCountAndDoseCount[0];
        BigDecimal toUndispenseDoseCount = clientUnDispenseUnitCountAndDoseCount[1];
        BigDecimal toUndispenseTotalCount = MathUtils.calculateTotalCount(toUndispenseUnitCount, toUndispenseDoseCount);

        GoodsDispenseDataItem goodsDispenseDataItem = new GoodsDispenseDataItem();
        goodsDispenseDataItem.setPharmacyNo(dispensedItem.getPharmacyNo());
        goodsDispenseDataItem.setDispenseItemId(dispensedItem.getId());
        goodsDispenseDataItem.setUnDispenseItemId(clientUnDispenseItem.getAssociateFormItemId());
        goodsDispenseDataItem.setMedicineName(dispensedItem.getName());
        goodsDispenseDataItem.setDisplaySpec(dispensedItem.getGoodsDispSpec());
        if(dispensedItem.getSnapPieceNum() != null){
            goodsDispenseDataItem.setPieceNum(dispensedItem.getSnapPieceNum().intValue());
        }
        //从快照上取规格
        goodsDispenseDataItem.setPieceUnit(dispensedItem.getSnapPieceUnit());
        goodsDispenseDataItem.setPackageUnit(dispensedItem.getSnapPackageUnit());

        goodsDispenseDataItem.setGoodsId(dispensedItem.getProductId());
        if (dispensedItem.getUseDismounting() == 1) {
            goodsDispenseDataItem.setPieceCount(toUndispenseTotalCount);
            goodsDispenseDataItem.setPackageCount(BigDecimal.ZERO);
        } else {
            goodsDispenseDataItem.setPackageCount(toUndispenseTotalCount);
            goodsDispenseDataItem.setPieceCount(BigDecimal.ZERO);
        }
        goodsDispenseDataItem.setSourceSheetId(dispensingSheet.getSourceSheetId());
        goodsDispenseDataItem.setSourceItemId(dispensedItem.getSourceFormItemId());

        List<DispensingFormItemBatchInfo> clientDispensingFormItemBatches = clientUnDispenseItem.getDispensingFormItemBatches();
        List<DispensingFormItemBatchInfo> dispensingFormItemBatches = dispensedItem.getDispensingFormItemBatches();
        if (!CollectionUtils.isEmpty(dispensingFormItemBatches) && !CollectionUtils.isEmpty(clientDispensingFormItemBatches)) {
            Map<Long, DispensingFormItemBatchInfo> batchIdToBatch = ListUtils.toMap(dispensingFormItemBatches, DispensingFormItemBatchInfo::getBatchId);
            Map<Long, BigDecimal> batchIdToDispenseTotalPrice = undispensePriceOpt.map(DispenseHelper.DispensingItemDispensePrice::getBatchIdToDispenseTotalPrice).orElse(Collections.emptyMap());
            List<GoodsDispenseDataItem.BatchCount> batchCounts = clientDispensingFormItemBatches.stream().map(clientDispensingBatchInfo -> {
                DispensingFormItemBatchInfo batchInfo = batchIdToBatch.get(clientDispensingBatchInfo.getBatchId());
                GoodsDispenseDataItem.BatchCount batchCount = new GoodsDispenseDataItem.BatchCount();
                batchCount.setBatchId(clientDispensingBatchInfo.getBatchId());
                BigDecimal unitCount = clientDispensingBatchInfo.getUnitCount();
                if (dispensedItem.getUseDismounting() == 1) {
                    batchCount.setPieceCount(unitCount);
                } else {
                    batchCount.setPackageCount(unitCount);
                }
                batchCount.setBatchTotalPrice(batchIdToDispenseTotalPrice.getOrDefault(clientDispensingBatchInfo.getBatchId(), BigDecimal.ZERO));
                batchCount.setTotalPieceCount(batchInfo.getUnitCount());
                return batchCount;
            }).collect(Collectors.toList());
            goodsDispenseDataItem.setBatchCountList(batchCounts);
        }

        // 计算退药的实收金额
        goodsDispenseDataItem.setTotalPrice(undispensePriceOpt.map(DispenseHelper.DispensingItemDispensePrice::getDispenseTotalPrice).orElse(BigDecimal.ZERO));
        // 拆零标识应该使用发药的，这样才能保证发药和退药的一致性
        goodsDispenseDataItem.setShebaoDismountingFlag(dispensedItem.getShebaoDismountingFlag());
        return goodsDispenseDataItem;
    }

    private void doUnDispensePreProcess() {
        // 提前生成退药项的ID
        preGenUnDispenseItemId();
        // 补全“无码”追溯码
        DispensingUtils.fillNoTraceableCode(clientUnDispenseItems, dispensingItemIdToDispensingItem, goodsConfig);
    }

    private void preGenUnDispenseItemId() {
        clientUnDispenseItems.forEach(unDispenseItem -> unDispenseItem.setAssociateFormItemId(abcIdGenerator.getUUID()));
    }

    private void load() {
        // 加载发药单
        if (dispensingSheet == null) {
            this.dispensingSheet = dispensingSheetEntityService.findByIdAndClinicId(dispensingSheetId, clinicId);
        }
        if (dispensingSheet == null) {
            throw new NoExistedSheetException();
        }

        patientOrder = patientOrderService.findPatientOrder(dispensingSheet.getPatientOrderId());
        DispensingUtils.checkChargeSheetIsRefunding(patientOrderService.listLocksByPatientOrderId(dispensingSheet.getPatientOrderId(), dispensingSheet.getChainId(), dispensingSheet.getClinicId()));

        this.dispensingItems = DTOConverter.collectDispensingSheetItems(dispensingSheet);
        this.dispensingItemIdToDispensingItem = ListUtils.toMap(dispensingItems, DispensingFormItem::getId);

        // 全部发药和部分发药支持退药
        if (dispensingSheet.getStatus() != DispensingSheet.Status.DISPENSED && dispensingSheet.getStatus() != DispensingSheet.Status.WAITING) {
            throw new WrongSheetStatusException();
        }
        this.beforeDispensingSheetStatus = DispensingTodoService.DispensingSheetStatus.of(dispensingSheet);
        // 查询商品信息
        dispensingSheet.refreshProductInfo(scGoodsFeignClient);
        DispensingUtils.doWithDispensingItem(dispensingSheet, (clientItem) -> {
            DispensingFormItem dispensingFormItem = dispensingItemIdToDispensingItem.get(clientItem.getId());
            if (dispensingFormItem == null) {
                return;
            }
            dispensingFormItem.setGoodsItem(clientItem.getGoodsItem());
        });

        // 加载收费单
        if (chargeSheet == null) {
            this.chargeSheet = chargeService.getChargeSheetById(dispensingSheet.getSourceSheetId());
        }
        if (chargeSheet == null) {
            log.error("发药单 [{}] 的收费单 [{}] 不存在", dispensingSheetId, dispensingSheet.getSourceSheetId());
            throw new NotFoundException("收费单不存在");
        }
        this.chargeItemIdToChargeItem = chargeSheet.getChargeForms().stream()
                .map(ChargeForm::getChargeFormItems).filter(Objects::nonNull)
                .flatMap(Collection::stream).filter(Objects::nonNull)
                .collect(Collectors.toMap(ChargeFormItem::getId, Function.identity(), (a, b) -> a));

        // 加载日志
        this.dispenseOperations = dispensingSheetOperationRepository.findAllByDispensingSheetIdAndIsDeletedOrderByCreatedDescOperationTypeDesc(dispensingSheetId, 0);
        this.dispenseItemLogs = dispensingLogRepository.findAllByDispensingSheetIdAndType(dispensingSheetId, DispensingLogItem.Type.DISPENSE);

        this.goodsConfig = scGoodsFeignClient.getGoodsConfig(clinicId);
        // 加载和校验每个发药项退药的数量
        this.loadAndCheckAllUnDispensedUnitCountAndDoseCount();
    }

    private void loadAndCheckAllUnDispensedUnitCountAndDoseCount() {
        // 发药的FormItemId -> 已退UnitCount
        Map<String, BigDecimal> dispenseItemIdToUnDispensedUnitCount = DispensingUtils.getUndispensedCountIdMapByUndispenseType(dispensingItems, DispensingFormItem.UndispenseType.BY_UNIT);

        // 发药的FormItemId -> 已退DoseCount
        Map<String, BigDecimal> dispenseItemIdToUnDispensedDoseCount;
        boolean hasChinesePrescription = dispensingSheet.getDispensingForms().stream().anyMatch(existedForm -> existedForm.getSourceFormType() == DispensingForm.SourceFormType.PRESCRIPTION_CHINESE);
        if (hasChinesePrescription) {
            dispenseItemIdToUnDispensedDoseCount = DispensingUtils.getUndispensedCountIdMapByUndispenseType(dispensingItems, DispensingFormItem.UndispenseType.BY_DOSE);
        } else {
            dispenseItemIdToUnDispensedDoseCount = new HashMap<>();
        }

        // 校验退药数量是否超过可退数量
        this.dispenseItemIdToUnDispenseUnitCountAndDoseCount = new HashMap<>(clientUnDispenseItems.size());
        clientUnDispenseItems.forEach(clientUnDispenseItem -> {
            String dispenseItemId = clientUnDispenseItem.getId();
            DispensingFormItem dispenseItem = dispensingItemIdToDispensingItem.get(dispenseItemId);
            if (dispenseItem == null) {
                return;
            }
            BigDecimal[] unDispenseUnitCountAndDoseCount = unDispensingService.getToUndispenseUnitCountAndDoseCount(dispensingSheet.getPharmacyType(), dispenseItemIdToUnDispensedUnitCount, dispenseItemIdToUnDispensedDoseCount, dispenseItem, clientUnDispenseItem);
            if (unDispenseUnitCountAndDoseCount == null) {
                return;
            }
            // 校验退药批次数量
            this.checkDispensingFormItemBatchDispenseCount(clientUnDispenseItem, dispenseItem);
            dispenseItemIdToUnDispenseUnitCountAndDoseCount.put(dispenseItemId, unDispenseUnitCountAndDoseCount);
        });
        if (Objects.nonNull(this.dispensingConfig) && this.dispensingConfig.getWholeSheetOperateEnable() == YesOrNo.YES) {
            List<DispensingFormItem> availableDispensingFormItem = dispensingItems.stream()
                    .filter(dispensingFormItem -> dispensingFormItem.getIsHistoryItem() != 1 && dispensingFormItem.getComposeType() != ComposeType.COMPOSE)
                    .collect(Collectors.toList());
            Map<String, DispensingFormItem> availableItemMap = availableDispensingFormItem.stream()
                    .filter(item -> StringUtils.isBlank(item.getAssociateFormItemId()))
                    .collect(Collectors.toMap(DispensingFormItem::getId, Function.identity(), (first, second) -> first));
            Map<String, DispensingFormItem> refundItemMap = availableDispensingFormItem.stream()
                    .filter(item -> StringUtils.isNotBlank(item.getAssociateFormItemId()))
                    .collect(Collectors.toMap(DispensingFormItem::getAssociateFormItemId, Function.identity(), (first, second) -> first));
            Set<String> dispensingFormItemIds = new HashSet<>();
            availableItemMap.forEach((id, item) -> {
                DispensingFormItem refundItem = refundItemMap.get(id);
                if (Objects.nonNull(refundItem)) {
                    boolean unDispenseOver = MathUtils.isZeroOrNull(
                            MathUtils.wrapBigDecimalSubtract(
                                    MathUtils.calculateTotalCount(item.getUnitCount(), item.getDoseCount()),
                                    MathUtils.calculateTotalCount(refundItem.getUnitCount(), refundItem.getDoseCount())
                            )
                    );
                    // 该项没有退完
                    if (!unDispenseOver) {
                        dispensingFormItemIds.add(id);
                    }
                } else {
                    // 表示不存在退药项
                    dispensingFormItemIds.add(id);
                }
            });
            Set<String> clientDispensingFormItemIds = clientUnDispenseItems.stream()
                    .map(DispensingFormItem::getId)
                    .collect(Collectors.toSet());
            if (dispensingFormItemIds.size() != clientDispensingFormItemIds.size() || !clientDispensingFormItemIds.containsAll(dispensingFormItemIds)) {
                throw new DispensingServiceException(DispensingServiceError.DISPENSING_SHEET_NOT_SUPPORT_PARTIAL_UNDISPENSE);
            }
        }
    }

    private void checkDispensingFormItemBatchDispenseCount(DispensingFormItem clientUnDispenseItem, DispensingFormItem dispenseItem) {
        if (clientUnDispenseItem == null || dispenseItem == null) {
            return;
        }
        if (CollectionUtils.isEmpty(dispenseItem.getDispenseFormItemBatches())) {
            // 由于门诊发药一定是先锁批次再发药，所以如果没有记录锁的批次则认为不支持按批次发药，但是如果客户端传入了批次信息，清空批次信息
            clientUnDispenseItem.setDispensingFormItemBatches(new ArrayList<>());
            return;
        } else if (CollectionUtils.isEmpty(clientUnDispenseItem.getDispensingFormItemBatches())) {
            // 如果客户端没有传入批次信息，则走一次发药这边的默认选批次逻辑
            clientUnDispenseItem.setDispensingFormItemBatches(DispenseHelper.recommendUndispenseBatches(MathUtils.wrapBigDecimalMultiply(clientUnDispenseItem.getUnitCount(), clientUnDispenseItem.getDoseCount()), dispenseItem.getDispenseFormItemBatches()));
        }

        Map<Long, DispensingFormItemBatchInfo> batchIdToDispenseItemBatch = ListUtils.toMap(dispenseItem.getDispenseFormItemBatches(), DispensingFormItemBatchInfo::getBatchId);
        BigDecimal clientDispenseFormItemBatchTotalUnitCount = BigDecimal.ZERO;
        for (DispensingFormItemBatchInfo clientDispensingFormItemBatch : clientUnDispenseItem.getDispensingFormItemBatches()) {
            if (clientDispensingFormItemBatch == null) {
                return;
            }

            Long batchId = clientDispensingFormItemBatch.getBatchId();
            DispensingFormItemBatchInfo dispenseItemBatch = batchIdToDispenseItemBatch.get(batchId);
            if (dispenseItemBatch == null) {
                log.error("发药单 [{}] 处方 [{}] 发药项 [{}] 的批次 [{}] 不存在", dispensingSheetId, clientUnDispenseItem.getDispensingFormId(), clientUnDispenseItem.getId(), batchId);
                throw new DispenseSheetChangedException();
            }

            BigDecimal remainUnitCount = dispenseItemBatch.getRemainUndispenseUnitCount();
            BigDecimal clientDispensingFormItemBatchUnitCount = clientDispensingFormItemBatch.getUnitCount();
            if (MathUtils.wrapBigDecimalCompare(clientDispensingFormItemBatchUnitCount, remainUnitCount) > 0) {
                log.error("发药单 [{}] 处方 [{}] 发药项 [{}] 的批次 [{}] 退药数量 [{}] 大于剩余可退药数量 [{}]",
                        dispensingSheetId, clientUnDispenseItem.getDispensingFormId(), clientUnDispenseItem.getId(), batchId, clientDispensingFormItemBatchUnitCount, remainUnitCount);
                throw new DispenseSheetChangedException();
            }

            clientDispenseFormItemBatchTotalUnitCount = MathUtils.wrapBigDecimalAdd(clientDispenseFormItemBatchTotalUnitCount, clientDispensingFormItemBatchUnitCount);
        }

        BigDecimal clientDispenseFormItemUnitCount = MathUtils.wrapBigDecimalMultiply(clientUnDispenseItem.getUnitCount(), clientUnDispenseItem.getDoseCount());
        if (MathUtils.wrapBigDecimalCompare(clientDispenseFormItemBatchTotalUnitCount, clientDispenseFormItemUnitCount) != 0) {
            log.error("发药单 [{}] 处方 [{}] 发药项 [{}] 的批次退药数量 [{}] 与发药项退药数量 [{}] 不一致",
                    dispensingSheetId, clientUnDispenseItem.getDispensingFormId(), clientUnDispenseItem.getId(), clientDispenseFormItemBatchTotalUnitCount, clientDispenseFormItemUnitCount);
            throw new DispenseSheetChangedException();
        }
    }

    @Accessors(chain = true)
    public static class UnDispenseOptBuilder {

        /**
         * 客户端传入的退药单
         */
        private DispensingSheet clientUnDispenseSheet;

        /**
         * 诊所ID
         */
        private String clinicId;

        /**
         * 操作人ID
         */
        private String operatorId;

        /**
         * 数据库中的发药单
         */
        @Setter
        private DispensingSheet dispensingSheet;

        /**
         * 发药的配置信息
         */
        @Setter
        private Dispensing dispensingConfig;

        public static UnDispenseOptBuilder of(DispensingSheet clientUnDispenseSheet, String clinicId, String operatorId) {
            if (clientUnDispenseSheet == null || TextUtils.isEmpty(clientUnDispenseSheet.getId())) {
                throw new ParamRequiredException("id");
            }
            UnDispenseOptBuilder unDispenseOptBuilder = new UnDispenseOptBuilder();
            unDispenseOptBuilder.clientUnDispenseSheet = clientUnDispenseSheet;
            unDispenseOptBuilder.clinicId = clinicId;
            unDispenseOptBuilder.operatorId = operatorId;
            return unDispenseOptBuilder;
        }

        public UnDispenseOpt build() {
            UnDispenseOpt unDispenseOpt = SpringUtils.getBean(UnDispenseOpt.class);
            unDispenseOpt.dispensingSheetId = clientUnDispenseSheet.getId();
            unDispenseOpt.clientUnDispenseSheet = clientUnDispenseSheet;
            unDispenseOpt.clinicId = clinicId;
            unDispenseOpt.operatorId = operatorId;
            unDispenseOpt.dispensingSheet = dispensingSheet;
            List<DispensingFormItem> clientUnDispenseItems = DTOConverter.collectDispensingSheetItems(clientUnDispenseSheet);
            unDispenseOpt.dispensingFormIdToClientUnDispensingForm = ListUtils.toMap(clientUnDispenseSheet.getDispensingForms(), DispensingForm::getId);
            unDispenseOpt.clientUnDispenseItems = clientUnDispenseItems;
            unDispenseOpt.dispenseItemIdToClientUnDispenseItem = ListUtils.toMap(clientUnDispenseItems, DispensingFormItem::getId);
            AbcIdGenerator abcIdGenerator = SpringUtils.getBean(AbcIdGenerator.class);
            unDispenseOpt.operationId = abcIdGenerator.getUID();
            unDispenseOpt.dispensingConfig = dispensingConfig;

            return unDispenseOpt;
        }
    }

}
