package cn.abcyun.cis.dispensing.service.dispense;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeConstants;
import cn.abcyun.bis.rpc.sdk.cis.model.common.BitFlagUtils;
import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispenseConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsDispenseDataItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsDispenseResultItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.TraceableCode;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.config.GoodsConfigView;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.config.LockConfig;
import cn.abcyun.bis.rpc.sdk.property.model.Dispensing;
import cn.abcyun.bis.rpc.sdk.property.model.TraceCodeConfig;
import cn.abcyun.cis.commons.amqp.message.PatientOrderMessage;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamNotValidException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.cis.commons.rpc.charge.ChargeForm;
import cn.abcyun.cis.commons.rpc.charge.ChargeFormItem;
import cn.abcyun.cis.commons.rpc.charge.ChargeSheet;
import cn.abcyun.cis.commons.rpc.charge.Constants;
import cn.abcyun.cis.commons.transform.ShebaoDismountingFlagConst;
import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.commons.util.MergeTool;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.core.util.SpringUtils;
import cn.abcyun.cis.dispensing.amqp.HAMQProducer;
import cn.abcyun.cis.dispensing.amqp.RocketMqProducer;
import cn.abcyun.cis.dispensing.base.DispensingConstants;
import cn.abcyun.cis.dispensing.base.exception.*;
import cn.abcyun.cis.dispensing.controller.DTOConverter;
import cn.abcyun.cis.dispensing.domain.*;
import cn.abcyun.cis.dispensing.listener.DispensingSheetListener;
import cn.abcyun.cis.dispensing.repository.DispensingLogRepository;
import cn.abcyun.cis.dispensing.repository.DispensingSheetOperationRepository;
import cn.abcyun.cis.dispensing.service.*;
import cn.abcyun.cis.dispensing.service.dto.DispenseResult;
import cn.abcyun.cis.dispensing.service.dto.operation.OperationRecordForDispense;
import cn.abcyun.cis.dispensing.service.dto.operation.OperationRecordForReDispense;
import cn.abcyun.cis.dispensing.service.huarun.SyncHuaRunOrderService;
import cn.abcyun.cis.dispensing.service.operation.SheetOperationCreateFactory;
import cn.abcyun.cis.dispensing.service.operation.SheetOperationDispense;
import cn.abcyun.cis.dispensing.service.operation.SheetOperationReDispense;
import cn.abcyun.cis.dispensing.service.rpc.CisMonitorService;
import cn.abcyun.cis.dispensing.service.rpc.GoodsLockingFeignClient;
import cn.abcyun.cis.dispensing.service.rpc.ScGoodsFeignClient;
import cn.abcyun.cis.dispensing.service.tianshentai.SyncTianShenTaiOrderService;
import cn.abcyun.cis.dispensing.smartdispensing.config.HuaRunConfig;
import cn.abcyun.cis.dispensing.util.*;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 发药操作
 *
 * <AUTHOR>
 * @date 2023/6/20 10:42
 **/
@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Component
@Scope("prototype")
public class DispenseOpt extends DispensingOpsBase {
    //********************* Bean-START ****************************

    @Autowired
    private DispensingSheetEntityService dispensingSheetEntityService;

    @Autowired
    private ChargeService chargeService;

    @Autowired
    private GoodsLockingFeignClient goodsLockingFeignClient;

    @Autowired
    private AbcCisScGoodsService abcCisScGoodsService;

    @Autowired
    private CisMonitorService cisMonitorService;

    @Autowired
    private DispensingLogService dispensingLogService;

    @Autowired
    private SheetOperationCreateFactory sheetOperationCreateFactory;

    @Autowired
    private DispensingService dispensingService;

    @Autowired
    private SyncTianShenTaiOrderService syncTianShenTaiOrderService;

    @Autowired
    private SyncHuaRunOrderService syncHuaRunOrderService;

    @Autowired
    private RocketMqProducer rocketMqProducer;

    @Autowired
    private HAMQProducer hamqProducer;

    @Autowired
    private DispensingSheetOperationRepository dispensingSheetOperationRepository;

    @Autowired
    private DispensingLogRepository dispensingItemLogRepository;

    @Autowired
    private DispensingTodoService dispensingTodoService;

    @Autowired
    private List<DispensingSheetListener> dispensingSheetListeners;

    @Autowired
    private AbcIdGenerator abcIdGenerator;

    @Autowired
    private ScGoodsFeignClient scGoodsFeignClient;

    @Autowired
    private PatientOrderService patientOrderService;

    //********************* Bean-END ****************************

    /**
     * 操作ID
     */
    @NotNull
    private String operationId;

    /**
     * 重新发药操作ID
     * 提前生成这个ID的原因就是因为在重新发药同时发药时会同时记录重新发药的操作日志和发药的操作日志，
     * 由于发药的操作日志ID是提前生成的，所以重新发药的操作日志ID也需要提前生成，并且重新发药的操作日志ID需要在发药的操作日志ID之前生成
     * 否则在按照创建时间排序的时候会出现重新发药的操作日志ID比发药的操作日志ID晚生成
     */
    private String redispenseOperationId;

    /**
     * 诊所ID
     */
    @NotNull
    private String clinicId;

    /**
     * 发药单ID
     */
    @NotNull
    private String dispensingSheetId;

    /**
     * 客户端发药请求
     */
    @NotNull
    private DispensingSheet clientDispenseSheet;

    /**
     * 客户端发药项请求
     */
    @NotNull
    private List<DispensingFormItem> clientDispenseItems;

    /**
     * 发药项ID -> 发药项客户端发药请求
     */
    @NotNull
    private Map<String, DispensingFormItem> dispenseItemIdToClientDispenseItem;

    /**
     * 是否是收费直接发药
     */
    private boolean isDirectDispenseChargeSheet;

    /**
     * 发药人ID列表
     */
    @NotNull
    private List<String> dispensedByIds;

    /**
     * 操作人ID
     */
    @NotNull
    private String operatorId;

    /**
     * 是否是重新发药
     */
    private int isReDispense;

    /**
     * 发药单重新发药次数
     */
    private int dispensingSheetReDispenseCount;

    /**
     * 本次发药操作影响的发药项
     */
    private List<DispensingFormItem> thisTimeDispensingFormItems = new ArrayList<>();

    /**
     * DB 中的发药单
     */
    private DispensingSheet dispensingSheet;

    /**
     * 原来发药单状态
     */
    private DispensingTodoService.DispensingSheetStatus beforeDispensingSheetStatus;

    /**
     * 发药单所有可发药项
     */
    private List<DispensingFormItem> dispensableDispenseItems;

    /**
     * 发药项ID -> 可发药的发药项
     */
    private Map<String, DispensingFormItem> dispenseItemIdToDispensableDispenseItem;

    /**
     * 发药项ID -> 处方
     */
    private Map<String, DispensingForm> dispenseItemIdToDispensingForm;

    /**
     * 处方ID -> 处方
     */
    private Map<String, DispensingForm> dispensingFormIdToDispensingForm;

    /**
     * 发药单对应的收费单
     */
    private ChargeSheet chargeSheet;

    /**
     * 收费项ID -> 收费项
     */
    Map<String, ChargeFormItem> chargeItemIdToChargeItem;

    /**
     * 发药单的所有操作日志
     */
    private List<DispensingSheetOperation> dispensingSheetOperations;

    /**
     * 所有发药项的所有发药日志
     */
    private List<DispensingLogItem> dispensingItemDispenseLogs;

    /**
     * 发药项ID -> 历史的发药 stockDealId 列表
     */
    private Map<String, List<String>> dispenseItemIdToHistoryDispenseStockDealIds;

    /**
     * 已退费的发药项ID列表
     */
    private Set<String> refundFeeDispensingFormItemIds = new HashSet<>();

    /**
     * goods 配置
     */
    private GoodsConfigView goodsConfig;

    /**
     * 发药配置
     */
    private Dispensing dispensingConfig;
    /**
     * 追溯码配置
     */
    private TraceCodeConfig traceCodeConfig;

    /**
     * 发药
     */
    public DispenseResult dispense() {
        // 数据加载和数据预处理
        this.loadAndPreProcess();
        // 请求参数校验
        this.paramCheck();
        // 发药
        DispenseResult dispenseResult = this.doDispense();
        // 推送发送结果
        this.pushDispenseResult();
        // 返回发药结果
        return dispenseResult;
    }

    private DispenseResult buildDispenseResult(List<GoodsDispenseResultItem> goodsDispenseResultItems) {
        DispenseResult dispenseResult = new DispenseResult();
        dispenseResult.setStatus(dispensingSheet.getStatus());
        dispenseResult.setDispensedCount(goodsDispenseResultItems.size());
        dispenseResult.setId(dispensingSheetId);
        return dispenseResult;
    }

    private void pushDispenseResult() {
        // 推送给华润39
        sendDispensingBroadCastToPushToHuaRunIfNeeded();
        // 推送给天慎泰
        sendDispensingBroadCastToPushToTianShenTaiIfNeeded();
        // 推送待办统计
        sendTodoCount();

        // 推送发药单更新事件，具体这个发药单是不是全部发完，要看sheet里面的状态
        hamqProducer.sendDispensingSheetUpdate(PatientOrderMessage.MSG_SUB_TYPE_DISPENGING_UPDATED_DISPENSE, dispensingSheet, operatorId, isReDispense);
        rocketMqProducer.broadcastDispensingSheetStatusChange(dispensingSheet, isDirectDispenseChargeSheet, operatorId);

        RocketMqProducer.doAfterTransactionCommit(() -> {
            for (DispensingSheetListener dispensingSheetListener : dispensingSheetListeners) {
                if (dispensingSheet.getStatus() == DispensingSheet.Status.DISPENSED && isReDispense == 0) {
                    dispensingSheetListener.onDispensingSheetDispensedAfterCommit(dispensingSheet.getChainId(), clinicId, dispensingSheet, operatorId);
                }
            }
        });
    }

    private void sendTodoCount() {
        if (dispensingSheet.getStatus() == DispensingSheet.Status.DISPENSED) {
            // 通知
            List<String> subKeyList = new ArrayList<>();
            int action = isReDispense == 1 ? DispensingTodoService.Action.REDISPENSE : DispensingTodoService.Action.DISPENSE;
            // 因为统计待办的时候是发药之前的 orderByDate 统计的，所以在扣减的时候需要使用发药之前的 orderByDate
            boolean sendTodoMessage = dispensingTodoService.getWaitingTodoSubKey(beforeDispensingSheetStatus.getOrderByDate(),
                    dispensingSheet.getIsOnline(), dispensingSheet.getDeliveredStatus(),
                    dispensingSheet.getProcessedStatus(), subKeyList, action, beforeDispensingSheetStatus,
                    DispensingTodoService.DispensingSheetStatus.of(dispensingSheet));
            RocketMqProducer.doAfterTransactionCommit(() -> {
                if (action == DispensingTodoService.Action.DISPENSE) {
                    dispensingTodoService.addDecPharmacyTodoCountTask(dispensingSheet.getChainId(), clinicId,
                            dispensingSheet.getPharmacyNo(), operatorId, subKeyList, sendTodoMessage);
                } else {
                    dispensingTodoService.addIncPharmacyTodoCountTask(dispensingSheet.getChainId(), clinicId,
                            dispensingSheet.getPharmacyNo(), operatorId, subKeyList, sendTodoMessage);
                }
            });
        } else if (isReDispense == 1 && dispensingSheet.getStatus() == DispensingSheet.Status.WAITING) {
            // 通知，按道理不应该能走到这个逻辑了
            List<String> subKeyList = new ArrayList<>();
            boolean sendTodoMessage = dispensingTodoService.getWaitingTodoSubKey(dispensingSheet.getOrderByDate(),
                    dispensingSheet.getIsOnline(), dispensingSheet.getDeliveredStatus(),
                    dispensingSheet.getProcessedStatus(), subKeyList, DispensingTodoService.Action.REDISPENSE,
                    beforeDispensingSheetStatus, DispensingTodoService.DispensingSheetStatus.of(dispensingSheet));
            RocketMqProducer.doAfterTransactionCommit(() -> {
                dispensingTodoService.addIncPharmacyTodoCountTask(dispensingSheet.getChainId(), clinicId,
                        dispensingSheet.getPharmacyNo(), operatorId, subKeyList, sendTodoMessage);
            });
        }

    }

    public void sendDispensingBroadCastToPushToHuaRunIfNeeded() {
        if (CollectionUtils.isEmpty(dispensingSheet.getDispensingForms())) {
            return;
        }

        if (dispensingSheet.getStatus() != DispensingSheet.Status.DISPENSED) {
            return;
        }

        HuaRunConfig.HuaRunVendor huaRunVendor = syncHuaRunOrderService.findSyncVendor(dispensingSheet.getClinicId());
        if (huaRunVendor == null || huaRunVendor.getEnable() == 0) {
            return;
        }

        //是否有中药处方
        boolean needPushToHuaRun = dispensingSheet.getDispensingForms().stream().anyMatch(dispensingForm -> dispensingForm.getSourceFormType() == DispensingForm.SourceFormType.PRESCRIPTION_CHINESE);
        if (!needPushToHuaRun) {
            return;
        }
        rocketMqProducer.broadcastDispensingSheetPushToHuaRun(dispensingSheet, operatorId);
    }

    private void sendDispensingBroadCastToPushToTianShenTaiIfNeeded() {
        if (CollectionUtils.isEmpty(dispensingSheet.getDispensingForms())
                || dispensingSheet.getPharmacyType() != GoodsConst.PharmacyType.VIRTUAL_PHARMACY) { //参数
            return;
        }
        if (dispensingSheet.getStatus() != DispensingSheet.Status.DISPENSED) {
            return;
        }
        boolean needPush = syncTianShenTaiOrderService.checkClinicNeedSyncToTianShenTai(dispensingSheet.getClinicId());
        if (!needPush) {
            return;
        }

        //是否有中药处方
        boolean needPushToHuaRun = dispensingSheet.getDispensingForms().stream().anyMatch(dispensingForm -> dispensingForm.getSourceFormType()
                == cn.abcyun.cis.commons.rpc.charge.Constants.SourceFormType.PRESCRIPTION_CHINESE);
        if (!needPushToHuaRun) {
            return;
        }
        rocketMqProducer.sendDispensingSheetPushToTianShenTai(dispensingSheet, operatorId);
    }

    private DispenseResult doDispense() {
        // 扣库
        List<GoodsDispenseResultItem> goodsDispenseResultItems = deductStock();

        // 自备发药项
        List<DispensingFormItem> selfProvidedToDispenseItems = clientDispenseItems.stream()
                .filter(clientDispenseItem -> dispenseItemIdToDispensableDispenseItem.get(clientDispenseItem.getId()).getSourceItemType() == DispensingFormItem.SourceItemType.SELF_PROVIDED)
                .collect(Collectors.toList());

        // 更新发药单
        updateDispenseToSheet(goodsDispenseResultItems, selfProvidedToDispenseItems);

        // 记录日志
        log(goodsDispenseResultItems);

        // 智能发药
        smartDispenseIfNeeded();

        // 释放库存锁
        releaseStockLockIfNeeded();

        // 更新追溯码
        updateTraceableCode();

        // 返回发药结果
        return buildDispenseResult(goodsDispenseResultItems);
    }

    private void updateTraceableCode() {
        if (dispensingSheet.getStatus() != DispensingSheet.Status.DISPENSED) {
            return;
        }

        // 在发药完成后，再更新一次追溯码，强保证追溯码一致性
        RocketMqProducer.doAfterTransactionCommit(() -> {
            // 无脑直接查询然后更新追溯码使用量
            long startTime = System.currentTimeMillis();
            List<DispensingSheet> dispensingSheets = dispensingSheetEntityService.findAllByChainIdSourceSheetId(dispensingSheet.getChainId(), dispensingSheet.getSourceSheetId());
            abcCisScGoodsService.updateUseTraceCode(dispensingSheet.getChainId(), dispensingSheet.getClinicId(), dispensingSheets, operatorId);
            log.info("updateUseTraceCode cost:{}ms", System.currentTimeMillis() - startTime);
        });
    }

    private void releaseStockLockIfNeeded() {
        if (dispensingSheet.getStatus() == DispenseConst.Status.DISPENSED) {
            // 发药解锁-不用再解锁进销存
            goodsLockingFeignClient.chargeTryUnLockGoodsStock(
                    dispensingSheet.getChainId(),
                    dispensingSheet.getClinicId(),
                    operatorId,
                    Collections.singletonList(dispensingSheet),
                    YesOrNo.YES
            );
        }
    }

    private void smartDispenseIfNeeded() {
        dispensingService.smartDispensing(dispensingSheet, () -> chargeSheet, thisTimeDispensingFormItems, operatorId, DispensingUtils.SmartDispensingScene.DISPENSE);
    }

    private void log(List<GoodsDispenseResultItem> goodsDispenseResultItems) {
        // 记录发药项发药日志
        dispensingLogService.logDispensingSheetDispense(dispensingSheet, chargeSheet, thisTimeDispensingFormItems, dispenseItemIdToClientDispenseItem, goodsDispenseResultItems, operationId, operatorId);

        // 记录重新发药操作日志
        if (isReDispense == 1) {
            SheetOperationReDispense sheetOperationReDispense = (SheetOperationReDispense) sheetOperationCreateFactory.createSheetOperation(DispensingSheetOperation.OperationType.RE_DISPENSE, dispensingSheetId, operatorId, operatorId, dispensingSheet.getChainId(), dispensingSheet.getClinicId());
            sheetOperationReDispense.bindDispensingSheetReDispenseCount(dispensingSheetReDispenseCount);
            sheetOperationReDispense.bindRepoId(redispenseOperationId);
            sheetOperationReDispense.createAndSaveSheetOperation();
        }

        // 记录发药操作日志
        SheetOperationDispense sheetOperationDispense = (SheetOperationDispense) sheetOperationCreateFactory.createSheetOperation(DispensingSheetOperation.OperationType.SEND_MEDICINE, dispensingSheetId, operatorId, operatorId, dispensingSheet.getChainId(), dispensingSheet.getClinicId());
        sheetOperationDispense.bindDispensingSheet(dispensingSheet)
                .bindDispensingFormItems(thisTimeDispensingFormItems)
                .bindDispenseItemIdToClientDispenseItemMap(dispenseItemIdToClientDispenseItem)
                .bindDispensedByIds(dispensedByIds)
                .bindRepoId(operationId)
                .bindDispensingSheetReDispenseCount(dispensingSheetReDispenseCount);
        sheetOperationDispense.createAndSaveSheetOperation();
    }

    private void updateDispenseToSheet(List<GoodsDispenseResultItem> goodsDispenseResultItems, List<DispensingFormItem> selfProvidedToDispenseItems) {
        // 根据 goods 的扣库结果将发药结果写回到发药单
        Map<String, GoodsDispenseResultItem> dispenseItemIdToGoodsDispenseResultItem = ListUtils.toMap(goodsDispenseResultItems, GoodsDispenseResultItem::getDispenseItemId);
        Map<String, DispensingFormItem> dispenseItemIdToSelfProvidedDispenseItem = ListUtils.toMap(selfProvidedToDispenseItems, DispensingFormItem::getId);
        dispensableDispenseItems.forEach(dispensableDispenseItem -> {
            String dispenseItemId = dispensableDispenseItem.getId();
            DispensingForm dispensingForm = dispenseItemIdToDispensingForm.get(dispenseItemId);
            GoodsDispenseResultItem goodsDispenseResultItem = dispenseItemIdToGoodsDispenseResultItem.get(dispenseItemId);
            DispensingFormItem selfProvidedDispenseItem = dispenseItemIdToSelfProvidedDispenseItem.get(dispenseItemId);
            updateDispenseToFormItem(dispensingForm, dispensableDispenseItem, goodsDispenseResultItem, selfProvidedDispenseItem);
        });

        // 如果全部发完了，则更新发药单状态为已发药
        long waitingCount = dispensableDispenseItems.stream()
                .filter(dispensingFormItem -> dispensingFormItem.getProductType() != Constants.ProductType.COMPOSE_PRODUCT)
                .filter(dispensingFormItem -> DispensingFormItem.Status.dispenseable.contains(dispensingFormItem.getStatus())).count();
        if (waitingCount == 0) {
            dispensingSheet.setStatus(DispensingSheet.Status.DISPENSED);
            dispensingSheet.setOrderByDate(Instant.now());
        } else if (isReDispense == 1) {
            // 如果是重新发药，并且剩余可发数量 > 0，则需要把发药状态改为待发
            dispensingSheet.setStatus(DispensingSheet.Status.WAITING);
        }

        // 如果当此是重新发药的同时进行的发药，还需要把历史的退药 item 记为历史ID
        if (isReDispense == 1) {
            DTOConverter.collectDispensingSheetItems(dispensingSheet)
                    .stream().filter(dispensingFormItem -> dispensingFormItem.getStatus() == DispensingFormItem.Status.UNDISPENSED
                            && !refundFeeDispensingFormItemIds.contains(dispensingFormItem.getAssociateFormItemId()))
                    .forEach(DispensingFormItem::markIsHistoryItem);
            dispensingSheet.setDispensingSheetInfo(new DispensingSheetInfo(dispensedByIds));
            dispensingSheet.deleteDispensingTag(DispensingUtils.DispensingTag.DISPENSING_TAG_HAS_UNDISPENSE);
        } else {
            dispensingSheet.setDispensingSheetInfo(new DispensingSheetInfo(mergeDispenseByIds(dispensingSheet.getDispensedByIds(), dispensedByIds)));
        }

        dispensingSheet.setDispensedTime(Instant.now());
        dispensingSheet.setDispensedBy(dispensedByIds.get(0));
        FillUtils.fillLastModifiedBy(dispensingSheet, operatorId);
        dispensingSheet.addDispensingTag(DispensingUtils.DispensingTag.DISPENSING_TAG_HAS_DISPENSE);
        dispensingSheet.addDispensingTag(DispensingUtils.DispensingTag.DISPENSING_TAG_HAS_UNDISPENSABLE);
        dispensingSheetEntityService.save(dispensingSheet);
    }

    private List<String> mergeDispenseByIds(List<String> originDispenseByIds, List<String> currentDispenseByIds) {
        if (CollectionUtils.isEmpty(originDispenseByIds)) {
            return currentDispenseByIds;
        } else if (CollectionUtils.isEmpty(currentDispenseByIds)) {
            return originDispenseByIds;
        }

        return Stream.concat(originDispenseByIds.stream(), currentDispenseByIds.stream()).filter(Objects::nonNull).distinct().collect(Collectors.toList());
    }

    private void updateDispenseToFormItem(DispensingForm dispensingForm, DispensingFormItem dispenseItem,
                                          GoodsDispenseResultItem goodsDispenseResultItem, DispensingFormItem selfProvidedDispenseItem) {
        // 更新普通发药项
        updateDispenseToFormNormalItem(dispensingForm, dispenseItem, goodsDispenseResultItem);

        // 更新自备药品发药项
        updateDispenseToFormSelfProvidedItem(dispenseItem, selfProvidedDispenseItem);

        DispensingFormItem clientDispensingFormItem = dispenseItemIdToClientDispenseItem.get(dispenseItem.getId());
        if (clientDispensingFormItem != null) {
            dispenseItem.setProductSnap(DispensingUtils.mergeProductSnap(dispenseItem.getProductSnap(), clientDispensingFormItem.getProductSnap()));
        }

        // 发药完成的话，删除所有未使用的追溯码
        removeUnusedTraceableCodeNoInfo(dispenseItem);
    }

    private void removeUnusedTraceableCodeNoInfo(DispensingFormItem dispenseItem) {
        if (dispenseItem == null || CollectionUtils.isEmpty(dispenseItem.getTraceableCodeList())
                || dispenseItem.getStatus() != DispensingFormItem.Status.DISPENSED) {
            return;
        }

        dispenseItem.setTraceableCodeList(
                dispenseItem.getTraceableCodeList().stream()
                        .filter(traceableCode -> traceableCode.getUsed() != DispensingConstants.TraceableCodeUsed.WAITING)
                        .collect(Collectors.toList()));
    }

    private void updateDispenseToFormSelfProvidedItem(DispensingFormItem dispenseItem,
                                                      DispensingFormItem selfProvidedDispenseItem) {
        if (dispenseItem.getSourceItemType() != DispensingFormItem.SourceItemType.SELF_PROVIDED) {
            return;
        }

        //更新自备药品发药状态
        if (selfProvidedDispenseItem == null) {
            return;
        }

        dispenseItem.setStatus(DispensingFormItem.Status.DISPENSED);
        /**
         * 打个标记，标记本次发药
         * */
        dispenseItem.setThisTimeDispensed(1);
        dispenseItem.setOperationId(operationId);
        FillUtils.fillLastModifiedBy(dispenseItem, operatorId);
    }

    private void updateDispenseToFormNormalItem(DispensingForm dispensingForm,
                                                DispensingFormItem dispensableItem,
                                                GoodsDispenseResultItem resultItem) {
        if (isReDispense == 1 && resultItem == null) {
            // 如果是重新发药，该可发药项实际上本次没有进行发药，并且也没有退费，则需要重置发药项的状态为待发
            String sourceFormItemId = dispensableItem.getSourceFormItemId();
            ChargeFormItem chargeFormItem = chargeItemIdToChargeItem.get(sourceFormItemId);
            if (chargeFormItem != null && chargeFormItem.getStatus() == ChargeConstants.ChargeFormItemStatus.CHARGED) {
                dispensableItem.setStatus(DispensingFormItem.Status.WAITING);
                dispensableItem.setRemainingUnitCount(dispensableItem.getUnitCount());
                dispensableItem.setRemainingDoseCount(dispensableItem.getDoseCount());
            }
            return;
        }

        if (resultItem == null || CollectionUtils.isEmpty(resultItem.getDispenseBatchInfoList())) {
            return;
        }

        // 在需要的时候将发药操作信息和发药 stockDealId 信息同步
        syncDispensingItemDispenseLogIfNeeded(dispensingForm, dispensableItem, resultItem);

        String dealId = resultItem.getDealId();
        if (StringUtils.isBlank(dealId)) {
            return;
        }
        List<GoodsDispenseResultItem.DispenseBatchInfo> dispenseItemAllDispenseBatchInfoList =
                CollectionUtils.isEmpty(dispensableItem.getDispenseFormItemBatches()) ? resultItem.getDispenseBatchInfoList() : resultItem.getOriginalDispenseBatchInfoList();
        // 本次发药的批次信息
        List<GoodsDispenseResultItem.DispenseBatchInfo> thisTimeDispenseBatchInfoList = dispenseItemAllDispenseBatchInfoList.stream()
                .filter(dispenseBatchInfo -> Objects.equals(dealId, dispenseBatchInfo.getDealId()))
                .collect(Collectors.toList());

        updateDispenseToFormItemBatchInfo(dispensableItem, resultItem, thisTimeDispenseBatchInfoList);

        // 每次返回的都是发药项所有的发药批次信息，所以直接累加
        BigDecimal totalDispensedCount;
        if (dispensableItem.getUseDismounting() == 1) {
            // 拆零出售
            totalDispensedCount = dispenseItemAllDispenseBatchInfoList.stream()
                    .map(batchInfo -> {
                        BigDecimal pieceNum = new BigDecimal(batchInfo.getPieceNum());
                        return DispensingUtils.calculateStockCount(dispensableItem.getUseDismounting() == 1, batchInfo.getPackageCount(), pieceNum, batchInfo.getPieceCount());
                    })
                    .reduce(BigDecimal.ZERO, MathUtils::wrapBigDecimalAdd);
        } else {
            // 整包出售
            totalDispensedCount = dispenseItemAllDispenseBatchInfoList.stream()
                    .map(batchInfo -> {
                        BigDecimal pieceNum = new BigDecimal(batchInfo.getPieceNum());
                        return DispensingUtils.calculateStockCountV2(dispensableItem.getUseDismounting() == 1,
                                batchInfo.getPackageCount(),
                                batchInfo.getClientReqOriginFlatPieceCount(),//2025-01锁库清道夫上线后这个字段开始有值
                                batchInfo.getClientReqOriginFlatPackageCount(),//2025-01锁库清道夫上线后这个字段开始有值
                                pieceNum,
                                batchInfo.getPieceCount());
                    })
                    .reduce(BigDecimal.ZERO, MathUtils::wrapBigDecimalAdd);
        }
        if (MathUtils.isZeroOrNull(totalDispensedCount)) {
            log.error("发药项本次发药的数量 0，dispenseFormItem:{}, dispenseResultItem:{}", JsonUtils.dump(dispensableItem), JsonUtils.dump(resultItem));
            throw new DispensingServiceException(DispensingServiceError.DISPENSE_QUANTITY_ERROR);
        }

        BigDecimal totalDispenseCount = MathUtils.calculateTotalCount(dispensableItem.getUnitCount(), dispensableItem.getDoseCount());
        BigDecimal unitCount = dispensableItem.getUnitCount();
        BigDecimal doseCount = dispensableItem.getDoseCount();

        // 如果总共需要发的数量 = 已发的数量，则认为发药完成了
        int status = totalDispenseCount.compareTo(totalDispensedCount) == 0 ? DispensingFormItem.Status.DISPENSED : DispensingFormItem.Status.PART_DISPENSE;
        dispensableItem.setStatus(status);

        if (dispensingForm.getSourceFormType() == DispenseConst.SourceFormType.PRESCRIPTION_CHINESE) {
            // 已发的剂数应该是一个整数
            BigDecimal dispensedDoseCount = totalDispensedCount.divide(unitCount, 2, RoundingMode.DOWN);
            BigDecimal remainingDoseCount = MathUtils.wrapBigDecimalSubtract(doseCount, dispensedDoseCount);
            dispensableItem.setRemainingDoseCount(remainingDoseCount);
            dispensableItem.setRemainingUnitCount(unitCount);
        } else {
            BigDecimal remainingUnitCount = MathUtils.wrapBigDecimalSubtract(totalDispenseCount, totalDispensedCount);
            dispensableItem.setRemainingUnitCount(remainingUnitCount);
            dispensableItem.setRemainingDoseCount(doseCount);
        }

        // 如果发完了，则需要把剩余未发的数量改为 0
        if (status == DispensingFormItem.Status.DISPENSED) {
            dispensableItem.setRemainingDoseCount(BigDecimal.ZERO);
            dispensableItem.setRemainingUnitCount(BigDecimal.ZERO);
        }

        /**
         * 打个标记，标记本次发药
         * */
        dispensableItem.setThisTimeDispensed(1);
        dispensableItem.setStockDealId(resultItem.getFormItemDealId());
        dispensableItem.setTotalCostPrice(MathUtils.wrapBigDecimal(resultItem.getThisTimeTotalCostPrice(), BigDecimal.ZERO));
        dispensableItem.setOperationId(operationId);
        FillUtils.fillLastModifiedBy(dispensableItem, operatorId);
        thisTimeDispensingFormItems.add(dispensableItem);
    }

    private void updateDispenseToFormItemBatchInfo(DispensingFormItem dispensableItem,
                                                   GoodsDispenseResultItem resultItem,
                                                   List<GoodsDispenseResultItem.DispenseBatchInfo> thisTimeDispenseBatchInfoList) {
        if (CollectionUtils.isEmpty(thisTimeDispenseBatchInfoList)) {
            log.warn("发药项本次没有发药批次信息，dispenseItemId:{}", dispensableItem.getId());
            return;
        }

        boolean hasBatchPreLocked = dispensingSheet.hasDispensingTag(DispensingUtils.DispensingTag.DISPENSING_TAG_BATCH_PRE_LOCKED);
        if (!hasBatchPreLocked) {// 如果没有按批次锁定，则不需要处理和记录批次信息
            return;
        }

        List<DispensingFormItemBatchInfo> dispensingFormItemBatches = dispensableItem.getDispensingFormItemBatches();
        if (dispensingFormItemBatches == null) {
            dispensingFormItemBatches = new ArrayList<>(thisTimeDispenseBatchInfoList.size());
        }
        boolean isDismounting = dispensableItem.getUseDismounting() == 1;
        MergeTool<GoodsDispenseResultItem.DispenseBatchInfo, DispensingFormItemBatchInfo> mergeTool = new MergeTool<>();
        mergeTool.setSrc(thisTimeDispenseBatchInfoList)
                .setDst(dispensingFormItemBatches)
                .setIsEqualKeyFunc((dispenseBatchInfoResult, dispensingBatchInfo) -> Objects.equals(dispenseBatchInfoResult.getBatchId(), dispensingBatchInfo.getBatchId()))
                .setInsertFunc(dispenseBatchInfoResult -> null)
                .setUpdateFunc((src, dst) -> {
                    BigDecimal stockCount = calculateStockCount(dispensableItem, src, isDismounting);
                    dst.setDispenseUnitCount(MathUtils.wrapBigDecimalAdd(dst.getDispenseUnitCount(), stockCount));
                    FillUtils.fillLastModifiedBy(dst, operatorId);
                }).setDeleteFunc(dst -> false)
                .doMerge();
        dispensableItem.setDispensingFormItemBatches(dispensingFormItemBatches);
    }

    private static BigDecimal calculateStockCount(DispensingFormItem dispensableItem,
                                                  GoodsDispenseResultItem.DispenseBatchInfo dispenseBatchInfoResult,
                                                  boolean isDismounting) {
        Integer pieceNum = dispenseBatchInfoResult.getPieceNum();
        BigDecimal formattedPieceNum = formatPieceNum(pieceNum);
        BigDecimal packageCount = dispenseBatchInfoResult.getPackageCount();
        BigDecimal pieceCount = dispenseBatchInfoResult.getPieceCount();
        if (isDismounting) {
            if (dispenseBatchInfoResult.getClientReqOriginFlatPieceCount() != null) {
                return dispenseBatchInfoResult.getClientReqOriginFlatPieceCount();
            }
            return MathUtils.wrapBigDecimalAdd(MathUtils.wrapBigDecimalMultiply(packageCount, formattedPieceNum), pieceCount);
        } else {
            if (dispenseBatchInfoResult.getClientReqOriginFlatPackageCount() != null) {
                return dispenseBatchInfoResult.getClientReqOriginFlatPackageCount();
            }
            BigDecimal piecePackageCount = BigDecimal.ZERO;
            if (MathUtils.wrapBigDecimalCompare(pieceCount, BigDecimal.ZERO) > 0) {
                // 如果除了以后有余数则报错
                // 由于用户开单就有可能开的是有小数的大包装，或者有小数的小包装，所以这里只能放开校验
//                boolean checkPieceCount = dispensableItem.getUseDismounting() == 0
//                        && MathUtils.wrapBigDecimalCompare(dispensableItem.getUnitCount().remainder(BigDecimal.ONE), BigDecimal.ZERO) <= 0;
//                if (checkPieceCount && MathUtils.wrapBigDecimalCompare(pieceCount.remainder(formattedPieceNum), BigDecimal.ZERO) > 0) {
//                    log.error("发药项 {} 本次发药批次 {} 的数量 {} 不是整数，dispenseItemId:{}", dispensableItem.getName(), dispenseBatchInfoResult.getBatchId(), JsonUtils.dump(dispenseBatchInfoResult), dispensableItem.getId());
//                    throw new DispensingServiceException(DispensingServiceError.DISPENSE_QUANTITY_ERROR);
//                }
                //几个月过后可以把这个分支删了，这个分支是2025-01之前的发药单
                piecePackageCount = pieceCount.divide(formattedPieceNum, 2, RoundingMode.DOWN);
            }
            return MathUtils.wrapBigDecimalAdd(packageCount, piecePackageCount);
        }
    }

    private static BigDecimal formatPieceNum(Integer pieceNum) {
        if (pieceNum == null || pieceNum <= 0) {
            return BigDecimal.ONE;
        }
        return BigDecimal.valueOf(pieceNum);
    }


    /**
     * 在需要的时候同步发药项发药日志
     * goods 扣库存成功了，但是发药失败了，需要补发药日志
     *
     * @param dispensingForm     处方
     * @param dispenseItem       发药项
     * @param dispenseResultItem 发药结果
     */
    public void syncDispensingItemDispenseLogIfNeeded(DispensingForm dispensingForm, DispensingFormItem dispenseItem,
                                                      GoodsDispenseResultItem dispenseResultItem) {
        if (dispenseItem == null || dispenseResultItem == null || CollectionUtils.isEmpty(dispenseResultItem.getDispenseBatchInfoList())) {
            return;
        }

        String dealId = dispenseResultItem.getDealId();
        if (StringUtils.isNotBlank(dealId)) {
            return;
        }

        List<GoodsDispenseResultItem.DispenseBatchInfo> dispenseBatchInfoList = dispenseResultItem.getDispenseBatchInfoList();
        if (dispenseBatchInfoList.size() == 1) {
            // 如果只有一条进销存批次记录，那就不需要同步发药操作日志，把就这次发药批次操作当前发药操作产生的
            dispenseResultItem.setDealId(dispenseBatchInfoList.get(0).getDealId());
            return;
        }

        // 同步除了最后一次以外的所有批次信息，最后一次的批次信息作为当前操作的产生的批次信息
        // 按照 dealId 排序，最后一次操作会排在第一个
        dispenseBatchInfoList.sort(Comparator.comparing(GoodsDispenseResultItem.DispenseBatchInfo::getDealId));
        GoodsDispenseResultItem.DispenseBatchInfo lastDispenseBatchInfo = dispenseBatchInfoList.get(0);
        dispenseResultItem.setDealId(lastDispenseBatchInfo.getDealId());
        // TODO 这个字段是不是就是这个
        dispenseResultItem.setThisTimeTotalCostPrice(lastDispenseBatchInfo.getPackageCostPrice());

        List<GoodsDispenseResultItem.DispenseBatchInfo> needSyncDispensingBatchLogs = dispenseBatchInfoList.subList(1, dispenseBatchInfoList.size());
        dispensingLogService.syncDispensingItemDispenseLog(dispensingSheet, dispensingForm, chargeSheet, dispenseItem, needSyncDispensingBatchLogs, operationId, operatorId);
    }

    private List<GoodsDispenseResultItem> deductStock() {
        // 1.构建扣库请求
        // 1). 计算本次发药的实收金额
        Map<String, DispenseHelper.DispensingItemDispensePrice> dispenseItemIdToClientDispensingItemDispensePrice =
                DispenseHelper.calculateItemCurrentDispensePrice(dispensableDispenseItems, dispenseItemIdToClientDispenseItem, chargeItemIdToChargeItem);
        // 2). 如果开启了锁库则需要获取库存操作凭证，在扣库的时候需要把凭证带上
        List<GoodsDispenseDataItem> allGoodsDispenseDataItems = buildGoodsDispenseDateItems(dispenseItemIdToClientDispensingItemDispensePrice);

        // 只有普通发药项才走扣库
        List<GoodsDispenseDataItem> goodsDispenseDataItems = allGoodsDispenseDataItems.stream()
                .filter(goodsItem -> dispenseItemIdToDispensableDispenseItem.get(goodsItem.getDispenseItemId()).getSourceItemType() == DispensingFormItem.SourceItemType.NORMAL)
                .collect(Collectors.toList());

        // 2.扣库
        if (!CollectionUtils.isEmpty(goodsDispenseDataItems)) {
            // 调用 goods 进行扣库
            List<GoodsDispenseResultItem> goodsDispenseResultItems = abcCisScGoodsService.dispense(
                    dispensingSheet.getClinicId(),
                    dispensingSheet.getChainId(),
                    operatorId,
                    patientOrder != null? patientOrder.getNo() +"":null,
                    dispensingSheet.getPatientOrderId(),
                    dispensingSheetId,
                    dispensingSheet.getPharmacyNo(),
                    dispensingSheet.getPharmacyType(),
                    goodsDispenseDataItems,
                    DTOConverter.dispensingFormItemMapToDispensingFormItemAbstractMap(dispenseItemIdToDispensableDispenseItem),
                    dispenseItemIdToHistoryDispenseStockDealIds,
                    0);
            if (CollectionUtils.isEmpty(goodsDispenseResultItems) || goodsDispenseResultItems.size() != goodsDispenseDataItems.size()) {
                throw new GoodsDispenseException();
            }

            return goodsDispenseResultItems;
        }

        return new ArrayList<>();
    }

    private List<GoodsDispenseDataItem> buildGoodsDispenseDateItems(Map<String, DispenseHelper.DispensingItemDispensePrice> dispenseItemIdToClientDispensingItemDispensePrice) {
        List<GoodsDispenseDataItem> goodsDispenseDataItems = dispensableDispenseItems.stream()
                .map(dispensableDispenseItem -> {
                    DispensingFormItem clientDispenseItem = dispenseItemIdToClientDispenseItem.get(dispensableDispenseItem.getId());
                    if (clientDispenseItem == null) {
                        return null;
                    }
//                    QueryGoodsLockingRsp.GoodsLockingRspItem goodsLockingRspItem = dispenseItemIdToLockId.get(dispensableDispenseItem.getId());
                    DispenseHelper.DispensingItemDispensePrice dispensingItemDispensePrice = dispenseItemIdToClientDispensingItemDispensePrice.get(dispensableDispenseItem.getId());
                    DispensingForm dispensingForm = dispenseItemIdToDispensingForm.get(dispensableDispenseItem.getId());

                    GoodsDispenseDataItem goodsDispenseDataItem = buildGoodsDispenseDateItem(dispensableDispenseItem, clientDispenseItem, dispensingForm, dispensingItemDispensePrice);
                    dealDispenseTraceCode(dispensableDispenseItem, clientDispenseItem, goodsDispenseDataItem);
                    return goodsDispenseDataItem;
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (goodsDispenseDataItems.size() != clientDispenseItems.size()) {
            log.info("to send good dispense size:{} != to dispense size:{}", goodsDispenseDataItems.size(), clientDispenseItems.size());
            throw new DispenseSheetChangedException();
        }

        return goodsDispenseDataItems;
    }

    /**
     * 发药追溯码处理
     * 用户没指定追溯码，需要将追溯码清掉
     */
    private void dealDispenseTraceCode(DispensingFormItem dispensableDispenseItem, DispensingFormItem clientDispenseItem, GoodsDispenseDataItem goodsDispenseDataItem) {
        boolean clientHasTraceableCode = dealClientNoTraceableCode(dispensableDispenseItem, clientDispenseItem);
        if (!clientHasTraceableCode) {
            return;
        }
        /**
         * 把clientDispenseItem里面的追溯码merge到 dispensableDispenseItem里面
         * 1.有可能dispensableDispenseItem里面已经有收费带过来的最俗码
         * */
        //服务端没extend
        if (dispensableDispenseItem.getExtendData() == null) {
            dispensableDispenseItem.setExtendData(new DispensingFormItemExtendData());
            dispensableDispenseItem.getExtendData().setTraceableCodeList(new ArrayList<>());
        }
        if (dispensableDispenseItem.getTraceableCodeList() == null) {
            dispensableDispenseItem.setTraceableCodeList(new ArrayList<>());
        } else if (dispensableDispenseItem.getTraceableCodeList().size() > 1) {
            dispensableDispenseItem.setTraceableCodeList(TraceCodeUtils.mergeTraceableCode(dispensableDispenseItem.getGoodsItem().getPieceNum(), dispensableDispenseItem.getTraceableCodeList()));
        }

        /**
         * 重新发药要把之前的追溯码清理了
         * */
        if (isReDispense == YesOrNo.YES) {
            dispensableDispenseItem.setTraceableCodeList(new ArrayList<>());
        }

        List<TraceableCode> serverTraceableCodeList = dispensableDispenseItem.getTraceableCodeList();
        List<TraceableCode> clientTraceableCodeList = clientDispenseItem.getTraceableCodeList();
        List<TraceableCode> thisTimeTraceCodeList = new ArrayList<>();
        if (clientTraceableCodeList.size() > 1) {
            clientTraceableCodeList = TraceCodeUtils.mergeTraceableCode(dispensableDispenseItem.getGoodsItem().getPieceNum(), clientTraceableCodeList);
        }
        for (TraceableCode clientTraceableCode : clientTraceableCodeList) {
            TraceableCode serverTraceableCode = serverTraceableCodeList.stream().filter(traceableCode -> Objects.equals(traceableCode.getNo(), clientTraceableCode.getNo())
                            && Objects.equals(traceableCode.getUsed(), DispensingConstants.TraceableCodeUsed.WAITING))
                    .findFirst().orElse(null);
            if (serverTraceableCode != null) {
                BigDecimal clientTraceTotalCount = MathUtils.wrapBigDecimalAdd(clientTraceableCode.getHisPieceCount(), MathUtils.wrapBigDecimalMultiply(clientTraceableCode.getHisPackageCount(), dispensableDispenseItem.getGoodsItem().getPieceNum()));
                BigDecimal serverTraceTotalCount = MathUtils.wrapBigDecimalAdd(serverTraceableCode.getHisPieceCount(), MathUtils.wrapBigDecimalMultiply(serverTraceableCode.getHisPackageCount(), dispensableDispenseItem.getGoodsItem().getPieceNum()));
                int compareFlag = MathUtils.wrapBigDecimalCompare(clientTraceTotalCount, serverTraceTotalCount);
                if (compareFlag > 0) {// 客户端大于服务端，直接把服务端的追溯码数量更新为客户端的数量
                    serverTraceableCode.setUsed(DispensingConstants.TraceableCodeUsed.DISPENSE);
                    serverTraceableCode.setHisPieceCount(clientTraceableCode.getHisPieceCount());
                    serverTraceableCode.setHisPackageCount(clientTraceableCode.getHisPackageCount());
                } else if (compareFlag < 0) {// 客户端小于服务端，拆成两条，一条是已出库，一条是在库
                    BigDecimal notDispenseTotalPieceCount = MathUtils.wrapBigDecimalSubtract(serverTraceTotalCount, clientTraceTotalCount);
                    if (notDispenseTotalPieceCount.remainder(dispensableDispenseItem.getGoodsItem().getPieceNum()).compareTo(BigDecimal.ZERO) == 0) {
                        serverTraceableCode.setHisPackageCount(notDispenseTotalPieceCount.divide(dispensableDispenseItem.getGoodsItem().getPieceNum(), 2, RoundingMode.DOWN));
                        serverTraceableCode.setHisPieceCount(null);
                        serverTraceableCode.setDismountingSn(null);
                    } else {
                        serverTraceableCode.setHisPackageCount(null);
                        serverTraceableCode.setHisPieceCount(notDispenseTotalPieceCount);
                        serverTraceableCode.setDismountingSn(clientTraceableCode.getDismountingSn());
                    }
                    serverTraceableCode.setId(clientTraceableCode.getId());

                    TraceableCode newTraceableCode = new TraceableCode();
                    newTraceableCode.setId(clientTraceableCode.getId());
                    newTraceableCode.setNo(clientTraceableCode.getNo());
                    newTraceableCode.setType(clientTraceableCode.getType());
                    newTraceableCode.setBatchId(clientTraceableCode.getBatchId());
                    newTraceableCode.setUsed(DispensingConstants.TraceableCodeUsed.DISPENSE);
                    if (MathUtils.wrapBigDecimalCompare(clientTraceableCode.getHisPieceCount(), BigDecimal.ZERO) > 0
                            || MathUtils.wrapBigDecimalCompare(clientTraceableCode.getHisPackageCount(), BigDecimal.ZERO) > 0) {
                        newTraceableCode.setHisPackageCount(clientTraceableCode.getHisPackageCount());
                        newTraceableCode.setHisPieceCount(clientTraceableCode.getHisPieceCount());
                    } else {
                        newTraceableCode.setCount(clientTraceableCode.getCount());
                    }
                    newTraceableCode.setDismountingSn(clientTraceableCode.getDismountingSn());

                    serverTraceableCodeList.add(newTraceableCode);
                    thisTimeTraceCodeList.add(newTraceableCode);
                } else {
                    serverTraceableCode.setUsed(DispensingConstants.TraceableCodeUsed.DISPENSE);
                }
            } else {
                serverTraceableCode = new TraceableCode();
                serverTraceableCode.setId(clientTraceableCode.getId());
                serverTraceableCode.setNo(clientTraceableCode.getNo());
                serverTraceableCode.setType(clientTraceableCode.getType());
                serverTraceableCode.setBatchId(clientTraceableCode.getBatchId());
                serverTraceableCode.setUsed(DispensingConstants.TraceableCodeUsed.DISPENSE);
                //老数据兼容
                if (MathUtils.wrapBigDecimalCompare(clientTraceableCode.getHisPieceCount(), BigDecimal.ZERO) > 0 || MathUtils.wrapBigDecimalCompare(clientTraceableCode.getHisPackageCount(), BigDecimal.ZERO) > 0) {
                    serverTraceableCode.setHisPackageCount(clientTraceableCode.getHisPackageCount());
                    serverTraceableCode.setHisPieceCount(clientTraceableCode.getHisPieceCount());
                } else {
                    serverTraceableCode.setCount(clientTraceableCode.getCount());
                }
                serverTraceableCode.setDismountingSn(clientTraceableCode.getDismountingSn());
                serverTraceableCodeList.add(serverTraceableCode);
            }

            if (serverTraceableCode.getUsed() == DispensingConstants.TraceableCodeUsed.DISPENSE) {
                thisTimeTraceCodeList.add(serverTraceableCode);
            }
        }

        // 合并追溯码
        dispensableDispenseItem.setTraceableCodeList(TraceCodeUtils.mergeTraceableCode(dispensableDispenseItem.getGoodsItem().getPieceNum(), serverTraceableCodeList));
        /**
         * 如果是指定追溯码发药，把追溯码加到scGoods的发药请求里面
         * */
        if (goodsDispenseDataItem != null) {
            goodsDispenseDataItem.setTraceableCodeList(thisTimeTraceCodeList);
        }
    }

    /**
     * 发药指定追溯码为空
     */
    private boolean dealClientNoTraceableCode(DispensingFormItem dispensableDispenseItem,
                                              DispensingFormItem clientDispenseItem) {
        if (clientDispenseItem == null
                || clientDispenseItem.getExtendData() == null
                || CollectionUtils.isEmpty(clientDispenseItem.getExtendData().getTraceableCodeList())) {
            doUpdateDispenseFormItemTraceableCodeOfClientNoCode(dispensableDispenseItem, clientDispenseItem);
            return false;
        }
        clientDispenseItem.getExtendData().setTraceableCodeList(clientDispenseItem.getExtendData().getTraceableCodeList().stream()
                .filter(traceableCode -> Objects.equals(traceableCode.getType(), GoodsConst.DrugIdentificationCodeType.NO_CODE) || !TextUtils.isEmpty(traceableCode.getNo()))
                .collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(clientDispenseItem.getExtendData().getTraceableCodeList())) {
            doUpdateDispenseFormItemTraceableCodeOfClientNoCode(dispensableDispenseItem, clientDispenseItem);
            return false;
        }
        return true;
    }

    private void doUpdateDispenseFormItemTraceableCodeOfClientNoCode(DispensingFormItem dispensableDispenseItem, DispensingFormItem clientDispenseItem) {
        if (dispensableDispenseItem.getExtendData() == null || CollectionUtils.isEmpty(dispensableDispenseItem.getExtendData().getTraceableCodeList())) {
            return;
        }
        if (isReDispense == YesOrNo.YES) {
            dispensableDispenseItem.getExtendData().setTraceableCodeList(null);
            if (dispensableDispenseItem.getExtendData().isEmpty()) {
                dispensableDispenseItem.setExtendData(null);
            }
        } else {
            BigDecimal remainingUnitCount = DispensingUtils.getRealDispenseItemRemainingUnitCount(dispensableDispenseItem);
            BigDecimal remainingDoseCount = DispensingUtils.getRealDispenseItemRemainingDoseCount(dispensableDispenseItem);
            BigDecimal remainingTotalCount = MathUtils.calculateTotalCount(remainingUnitCount, remainingDoseCount);
            BigDecimal clientTotalCount = MathUtils.calculateTotalCount(clientDispenseItem.getUnitCount(), clientDispenseItem.getDoseCount());
            // 全发完时用户未指定追溯码，清理未使用的追溯码
            if (MathUtils.wrapBigDecimalCompare(remainingTotalCount, clientTotalCount) == 0) {
                List<TraceableCode> usedTraceableCodeList = dispensableDispenseItem.getExtendData().getTraceableCodeList().stream()
                        .filter(it -> it.getUsed() != DispensingConstants.TraceableCodeUsed.WAITING).collect(Collectors.toList());
                List<TraceableCode> notUsedTraceableCodeList = dispensableDispenseItem.getExtendData().getTraceableCodeList().stream()
                        .filter(it -> it.getUsed() == DispensingConstants.TraceableCodeUsed.WAITING).collect(Collectors.toList());
                log.info("发药未指定追溯码，清空掉录入的码，notUsedTraceableCodeList:{}", JsonUtils.dump(notUsedTraceableCodeList));
                dispensableDispenseItem.getExtendData().setTraceableCodeList(
                        usedTraceableCodeList);
                if (dispensableDispenseItem.getExtendData().isEmpty()) {
                    dispensableDispenseItem.setExtendData(null);
                }
            }
        }
    }

    private GoodsDispenseDataItem buildGoodsDispenseDateItem(DispensingFormItem dispenseableDispenseItem,
                                                             DispensingFormItem clientDispenseItem,
                                                             DispensingForm dispensingForm,
                                                             DispenseHelper.DispensingItemDispensePrice dispensingItemDispensePrice) {
        Optional<DispenseHelper.DispensingItemDispensePrice> dispensingItemDispensePriceOpt = Optional.ofNullable(dispensingItemDispensePrice);
        BigDecimal remainingUnitCount = isReDispense == 1 ? dispenseableDispenseItem.getUnitCount() : DispensingUtils.getRealDispenseItemRemainingUnitCount(dispenseableDispenseItem);
        BigDecimal remainingDoseCount = isReDispense == 1 ? dispenseableDispenseItem.getDoseCount() : DispensingUtils.getRealDispenseItemRemainingDoseCount(dispenseableDispenseItem);
        BigDecimal clientUnitCount = clientDispenseItem.getUnitCount();
        BigDecimal clientDoseCount = clientDispenseItem.getDoseCount();
        if (MathUtils.wrapBigDecimalCompare(clientUnitCount, remainingUnitCount) > 0
                || MathUtils.wrapBigDecimalCompare(clientDoseCount, remainingDoseCount) > 0) {
            // 如果请求的发药数量大于了剩余可发数量
            log.error("发药单 [{}] 中的发药项 [{}] 的发药数量 [{},{}] 大于了剩余可发数量 [{},{}]", dispensingSheetId, dispenseableDispenseItem.getId(), clientUnitCount, clientDoseCount, remainingUnitCount, remainingDoseCount);
            throw new DispenseSheetChangedException();
        }

        BigDecimal clientDispenseCount = MathUtils.calculateTotalCount(clientUnitCount, clientDoseCount);
        BigDecimal totalDispenseCount = MathUtils.calculateTotalCount(dispenseableDispenseItem.getUnitCount(), dispenseableDispenseItem.getDoseCount());
        GoodsDispenseDataItem goodsDispenseDataItem = new GoodsDispenseDataItem();

        goodsDispenseDataItem.setMedicineName(dispenseableDispenseItem.getName());
        goodsDispenseDataItem.setDisplaySpec(dispenseableDispenseItem.getGoodsDispSpec());
        if (dispenseableDispenseItem.getSnapPieceNum() != null) {
            goodsDispenseDataItem.setPieceNum(dispenseableDispenseItem.getSnapPieceNum().intValue());
        }//从快照上取规格
        goodsDispenseDataItem.setPieceUnit(dispenseableDispenseItem.getSnapPieceUnit());
        goodsDispenseDataItem.setPackageUnit(dispenseableDispenseItem.getSnapPackageUnit());

        goodsDispenseDataItem.setPharmacyNo(dispenseableDispenseItem.getPharmacyNo());
        goodsDispenseDataItem.setDispenseItemId(dispenseableDispenseItem.getId());
        goodsDispenseDataItem.setGoodsId(dispenseableDispenseItem.getProductId());
        // 如果开启了锁库，扣库的时候需要把锁库的凭证一并带上，优先用自己的，自己没有再用查询到的
        goodsDispenseDataItem.setLockId(dispenseableDispenseItem.getLockId());

        // 设置扣库数量
        if (dispenseableDispenseItem.getUseDismounting() == 1) {
            goodsDispenseDataItem.setPieceCount(clientDispenseCount);
            goodsDispenseDataItem.setPackageCount(BigDecimal.ZERO);
            goodsDispenseDataItem.setTotalDispenseFormItemPieceCount(totalDispenseCount);
            goodsDispenseDataItem.setTotalDispenseFormItemPackageCount(BigDecimal.ZERO);
        } else {
            goodsDispenseDataItem.setPackageCount(clientDispenseCount);
            goodsDispenseDataItem.setPieceCount(BigDecimal.ZERO);
            goodsDispenseDataItem.setTotalDispenseFormItemPackageCount(totalDispenseCount);
            goodsDispenseDataItem.setTotalDispenseFormItemPieceCount(BigDecimal.ZERO);
        }
        goodsDispenseDataItem.setSourceSheetId(dispensingSheet.getSourceSheetId());
        goodsDispenseDataItem.setSourceItemId(dispenseableDispenseItem.getSourceFormItemId());
        List<DispensingFormItemBatchInfo> clientDispensingFormItemBatches = clientDispenseItem.getDispensingFormItemBatches();
        List<DispensingFormItemBatchInfo> dispensingFormItemBatches = dispenseableDispenseItem.getDispensingFormItemBatches();
        if (!CollectionUtils.isEmpty(dispensingFormItemBatches) && !CollectionUtils.isEmpty(clientDispensingFormItemBatches)) {
            Map<Long, BigDecimal> batchIdToDispenseTotalPrice = dispensingItemDispensePriceOpt.map(DispenseHelper.DispensingItemDispensePrice::getBatchIdToDispenseTotalPrice).orElse(Collections.emptyMap());
            Map<Long, DispensingFormItemBatchInfo> batchIdToBatch = ListUtils.toMap(dispensingFormItemBatches, DispensingFormItemBatchInfo::getBatchId);
            List<GoodsDispenseDataItem.BatchCount> batchCounts = clientDispensingFormItemBatches.stream().map(clientDispensingBatchInfo -> {
                DispensingFormItemBatchInfo batchInfo = batchIdToBatch.get(clientDispensingBatchInfo.getBatchId());
                GoodsDispenseDataItem.BatchCount batchCount = new GoodsDispenseDataItem.BatchCount();
                batchCount.setBatchId(clientDispensingBatchInfo.getBatchId());
                BigDecimal unitCount = clientDispensingBatchInfo.getUnitCount();
                if (dispenseableDispenseItem.getUseDismounting() == 1) {
                    batchCount.setPieceCount(unitCount);
                } else {
                    batchCount.setPackageCount(unitCount);
                }
                batchCount.setTotalPieceCount(batchInfo.getUnitCount());
                batchCount.setBatchTotalPrice(batchIdToDispenseTotalPrice.getOrDefault(clientDispensingBatchInfo.getBatchId(), BigDecimal.ZERO));
                return batchCount;
            }).collect(Collectors.toList());
            //指定批次发药，强制发这些批次
            goodsDispenseDataItem.setLockBatch(LockConfig.LockBatchOps.BATCH);
            goodsDispenseDataItem.setBatchCountList(batchCounts);
        }

        // 虚拟药房供应商，指定了会强制扣这个供应商的库存  (退药不需要指定)
        if (dispensingForm != null && dispensingForm.getVendorId() != null) {
            goodsDispenseDataItem.setSupplierShadowId(dispensingForm.getVendorId());
        }
        goodsDispenseDataItem.setTotalPrice(dispensingItemDispensePriceOpt.map(DispenseHelper.DispensingItemDispensePrice::getDispenseTotalPrice).orElse(BigDecimal.ZERO));
        Integer shebaoDismountingFlag = dispenseableDispenseItem.getShebaoDismountingFlag();
        if (shebaoDismountingFlag == null || DispensingUtils.checkFlagOn(shebaoDismountingFlag, ShebaoDismountingFlagConst.IS_EDITABLE)) {
            goodsDispenseDataItem.setShebaoDismountingFlag(clientDispenseItem.getShebaoDismountingFlag());
        } else {
            goodsDispenseDataItem.setShebaoDismountingFlag(shebaoDismountingFlag);
        }

        return goodsDispenseDataItem;
    }

    private void paramCheck() {
        // 校验发药数量
        checkAndFormatDispensingSheetDispenseCount();
        // 补全“无码”追溯码
        DispensingUtils.fillNoTraceableCode(clientDispenseItems, dispenseItemIdToDispensableDispenseItem, goodsConfig);
    }

    private void checkAndFormatDispensingSheetDispenseCount() {
        clientDispenseSheet.getDispensingForms().forEach(clientDispensingForm -> {
            if (clientDispensingForm == null || CollectionUtils.isEmpty(clientDispensingForm.getDispensingFormItems())) {
                return;
            }

            String dispensingFormId = clientDispensingForm.getId();
            if (StringUtils.isBlank(dispensingFormId)) {
                throw new ParamRequiredException("dispensingFormId");
            }
            DispensingForm dispensingForm = dispensingFormIdToDispensingForm.get(dispensingFormId);
            if (dispensingForm == null) {
                log.error("发药单 [{}] 处方 [{}] 不存在", dispensingSheetId, dispensingFormId);
                throw new ParamNotValidException("处方不存在");
            }

            List<DispensingFormItem> clientDispenseItems = clientDispensingForm.getDispensingFormItems();
            List<DispensingFormItem> availableDispenseItems = DTOConverter.collectDispensingFormItemsWithStatus(dispensingForm, DispensingFormItem.Status.available);
            List<DispensingFormItem> dispensableDispenseItems = dispensingForm.getDispensingFormItems().stream()
                    .filter(dispensingFormItem -> dispenseItemIdToDispensableDispenseItem.containsKey(dispensingFormItem.getId()))
                    .collect(Collectors.toList());
            this.checkAndFormatDispensingFormDispenseCount(dispensingForm, dispensableDispenseItems, availableDispenseItems, clientDispensingForm, clientDispenseItems);
//            try {
//                this.checkDispensingFormItemTraceableCodeList(dispensableDispenseItems, clientDispenseItems);
//            } catch (DispensingServiceException e) {
//                if (isDirectDispenseChargeSheet && e.getCode() == DispensingServiceError.TRACE_CODE_VALID_FAIL.getCode()) {
//                    // 收费同时发药放过追溯码验证-但要告警
//                    //发送小喇叭消息
//                    String content = String.format("chargeSheetId: %s, operatorId: %s, reason: %s", chargeSheet.getId(), operatorId, e.getMessage());
//                    cisMonitorService.sendServiceAlertMessage("收费同时发药追溯码异常", content, JsonUtils.dumpAsJsonNode(chargeSheet.getId()));
//                    return;
//                }
//                throw e;
//            }
        });
        // 检查开启了整单发药的情况下,发药item必须等于可发药的item数量,且发药数量等于可发药数量
        if (Objects.nonNull(this.dispensingConfig) && dispensingConfig.getWholeSheetOperateEnable() == YesOrNo.YES) {
            Set<String> dispensingFormItemIds = dispensingFormIdToDispensingForm.values().stream()
                    .map(dispensingForm -> DTOConverter.collectDispensingFormItemsWithStatus(dispensingForm, DispensingFormItem.Status.dispenseable))
                    .flatMap(Collection::stream)
                    // 过滤掉历史的发药项 + 过滤掉套餐母项
                    .filter(dispensingFormItem -> dispensingFormItem.getIsHistoryItem() != 1 && dispensingFormItem.getComposeType() != ComposeType.COMPOSE)
                    .map(DispensingFormItem::getId)
                    .collect(Collectors.toSet());
            Set<String> clientDispensingFormItemIds = DTOConverter.collectDispensingSheetItems(clientDispenseSheet)
                    .stream()
                    .map(DispensingFormItem::getId)
                    .collect(Collectors.toSet());
            if (dispensingFormItemIds.size() != clientDispensingFormItemIds.size() || !clientDispensingFormItemIds.containsAll(dispensingFormItemIds)) {
                throw new DispensingServiceException(DispensingServiceError.DISPENSING_SHEET_NOT_SUPPORT_PARTIAL_DISPENSE);
            }
        }
    }

    private void checkDispensingFormItemTraceableCodeList(List<DispensingFormItem> dispensableDispenseItems, List<DispensingFormItem> clientDispenseItems) {
        // 不是医保结算 不拦
        if (!BitFlagUtils.checkFlagOn(dispensingSheet.getDispensingTag(), DispensingUtils.DispensingTag.DISPENSING_TAG_SHEBAO_PAY_FLAG)) {
            return;
        }
        if (traceCodeConfig == null || !Objects.equals(traceCodeConfig.getCollectionSwitch(), YesOrNo.YES) || !Objects.equals(traceCodeConfig.getDispenseCollCheckStrictMode(), YesOrNo.YES)) {
            return;
        }
        if (CollectionUtils.isEmpty(dispensableDispenseItems) || CollectionUtils.isEmpty(clientDispenseItems)) {
            log.warn("dispensableDispenseItems or clientDispenseItems is empty");
            return;
        }
        Map<String, DispensingFormItem> dispensingFormItemMap = ListUtils.toMap(dispensableDispenseItems, DispensingFormItem::getId);
        for (DispensingFormItem clientDispenseItem : clientDispenseItems) {
            DispensingFormItem dispensingFormItem = dispensingFormItemMap.get(clientDispenseItem.getId());
            if (dispensingFormItem == null) {
                continue;
            }
            // 只有本地药房跟合作药方才校验
            if (!Arrays.asList(GoodsConst.PharmacyType.LOCAL_PHARMACY, GoodsConst.PharmacyType.CO_PHARMACY).contains(dispensingFormItem.getPharmacyType())) {
                continue;
            }
            if (BitFlagUtils.checkFlagOn(dispensingFormItem.getShebaoDismountingFlag(), ShebaoDismountingFlagConst.IS_DISMOUNTING)) {
                // 医保拆零，不校验追溯码
                continue;
            }
            BigDecimal traceableCodeNum = dispensingFormItem.getTraceableCodeNum();
            if (traceableCodeNum == null) {
                // 没有应采数量，不校验
                continue;
            }
            if (MathUtils.wrapBigDecimalCompare(traceableCodeNum, BigDecimal.ZERO) == 0) {
                // 应采数量是0，不允许采集追溯码
                if (!CollectionUtils.isEmpty(clientDispenseItem.getTraceableCodeList())) {
                    log.warn("dispensingFormItemId:{} 应采数量为0，但是采集了追溯码", clientDispenseItem.getId());
                    throw new DispensingServiceException(DispensingServiceError.TRACE_CODE_VALID_FAIL, "医保要求，在结算时费用明细上传阶段必须准确传输“最小包装追溯码数量”，并严格按照“最小包装追溯码数量”采集追溯码。");
                }
                continue;
            }

            List<TraceableCode> clientTraceableCodeList = clientDispenseItem.getTraceableCodeList();
            if (CollectionUtils.isEmpty(clientTraceableCodeList)) {
                throw new DispensingServiceException(DispensingServiceError.TRACE_CODE_VALID_FAIL, "未采集追溯码，医保要求，在结算时费用明细上传阶段必须准确传输“最小包装追溯码数量”，并严格按照“最小包装追溯码数量”采集追溯码。");
            }

            /*
                TODO 暂时先校验应采和已采不一致，等待另一个需求处理
             */
            // 先换算成小包装比较再比较应采大包数量
//            BigDecimal traceableCodeNumOfPiece = MathUtils.wrapBigDecimalMultiply(traceableCodeNum, dispensingFormItem.getSnapPieceNum());
//            BigDecimal clientTraceableCodeNumOfPiece = clientTraceableCodeList.stream()
//                    .filter(Objects::nonNull)
//                    .reduce(BigDecimal.ZERO, (a, b) -> MathUtils.wrapBigDecimalAdd(a, MathUtils.wrapBigDecimalAdd(b.getHisPieceCount(), MathUtils.wrapBigDecimalMultiply(b.getHisPackageCount(), dispensingFormItem.getSnapPieceNum()))), MathUtils::wrapBigDecimalAdd);
//
//            BigDecimal traceableCodeNumOfPackage = new BigDecimal(traceableCodeNum.toBigInteger());
//            BigDecimal clientTraceCodeNumOfPackage = clientTraceableCodeList.stream()
//                    .filter(Objects::nonNull)
//                    .reduce(BigDecimal.ZERO, (a, b) -> MathUtils.wrapBigDecimalAdd(a, b.getHisPackageCount()), MathUtils::wrapBigDecimalAdd);
//            if (MathUtils.wrapBigDecimalCompare(traceableCodeNumOfPiece, clientTraceableCodeNumOfPiece) != 0) {
//                throw new DispensingServiceException(DispensingServiceError.TRACE_CODE_VALID_FAIL, "医保要求，在结算时费用明细上传阶段必须准确传输“最小包装追溯码数量”，并严格按照“最小包装追溯码数量”采集追溯码。");
//            }
//            if (MathUtils.wrapBigDecimalCompare(traceableCodeNumOfPackage, clientTraceCodeNumOfPackage) != 0) {
//                throw new DispensingServiceException(DispensingServiceError.TRACE_CODE_VALID_FAIL, String.format("最少需采集%s%s追溯码，实际采集%s%s，请重新采集", traceableCodeNumOfPackage, dispensingFormItem.getSnapPackageUnit(), clientTraceCodeNumOfPackage, dispensingFormItem.getSnapPackageUnit()));
//            }
        }
    }

    /**
     * 校验并格式化处方发药数量
     * 中药处方：只能进行单个发药项全发或者整个处方按照剂数发，如果进行了其中一种操作，则后续只能进行该种操作
     * 其他处方：发药数量不能超过剩余可发药数量
     *
     * @param dispensingForm               处方
     * @param formDispensableDispenseItems 处方可发药项
     * @param formAvailableDispenseItems   处方有效的发药项
     * @param clientDispensingForm         客户端传入的处方发药请求
     * @param clientFormDispenseItems      客户端传入的处方发药项发药请求
     */
    private void checkAndFormatDispensingFormDispenseCount(DispensingForm dispensingForm,
                                                           List<DispensingFormItem> formDispensableDispenseItems,
                                                           List<DispensingFormItem> formAvailableDispenseItems,
                                                           DispensingForm clientDispensingForm,
                                                           List<DispensingFormItem> clientFormDispenseItems) {
        if (dispensingForm == null || CollectionUtils.isEmpty(formDispensableDispenseItems) || CollectionUtils.isEmpty(formAvailableDispenseItems)
                || clientDispensingForm == null || CollectionUtils.isEmpty(clientFormDispenseItems)) {
            return;
        }
        Map<String, DispensingFormItem> dispenseItemIdToClientFormDispenseItem = ListUtils.toMap(clientFormDispenseItems, DispensingFormItem::getId);
        if (dispensingForm.getSourceFormType() == DispenseConst.SourceFormType.PRESCRIPTION_CHINESE) {
            Integer beforeDispenseOperationType = getChinesePrescriptionBeforeDispenseOperationType(formAvailableDispenseItems);
            BigDecimal clientDoseCount = clientDispensingForm.getDoseCount();
            if (MathUtils.wrapBigDecimalCompare(BigDecimal.ZERO, MathUtils.wrapBigDecimalOrZero(clientDoseCount)) >= 0) {
                // 如果没有传剂数或者小于等于0则说明是勾选的处方中部分发药项进行全部发药
                if (beforeDispenseOperationType != null && beforeDispenseOperationType != 0) {
                    log.error("发药单 [{}] 处方 [{}] 已经发生过按照剂数部分发药，不能再进行按照单个发药项全部发药的操作了", dispensingSheetId, dispensingForm.getId());
                    throw new DispenseSheetChangedException();
                }
                formDispensableDispenseItems.forEach(dispenseItem -> {
                    String dispenseItemId = dispenseItem.getId();
                    DispensingFormItem clientFormDispenseItem = dispenseItemIdToClientFormDispenseItem.get(dispenseItemId);
                    if (clientFormDispenseItem == null) {
                        return;
                    }
                    // 全部发药
                    clientFormDispenseItem.setDoseCount(dispenseItem.getDoseCount());
                    clientFormDispenseItem.setUnitCount(dispenseItem.getUnitCount());
                    // 检查批次信息
                    this.checkDispensingFormItemBatchDispenseCount(clientFormDispenseItem, dispenseItem);
                });
            } else {
                // 如果传了剂数则说明是勾选的处方中所有可发药项按照剂数发药
                if (beforeDispenseOperationType != null && beforeDispenseOperationType != 1) {
                    log.error("发药单 [{}] 处方 [{}] 已经发生过按照单个发药项全部发药的操作，不能再进行按照剂数部分发药的操作了", dispensingSheetId, dispensingForm.getId());
                    throw new DispenseSheetChangedException();
                }
                // 校验客户端是否传入了该处方所有可发药项进行发药
                formDispensableDispenseItems.forEach(dispenseItem -> {
                    String dispenseItemId = dispenseItem.getId();
                    DispensingFormItem clientFormDispenseItem = dispenseItemIdToClientFormDispenseItem.get(dispenseItemId);
                    if (clientFormDispenseItem == null) {
                        // 如果客户端没有传入该处方所有可发药项进行发药，则抛出异常
                        log.error("发药单 [{}] 处方 [{}] 在进行按照剂数部分发药时，未传入可发药项 [{}] 进行发药", dispensingSheetId, dispensingForm.getId(), dispenseItemId);
                        throw new DispenseSheetChangedException();
                    }
                    BigDecimal remainingDoseCount = isReDispense == 1 ? dispenseItem.getDoseCount() : DispensingUtils.getRealDispenseItemRemainingDoseCount(dispenseItem);
                    if (MathUtils.wrapBigDecimalCompare(clientDoseCount, remainingDoseCount) > 0) {
                        log.error("发药单 [{}] 处方 [{}] 在进行按照剂数部分发药时，剂数 [{}] 大于可发药项 [{}] 的剩余可发药剂数 [{}]",
                                dispensingSheetId, dispensingForm.getId(), clientDoseCount, dispenseItemId, remainingDoseCount);
                        throw new DispenseSheetChangedException();
                    }
                    clientFormDispenseItem.setDoseCount(clientDoseCount);
                    clientFormDispenseItem.setUnitCount(dispenseItem.getUnitCount());
                    // 检查批次信息
                    this.checkDispensingFormItemBatchDispenseCount(clientFormDispenseItem, dispenseItem);
                });
            }
        } else {
            formDispensableDispenseItems.forEach(dispenseItem -> {
                String dispenseItemId = dispenseItem.getId();
                DispensingFormItem clientFormDispenseItem = dispenseItemIdToClientFormDispenseItem.get(dispenseItemId);
                // 此处不用检查整单发药,因为在上一步骤已经校验过数据库每一项是否能够与客户端发药项数量一致
                if (clientFormDispenseItem == null) {
                    return;
                }
                BigDecimal remainingUnitCount = isReDispense == 1 ? dispenseItem.getUnitCount() : DispensingUtils.getRealDispenseItemRemainingUnitCount(dispenseItem);
                BigDecimal clientUnitCount = clientFormDispenseItem.getUnitCount();
                clientFormDispenseItem.setDoseCount(BigDecimal.ONE);
                if (MathUtils.isZeroOrNull(clientUnitCount)) {
                    // 兼容传空的情况，认为是全部发药
                    clientFormDispenseItem.setUnitCount(dispenseItem.getUnitCount());
                } else if (MathUtils.wrapBigDecimalCompare(clientUnitCount, BigDecimal.ZERO) < 0) {
                    throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "发药数量不能为负");
                } else if (clientUnitCount.compareTo(remainingUnitCount) > 0) {
                    log.error("发药单 [{}] 处方 [{}] 发药项 [{}] 发药数量 [{}] 大于的剩余可发药数量 [{}]",
                            dispensingSheetId, dispensingForm.getId(), dispenseItem.getId(), clientUnitCount, remainingUnitCount);
                    throw new DispenseSheetChangedException();
                }
                // 检查批次信息
                this.checkDispensingFormItemBatchDispenseCount(clientFormDispenseItem, dispenseItem);
            });
        }
    }

    private void checkDispensingFormItemBatchDispenseCount(DispensingFormItem clientFormDispenseItem, DispensingFormItem dispenseItem) {
        if (clientFormDispenseItem == null || dispenseItem == null) {
            return;
        }
        if (CollectionUtils.isEmpty(dispenseItem.getDispenseFormItemBatches())) {
            // 由于门诊发药一定是先锁批次再发药，所以如果没有记录锁的批次则认为不支持按批次发药，但是如果客户端传入了批次信息，清空批次信息
            clientFormDispenseItem.setDispensingFormItemBatches(new ArrayList<>());
            return;
        } else if (CollectionUtils.isEmpty(clientFormDispenseItem.getDispensingFormItemBatches())) {
            // 如果客户端没有传入批次信息，则走一次发药这边的默认选批次逻辑
            clientFormDispenseItem.setDispensingFormItemBatches(DispenseHelper.recommendDispenseBatches(MathUtils.wrapBigDecimalMultiply(clientFormDispenseItem.getUnitCount(), clientFormDispenseItem.getDoseCount()), dispenseItem.getDispenseFormItemBatches()));
        }

        Map<Long, DispensingFormItemBatchInfo> batchIdToDispenseItemBatch = ListUtils.toMap(dispenseItem.getDispenseFormItemBatches(), DispensingFormItemBatchInfo::getBatchId);
        BigDecimal clientDispenseFormItemBatchTotalUnitCount = BigDecimal.ZERO;
        for (DispensingFormItemBatchInfo clientDispensingFormItemBatch : clientFormDispenseItem.getDispensingFormItemBatches()) {
            if (clientDispensingFormItemBatch == null) {
                return;
            }

            Long batchId = clientDispensingFormItemBatch.getBatchId();
            DispensingFormItemBatchInfo dispenseItemBatch = batchIdToDispenseItemBatch.get(batchId);
            if (dispenseItemBatch == null) {
                log.error("发药单 [{}] 处方 [{}] 发药项 [{}] 的批次 [{}] 不存在", dispensingSheetId, clientFormDispenseItem.getDispensingFormId(), clientFormDispenseItem.getId(), batchId);
                throw new DispenseSheetChangedException();
            }

            BigDecimal remainUnitCount = dispenseItemBatch.getRemainUnitCount();
            BigDecimal clientDispensingFormItemBatchUnitCount = clientDispensingFormItemBatch.getUnitCount();
            if (MathUtils.wrapBigDecimalCompare(clientDispensingFormItemBatchUnitCount, remainUnitCount) > 0) {
                log.error("发药单 [{}] 处方 [{}] 发药项 [{}] 的批次 [{}] 发药数量 [{}] 大于剩余可发药数量 [{}]",
                        dispensingSheetId, clientFormDispenseItem.getDispensingFormId(), clientFormDispenseItem.getId(), batchId, clientDispensingFormItemBatchUnitCount, remainUnitCount);
                throw new DispenseSheetChangedException();
            }

            clientDispenseFormItemBatchTotalUnitCount = MathUtils.wrapBigDecimalAdd(clientDispenseFormItemBatchTotalUnitCount, clientDispensingFormItemBatchUnitCount);
        }

        BigDecimal clientDispenseFormItemUnitCount = MathUtils.wrapBigDecimalMultiply(clientFormDispenseItem.getUnitCount(), clientFormDispenseItem.getDoseCount());
        if (MathUtils.wrapBigDecimalCompare(clientDispenseFormItemBatchTotalUnitCount, clientDispenseFormItemUnitCount) != 0) {
            log.error("发药单 [{}] 处方 [{}] 发药项 [{}] 的批次发药数量 [{}] 与发药项发药数量 [{}] 不一致",
                    dispensingSheetId, clientFormDispenseItem.getDispensingFormId(), clientFormDispenseItem.getId(), clientDispenseFormItemBatchTotalUnitCount, clientDispenseFormItemUnitCount);
            throw new DispenseSheetChangedException();
        }
    }

    /**
     * 获取中药处方之前的发药操作类型
     *
     * @param formAvailableDispenseItems 处方有效的发药项
     * @return 发药操作类型 null:之前没有发药操作，0:之前进行了单个发药项全部发药操作 1:之前进行了整个处方按照剂数发药操作
     */
    private Integer getChinesePrescriptionBeforeDispenseOperationType(List<DispensingFormItem> formAvailableDispenseItems) {
        if (CollectionUtils.isEmpty(formAvailableDispenseItems)) {
            return null;
        }

        boolean hasDispense = formAvailableDispenseItems.stream()
                .anyMatch(formAvailableDispenseItem -> DispensingFormItem.Status.hasDispense.contains(formAvailableDispenseItem.getStatus()));
        if (isReDispense == 1 || !hasDispense) {
            // 如果是重新发药或着发药项没有发过药的，则说明之前没有发药操作
            return null;
        }

        DispensingFormItem firstAvailableDispenseItem = formAvailableDispenseItems.get(0);
        int firstAvailableDispenseItemStatus = firstAvailableDispenseItem.getStatus();
        if (firstAvailableDispenseItemStatus == DispensingFormItem.Status.WAITING
                || firstAvailableDispenseItemStatus == DispensingFormItem.Status.DISPENSED) {
            // 如果存在待发药的发药项，则说明之前进行了部分发药操作
            return 0;
        } else if (firstAvailableDispenseItemStatus == DispensingFormItem.Status.PART_DISPENSE) {
            // 如果存在部分发药的发药项，则说明之前进行过整个处方按照剂数发药操作
            return 1;
        }

        log.error("获取中药处方之前的发药操作类型失败，发药项状态错误，dispenseItemId: {}, dispenseItemStatus: {}", firstAvailableDispenseItem.getId(), firstAvailableDispenseItemStatus);
        throw new DispenseSheetChangedException();
    }

    private void loadAndPreProcess() {
        // 加载 DB 中的发药单
        this.dispensingSheet = dispensingSheetEntityService.findByIdAndClinicId(dispensingSheetId, clinicId);
        if (dispensingSheet == null) {
            throw new NoExistedSheetException();
        }

        patientOrder = patientOrderService.findPatientOrder(dispensingSheet.getPatientOrderId());
        //校验收费单是否在退费中
        DispensingUtils.checkChargeSheetIsRefunding(patientOrderService.listLocksByPatientOrderId(dispensingSheet.getPatientOrderId(), dispensingSheet.getChainId(), dispensingSheet.getClinicId()));


        // 状态为不是待发、并且不是重新发药同时发药
        if (dispensingSheet.getStatus() != DispensingSheet.Status.WAITING
                && !(dispensingSheet.getStatus() == DispensingSheet.Status.UNDISPENSED && isReDispense == 0)) {
            throw new DispenseSheetChangedException();
        }
        if (dispensingSheet.getStatus() != DispensingSheet.Status.UNDISPENSED && isReDispense != 0) {
            // 如果发药单不是已退，但是客户端传的 isReDispense 为 1 的时候需要置为 0，防止后面的判断有问题
            isReDispense = 0;
        }
        this.beforeDispensingSheetStatus = DispensingTodoService.DispensingSheetStatus.of(dispensingSheet);

        // 加载收费单
        if (chargeSheet == null) {
            this.chargeSheet = chargeService.getChargeSheetById(dispensingSheet.getSourceSheetId());
        }
        if (chargeSheet == null) {
            log.error("发药单 [{}] 的收费单 [{}] 不存在", dispensingSheetId, dispensingSheet.getSourceSheetId());
            throw new NotFoundException("收费单不存在");
        }
        this.chargeItemIdToChargeItem = chargeSheet.getChargeForms().stream()
                .map(ChargeForm::getChargeFormItems).filter(Objects::nonNull)
                .flatMap(Collection::stream).filter(Objects::nonNull)
                .collect(Collectors.toMap(ChargeFormItem::getId, Function.identity(), (a, b) -> a));

        // 发药单所有可发药的发药项
        this.dispensableDispenseItems = collectDispenseableItems();
        if (CollectionUtils.isEmpty(dispensableDispenseItems)) {
            log.error("发药单没有可发药项，id: {}", dispensingSheetId);
            throw new DispenseSheetChangedException();
        }

        this.dispenseItemIdToDispensableDispenseItem = ListUtils.toMap(dispensableDispenseItems, DispensingFormItem::getId);
        this.dispenseItemIdToDispensingForm = new HashMap<>();
        dispensingSheet.getDispensingForms().forEach(dispensingForm -> {
            dispensingForm.getDispensingFormItems().forEach(dispensingFormItem -> {
                dispenseItemIdToDispensingForm.put(dispensingFormItem.getId(), dispensingForm);
            });
        });
        this.dispensingFormIdToDispensingForm = ListUtils.toMap(dispensingSheet.getDispensingForms(), DispensingForm::getId);

        // 查询商品信息
        refreshClientDispenseSheetProductInfo(dispensingSheet, dispenseItemIdToDispensableDispenseItem, clientDispenseSheet);

        // 查询goods 配置
        this.goodsConfig = scGoodsFeignClient.getGoodsConfig(clinicId);

        // 加载发药单的所有操作日志
        this.dispensingSheetOperations = dispensingSheetOperationRepository.findAllByDispensingSheetIdAndIsDeletedOrderByCreatedDescOperationTypeDesc(dispensingSheetId, 0);
        // 得到当前发药单重新发药的次数
        this.dispensingSheetReDispenseCount = dispensingSheetOperations.stream().filter(operation -> operation.getOperationType() == DispensingSheetOperation.OperationType.RE_DISPENSE)
                .map(operation -> JsonUtils.readValue(operation.getRecordJson(), OperationRecordForReDispense.class)).filter(Objects::nonNull)
                .map(OperationRecordForReDispense::getDispensingSheetReDispenseCount).max(Integer::compareTo).orElse(0);
        if (isReDispense == 1) {
            // 如果当前就是重新发药，则需要把次数 + 1
            this.dispensingSheetReDispenseCount++;
        }
        // 加载发药项所有发药的日志
        this.dispensingItemDispenseLogs = dispensingItemLogRepository.findAllByDispensingSheetIdAndType(dispensingSheetId, DispensingLogItem.Type.DISPENSE);
        // 得到每个发药项历史的发药 stockDealId
        this.dispenseItemIdToHistoryDispenseStockDealIds = getDispenseItemHistoryDispenseDealIds();
    }

    /**
     * 绑定商品信息到客户端请求发药单上（因为需要记录每次发药的 traceableCodeNoInfoList 的快照信息）
     */
    private void refreshClientDispenseSheetProductInfo(DispensingSheet dispensingSheet,
                                                       Map<String, DispensingFormItem> dispenseItemIdToDispensableDispenseItem,
                                                       DispensingSheet clientDispenseSheet) {
        // 由于客户端请求中没有带上这些信息，所以需要先将这些信息补全
        clientDispenseSheet.setChainId(dispensingSheet.getChainId());
        clientDispenseSheet.setClinicId(dispensingSheet.getClinicId());
        clientDispenseSheet.setPharmacyNo(dispensingSheet.getPharmacyNo());
        clientDispenseSheet.setPharmacyType(dispensingSheet.getPharmacyType());

        DispensingUtils.doWithDispensingItem(clientDispenseSheet, (clientItem) -> {
            DispensingFormItem dispensingFormItem = dispenseItemIdToDispensableDispenseItem.get(clientItem.getId());
            if (dispensingFormItem == null) {
                return;
            }
            clientItem.setProductId(dispensingFormItem.getProductId());
            clientItem.setLockId(dispensingFormItem.getLockId());
        });


        clientDispenseSheet.refreshProductInfo(scGoodsFeignClient);
        DispensingUtils.doWithDispensingItem(clientDispenseSheet, (clientItem) -> {
            DispensingFormItem dispensingFormItem = dispenseItemIdToDispensableDispenseItem.get(clientItem.getId());
            if (dispensingFormItem == null) {
                return;
            }
            dispensingFormItem.setGoodsItem(clientItem.getGoodsItem());
        });
    }

    /**
     * 得到每个发药项历史的 stockDealId 列表，因为点击重新发药后就算是一次新的开始了，由于 goods 并不关注 dispensing 这边的重新发药，
     * 每次发药后都会把历史的发药 stockDealId 都返回回来，为了保证这是一次"新的开始"，就需要把当此重新发药之前发的 stockDealId 都过滤掉。
     * <p>
     * 历史：只要发药日志所属的重新发药次数小于当前重新发药次数的都认为是历史的，
     *
     * @return 发药的发药项ID -> 历史的发药 stockDealId 列表
     */
    private Map<String, List<String>> getDispenseItemHistoryDispenseDealIds() {
        if (dispensingSheetReDispenseCount <= 0 || CollectionUtils.isEmpty(dispensingItemDispenseLogs)) {
            return new HashMap<>();
        }

        // 得到历史的发药操作ID
        List<String> historyDispenseOperationIds = dispensingSheetOperations.stream()
                .filter(operation -> {
                    if (operation.getOperationType() != DispensingSheetOperation.OperationType.SEND_MEDICINE) {
                        return false;
                    }

                    OperationRecordForDispense operationRecordForDispense = JsonUtils.readValue(operation.getRecordJson(), OperationRecordForDispense.class);
                    if (operationRecordForDispense == null) {
                        return false;
                    }

                    if (dispensingSheetReDispenseCount > operationRecordForDispense.getDispensingSheetReDispenseCount()) {
                        return true;
                    }
                    return false;
                }).map(DispensingSheetOperation::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(historyDispenseOperationIds)) {
            return new HashMap<>();
        }

        return dispensingItemDispenseLogs.stream()
                // 兼容历史逻辑：在发药项可部分数量发药需求之前由于发药的 stockDealId 和 operationId 都只记录在了 dispensingFormItem 上，所以直接过滤
                .filter(dispensingItemDispenseLog -> StringUtils.isNotBlank(dispensingItemDispenseLog.getStockDealId()) && StringUtils.isNotBlank(dispensingItemDispenseLog.getOperationId()))
                .filter(dispensingItemDispenseLog -> historyDispenseOperationIds.contains(dispensingItemDispenseLog.getOperationId()))
                .collect(Collectors.groupingBy(DispensingLogItem::getDispensingFormItemId, Collectors.mapping(DispensingLogItem::getStockDealId, Collectors.toList())));
    }

    /**
     * 获取发药单所有可发药项
     * 场景一：如果发药单状态就是待发的状态，则直接返回所有可发的发药项
     * 场景二：如果发药单是已退状态，但是本次是重新发药的请求，那么所有已退药并且未退费的项都可以重新发
     */
    private List<DispensingFormItem> collectDispenseableItems() {
        int dispensingSheetStatus = dispensingSheet.getStatus();
        if (dispensingSheetStatus == DispensingSheet.Status.WAITING) {
            return DTOConverter.collectDispensingSheetItemsWithStatus(dispensingSheet, DispensingFormItem.Status.dispenseable);
        } else if (dispensingSheetStatus == DispensingSheet.Status.UNDISPENSED && isReDispense == 1) {
            List<DispensingFormItem> dispensingItems = DTOConverter.collectDispensingSheetItems(dispensingSheet);
            if (CollectionUtils.isEmpty(dispensingItems) || CollectionUtils.isEmpty(chargeItemIdToChargeItem)) {
                return new ArrayList<>();
            }

            return dispensingItems.stream()
                    .filter(dispensingItem -> {
                        if (dispensingItem == null) {
                            return false;
                        }

                        int dispensingItemStatus = dispensingItem.getStatus();
                        String chargeItemId = dispensingItem.getSourceFormItemId();
                        if (StringUtils.isBlank(chargeItemId) || !chargeItemIdToChargeItem.containsKey(chargeItemId)) {
                            return false;
                        }
                        ChargeFormItem chargeFormItem = chargeItemIdToChargeItem.get(chargeItemId);

                        if (chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.CHARGED) {
                            refundFeeDispensingFormItemIds.add(dispensingItem.getId());
                        }

                        /*
                            只有已退药并且未退费的项才能重新发药
                            因为虽然有发药项有'已取消'状态来标示发药项退费了，但是如果一个药是发药然后退药然后再退费的话，状态其实是已退，而不是已取消
                         */
                        if (dispensingItemStatus == DispensingFormItem.Status.DISPENSED
                                && chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED) {
                            return true;
                        }
                        return false;
                    })
                    .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Setter
    @Accessors(chain = true)
    public static class DispenseOptBuilder {

        /**
         * 诊所ID
         */
        private String clinicId;

        /**
         * 客户端发药请求
         */
        private DispensingSheet clientDispenseSheet;

        /**
         * 收费单
         */
        private ChargeSheet chargeSheet;

        /**
         * 是否是收费直接发药
         */
        private boolean isDirectDispenseChargeSheet;

        /**
         * 发药人ID列表
         */
        private List<String> dispensedByIds;

        /**
         * 操作人ID
         */
        private String operatorId;

        /**
         * 是否是重新发药
         */
        private int isReDispense;

        /**
         * 发药的配置文件
         */
        private Dispensing dispensingConfig;
        /**
         * 追溯码配置
         */
        private TraceCodeConfig traceCodeConfig;

        public static DispenseOptBuilder of(DispensingSheet clientDispenseSheet, String clinicId, String operatorId) {
            if (clientDispenseSheet == null || TextUtils.isEmpty(clientDispenseSheet.getId())) {
                throw new ParamRequiredException("id");
            }

            DispenseOptBuilder dispenseOptBuilder = new DispenseOptBuilder();
            dispenseOptBuilder.setClientDispenseSheet(clientDispenseSheet);
            dispenseOptBuilder.setClinicId(clinicId);
            dispenseOptBuilder.setOperatorId(operatorId);
            return dispenseOptBuilder;
        }

        public DispenseOpt build() {
            List<DispensingForm> dispensingFormList = DTOConverter.collectDispensingSheetFormList(clientDispenseSheet);
            if (CollectionUtils.isEmpty(dispensingFormList)) {
                throw new NoDispenseItemException();
            }
            // 发药项客户端发药请求列表
            List<DispensingFormItem> clientDispenseItems = DTOConverter.collectDispensingSheetItems(clientDispenseSheet);
            if (CollectionUtils.isEmpty(clientDispenseItems)) {
                throw new NoDispenseItemException();
            }
            AbcIdGenerator abcIdGenerator = SpringUtils.getBean(AbcIdGenerator.class);

            // 客户端参数装配
            DispenseOpt dispenseOpt = SpringUtils.getBean(DispenseOpt.class);
            if (isReDispense == 1) {
                dispenseOpt.setRedispenseOperationId(abcIdGenerator.getUID());
            }
            dispenseOpt.setOperationId(abcIdGenerator.getUID());
            dispenseOpt.setClinicId(clinicId);
            dispenseOpt.setDispensingSheetId(clientDispenseSheet.getId());
            dispenseOpt.setClientDispenseSheet(clientDispenseSheet);
            dispenseOpt.setClientDispenseItems(clientDispenseItems);
            dispenseOpt.setDispenseItemIdToClientDispenseItem(ListUtils.toMap(clientDispenseItems, DispensingFormItem::getId));
            dispenseOpt.setDirectDispenseChargeSheet(isDirectDispenseChargeSheet);
            dispenseOpt.setDispensedByIds(CollectionUtils.isEmpty(dispensedByIds) ? Collections.singletonList(operatorId) : dispensedByIds);
            dispenseOpt.setOperatorId(operatorId);
            dispenseOpt.setChargeSheet(chargeSheet);
            dispenseOpt.setIsReDispense(isReDispense);
            dispenseOpt.setDispensingConfig(dispensingConfig);
            dispenseOpt.setTraceCodeConfig(traceCodeConfig);

            return dispenseOpt;
        }

    }

}
