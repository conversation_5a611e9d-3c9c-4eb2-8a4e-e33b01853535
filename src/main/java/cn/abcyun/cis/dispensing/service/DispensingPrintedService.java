package cn.abcyun.cis.dispensing.service;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeRefundedBatchView;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeRefundedItemView;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.OrganPrintView;
import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.*;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslog.GoodsStockLogItem;
import cn.abcyun.bis.rpc.sdk.cis.model.patient.GetPatientBasicInfosRsp;
import cn.abcyun.bis.rpc.sdk.property.base.PropertyKey;
import cn.abcyun.bis.rpc.sdk.property.model.ChainBasic;
import cn.abcyun.bis.rpc.sdk.property.model.PrintDispensingSetting;
import cn.abcyun.bis.rpc.sdk.property.model.PrintMedicalDocumentsInfusion;
import cn.abcyun.bis.rpc.sdk.property.service.PropertyService;
import cn.abcyun.cis.commons.amqp.message.ChargeSheetMessage;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.message.ToBMessage;
import cn.abcyun.cis.commons.message.WebMessageBody;
import cn.abcyun.cis.commons.model.CisBarcodeType;
import cn.abcyun.cis.commons.model.CisPatientAge;
import cn.abcyun.cis.commons.model.CisPatientInfo;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.cis.commons.rpc.charge.*;
import cn.abcyun.cis.commons.rpc.outpatient.UsageInfo;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.commons.util.MathUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import cn.abcyun.cis.dispensing.amqp.RocketMqProducer;
import cn.abcyun.cis.dispensing.api.protocol.*;
import cn.abcyun.cis.dispensing.api.protocol.print.DispensingPrintLogReq;
import cn.abcyun.cis.dispensing.api.protocol.print.RefundFeeAutoPrintMessage;
import cn.abcyun.cis.dispensing.controller.DispensingItemMerger;
import cn.abcyun.cis.dispensing.domain.*;
import cn.abcyun.cis.dispensing.repository.DispensingSheetOperationRepository;
import cn.abcyun.cis.dispensing.repository.DispensingSheetRepository;
import cn.abcyun.cis.dispensing.repository.DispensingSheetV2Repository;
import cn.abcyun.cis.dispensing.service.dto.*;
import cn.abcyun.cis.dispensing.service.dto.operation.DispensingFormRecordViewForAudit;
import cn.abcyun.cis.dispensing.service.dto.operation.DispensingFormRecordViewForCompound;
import cn.abcyun.cis.dispensing.service.dto.operation.OperationRecordForAudit;
import cn.abcyun.cis.dispensing.service.dto.operation.OperationRecordForCompound;
import cn.abcyun.cis.dispensing.service.dto.print.DispensingFormItemBatchInfoPrintView;
import cn.abcyun.cis.dispensing.service.dto.print.DispensingFormItemPrintView;
import cn.abcyun.cis.dispensing.service.dto.print.DispensingFormPrintView;
import cn.abcyun.cis.dispensing.service.dto.print.DispensingSheetPrintView;
import cn.abcyun.cis.dispensing.service.operation.SheetOperationBase;
import cn.abcyun.cis.dispensing.service.operation.SheetOperationCreateFactory;
import cn.abcyun.cis.dispensing.service.rpc.CisCrmFeignClient;
import cn.abcyun.cis.dispensing.service.rpc.ClinicClient;
import cn.abcyun.cis.dispensing.service.rpc.GoodsLogService;
import cn.abcyun.cis.dispensing.service.rpc.ScGoodsFeignClient;
import cn.abcyun.cis.dispensing.util.DispensingUtils;
import cn.abcyun.cis.dispensing.util.DispensingViewUtils;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 打印服务
 *
 * <AUTHOR>
 * @modified 从DispensingService 里面打印相关的方法迁移过来
 * 事务注解全放到前面调用的DispensingService 或 RpcService上·
 */
@Slf4j
@Service
public class DispensingPrintedService {

    public static final Logger sLogger = LoggerFactory.getLogger(DispensingPrintedService.class);
    @Autowired
    private DispensingSheetRepository dispensingSheetRepository;
    @Autowired
    private DispensingSheetOperationRepository dispensingSheetOperationRepository;

    @Autowired
    private AbcIdGenerator abcIdGenerator;
    @Autowired
    private RocketMqProducer rocketMqProducer;

    @Autowired
    private CisCrmFeignClient crmFeignClient;
    @Autowired
    private ClinicClient clinicClient;
    @Autowired
    private ScGoodsFeignClient scGoodsFeignClient;

    @Autowired
    private PatientOrderService patientOrderService;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private ChargeService chargeService;
    @Autowired
    private DispensingProcessService dispensingProcessService;
    @Autowired
    private PropertyService propertyService;

    @Autowired
    private DispensingSheetEntityService dispensingSheetEntityService;

    @Autowired
    private GoodsLogService goodsLogService;
    @Autowired
    private DispensingSheetV2Repository dispensingSheetV2Repository;
    @Autowired
    private SheetOperationCreateFactory sheetOperationCreateFactory;

    /**
     * 发药单打印接口
     *
     * @param dispensingSheetId    发药单Id
     * @param headerClinicId       发药单所属门店Id
     * @param printUndispense      打印退药单
     * @param chargeTransactionIds
     * @return 发药打印信息
     */
    public DispensingSheetPrintView findDispensingSheetPrintViewById(String dispensingSheetId,
                                                                     String headerChainId,
                                                                     String headerClinicId,
                                                                     boolean printUndispense,
                                                                     List<String> chargeTransactionIds) {
        // first get the dispensingSheet from db
        DispensingSheet dispensingSheet = dispensingSheetEntityService.findByIdAndClinicId(dispensingSheetId, headerClinicId);
        if (dispensingSheet == null) {
            throw new NotFoundException();
        }
        bindVirtualDispensingFormItemBatchInfo(headerChainId, headerClinicId, Collections.singletonList(dispensingSheet), false);
        boolean filterByRecords = false;
        Map<String, ChargeRefundedItemView> chargeFormItemIdToRefundedItem = Collections.emptyMap();
        if (printUndispense) {
            if (!CollectionUtils.isEmpty(chargeTransactionIds)) {
                filterByRecords = true;
            }
            chargeFormItemIdToRefundedItem = buildRefundFeeItemMapByChargeTransactionIds(headerChainId, headerClinicId, dispensingSheetId, dispensingSheet.getSourceSheetId(), chargeTransactionIds);
            if (filterByRecords && CollectionUtils.isEmpty(chargeFormItemIdToRefundedItem)) {
                return null;
            }
        }

        // then pull the print related info from other service  and create the response
        DispensingSheetPrintView dispensingSheetPrintView = generateDispensingSheetPrintView(dispensingSheet,
                patientOrderService.findPatientOrder(dispensingSheet.getPatientOrderId()),
                clinicClient.getOrganPrintInfo(dispensingSheet.getClinicId()),
                employeeService.findEmployeeNameNoExp(dispensingSheet.getDispensedBy(), dispensingSheet.getChainId()),
                chargeService.getSimpleChargeInfo(dispensingSheet.getSourceSheetId()),
                scGoodsFeignClient.findPharmacyByClinic(dispensingSheet.getChainId(), dispensingSheet.getClinicId())
                        .stream()
                        .filter(goodsPharmacyView -> Objects.equals(goodsPharmacyView.getNo(), dispensingSheet.getPharmacyNo()))
                        .findFirst()
                        .orElse(null),
                scGoodsFeignClient.getGoodsConfig(headerClinicId).getOpenPharmacyFlag(),
                printUndispense, filterByRecords, chargeFormItemIdToRefundedItem);

        if (dispensingSheetPrintView == null) {
            throw new NotFoundException();
        }

        return dispensingSheetPrintView;
    }

    private void bindLockedBatchInfo(DispensingSheet dispensingSheet, Map<String, GoodsItem> dispenseFormItemIdToGoodsItem) {

        if (dispensingSheet.getStatus() != DispensingSheet.Status.WAITING) {
            return;
        }

        //如果发药单上有lockId，但是又没有批次的item，就需要去查询goods，拿到lockId对应锁的批次
        boolean needQueryItemBatchInfo = DispensingUtils.getDispensingFormItems(dispensingSheet)
                .stream()
                .anyMatch(dispensingFormItem ->
                        Objects.nonNull(dispensingFormItem.getLockId())
                                && CollectionUtils.isEmpty(dispensingFormItem.getDispensingFormItemBatches())
                        && dispensingFormItem.getStatus() == DispensingFormItem.Status.WAITING
                );

        if (!needQueryItemBatchInfo) {
            return;
        }

        if (dispenseFormItemIdToGoodsItem == null) {
            dispenseFormItemIdToGoodsItem = scGoodsFeignClient.queryGoodsItemWitchStockByDispensingSheet(dispensingSheet.getClinicId(), dispensingSheet.getChainId(), true, 1, dispensingSheet);
        }

        if (MapUtils.isEmpty(dispenseFormItemIdToGoodsItem)) {
            return;
        }

        Map<String, GoodsItem> finalDispenseFormItemIdToGoodsItem = dispenseFormItemIdToGoodsItem;
        DispensingUtils.doWithDispensingItem(Arrays.asList(dispensingSheet), dispensingFormItem -> {
            if (!CollectionUtils.isEmpty(dispensingFormItem.getDispensingFormItemBatches())) {// 如果已经有批次信息了不需要再去查了
                return;
            }

            if (Objects.isNull(dispensingFormItem.getLockId())) {
                return;
            }

            if (dispensingFormItem.getStatus() != DispensingFormItem.Status.WAITING) {
                return;
            }

            GoodsItem goodsItem = finalDispenseFormItemIdToGoodsItem.get(dispensingFormItem.getId());
            if (Objects.isNull(goodsItem) || CollectionUtils.isEmpty(goodsItem.getGoodsBatchInfoList())) {
                return;
            }

            BigDecimal batchTotalCount = goodsItem.getGoodsBatchInfoList()
                    .stream()
                    .map(goodsBatchInfo -> dispensingFormItem.getUseDismounting() == 1 ? goodsBatchInfo.getCutPieceCount() : goodsBatchInfo.getCutPackageCount())
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (batchTotalCount.compareTo(MathUtils.calculateTotalCount(dispensingFormItem.getUnitCount(), dispensingFormItem.getDoseCount())) != 0) {
                return;
            }

            dispensingFormItem.setDispensingFormItemBatches(goodsItem.getGoodsBatchInfoList()
                    .stream()
                    .map(goodsBatchInfo -> {
                        DispensingFormItemBatchInfo dispensingFormItemBatchInfo = new DispensingFormItemBatchInfo();
                        dispensingFormItemBatchInfo.setProductId(dispensingFormItem.getProductId());
                        dispensingFormItemBatchInfo.setBatchId(goodsBatchInfo.getBatchId());
                        if (dispensingFormItem.getUseDismounting() == 1) {
                            dispensingFormItemBatchInfo.setUnitCount(goodsBatchInfo.getCutPieceCount());
                        } else {
                            dispensingFormItemBatchInfo.setUnitCount(goodsBatchInfo.getCutPackageCount());
                        }

                        return dispensingFormItemBatchInfo;
                    }).collect(Collectors.toList()));
        });
    }

    private Map<String, ChargeRefundedItemView> buildRefundFeeItemMapByChargeTransactionIds(String headerChainId, String headerClinicId, String dispensingSheetId, String chargeSheetId, List<String> chargeTransactionIds) {
        // 查询当此交易的信息
        List<ChargeRefundedItemView> chargeRefundedItemViews = chargeService.queryChargeRefundItems(headerChainId, headerClinicId, chargeSheetId, chargeTransactionIds);
        return ListUtils.toMap(chargeRefundedItemViews, ChargeRefundedItemView::getChargeFormItemId);
    }


    /***
     * 发药单打印接口
     * @param sourceSheetId 发药单Id
     * @param clinicId 发药单所属门店Id
     * @return 发药打印信息
     * */
    @Deprecated
    public DispensingSheetPrintView findDispensingSheetPrintViewBySourceSheetId(String sourceSheetId, String clinicId) throws ServiceInternalException {
        DispensingSheet dispensingSheet = dispensingSheetRepository.findFirstBySourceSheetIdAndClinicIdAndIsDeleted(sourceSheetId, clinicId, 0).orElse(null);
        if (dispensingSheet == null) {
            return null;
        }

        DispensingSheetPrintView dispensingSheetPrintView = generateDispensingSheetPrintView(dispensingSheet,
                patientOrderService.findPatientOrder(dispensingSheet.getPatientOrderId()),
                clinicClient.getOrganPrintInfo(dispensingSheet.getClinicId()),
                employeeService.findEmployeeNameNoExp(dispensingSheet.getDispensedBy(), dispensingSheet.getChainId()),
                chargeService.getSimpleChargeInfo(dispensingSheet.getSourceSheetId()),
                scGoodsFeignClient.findPharmacyByClinic(dispensingSheet.getChainId(), dispensingSheet.getClinicId())
                        .stream()
                        .filter(goodsPharmacyView -> Objects.equals(goodsPharmacyView.getNo(), dispensingSheet.getPharmacyNo()))
                        .findFirst()
                        .orElse(null),
                scGoodsFeignClient.getGoodsConfig(clinicId).getOpenPharmacyFlag(), false, false, null);
        if (dispensingSheet == null) {
            throw new NotFoundException();
        }
        return dispensingSheetPrintView;
    }

    /**
     * 打印收费单的发药单
     *
     * @param chargeSheetId   收药单Id
     * @param headerClinicId  发药单所属门店Id
     * @param printUndispense 打印退药单
     * @return 发药打印信息
     * 和上面的打印一个发药单逻辑差不多 一个收费单可能有多个发药单
     */
    public DispensingSheetPrintViewListRsp findDispensingSheetPrintViewListBySourceSheetId(String chargeSheetId,
                                                                                           String headerChainId,
                                                                                           String headerClinicId,
                                                                                           boolean printUndispense) throws ServiceInternalException {
        DispensingSheetPrintViewListRsp clientRsp = new DispensingSheetPrintViewListRsp();
        clientRsp.setDispensingSheets(new ArrayList<>());

        //one charge maybe have more than one dispensing sheet.
        List<DispensingSheet> dispensingSheets = dispensingSheetEntityService.findAllBySourceSheetId(headerChainId, chargeSheetId, printUndispense ? 1 : 0, printUndispense ? 1 : 0);
        if (CollectionUtils.isEmpty(dispensingSheets)) {
            return clientRsp;
        }
        bindVirtualDispensingFormItemBatchInfo(headerChainId, headerClinicId, dispensingSheets, false);
        dispensingSheets.sort(Comparator.comparing(DispensingSheet::getCreated).reversed());

        boolean filterByRecords;
        Map<String, ChargeRefundedItemView> chargeFormItemIdToRefundFeeItem;
        if (printUndispense) {
            List<ChargeRefundedItemView> chargeRefundedItems = chargeService.queryChargeRefundItems(headerChainId, headerClinicId, chargeSheetId, null);
            if (CollectionUtils.isEmpty(chargeRefundedItems)) {
                return clientRsp;
            }
            chargeFormItemIdToRefundFeeItem = ListUtils.toMap(chargeRefundedItems, ChargeRefundedItemView::getChargeFormItemId);
            filterByRecords = true;
        } else {
            chargeFormItemIdToRefundFeeItem = Collections.emptyMap();
            filterByRecords = false;
        }

        // then pull the print related info from other service  and create the response
        PatientOrder patientOrder = patientOrderService.findPatientOrder(dispensingSheets.get(0).getPatientOrderId());
        OrganPrintView organPrintInfo = clinicClient.getOrganPrintInfo(headerClinicId);
        SimpleChargeInfo simpleChargeInfo = chargeService.getSimpleChargeInfo(chargeSheetId);
        List<String> employeeIds = dispensingSheets.stream()
                .filter(dispensingSheet -> StringUtils.isNotEmpty(dispensingSheet.getDispensedBy()))
                .map(dispensingSheet -> dispensingSheet.getDispensedBy())
                .distinct().collect(Collectors.toList());
        Map<String, String> employeeIdToEmployeeName = employeeService.findEmployeeNameMap(employeeIds, headerChainId);
        // 药房名称
        Map<Integer, GoodsPharmacyView> pharmacyNoToPharmacyView = scGoodsFeignClient.findPharmacyByClinic(dispensingSheets.get(0).getChainId(), dispensingSheets.get(0).getClinicId())
                .stream()
                .collect(Collectors.toMap(GoodsPharmacyView::getNo, Function.identity(), (a, b) -> a));

        // 是否开启多药房
        int openPharmacyFlag = scGoodsFeignClient.getGoodsConfig(headerClinicId).getOpenPharmacyFlag();

        clientRsp.getDispensingSheets().addAll(dispensingSheets.stream().map(dispensingSheet -> {
            String dispensedByName = "";
            if (StringUtils.isNotEmpty(dispensingSheet.getDispensedBy())) {
                dispensedByName = employeeIdToEmployeeName.getOrDefault(dispensingSheet.getDispensedBy(), "");
            }
            return generateDispensingSheetPrintView(dispensingSheet, patientOrder, organPrintInfo, dispensedByName, simpleChargeInfo,
                    pharmacyNoToPharmacyView.get(dispensingSheet.getPharmacyNo()), openPharmacyFlag, printUndispense, filterByRecords, chargeFormItemIdToRefundFeeItem);
        }).filter(Objects::nonNull).collect(Collectors.toList()));

        return clientRsp;
    }

    /**
     * 绑定虚拟的 dispensingFormItemBatchInfo
     * @param alwaysBind 是否强制绑定
     */
    private void bindVirtualDispensingFormItemBatchInfo(String headerChainId, String headerClinicId, List<DispensingSheet> dispensingSheets, boolean alwaysBind) {
        if (CollectionUtils.isEmpty(dispensingSheets)) {
            return;
        }

        if (!alwaysBind) {
            // 只有开启了打印批号配置的门店才需要去查
            PrintDispensingSetting printDispensingSetting = propertyService.getPropertyValueByKey(PropertyKey.PRINT_DISPENSING, headerClinicId, PrintDispensingSetting.class);
            if (printDispensingSetting == null || printDispensingSetting.getMaterialGoods() == null || printDispensingSetting.getWesternMedicine() == null
                    || (printDispensingSetting.getMaterialGoods().getBatchNo() == 0 && printDispensingSetting.getWesternMedicine().getBatchNo() == 0)) {
                return;
            }
        }

        Map<String, List<GoodsStockLogItem>> dispensingFormItemIdToGoodsLogItems = goodsLogService.getDispensingSheetCurrentGoodsStockLogMap(headerChainId, headerClinicId, dispensingSheets);
        if (CollectionUtils.isEmpty(dispensingFormItemIdToGoodsLogItems)) {
            return;
        }

        DispensingUtils.doWithDispensingItem(dispensingSheets, dispensingFormItem -> {
            if (!CollectionUtils.isEmpty(dispensingFormItem.getDispensingFormItemBatches())) {// 如果已经有批次信息了不需要再去查了
                return;
            }

            List<GoodsStockLogItem> goodsStockLogItems = dispensingFormItemIdToGoodsLogItems.get(dispensingFormItem.getId());
            if (CollectionUtils.isEmpty(goodsStockLogItems)) {
                return;
            }

            List<DispensingFormItemBatchInfo> dispensingFormItemBatchInfos = buildVirtualDispensingFormItemBatchInfos(dispensingFormItem, goodsStockLogItems);
            dispensingFormItem.setDispensingFormItemBatches(dispensingFormItemBatchInfos);
        });
    }

    private List<DispensingFormItemBatchInfo> buildVirtualDispensingFormItemBatchInfos(DispensingFormItem dispensingFormItem,
                                                                                       List<GoodsStockLogItem> itemStockLogs) {
        if (CollectionUtils.isEmpty(itemStockLogs) || dispensingFormItem == null) {
            return new ArrayList<>();
        }

        List<DispensingFormItemBatchInfo> dispensingFormItemBatchInfos = new ArrayList<>();
        ListUtils.groupByKey(itemStockLogs, GoodsStockLogItem::getBatchId).forEach((batchId, batchStockLogs) -> {
            if (CollectionUtils.isEmpty(batchStockLogs)) {
                return;
            }

            GoodsStockLogItem firstGoodsStockLog = batchStockLogs.get(0);
            DispensingFormItemBatchInfo virtualDispensingFormItemBatchInfo = new DispensingFormItemBatchInfo();
            virtualDispensingFormItemBatchInfo.setProductId(firstGoodsStockLog.getGoodsId());
            virtualDispensingFormItemBatchInfo.setBatchId(firstGoodsStockLog.getBatchId());

            for (GoodsStockLogItem batchStockLog : batchStockLogs) {
                BigDecimal packageCount = batchStockLog.getChangePackageCount();
                BigDecimal pieceCount = batchStockLog.getChangePieceCount();
                BigDecimal formattedPieceNum = BigDecimal.valueOf(batchStockLog.getPieceNum());
                BigDecimal count;
                if (dispensingFormItem.getUseDismounting() == 1) {
                    count = MathUtils.wrapBigDecimalAdd(cn.abcyun.cis.dispensing.util.MathUtils.wrapBigDecimalMultiply(packageCount, formattedPieceNum), pieceCount);
                } else {
                    BigDecimal piecePackageCount = BigDecimal.ZERO;
                    if (MathUtils.wrapBigDecimalCompare(pieceCount, BigDecimal.ZERO) != 0) {
                        piecePackageCount = pieceCount.divide(formattedPieceNum, 2, RoundingMode.DOWN);
                    }
                    count = MathUtils.wrapBigDecimalAdd(packageCount, piecePackageCount);
                }
                count = count.abs();

                if (Objects.equals(batchStockLog.getAction(), GoodsConst.StockLogAction.ACTION_DISPENSE)) {
                    virtualDispensingFormItemBatchInfo.setDispenseUnitCount(MathUtils.wrapBigDecimalAdd(virtualDispensingFormItemBatchInfo.getDispenseUnitCount(), count));
                    // 这个值只有在发药项是已发药的时候才是准确的！！！
                    virtualDispensingFormItemBatchInfo.setUnitCount(virtualDispensingFormItemBatchInfo.getDispenseUnitCount());
                } else if (Objects.equals(batchStockLog.getAction(), GoodsConst.StockLogAction.ACTION_UN_DISPENSE)) {
                    virtualDispensingFormItemBatchInfo.setUndispenseUnitCount(MathUtils.wrapBigDecimalAdd(virtualDispensingFormItemBatchInfo.getUndispenseUnitCount(), count));
                }
            }

            dispensingFormItemBatchInfos.add(virtualDispensingFormItemBatchInfo);
        });

        return dispensingFormItemBatchInfos;
    }

    /***
     * 批量 修改 发药单是否打印状态
     * */
    public UpdatePrintedNumberListRsp putPrintedNumber(UpdatePrintedNumberListReq req) {
        UpdatePrintedNumberListRsp rsp = new UpdatePrintedNumberListRsp();
        if (!req.parameterCheck()) {
            return rsp;
        }
        // 已打印发药单Id集合
        List<String> dispensingIds = req.getPrintedNumbers().stream().filter(printedNumber -> printedNumber.getIsPrinted() > 0).map(UpdatePrintedNumberListReq.UpdatePrintedNumber::getDispensingId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dispensingIds)) {
            return rsp;
        }
        List<DispensingSheet> dispensingSheets = dispensingSheetRepository.findAllByChainIdAndIdInAndIsDeleted(req.getChainId(), dispensingIds, 0).orElse(null);
        if (CollectionUtils.isEmpty(dispensingSheets)) {
            return rsp;
        }
        dispensingSheets.forEach(dispensingSheet -> {
            dispensingSheet.setPrintedNumber((dispensingSheet.getPrintedNumber() + 1));
            DispensingSheetAbstract dispensingSheetAbstract = new DispensingSheetAbstract();
            BeanUtils.copyProperties(dispensingSheet, dispensingSheetAbstract);
            rsp.getDispensingSheetAbstracts().add(dispensingSheetAbstract);
        });
        dispensingSheetRepository.saveAll(dispensingSheets);
        return rsp;
    }

    /**
     * 用药标签打印
     */
    public DispensingSheetView findDispensingPrintLabel(String dispensingId, String clinicId) {
        DispensingSheetView dispensingSheetView = new DispensingSheetView();
        DispensingSheet dispensingSheet = dispensingSheetRepository.findByIdAndClinicIdAndIsDeleted(dispensingId, clinicId, 0).orElse(null);
        if (dispensingSheet == null) {
            return dispensingSheetView;
        }
        if(dispensingSheet.hasDispensingTag(DispensingUtils.DispensingTag.DISPENSING_TAG_SHEBAO_PAY_FLAG)){
            dispensingSheetView.setIsShebaoPay(YesOrNo.YES);
        }

        /***
         * 先检查状态
         * */
        if (dispensingSheet.getStatus() != DispensingSheet.Status.WAITING
                && dispensingSheet.getStatus() != DispensingSheet.Status.DISPENSED) {
            return dispensingSheetView;
        }
        /**
         * 从crm拿患者信息
         * */
        GetPatientBasicInfosRsp patientBasicInfos = crmFeignClient.getPatientBasicInfos(dispensingSheet.getChainId(), Arrays.asList(dispensingSheet.getPatientId()));
        if (Objects.nonNull(patientBasicInfos) && !CollectionUtils.isEmpty(patientBasicInfos.getPatientInfos())) {
            CisPatientInfo cisPatientInfo = new CisPatientInfo();
            BeanUtils.copyProperties(patientBasicInfos.getPatientInfos().get(0), cisPatientInfo);
            if (patientBasicInfos.getPatientInfos().get(0).getAge() != null) {
                CisPatientAge cisPatientAge = new CisPatientAge();
                BeanUtils.copyProperties(patientBasicInfos.getPatientInfos().get(0).getAge(), cisPatientAge);
                cisPatientInfo.setAge(cisPatientAge);
            }
            dispensingSheetView.setPatient(cisPatientInfo);
        }

        //查询patientOrder
        PatientOrder patientOrder = patientOrderService.findPatientOrder(dispensingSheet.getPatientOrderId());
        if (Objects.nonNull(patientOrder)) {
            dispensingSheetView.setSource(patientOrder.getSource());
            dispensingSheetView.setPatientOrderNo(String.valueOf(patientOrder.getNo()));
            dispensingSheetView.setAirPharmacyOrderId(patientOrder.getAirPharmacyOrderId());
        }

        //查询doctorName
        if (StringUtils.isNotEmpty(dispensingSheet.getDoctorId())) {
            //查询employee
            Map<String, String> employeeNameMap = employeeService.findEmployeeNameMap(Arrays.asList(dispensingSheet.getDoctorId()), dispensingSheet.getChainId());
            dispensingSheetView.setDoctorName(employeeNameMap.get(dispensingSheet.getDoctorId()));
        }

        /**
         * init sheetView
         * */
        BeanUtils.copyProperties(dispensingSheet, dispensingSheetView, "dispensingForms");
        if (!CollectionUtils.isEmpty(dispensingSheet.getDispensingForms())) {
            /**
             * for init formView
             * */
            dispensingSheetView.setDispensingForms(new ArrayList<>());
            dispensingSheet.getDispensingForms().sort(Comparator.comparing(DispensingForm::getSort));
            for (DispensingForm dispensingForm : dispensingSheet.getDispensingForms()) {
                DispensingFormView dispensingFormView = new DispensingFormView();
                BeanUtils.copyProperties(dispensingForm, dispensingFormView, "dispensingFormItems");
                if (!CollectionUtils.isEmpty(dispensingForm.getDispensingFormItems())) {
                    dispensingForm.getDispensingFormItems().sort(Comparator.comparing(DispensingFormItem::getSort));
                    dispensingFormView.setDispensingFormItems(new ArrayList<>());
                    for (DispensingFormItem dispensingFormItem : dispensingForm.getDispensingFormItems()) {
                        if (DispensingFormItem.Status.available.contains(dispensingFormItem.getStatus())) {
                            DispensingFormItemView itemView = new DispensingFormItemView();
                            BeanUtils.copyProperties(dispensingFormItem, itemView);
                            dispensingFormView.getDispensingFormItems().add(itemView);
                        }
                    }
                }
                dispensingSheetView.getDispensingForms().add(dispensingFormView);
            }
        }
        /**
         * 更新Goods信息
         * */
        dispensingSheetView.refreshProductInfo(scGoodsFeignClient, dispensingSheet);
        return dispensingSheetView;
    }


    /**
     * 生成一个发药单的打印信息
     *
     * @param dispensingSheet                 发药单
     * @param patientOrder                    就诊单
     * @param organPrintInfo                  门店打印配置信息
     * @param dispensedByName                 发药人
     * @param simpleChargeInfo                收费单信息摘要
     * @param pharmacyName                    药房名称
     * @param openPharmacyFlag                是否开启多药房
     * @param printUndispense                 是否为打印退药
     * @param filterByRecords                 是否根据 records 过滤
     * @param chargeFormItemIdToRefundFeeItem chargeFormItemIdToRefundFeeItem
     * @return 发药单打印信息
     */
    private DispensingSheetPrintView generateDispensingSheetPrintView(DispensingSheet dispensingSheet,
                                                                      PatientOrder patientOrder,
                                                                      OrganPrintView organPrintInfo,
                                                                      String dispensedByName,
                                                                      SimpleChargeInfo simpleChargeInfo,
                                                                      GoodsPharmacyView pharmacyView,
                                                                      int openPharmacyFlag,
                                                                      boolean printUndispense,
                                                                      boolean filterByRecords,
                                                                      Map<String, ChargeRefundedItemView> chargeFormItemIdToRefundFeeItem) throws ServiceInternalException {
        if (dispensingSheet == null) {
            return null;
        }
        if (filterByRecords && CollectionUtils.isEmpty(chargeFormItemIdToRefundFeeItem)) {
            return null;
        }

        Map<String, SimpleChargeInfo.ChargeFormItemSimpleView> chargeFormItemSimpleViewMap = Optional.ofNullable(simpleChargeInfo)
                .map(SimpleChargeInfo::getChargeFormItems)
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(SimpleChargeInfo.ChargeFormItemSimpleView::getId, Function.identity(), (a, b) -> a));

        DispensingItemMerger.mergeForDispensingSheet(dispensingSheet);

        DispensingSheetPrintView dispensingSheetPrintView = new DispensingSheetPrintView();
        dispensingSheetPrintView.setPharmacyNo(dispensingSheet.getPharmacyNo());
        dispensingSheetPrintView.setPharmacyType(dispensingSheet.getPharmacyType());
        dispensingSheetPrintView.setPharmacyName(pharmacyView != null ? pharmacyView.getName() : null);
        dispensingSheetPrintView.setPharmacyAddress(pharmacyView != null ? pharmacyView.getPharmacyAddress() : null);
        dispensingSheetPrintView.setOpenPharmacyFlag(openPharmacyFlag);
        dispensingSheetPrintView.setDeliveredStatus(dispensingSheet.getDeliveredStatus());
        dispensingSheetPrintView.setProcessedStatus(dispensingSheet.getProcessedStatus());
        dispensingSheetPrintView.setIsPatientSelfPay(dispensingSheet.getIsPatientSelfPay());
        CisPatientInfo patient = DispensingUtils.parseCisPatientInfoFromPatientOrder(patientOrder);
        dispensingSheetPrintView.setPatient(patient);
        if (filterByRecords) {
            dispensingSheetPrintView.setChargeTransactionTime(chargeFormItemIdToRefundFeeItem.values().stream().findFirst().map(ChargeRefundedItemView::getCreated).orElse(null));
        }

        dispensingSheetPrintView.setOrgan(organPrintInfo != null ? new cn.abcyun.cis.commons.rpc.print.OrganPrintView(organPrintInfo.getId(), organPrintInfo.getName(), organPrintInfo.getName(), organPrintInfo.getAddressDetail(), organPrintInfo.getContactPhone()) : null);

        dispensingSheetPrintView.setDispensedByName(StringUtils.join(employeeService.findEmployeeNames(dispensingSheet.getDispensedByIds(), dispensingSheet.getChainId()), "，"));
        dispensingSheetPrintView.setDispensedTime(dispensingSheet.getDispensedTime());

        boolean isVirtualPharmacy = dispensingSheetPrintView.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY;
        boolean isDirectCharge = patientOrder != null && patientOrder.getSource() == PatientOrder.Source.CHARGE_RETAIL;
        if (dispensingSheet.getDispensingForms() != null) {
            Map<String, GoodsItem> dispenseFormItemIdToGoodsItem = scGoodsFeignClient.queryGoodsItemWitchStockByDispensingSheet(dispensingSheet.getClinicId(), dispensingSheet.getChainId(), true, 1, dispensingSheet);
            bindLockedBatchInfo(dispensingSheet, dispenseFormItemIdToGoodsItem);
            // 查询批次信息
            Map<String, GetGoodsStockBatchesReq.GetGoodsStockBatch> goodsIdToGetGoodsStockBatch = new HashMap<>();
            DispensingUtils.doWithDispensingItemBatch(dispensingSheet, batchInfo -> {
                //从GoodsItem里面捞出来的批次信息
                if (batchInfo.getGoodsBatchInfo() != null) {
                    return;
                }
                GetGoodsStockBatchesReq.GetGoodsStockBatch goodsStockBatch = goodsIdToGetGoodsStockBatch.get(batchInfo.getProductId());
                if (goodsStockBatch == null) {
                    goodsStockBatch = new GetGoodsStockBatchesReq.GetGoodsStockBatch();
                    goodsStockBatch.setGoodsId(batchInfo.getProductId());
                    goodsStockBatch.setBatchIds(new ArrayList<>());
                    goodsIdToGetGoodsStockBatch.put(batchInfo.getProductId(), goodsStockBatch);
                }
                goodsStockBatch.getBatchIds().add(batchInfo.getBatchId());
            });
            GetGoodsStockBatchesRsp goodsStockBatchesRsp = scGoodsFeignClient.getGoodsStockBatches(dispensingSheet.getClinicId(), new ArrayList<>(goodsIdToGetGoodsStockBatch.values()));
            Map<Long, GoodsBatchInfo> batchIdToBatchInfo = Optional.ofNullable(goodsStockBatchesRsp).map(GetGoodsStockBatchesRsp::getList)
                    .orElseGet(Lists::newArrayList).stream()
                    .map(BatchesGoodsStockItem::getList).flatMap(Collection::stream)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(GoodsBatchInfo::getBatchId, Function.identity(), (a, b) -> a));

            String isNotCombineForm = "isNotCombineFormKey";
            String isCombineForm = "isCombineFormKey";

            Map<String, List<DispensingForm>> dispensingFormsMap = dispensingSheet.getDispensingForms()
                    .stream()
                    .collect(Collectors.groupingBy(dispensingForm -> {
                        if (DispensingUtils.isNotCombineForm(dispensingForm.getSourceFormType())) {
                            return isNotCombineForm;
                        } else {
                            return isCombineForm;
                        }
                    }));
            List<DispensingFormPrintView> formPrintViews = new ArrayList<>();

            if (dispensingFormsMap != null) {

                formPrintViews.addAll(dispensingFormsMap.getOrDefault(isNotCombineForm, new ArrayList<>())
                        .stream()
                        .sorted((a, b) -> {
                            if (a.getSourceFormType() == b.getSourceFormType()) {
                                return ObjectUtils.compare(a.getSort(), b.getSort());
                            } else {
                                return ObjectUtils.compare(a.getSourceFormType(), b.getSourceFormType());
                            }
                        })
                        .flatMap(dispensingForm -> generateDispensingFormPrintView(dispensingSheet, dispensingForm, isVirtualPharmacy, isDirectCharge, dispenseFormItemIdToGoodsItem, chargeFormItemSimpleViewMap, batchIdToBatchInfo, printUndispense, filterByRecords, chargeFormItemIdToRefundFeeItem).stream())
                        .collect(Collectors.toList()));


                List<DispensingFormItemPrintView> itemPrintViews = dispensingFormsMap.getOrDefault(isCombineForm, new ArrayList<>())
                        .stream()
                        .filter(dispensingForm -> !CollectionUtils.isEmpty(dispensingForm.getDispensingFormItems()))
                        .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream())
                        .map(dispensingFormItem -> generateIsCombineDispensingFormItemPrintViews(dispensingFormItem, dispenseFormItemIdToGoodsItem, chargeFormItemSimpleViewMap, batchIdToBatchInfo, printUndispense, filterByRecords, chargeFormItemIdToRefundFeeItem))
                        .flatMap(Collection::stream)
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(itemPrintViews)) {
                    DispensingFormPrintView isCombineFormPrintView = new DispensingFormPrintView();
                    isCombineFormPrintView.setDispensingFormItems(itemPrintViews);
                    isCombineFormPrintView.setIsCombineForm(1);
                    formPrintViews.add(isCombineFormPrintView);
                }
            }


            dispensingSheetPrintView.setDispensingForms(formPrintViews);
        }

        if (CollectionUtils.isEmpty(dispensingSheetPrintView.getDispensingForms())) {
            return null;
        }

        if (simpleChargeInfo != null) {
            dispensingSheetPrintView.setChargedByName(simpleChargeInfo.getChargedByName());
            dispensingSheetPrintView.setChargedTime(simpleChargeInfo.getChargedTime());
            dispensingSheetPrintView.setSellerName(simpleChargeInfo.getSellerName());
            dispensingSheetPrintView.setDoctorName(simpleChargeInfo.getDoctorName());
            if (!isVirtualPharmacy && dispensingSheetPrintView.getDeliveredStatus() != DispensingSheet.DeliveredStatus.NONE) {
                dispensingSheetPrintView.setDeliveryInfo(simpleChargeInfo.getDeliveryInfo());
            }
            dispensingSheetPrintView.setDepartmentName(simpleChargeInfo.getDepartmentName());
            dispensingSheetPrintView.setDiagnose(simpleChargeInfo.getDiagnose());
            dispensingSheetPrintView.setDoctorAdvice(simpleChargeInfo.getDoctorAdvice());
            if (printUndispense) {// TODO 不清楚这样写是不是对的
                dispensingSheetPrintView.setNetIncomeFee(dispensingSheetPrintView.getReceivedPrice());
            } else {
                dispensingSheetPrintView.setNetIncomeFee(simpleChargeInfo.getNetIncomeFee());
            }

            if (simpleChargeInfo.getVirtualPharmacyChargeForms() != null) {

                Map<String, Map<String, SimpleChargeInfo.ChargeFormSimpleView>> virtualPharmacyChargeFormSimpleViewMap = simpleChargeInfo.getVirtualPharmacyChargeForms().stream()
                        .collect(Collectors.groupingBy(chargeFormSimpleView -> String.format("%d-%d", chargeFormSimpleView.getPharmacyNo(), chargeFormSimpleView.getPharmacyType()),
                                Collectors.toMap(SimpleChargeInfo.ChargeFormSimpleView::getId, Function.identity(), (a, b) -> a)));

                Map<String, SimpleChargeInfo.ChargeFormSimpleView> chargeFormSimpleViewMap = virtualPharmacyChargeFormSimpleViewMap.getOrDefault(String.format("%d-%d", dispensingSheetPrintView.getPharmacyNo(), dispensingSheetPrintView.getPharmacyType()), null);

                if (chargeFormSimpleViewMap != null) {
                    Optional.ofNullable(dispensingSheetPrintView.getDispensingForms()).orElse(new ArrayList<>())
                            .stream()
                            .filter(dispensingFormPrintView -> StringUtils.isNotEmpty(dispensingFormPrintView.getSourceFormId()))
                            .forEach(dispensingFormPrintView -> {
                                SimpleChargeInfo.ChargeFormSimpleView chargeFormSimpleView = chargeFormSimpleViewMap.getOrDefault(dispensingFormPrintView.getSourceFormId(), null);

                                if (chargeFormSimpleView == null) {
                                    return;
                                }

                                dispensingFormPrintView.setDeliveryInfo(chargeFormSimpleView.getDeliveryInfo());
                                if (chargeFormSimpleView.getIsProcess() == 1) {
                                    dispensingFormPrintView.setProcessUsage("代煎");
                                    dispensingFormPrintView.setProcessUsageInfo("加工费");
                                    dispensingFormPrintView.setContactMobile(Optional.ofNullable(dispensingSheetPrintView.getPatient()).map(CisPatientInfo::getMobile).orElse(null));
                                }
                            });
                }

            }

        }

        if (patientOrder != null) {
            if (patientOrder.getSource() == PatientOrder.Source.CHARGE_RETAIL) {
                dispensingSheetPrintView.setPatientOrderNo("");
            } else {
                dispensingSheetPrintView.setPatientOrderNo(String.format("%08d", patientOrder.getNo()));
            }
        }

        // 代煎信息
        DispensingFormPrintView latestDecoctionFormView = dispensingSheetPrintView.getDispensingForms()
                .stream()
                .filter(DispensingFormPrintView::getIsDecoction)
                .reduce((a, b) -> b)
                .orElse(null);
        if (latestDecoctionFormView != null) {
            dispensingSheetPrintView.setIsDecoction(latestDecoctionFormView.getIsDecoction());
            dispensingSheetPrintView.setContactMobile(latestDecoctionFormView.getContactMobile());
        }
        if (patientOrder != null) {
            dispensingSheetPrintView.setBarcode(String.format("%08d", patientOrder.getNo()) + "-" + CisBarcodeType.DISPENSING);
        } else {
            dispensingSheetPrintView.setBarcode("");
        }
        setReviewer(dispensingSheet.getId(), dispensingSheetPrintView);
        setCompounder(dispensingSheet.getId(), dispensingSheetPrintView);

        return dispensingSheetPrintView;
    }

    private List<DispensingFormPrintView> generateDispensingFormPrintView(DispensingSheet dispensingSheet,
                                                                          DispensingForm dispensingForm,
                                                                          boolean isVirtualPharmacy,
                                                                          boolean isDirectCharge,
                                                                          Map<String, GoodsItem> goodsItemMap,
                                                                          Map<String, SimpleChargeInfo.ChargeFormItemSimpleView> chargeFormItemSimpleViewMap,
                                                                          Map<Long, GoodsBatchInfo> batchIdToBatchInfo,
                                                                          boolean printUndispense,
                                                                          boolean filterByRecords,
                                                                          Map<String, ChargeRefundedItemView> chargeFormItemIdToRefundFeeItem) {
        List<DispensingFormPrintView> printViews = new ArrayList<>();
        if (dispensingForm == null || dispensingForm.getDispensingFormItems() == null) {
            return printViews;
        } else if (filterByRecords && CollectionUtils.isEmpty(chargeFormItemIdToRefundFeeItem)) {
            return printViews;
        }

        List<DispensingFormItemPrintView> itemPrintViews = dispensingForm.getDispensingFormItems()
                .stream()
                .filter(Objects::nonNull)
                .sorted((a, b) -> ObjectUtils.compare(a.getSort(), b.getSort()))
                .map(dispensingFormItem -> generateDispensingFormItemPrintView(dispensingFormItem, goodsItemMap, chargeFormItemSimpleViewMap.get(dispensingFormItem.getSourceFormItemId()), batchIdToBatchInfo, printUndispense, filterByRecords, chargeFormItemIdToRefundFeeItem))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (itemPrintViews.size() == 0) {
            return printViews;
        }
        // 直接收费需要将中西医分开
        if (dispensingForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE && isDirectCharge) {
            DispensingFormPrintView formPrintView = null;
            for (DispensingFormItemPrintView itemPrintView : itemPrintViews) {
                if (formPrintView == null || !ObjectUtils.equals(formPrintView.getDoseCount(), itemPrintView.getDoseCount())) {
                    if (formPrintView != null) {
                        formPrintView.setProcessedStatus(dispensingForm.getProcessedStatus());
                        formPrintView.setDispensingFormId(dispensingForm.getId());
                        printViews.add(formPrintView);
                    }
                    //new
                    formPrintView = new DispensingFormPrintView();
                    formPrintView.setSourceFormType(dispensingForm.getSourceFormType());
                    formPrintView.addDispensingFormItemPrintView(itemPrintView);
                    formPrintView.setDoseCount(itemPrintView.getDoseCount());
                    formPrintView.setPharmacyNo(dispensingSheet.getPharmacyNo());
                    formPrintView.setPharmacyType(dispensingSheet.getPharmacyType());
                } else {
                    formPrintView.addDispensingFormItemPrintView(itemPrintView);
                }
            }
            if (formPrintView != null) {
                UsageInfo usageInfo = JsonUtils.readValue(dispensingForm.getUsageInfoJson(), UsageInfo.class);
                if (usageInfo != null) {
                    BeanUtils.copyProperties(usageInfo, formPrintView, new String[]{"doseCount"});
                    if (dispensingForm.getProcessedStatus() == DispensingForm.ProcessedStatus.NONE) {
                        //加工退费后不打印加工信息
                        formPrintView.setProcessBagUnitCount(0);
                        formPrintView.setProcessUsage(null);
                        formPrintView.setProcessUsageInfo(null);
                    }
                }
                if (itemPrintViews.stream().anyMatch(prescriptionFormItemPrintView -> TextUtils.equals(prescriptionFormItemPrintView.getCMSpec(), "中药饮片"))) {
                    formPrintView.setCMSpec("中药饮片");
                } else if (itemPrintViews.stream().anyMatch(prescriptionFormItemPrintView -> TextUtils.equals(prescriptionFormItemPrintView.getCMSpec(), "中药颗粒"))) {
                    formPrintView.setCMSpec("中药颗粒");
                }

                //虚拟药房需要sourceFormId去找对应的快递加工信息
                if (isVirtualPharmacy) {
                    formPrintView.setSourceFormId(dispensingForm.getSourceFormId());
                }
                formPrintView.setProcessedStatus(dispensingForm.getProcessedStatus());
                formPrintView.setDispensingFormId(dispensingForm.getId());
                printViews.add(formPrintView);
            }
        } else {
            DispensingFormPrintView formPrintView = new DispensingFormPrintView();
            formPrintView.setSourceFormType(dispensingForm.getSourceFormType());
            formPrintView.setPharmacyNo(dispensingSheet.getPharmacyNo());
            formPrintView.setPharmacyType(dispensingSheet.getPharmacyType());
            if (dispensingForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE) {
                UsageInfo usageInfo = JsonUtils.readValue(dispensingForm.getUsageInfoJson(), UsageInfo.class);
                if (usageInfo != null) {
                    BeanUtils.copyProperties(usageInfo, formPrintView, new String[]{"doseCount"});
                    if (dispensingForm.getProcessedStatus() == DispensingForm.ProcessedStatus.NONE) {
                        //加工退费后不打印加工信息
                        formPrintView.setProcessBagUnitCount(0);
                        formPrintView.setProcessUsage(null);
                        formPrintView.setProcessUsageInfo("");
                    }
                }
                itemPrintViews.stream().findFirst().ifPresent(item -> formPrintView.setDoseCount(item.getDoseCount()));

                if (itemPrintViews.stream().anyMatch(prescriptionFormItemPrintView -> TextUtils.equals(prescriptionFormItemPrintView.getCMSpec(), "中药饮片"))) {
                    formPrintView.setCMSpec("中药饮片");
                } else if (itemPrintViews.stream().anyMatch(prescriptionFormItemPrintView -> TextUtils.equals(prescriptionFormItemPrintView.getCMSpec(), "中药颗粒"))) {
                    formPrintView.setCMSpec("中药颗粒");
                }

                if (TextUtils.isEmpty(formPrintView.getCMSpec()) && usageInfo != null && !TextUtils.isEmpty(usageInfo.getSpecification())) {
                    formPrintView.setCMSpec(usageInfo.getSpecification());
                }

            }
            //虚拟药房需要sourceFormId去找对应的快递加工信息
            if (isVirtualPharmacy) {
                formPrintView.setSourceFormId(dispensingForm.getSourceFormId());
            }
            formPrintView.setDispensingFormItems(itemPrintViews);
            formPrintView.setProcessedStatus(dispensingForm.getProcessedStatus());
            formPrintView.setDispensingFormId(dispensingForm.getId());
            printViews.add(formPrintView);
        }
        DispensingFormAdditional additional = dispensingForm.getAdditional();
        if (Objects.nonNull(additional)) {
            for (DispensingFormPrintView view : printViews) {
                BeanUtils.copyProperties(additional, view);
            }
        }
        return printViews;
    }

    private DispensingFormItemPrintView generateDispensingFormItemPrintView(DispensingFormItem dispensingFormItem,
                                                                            Map<String, GoodsItem> goodsItemMap,
                                                                            SimpleChargeInfo.ChargeFormItemSimpleView chargeFormItemSimpleView,
                                                                            Map<Long, GoodsBatchInfo> batchIdToBatchInfo,
                                                                            boolean printUndispense,
                                                                            boolean filterByRecords,
                                                                            Map<String, ChargeRefundedItemView> chargeFormItemIdToRefundFeeItem) {
        if (dispensingFormItem == null || filterByRecords && CollectionUtils.isEmpty(chargeFormItemIdToRefundFeeItem)) {
            return null;
        }

        chargeFormItemIdToRefundFeeItem = chargeFormItemIdToRefundFeeItem != null ? chargeFormItemIdToRefundFeeItem : Collections.emptyMap();
        if (!printUndispense && (dispensingFormItem.getStatus() == DispensingFormItem.Status.CANCELED ||
                dispensingFormItem.getStatus() == DispensingFormItem.Status.UNDISPENSED)) {
            return null;
        } else if (printUndispense) {
            if (!filterByRecords && dispensingFormItem.getStatus() != DispensingFormItem.Status.UNDISPENSED) {
                // 如果是打印退药单，只打印已退药状态的发药项
                return null;
            } else if (filterByRecords && dispensingFormItem.getStatus() == DispensingFormItem.Status.UNDISPENSED
                    && !Objects.equals(dispensingFormItem.getIsUndispenseAll(), 1)) {// 由于经历过合并，所以如果发的 item 已经被合并掉了，还是需要处理退的
                // 如果打印退费退药单，不打印已退药的发药项
                return null;
            }
        }

        BigDecimal unitCount = MathUtils.wrapBigDecimalOrZero(dispensingFormItem.getUnitCount());
        BigDecimal doseCount = MathUtils.wrapBigDecimalOrZero(dispensingFormItem.getDoseCount());
        Map<String, ChargeRefundedBatchView> batchIdToRecordRefundBatchInfo = Collections.emptyMap();
        BigDecimal receivedPrice;
        if (printUndispense) {
            String sourceFormItemId = dispensingFormItem.getSourceFormItemId();
            ChargeRefundedItemView refundFeeItem = chargeFormItemIdToRefundFeeItem.get(sourceFormItemId);
            if (filterByRecords) {
                if (refundFeeItem == null) {
                    return null;
                }
                unitCount = refundFeeItem.getUnitCount();
                doseCount = refundFeeItem.getDoseCount();
                receivedPrice = refundFeeItem.getRefundedPrice();
                if (!CollectionUtils.isEmpty(refundFeeItem.getBatchInfos())) {
                    batchIdToRecordRefundBatchInfo = ListUtils.toMap(refundFeeItem.getBatchInfos(), ChargeRefundedBatchView::getBatchId);
                }
            } else {
                // 打印已退费的金额，就算退药 5 盒，退费 3 盒，也是打印 3 盒的退费金额
                receivedPrice = refundFeeItem == null ? BigDecimal.ZERO : refundFeeItem.getRefundedPrice();
            }
            // 删除的原因是因为，查询出来的可能包含已删除的发药单，由于退费重收可能多次，所以可能会有信息完全相同的多个发药单
            // 只要这个 item 已经处理过了，后续就不再处理了
            chargeFormItemIdToRefundFeeItem.remove(sourceFormItemId);
        } else {
            receivedPrice = Optional.ofNullable(chargeFormItemSimpleView).map(SimpleChargeInfo.ChargeFormItemSimpleView::getReceivedPrice).orElse(BigDecimal.ZERO);
        }

        if (unitCount.compareTo(BigDecimal.ZERO) <= 0 || doseCount.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }

        DispensingFormItemPrintView printView = new DispensingFormItemPrintView();
        printView.setId(dispensingFormItem.getId());
        printView.setSourceFormItemId(dispensingFormItem.getSourceFormItemId());
        printView.setName(dispensingFormItem.getName());
        printView.setUnit(dispensingFormItem.getUnit());
        printView.setProductType(dispensingFormItem.getProductType());
        printView.setProductSubType(dispensingFormItem.getProductSubType());
        printView.setSourceItemType(dispensingFormItem.getSourceItemType());
        printView.setUsageInfo(DispensingUtils.toJsonNodeUsageInfo(dispensingFormItem.getUsageInfoJson()));
        printView.setReceivedPrice(receivedPrice);
        if (!CollectionUtils.isEmpty(dispensingFormItem.getDispensingFormItemBatches())) {
            Map<String, ChargeRefundedBatchView> finalBatchIdToRecordRefundBatchInfo = batchIdToRecordRefundBatchInfo;
            printView.setDispensingFormItemBatches(dispensingFormItem.getDispensingFormItemBatches().stream()
                    .map(batchInfo -> generateDispensingFormItemBatchInfoPrintView(batchInfo, batchIdToBatchInfo, printUndispense, filterByRecords, finalBatchIdToRecordRefundBatchInfo))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
        }

        GoodsItem goodsItem = goodsItemMap.getOrDefault(dispensingFormItem.getId(), null);
        printView.setFormatSpec(getGoodsItemFormatSpec(goodsItem));
        printView.setPosition(goodsItem != null ? goodsItem.getPosition() : null);
        if (printView.getPosition() == null) {
            printView.setPosition("");
        }
        printView.setManufacturer(getManufacturer(goodsItem));

        if (dispensingFormItem.getProductType() == Constants.ProductType.MEDICINE && dispensingFormItem.getProductSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE) {
            printView.setCount(unitCount);
            printView.setDoseCount(doseCount.intValue());

            String cMSpec = goodsItem != null ? goodsItem.getCMSpec() : null;
            String materialSpec = goodsItem != null ? goodsItem.getMaterialSpec() : null;
            if (cMSpec != null) {
                printView.setCMSpec(cMSpec);
            } else if (materialSpec != null) {
                printView.setCMSpec(materialSpec);
            }

        } else {
            printView.setCount(MathUtils.calculateTotalCount(unitCount, doseCount));
        }
        DispenseUsageInfo usageInfo = DispensingUtils.toDbUsageInfo(dispensingFormItem.getUsageInfoJson());
        if (usageInfo != null && !TextUtils.isEmpty(usageInfo.getSpecialRequirement())) {
            printView.setSpecialRequirement(usageInfo.getSpecialRequirement());
        }
        List<TraceableCode> traceableCodeList = dispensingFormItem.getTraceableCodeList();
        if (!CollectionUtils.isEmpty(traceableCodeList)) {
            printView.setTraceableCodeList(traceableCodeList);
        }
        return printView;
    }

    private DispensingFormItemBatchInfoPrintView generateDispensingFormItemBatchInfoPrintView(DispensingFormItemBatchInfo batchInfo,
                                                                                              Map<Long, GoodsBatchInfo> batchIdToBatchInfo,
                                                                                              boolean printUndispense,
                                                                                              boolean filterByRecords,
                                                                                              Map<String, ChargeRefundedBatchView> batchIdToRefundBatchInfo) {
        if (batchIdToBatchInfo == null) {
            return null;
        } else if (filterByRecords && (CollectionUtils.isEmpty(batchIdToRefundBatchInfo)
                || batchIdToRefundBatchInfo.get(Objects.toString(batchInfo.getBatchId())) == null)) {
            return null;
        }

        // 打印只打印发药的数量
        BigDecimal unitCount;
        if (filterByRecords) {
            unitCount = batchIdToRefundBatchInfo.get(Objects.toString(batchInfo.getBatchId())).getUnitCount();
        } else {
            unitCount = printUndispense ? batchInfo.getUndispenseUnitCount() : batchInfo.getUnitCount();
        }
        if (MathUtils.wrapBigDecimalCompare(unitCount, BigDecimal.ZERO) > 0) {
            GoodsBatchInfo goodsBatchInfo = batchIdToBatchInfo.get(batchInfo.getBatchId());
            return DispensingFormItemBatchInfoPrintView.from(batchInfo, unitCount, goodsBatchInfo);
        } else {
            return null;
        }
    }

    private String getManufacturer(GoodsItem goodsItem) {
        if (Objects.isNull(goodsItem)) {
            return "";
        }
        return goodsItem.getManufacturerFull() != null ? goodsItem.getManufacturerFull() : "";
    }

    private String getGoodsItemFormatSpec(GoodsItem goodsItem) {
        String formatSpec = "";
        if (goodsItem == null) {
            return formatSpec;
        }

        if (goodsItem.getType() == Constants.ProductType.MATERIAL || goodsItem.getType() == Constants.ProductType.SALE_PRODUCT) {
            if (goodsItem.getMaterialSpec() != null && !goodsItem.getMaterialSpec().equals("")) {
                formatSpec = goodsItem.getMaterialSpec() + "*";
            }
            formatSpec += String.format("%s%s/%s", MathUtils.wrapBigDecimalOrZero(goodsItem.getPieceNum()).stripTrailingZeros().toPlainString(), goodsItem.getPieceUnit(), goodsItem.getPackageUnit());
        }

        if (goodsItem.getType() == Constants.ProductType.MEDICINE) {
            if (goodsItem.getSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE) {
                if (goodsItem.getExtendSpec() != null) {
                    formatSpec = goodsItem.getExtendSpec();
                }
            } else {
                if (goodsItem.getMedicineDosageNum() != null && !TextUtils.isEmpty(goodsItem.getMedicineDosageUnit())) {
                    formatSpec += goodsItem.getMedicineDosageNum().stripTrailingZeros().toPlainString() + goodsItem.getMedicineDosageUnit() + "*";
                }
                formatSpec += String.format("%s%s/%s", goodsItem.getPieceNum(), goodsItem.getPieceUnit(), goodsItem.getPackageUnit());
            }
        }
        if (goodsItem.getType() == Constants.ProductType.EYE) {
            if (goodsItem.getDisplaySpec() != null) {
                formatSpec = goodsItem.getDisplaySpec();
            }
        }
        return formatSpec;
    }

    private void setReviewer(String dispensingSheetId, DispensingSheetPrintView dispensingSheetPrintView) {
        List<DispensingSheetOperation> dispensingSheetOperations = dispensingSheetOperationRepository.findAllByDispensingSheetIdAndOperationTypeInAndIsDeletedOrderByCreated(dispensingSheetId, Arrays.asList(DispensingSheetOperation.OperationType.AUDIT, DispensingSheetOperation.OperationType.CANCEL_AUDIT), 0);
        if (CollectionUtils.isEmpty(dispensingSheetOperations)) {
            return;
        }
        Map<String, DispensingFormPrintView> formViewMap = dispensingSheetPrintView.getDispensingForms().stream().collect(Collectors.toMap(DispensingFormPrintView::getDispensingFormId, formView -> formView, (formView1, formView2) -> formView1));
        dispensingSheetOperations.forEach(dispensingSheetOperation -> {
            if (StringUtils.isNotBlank(dispensingSheetOperation.getRecordJson())) {
                OperationRecordForAudit operationRecordForAudit = dispensingProcessService.getAuditOperationRecordFromJsonRecord(dispensingSheetOperation.getRecordJson());
                if (operationRecordForAudit == null || operationRecordForAudit.getDispensingForms() == null) {
                    return;
                }
                List<DispensingFormRecordViewForAudit> dispensingForms = operationRecordForAudit.getDispensingForms();
                if (!CollectionUtils.isEmpty(dispensingForms)) {
                    for (DispensingFormRecordViewForAudit dispensingForm : dispensingForms) {
                        DispensingFormPrintView dispensingFormPrintView = formViewMap.get(dispensingForm.getId());
                        if (Objects.nonNull(dispensingFormPrintView)) {
                            if (dispensingSheetOperation.getOperationType() == DispensingSheetOperation.OperationType.AUDIT) {
                                dispensingFormPrintView.setAuditId(dispensingSheetOperation.getOperatorId());
                                dispensingFormPrintView.setAuditName(dispensingSheetOperation.getOperatorName());
                                // 产品说简单处理，只打印最后那个审核人
                                dispensingSheetPrintView.setAuditId(dispensingSheetOperation.getOperatorId());
                                dispensingSheetPrintView.setAuditName(dispensingSheetOperation.getOperatorName());
                            } else {
                                dispensingFormPrintView.setAuditId(null);
                                dispensingFormPrintView.setAuditName(null);
                                dispensingSheetPrintView.setAuditId(null);
                                dispensingSheetPrintView.setAuditName(null);
                            }
                        }
                    }
                }
            }
        });
    }

    private void setCompounder(String dispensingSheetId, DispensingSheetPrintView dispensingSheetPrintView) {
        List<DispensingSheetOperation> dispensingSheetOperations = dispensingSheetOperationRepository.findAllByDispensingSheetIdAndOperationTypeInAndIsDeletedOrderByCreated(dispensingSheetId, Arrays.asList(DispensingSheetOperation.OperationType.COMPOUND, DispensingSheetOperation.OperationType.CANCEL_COMPOUND), 0);
        if (CollectionUtils.isEmpty(dispensingSheetOperations)) {
            return;
        }
        Map<String, DispensingFormPrintView> formViewMap = dispensingSheetPrintView.getDispensingForms().stream().collect(Collectors.toMap(DispensingFormPrintView::getDispensingFormId, formView -> formView, (formView1, formView2) -> formView1));
        dispensingSheetOperations.forEach(dispensingSheetOperation -> {
            if (StringUtils.isNotBlank(dispensingSheetOperation.getRecordJson())) {
                OperationRecordForCompound operationRecordForCompound = JsonUtils.readValue(dispensingSheetOperation.getRecordJson(), OperationRecordForCompound.class);
                if (operationRecordForCompound == null || operationRecordForCompound.getDispensingForms() == null) {
                    return;
                }
                List<DispensingFormRecordViewForCompound> dispensingForms = operationRecordForCompound.getDispensingForms();
                if (!CollectionUtils.isEmpty(dispensingForms)) {
                    for (DispensingFormRecordViewForCompound dispensingForm : dispensingForms) {
                        DispensingFormPrintView dispensingFormPrintView = formViewMap.get(dispensingForm.getId());
                        if (Objects.nonNull(dispensingFormPrintView)) {
                            String operatorId;
                            String compoundName;
                            if (!CollectionUtils.isEmpty(operationRecordForCompound.getDispensedCompoundByIds())) {
                                operatorId = operationRecordForCompound.getDispensedCompoundByIds().get(0);
                            } else {
                                operatorId = dispensingSheetOperation.getOperatorId();
                            }
                            if (StringUtils.isNotBlank(operationRecordForCompound.getDispensedCompoundName())) {
                                compoundName = operationRecordForCompound.getDispensedCompoundName().split(",")[0];
                            } else {
                                compoundName = dispensingSheetOperation.getOperatorName();
                            }
                            if (dispensingSheetOperation.getOperationType() == DispensingSheetOperation.OperationType.COMPOUND) {
                                dispensingFormPrintView.setCompoundId(operatorId);
                                dispensingFormPrintView.setCompoundName(compoundName);
                                // 只打印最后那个调配人
                                dispensingSheetPrintView.setCompoundId(operatorId);
                                dispensingSheetPrintView.setCompoundName(compoundName);
                            } else {
                                dispensingFormPrintView.setCompoundId(null);
                                dispensingFormPrintView.setCompoundName(null);
                                dispensingSheetPrintView.setCompoundId(null);
                                dispensingSheetPrintView.setCompoundName(null);
                            }
                        }
                    }
                }
            }
        });
    }

    private List<DispensingFormItemPrintView> generateIsCombineDispensingFormItemPrintViews(DispensingFormItem dispensingFormItem,
                                                                                            Map<String, GoodsItem> goodsItemMap,
                                                                                            Map<String, SimpleChargeInfo.ChargeFormItemSimpleView> chargeFormItemSimpleViewMap,
                                                                                            Map<Long, GoodsBatchInfo> batchIdToBatchInfo,
                                                                                            boolean printUndispense,
                                                                                            boolean filterByRecords,
                                                                                            Map<String, ChargeRefundedItemView> chargeFormItemIdToRefundedItem) {

        List<DispensingFormItemPrintView> dispensingFormItemPrintViews = new ArrayList<>();

        if (dispensingFormItem == null || goodsItemMap == null) {
            return dispensingFormItemPrintViews;
        }

        if (dispensingFormItem.getComposeType() == ComposeType.NOT_COMPOSE) {

            DispensingFormItemPrintView dispensingFormItemPrintView = generateDispensingFormItemPrintView(dispensingFormItem, goodsItemMap, chargeFormItemSimpleViewMap.get(dispensingFormItem.getSourceFormItemId()), batchIdToBatchInfo, printUndispense, filterByRecords, chargeFormItemIdToRefundedItem);
            if (dispensingFormItemPrintView != null) {
                dispensingFormItemPrintViews.add(dispensingFormItemPrintView);
            }
        } else if (dispensingFormItem.getComposeType() == ComposeType.COMPOSE && !CollectionUtils.isEmpty(dispensingFormItem.getComposeChildren())) {
            GoodsItem composeGoodsItem = goodsItemMap.getOrDefault(dispensingFormItem.getProductId(), null);

            List<GoodsItem> childGoodsItems = composeGoodsItem != null ? (composeGoodsItem.getChildren() != null ? composeGoodsItem.getChildren() : new ArrayList<>()) : new ArrayList<>();

            Map<String, GoodsItem> childGoodsItemMap = ListUtils.toMap(childGoodsItems, GoodsItem::getId);

            dispensingFormItem.getComposeChildren()
                    .stream()
                    .sorted((a, b) -> ObjectUtils.compare(a.getSort(), b.getSort()))
                    .forEach(child -> {
                        DispensingFormItemPrintView dispensingFormItemPrintView = generateDispensingFormItemPrintView(child, childGoodsItemMap, chargeFormItemSimpleViewMap.get(child.getSourceFormItemId()), batchIdToBatchInfo, printUndispense, filterByRecords, chargeFormItemIdToRefundedItem);
                        if (dispensingFormItemPrintView != null) {
                            dispensingFormItemPrintViews.add(dispensingFormItemPrintView);
                        }
                    });

        }

        return dispensingFormItemPrintViews.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 建发药单后立即发送的自动打印消息
     */
    public void sendAutoPrintMessage(DispensingSheet dispensingSheet, List<String> selectedPrintDispensingFormIdList, String action) {

        if (dispensingSheet.getPharmacyType() != GoodsConst.PharmacyType.LOCAL_PHARMACY) {
            return;
        }
        /**
         * 发消息要告诉是否能打患者标签，所以需要加载患者信息
         * */
        DispensingSheetView dispensingSheetView = DispensingSheetView.from(dispensingSheet);
        if (!StringUtils.isEmpty(dispensingSheet.getPatientId())) {
            GetPatientBasicInfosRsp patientBasicInfos = crmFeignClient.getPatientBasicInfos(dispensingSheet.getChainId(), Arrays.asList(dispensingSheet.getPatientId()));
            if (patientBasicInfos != null && !CollectionUtils.isEmpty(patientBasicInfos.getPatientInfos())) {
                CisPatientInfo cisPatientInfo = new CisPatientInfo();
                BeanUtils.copyProperties(patientBasicInfos.getPatientInfos().get(0), cisPatientInfo);
                dispensingSheetView.setPatient(cisPatientInfo);
            }
        }
        /**
         * 如果选择
         * */
        if (!CollectionUtils.isEmpty(selectedPrintDispensingFormIdList) && selectedPrintDispensingFormIdList.size() < dispensingSheet.getDispensingForms().size()) {
            ChargeSheet chargeSheet = chargeService.getChargeSheetById(dispensingSheet.getSourceSheetId());
            if (chargeSheet != null) {
                Set<String> setDispensingFormIdSet = selectedPrintDispensingFormIdList.stream().collect(Collectors.toSet());
                Set<String> setChargeFormIdSet = dispensingSheet.getDispensingForms().stream().filter(dispensingForm -> setDispensingFormIdSet.contains(dispensingForm.getId())).map(DispensingForm::getSourceFormId).collect(Collectors.toSet());
                dispensingSheetView.setPrintDispensingFormIdList(selectedPrintDispensingFormIdList);
                dispensingSheetView.setPrintOutpatientFormIdList(chargeSheet.getChargeForms().stream().filter(chargeForm -> setChargeFormIdSet.contains(chargeForm.getId())).map(ChargeForm::getSourceFormId).collect(Collectors.toList()));
            }
        }
        /**
         * 生成消息
         * */
        DispensingSheetMessageRsp messageBody = DispensingViewUtils.autoPrintMessageViewFromDispensingView(
                dispensingSheetView,
                propertyService.getPropertyValueByKey(PropertyKey.PRINT_MEDICAL_DOCUMENTS_INFUSION, dispensingSheet.getClinicId(), PrintMedicalDocumentsInfusion.class),
                propertyService.getPropertyValueByKey(PropertyKey.CHAIN_BASIC, dispensingSheet.getChainId(), ChainBasic.class)
        );


        sendAutoPushDispensingMessageToWeb(messageBody, action);
    }

    /***
     * 自动打印
     * */
    private void sendAutoPushDispensingMessageToWeb(DispensingSheetMessageRsp messageBody, String autoPushMsgEvent) {
        JsonNode data = JsonUtils.dumpAsJsonNode(messageBody);
        String chainId = messageBody.getChainId();
        String clinicId = messageBody.getClinicId();

        WebMessageBody.WebPushMessage webPushMessage = new WebMessageBody.WebPushMessage();
        webPushMessage.setEvent(autoPushMsgEvent);
        webPushMessage.setScope(WebMessageBody.WebMessageScope.CLINIC);
        webPushMessage.setScopeId(clinicId);
        webPushMessage.setParams(data);


        WebMessageBody webMessageBody = new WebMessageBody();
        webMessageBody.setTrigger(webPushMessage);
        webMessageBody.setMemoryFlag(WebMessageBody.MemoryLogType.WRITE_LOG);

        ToBMessage toBMessage = new ToBMessage();
        toBMessage.setChainId(chainId);
        toBMessage.setClinicId(clinicId);
        toBMessage.setChannel(ToBMessage.MessageChannel.Web);
        toBMessage.setMsgId(abcIdGenerator.getUUID());
        toBMessage.setBody(cn.abcyun.cis.core.util.JsonUtils.dumpAsJsonNode(webMessageBody));

//        sLogger.info("sendAutoPushDispensingMessageToWeb toBMessage={}",toBMessage);

        rocketMqProducer.rocketMqNotifyToBMessageAfterTransactionCommit(toBMessage);
    }

    /**
     * 发送退费自动打印退药单消息
     */
    public void sendRefundFeeAutoPrintMessage(DispensingSheet dispensingSheet, ChargeSheetMessage chargeSheetMessage) {
        if (dispensingSheet.getPharmacyType() != GoodsConst.PharmacyType.LOCAL_PHARMACY) {
            return;
        }

        List<String> chargeTransactionIds = new ArrayList<>();
        if (chargeSheetMessage != null && chargeSheetMessage.getChargeSheet() != null && !CollectionUtils.isEmpty(chargeSheetMessage.getChargeSheet().getCurrentAddedTransactions())) {
            chargeTransactionIds.addAll(ListUtils.extractUniqueProperty(chargeSheetMessage.getChargeSheet().getCurrentAddedTransactions(), ChargeTransaction::getId));
        }
        RefundFeeAutoPrintMessage messageBody = RefundFeeAutoPrintMessage.of(dispensingSheet, chargeTransactionIds);
        if (messageBody == null) {
            return;
        }

        JsonNode data = JsonUtils.dumpAsJsonNode(messageBody);
        String chainId = messageBody.getChainId();
        String clinicId = messageBody.getClinicId();

        WebMessageBody.WebPushMessage webPushMessage = new WebMessageBody.WebPushMessage();
        webPushMessage.setEvent(WebMessageBody.WebMessageEvent.DISPENSING_AUTO_PRINT_FROM_REFUND_FEE);
        webPushMessage.setScope(WebMessageBody.WebMessageScope.CLINIC);
        webPushMessage.setScopeId(clinicId);
        webPushMessage.setParams(data);


        WebMessageBody webMessageBody = new WebMessageBody();
        webMessageBody.setTrigger(webPushMessage);
        webMessageBody.setMemoryFlag(WebMessageBody.MemoryLogType.WRITE_LOG);

        ToBMessage toBMessage = new ToBMessage();
        toBMessage.setChainId(chainId);
        toBMessage.setClinicId(clinicId);
        toBMessage.setChannel(ToBMessage.MessageChannel.Web);
        toBMessage.setMsgId(abcIdGenerator.getUUID());
        toBMessage.setBody(cn.abcyun.cis.core.util.JsonUtils.dumpAsJsonNode(webMessageBody));

        rocketMqProducer.rocketMqNotifyToBMessageAfterTransactionCommit(toBMessage);
    }

    public void logDispensingSheetPrint(DispensingPrintLogReq req) {
        DispensingSheetV2 dispensingSheetV2 = dispensingSheetV2Repository.findAllByChainIdAndClinicIdAndIdInAndIsDeleted(req.getChainId(),
                req.getClinicId(), Arrays.asList(req.getId()), DispensingUtils.DeleteFlag.NOT_DELETED).stream().findFirst().orElse(null);
        if (dispensingSheetV2 == null) {
            log.info("logDispensingSheetPrint not found sheet, sheetId={}", req.getId());
            return;
        }
        SheetOperationBase operationBase =
                sheetOperationCreateFactory.createSheetOperation(DispensingSheetOperation.OperationType.PRINT_SHEET, req.getId(),
                        req.getEmployeeId(), String.join(",", req.getDispensedByIds()), req.getChainId(), req.getClinicId());
        operationBase.bindSheetStatus(dispensingSheetV2.getStatus());
        operationBase.createAndSaveSheetOperation();

    }

    public DispensingFormItemPrintListRsp getDispensingFormItemPrintListRspBySourceSheetId(String sourceSheetId, boolean printUnDispense) {
        DispensingFormItemPrintListRsp rsp = new DispensingFormItemPrintListRsp();
        rsp.setDispensingFormItems(new ArrayList<>());

        if (StringUtils.isEmpty(sourceSheetId)) {
            throw new ParamRequiredException("sourceSheetId");
        }


        // 1、查询所有发药单
        List<DispensingSheet> dispensingSheetList = dispensingSheetEntityService.findAllBySourceSheetId(sourceSheetId);
        if (CollectionUtils.isEmpty(dispensingSheetList)) {
            sLogger.info("sourceSheetId ={} ,dispensingSheets is null", sourceSheetId);
            throw new NotFoundException();
        }
        // 过滤掉历史退药项
        dispensingSheetList.forEach(dispensingSheet -> {
            DispensingUtils.doWithDispensingForm(dispensingSheet, form -> {
                if (CollectionUtils.isEmpty(form.getDispensingFormItems())) {
                    return;
                }

                form.setDispensingFormItems(form.getDispensingFormItems().stream().filter(item -> item.getIsHistoryItem() == 0).collect(Collectors.toList()));
            });
        });

        if (org.apache.commons.collections.CollectionUtils.isEmpty(dispensingSheetList)) {
            return rsp;
        }

        String chainId = dispensingSheetList.get(0).getChainId();
        String clinicId = dispensingSheetList.get(0).getClinicId();

        bindVirtualDispensingFormItemBatchInfo(chainId, clinicId, dispensingSheetList, true);


        List<DispensingFormItemPrintView> dispensingFormItemPrintViews = dispensingSheetList.stream()
                .flatMap(dispensingSheet -> {

                    DispensingItemMerger.mergeForDispensingSheet(dispensingSheet);

                    if (org.apache.commons.collections.CollectionUtils.isEmpty(dispensingSheet.getDispensingForms())) {
                        return new ArrayList<DispensingFormItemPrintView>().stream();
                    }

                    bindLockedBatchInfo(dispensingSheet, null);

                    // 查询批次信息
                    Map<String, GetGoodsStockBatchesReq.GetGoodsStockBatch> goodsIdToGetGoodsStockBatch = new HashMap<>();
                    DispensingUtils.doWithDispensingItemBatch(dispensingSheet, batchInfo -> {
                        //从GoodsItem里面捞出来的批次信息
                        if (batchInfo.getGoodsBatchInfo() != null) {
                            return;
                        }
                        GetGoodsStockBatchesReq.GetGoodsStockBatch goodsStockBatch = goodsIdToGetGoodsStockBatch.get(batchInfo.getProductId());
                        if (goodsStockBatch == null) {
                            goodsStockBatch = new GetGoodsStockBatchesReq.GetGoodsStockBatch();
                            goodsStockBatch.setGoodsId(batchInfo.getProductId());
                            goodsStockBatch.setBatchIds(new ArrayList<>());
                            goodsIdToGetGoodsStockBatch.put(batchInfo.getProductId(), goodsStockBatch);
                        }
                        goodsStockBatch.getBatchIds().add(batchInfo.getBatchId());
                    });
                    GetGoodsStockBatchesRsp goodsStockBatchesRsp = scGoodsFeignClient.getGoodsStockBatches(dispensingSheet.getClinicId(), new ArrayList<>(goodsIdToGetGoodsStockBatch.values()));
                    Map<Long, GoodsBatchInfo> batchIdToBatchInfo = Optional.ofNullable(goodsStockBatchesRsp).map(GetGoodsStockBatchesRsp::getList)
                            .orElseGet(Lists::newArrayList).stream()
                            .map(BatchesGoodsStockItem::getList).flatMap(Collection::stream)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toMap(GoodsBatchInfo::getBatchId, Function.identity(), (a, b) -> a));

                    String isNotCombineForm = "isNotCombineFormKey";
                    String isCombineForm = "isCombineFormKey";

                    Map<String, List<DispensingForm>> dispensingFormsMap = dispensingSheet.getDispensingForms()
                            .stream()
                            .collect(Collectors.groupingBy(dispensingForm -> {
                                if (DispensingUtils.isNotCombineForm(dispensingForm.getSourceFormType())) {
                                    return isNotCombineForm;
                                } else {
                                    return isCombineForm;
                                }
                            }));

                    if (MapUtils.isEmpty(dispensingFormsMap)) {
                        return new ArrayList<DispensingFormItemPrintView>().stream();
                    }

                    List<DispensingFormItemPrintView> itemPrintViews = dispensingFormsMap.getOrDefault(isNotCombineForm, new ArrayList<>())
                            .stream()
                            .sorted((a, b) -> {
                                if (a.getSourceFormType() == b.getSourceFormType()) {
                                    return ObjectUtils.compare(a.getSort(), b.getSort());
                                } else {
                                    return ObjectUtils.compare(a.getSourceFormType(), b.getSourceFormType());
                                }
                            })
                            .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems()
                                    .stream()
                                    .filter(Objects::nonNull)
                                    .sorted((a, b) -> ObjectUtils.compare(a.getSort(), b.getSort()))
                                    .map(dispensingFormItem -> generateDispensingFormItemPrintView(dispensingFormItem, new HashMap<>(),  null, batchIdToBatchInfo, printUnDispense, false, new HashMap<>()))
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toList()).stream())
                            .collect(Collectors.toList());


                    itemPrintViews.addAll(dispensingFormsMap.getOrDefault(isCombineForm, new ArrayList<>())
                            .stream()
                            .filter(dispensingForm -> !CollectionUtils.isEmpty(dispensingForm.getDispensingFormItems()))
                            .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream())
                            .map(dispensingFormItem -> generateIsCombineDispensingFormItemPrintViews(dispensingFormItem, new HashMap<>(), new HashMap<>(), batchIdToBatchInfo, printUnDispense, false, new HashMap<>()))
                            .flatMap(Collection::stream)
                            .collect(Collectors.toList())
                    );

                    return itemPrintViews.stream();
                })
                .collect(Collectors.toList());

        rsp.setDispensingFormItems(dispensingFormItemPrintViews);
        return rsp;
    }
}
