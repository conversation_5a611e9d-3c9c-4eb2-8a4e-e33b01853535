package cn.abcyun.cis.dispensing.service;

import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispenseConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsDispenseDataItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsDispenseResultItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.TraceableCode;
import cn.abcyun.bis.rpc.sdk.property.base.PropertyKey;
import cn.abcyun.bis.rpc.sdk.property.model.Dispensing;
import cn.abcyun.bis.rpc.sdk.property.service.PropertyService;
import cn.abcyun.cis.commons.exception.CisCustomException;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.rpc.charge.ChargeForm;
import cn.abcyun.cis.commons.rpc.charge.ChargeSheet;
import cn.abcyun.cis.commons.rpc.charge.Constants;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.dispensing.api.protocol.dispense.DispensingFormItemBatchReq;
import cn.abcyun.cis.dispensing.api.protocol.order.DispenseDispenseOrderReq;
import cn.abcyun.cis.dispensing.base.DispensingConstants;
import cn.abcyun.cis.dispensing.base.exception.DispenseSheetChangedException;
import cn.abcyun.cis.dispensing.base.exception.DispensingServiceError;
import cn.abcyun.cis.dispensing.base.exception.DispensingServiceException;
import cn.abcyun.cis.dispensing.base.exception.GoodsUndispenseException;
import cn.abcyun.cis.dispensing.controller.DTOConverter;
import cn.abcyun.cis.dispensing.controller.StatusNameTranslator;
import cn.abcyun.cis.dispensing.domain.*;
import cn.abcyun.cis.dispensing.repository.DispensingLogRepository;
import cn.abcyun.cis.dispensing.repository.DispensingSheetOperationRepository;
import cn.abcyun.cis.dispensing.service.dispense.UnDispenseOpt;
import cn.abcyun.cis.dispensing.service.dto.DispenseUsageInfo;
import cn.abcyun.cis.dispensing.service.dto.UndispenseResult;
import cn.abcyun.cis.dispensing.service.operation.SheetOperationBase;
import cn.abcyun.cis.dispensing.service.operation.SheetOperationCreateFactory;
import cn.abcyun.cis.dispensing.util.DispensingUtils;
import cn.abcyun.cis.dispensing.util.FillUtils;
import cn.abcyun.cis.dispensing.util.GoodsUtils;
import cn.abcyun.cis.dispensing.util.MathUtils;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Transactional(rollbackFor = Throwable.class)
public class UndispensingService {

    private static final Logger log = LoggerFactory.getLogger(UndispensingService.class);

    @Autowired
    private AbcCisScGoodsService abcCisScGoodsService;

    @Autowired
    private DispensingSheetEntityService dispensingSheetEntityService;

    @Autowired
    private DispensingLogService dispensingLogService;

    @Autowired
    private ChargeService chargeService;

    @Autowired
    private AbcIdGenerator abcIdGenerator;

    @Autowired
    private SheetOperationCreateFactory sheetOperationCreateFactory;

    @Autowired
    private DispensingSheetOperationRepository dispensingSheetOperationRepository;

    @Autowired
    private DispensingLogRepository dispensingLogRepository;

    @Autowired
    private PropertyService propertyService;

    /**
     * 这个接口是以前给虚拟药房jenkins直接发药的现在作为 ，openAPI接口的发药接口
     */
    @Transactional(rollbackFor = Exception.class)
    public UndispenseResult unDispenseSheet(String dispensingSheetId, String clinicId, String operatorId) {

        DispensingSheet dispensingSheet = dispensingSheetEntityService.findByIdAndClinicId(dispensingSheetId, clinicId);

        if (dispensingSheet == null) {
            log.info("dispensingSheet is null");
            throw new NotFoundException();
        }

        if (dispensingSheet.getStatus() == DispensingSheet.Status.UNDISPENSED) {
            log.info("发药单已退药，不处理该消息");
            return null;
        }

        if (dispensingSheet.getStatus() != DispensingSheet.Status.DISPENSED) {
            log.info("发药单不是已发药状态, status: {}", dispensingSheet.getStatus());
            throw new ServiceInternalException("dispensingSheet status error");
        }

        ChargeSheet chargeSheet = chargeService.getChargeSheetById(dispensingSheet.getSourceSheetId());
        if (chargeSheet == null) {
            log.info("not found charge sheet for id:{}", dispensingSheet.getSourceSheetId());
            throw new NotFoundException("chargeSheet未找到");
        }

        if (!(chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED ||
                chargeSheet.getStatus() == Constants.ChargeSheetStatus.CHARGED ||
                chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_REFUNDED ||
                chargeSheet.getStatus() == Constants.ChargeSheetStatus.REFUNDED)) {
            log.info("chargeSheet status error, id:{}, status:{}", chargeSheet.getId(), chargeSheet.getStatus());
            throw new ServiceInternalException("chargeSheet status error");
        }

        if (chargeSheet.getChargeForms() == null) {
            chargeSheet.setChargeForms(new ArrayList<>());
        }

        Map<Integer, List<ChargeForm>> groupByPharmacyNoChargeFormMap = ListUtils.groupByKey(chargeSheet.getChargeForms(), ChargeForm::getPharmacyNo);

        List<ChargeForm> chargeForms = groupByPharmacyNoChargeFormMap.getOrDefault(dispensingSheet.getPharmacyNo(), new ArrayList<>());

        DispensingSheet toUnDispensingSheet = DTOConverter.convertToUnDispensingSheet(dispensingSheet, chargeForms);

        if (toUnDispensingSheet == null) {
            log.info("toUnDispensingSheet is null");
            return null;
        }

        //        log.info("unDispenseResult: {}", JsonUtils.dump(unDispenseResult));
        return undispenseSheet(toUnDispensingSheet, dispensingSheet, dispensingSheet.getClinicId(), operatorId);
    }


    /**
     * 药房退药: 一个药 一次可以退部分
     *
     * @param undispenseSheet 根据客户端请求 转出来的 退药单请求对象  不是 JPA
     * @param existedSheet    退药 关联发药单的JPA对象。在用户退药调用这个参数为null/ 在自动退药流程这个为了少一次jpaload 传进来的
     * @param clinicId        退药门店
     */
    public UndispenseResult undispenseSheet(DispensingSheet undispenseSheet, DispensingSheet existedSheet, String clinicId, String operatorId) throws ParamRequiredException, CisCustomException, ServiceInternalException {
        return UnDispenseOpt.UnDispenseOptBuilder.of(undispenseSheet, clinicId, operatorId)
                .setDispensingSheet(existedSheet)
                .setDispensingConfig(propertyService.getPropertyValueByKey(PropertyKey.DISPENSING, clinicId, Dispensing.class))
                .build()
                .unDispense();
    }

    /**
     * 计算退药数量
     * 代煎代配药房只能全退
     *
     * @return BigDecimal[0] 退药unitCount BigDecimal[1] 退药doseCount
     */
    public BigDecimal[] getToUndispenseUnitCountAndDoseCount(int pharmacyType, Map<String, BigDecimal> undispensedUnitCountIdMap,
                                                             Map<String, BigDecimal> undispensedDoseCountIdMap,
                                                             DispensingFormItem dispensedItem,
                                                             DispensingFormItem toUndispenseItem) {
        if (dispensedItem.getProductType() == Constants.ProductType.MEDICINE && dispensedItem.getProductSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE) {
            return getToUndispenseUnitCountAndDoseCountForChineseMedicine(pharmacyType, undispensedUnitCountIdMap, undispensedDoseCountIdMap, dispensedItem, toUndispenseItem);
        } else {
            return getToUndispenseUnitCountAndDoseCountForWesternMedicine(pharmacyType, undispensedUnitCountIdMap, dispensedItem, toUndispenseItem);
        }
    }

    /**
     * For中药外的其他商品(包括西药,材料商品等),只按unit退药
     */
    private BigDecimal[] getToUndispenseUnitCountAndDoseCountForWesternMedicine(int pharmacyType, Map<String, BigDecimal> undispensedUnitCountIdMap, DispensingFormItem dispensedItem, DispensingFormItem toUndispenseItem) {
        BigDecimal toUndispenseUnitCount = toUndispenseItem.getUnitCount();
        BigDecimal dispensedUnitCount = dispensedItem.getDispensedUnitCount();
        BigDecimal undispensedUnitCount = undispensedUnitCountIdMap.getOrDefault(dispensedItem.getId(), BigDecimal.ZERO);
        BigDecimal canUndispenseUnitCount = dispensedUnitCount.subtract(undispensedUnitCount);
        if (toUndispenseUnitCount.compareTo(canUndispenseUnitCount) > 0) {
            log.info("toUndispenseItem id:{}, {} more than can undispensedUnitCount {}", toUndispenseItem.getId(), toUndispenseUnitCount, canUndispenseUnitCount);
            return null;
        }
        // 如果本次会退完，则打上标记
        if (MathUtils.wrapBigDecimalCompare(toUndispenseUnitCount, canUndispenseUnitCount) == 0) {
            toUndispenseItem.setIsAllUndispense(true);
        }
        /**
         * 代煎代配药房只能全退
         * */
        if (pharmacyType == GoodsConst.PharmacyType.VIRTUAL_PHARMACY && toUndispenseUnitCount.compareTo(canUndispenseUnitCount) < 0) {
            log.info("toUndispenseItem virturalPharmayc id:{}, {} less than can undispensedUnitCount {}", toUndispenseItem.getId(), toUndispenseUnitCount, canUndispenseUnitCount);
            throw new DispensingServiceException(DispensingServiceError.DISPENSING_SHEET_VIRTUAL_UNDISPENSE_ALL);
        }
        return new BigDecimal[]{toUndispenseUnitCount, dispensedItem.getDoseCount()};
    }

    /**
     * For中药,只按剂退药,但是兼容了以前按unit退的情况
     */
    private BigDecimal[] getToUndispenseUnitCountAndDoseCountForChineseMedicine(int pharmacyType, Map<String, BigDecimal> undispensedUnitCountIdMap, Map<String, BigDecimal> undispensedDoseCountIdMap, DispensingFormItem dispensedItem, DispensingFormItem toUndispenseItem) {
        BigDecimal toUndispenseUnitCount = toUndispenseItem.getUnitCount();
        BigDecimal dispensedUnitCount = dispensedItem.getDispensedUnitCount();
        BigDecimal undispensedUnitCount = undispensedUnitCountIdMap.getOrDefault(dispensedItem.getId(), BigDecimal.ZERO);
        BigDecimal canUndispenseUnitCount = dispensedUnitCount.subtract(undispensedUnitCount);
        //校验是否为按剂退药(如果是按剂退药,toUndispenseUnitCount必须与canUndispenseUnitCount一致)
        if (toUndispenseUnitCount.compareTo(canUndispenseUnitCount) != 0) {
            throw new DispensingServiceException(DispensingServiceError.WRONG_TO_UNDISPENSE_UNIT_COUNT);
        }

        BigDecimal toUndispenseDoseCount = toUndispenseItem.getDoseCount();
        BigDecimal dispensedDoseCount = dispensedItem.getDispensedDoseCount();
        BigDecimal undispensedDoseCount = undispensedDoseCountIdMap.getOrDefault(dispensedItem.getId(), BigDecimal.ZERO);
        BigDecimal canUndispenseDoseCount = dispensedDoseCount.subtract(undispensedDoseCount);
        if (toUndispenseDoseCount.compareTo(canUndispenseDoseCount) > 0) {
            log.info("toUndispenseItem id:{}, {} more than can undispensedDoseCount {}", toUndispenseItem.getId(), toUndispenseDoseCount, canUndispenseDoseCount);
            return null;
        }
        // 如果本次会退完，则打上标记
        if (MathUtils.wrapBigDecimalCompare(toUndispenseDoseCount, canUndispenseDoseCount) == 0) {
            toUndispenseItem.setIsAllUndispense(true);
        }
        /*
          代煎代配药房只能全退
          */
        if (pharmacyType == GoodsConst.PharmacyType.VIRTUAL_PHARMACY && toUndispenseDoseCount.compareTo(canUndispenseDoseCount) < 0) {
            log.info("toUndispenseItem virturalPharmayc id:{}, {} less than can undispensedDoseCount {}", toUndispenseItem.getId(), toUndispenseDoseCount, canUndispenseDoseCount);
            throw new DispensingServiceException(DispensingServiceError.DISPENSING_SHEET_VIRTUAL_UNDISPENSE_ALL);
        }
        return new BigDecimal[]{toUndispenseUnitCount, toUndispenseDoseCount};
    }

    /**
     * 药房退药: 一个药 一次可以退部分
     * 可以处理退药申请和退药确认
     *
     * @param unDispenseSheet     JPA对象 ，需要退药的发药单
     * @param clientUnDispenseReq 退药请求对象
     * @param operatorId          退药操作人
     *                            备注：医院管家拆出来的代码，目前冗余 ，老的退药代码里面融合和charge的代码
     *                            后面这拖代码将作为中台代码的基础
     */
    public UndispenseResult unDispenseSheetCore(
            DispensingOrder dispensingOrder,
            String patientOrderNo,
            DispensingSheetV2 unDispenseSheet,
            DispenseDispenseOrderReq.DispenseSheetReq clientUnDispenseReq,//本次要发的
            List<DispensingSheetV2> sndMessageDispenseList,
            String operatorId,
            List<DispensingFormItemV2> orderLogFormItemList,
            DispensingOrder unDispensingOrder) throws ParamRequiredException, CisCustomException, ServiceInternalException {
        UndispenseResult undispenseResult = new UndispenseResult();

        //参数检查
        clientUnDispenseReq.parameterCheck();
        if (clientUnDispenseReq.getDispenseType() != DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_DIRECT //直接退
                && clientUnDispenseReq.getDispenseType() != DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_APPLY //申请退
                && clientUnDispenseReq.getDispenseType() != DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_CONFIRM//通过申请
                && clientUnDispenseReq.getDispenseType() != DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_REJECT //拒绝申请
                && clientUnDispenseReq.getDispenseType() != DispenseDispenseOrderReq.DispenseType.RE_APPLY_UNDISPENSE //重新退药申请
        ) {
            throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "请指定有效的退药类型");
        }
        //DB 发药单上的 可以退药的退药项
        List<DispensingFormItemV2> canDispensedItems = unDispenseSheet.canUnDispenseItems();
        if (canDispensedItems.isEmpty()) {
            return undispenseResult;
        }

        // 本次请求退药打平的 formItem 列表
        List<DispenseDispenseOrderReq.DispenseFormItemReq> clientReqUnDispenseFormItems = clientUnDispenseReq.getAllDispenseFormItems();
        if (clientReqUnDispenseFormItems.isEmpty()) {
            return undispenseResult;
        }

        if (clientUnDispenseReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_APPLY
                || clientUnDispenseReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.RE_APPLY_UNDISPENSE) {
            // 申请退药设置申请时间
            dispensingOrder.setApplyUndispenseTime(Instant.now());
//            FillUtils.fillLastModifiedBy(dispensingOrder, operatorId);
        }

        //DB 发药单上 全部发药FromItem的打平
        List<DispensingFormItemV2> svrAllFormItems = unDispenseSheet.allDispenseItems();
        Map<String, DispensingFormItemV2> formItemIdToFormItem = svrAllFormItems.stream().collect(Collectors.toMap(DispensingFormItemV2::getId, Function.identity(), (a, b) -> a));

        //发药FormItemId -> 已退UnitCount
        Map<String, BigDecimal> dispenseFormItemIdToUnDispensedUnitCount = new HashMap<>();
        //发药FormItemId -> 已退DoseCount
        Map<String, BigDecimal> dispenseFormItemIdToUnDispensedDoseCount = new HashMap<>();
        // 发药 formItemId -> (批次ID -> 批次可退数量)
        Map<String, Map<Long, BigDecimal>> dispenseFormItemIdToBatchIdToUndispensableUnitCount = new HashMap<>();

        //计算已经退的数量 包括 已退和可退的
        calUnDispensedCount(svrAllFormItems, dispenseFormItemIdToUnDispensedUnitCount, dispenseFormItemIdToUnDispensedDoseCount, dispenseFormItemIdToBatchIdToUndispensableUnitCount);

        //发送退药请求到scGoods 处理库存
        Map<String, GoodsDispenseResultItem> dispenseFormItemIdToResultItem = null;
        if (clientUnDispenseReq.needSendScGoodsToStockOps()) {
            dispenseFormItemIdToResultItem = sendGoodsUnDispenseDataItemsToScGoods(unDispenseSheet,
                    patientOrderNo,
                    clientReqUnDispenseFormItems,
                    formItemIdToFormItem,
                    operatorId,
                    clientUnDispenseReq.getDispenseType(),
                    dispenseFormItemIdToUnDispensedUnitCount,
                    dispenseFormItemIdToUnDispensedDoseCount,
                    dispenseFormItemIdToBatchIdToUndispensableUnitCount);
        }
        String operationId = abcIdGenerator.getUID();
        List<DispensingFormItemV2> logUndispensingFormItems = new ArrayList<>();
        boolean needSendMsg = updateUnDispenseToSheet(
                unDispensingOrder,
                operationId,
                clientUnDispenseReq,
                unDispenseSheet,
                formItemIdToFormItem,
                dispenseFormItemIdToResultItem,
                dispenseFormItemIdToUnDispensedUnitCount,
                dispenseFormItemIdToUnDispensedDoseCount,
                operatorId,
                logUndispensingFormItems,
                orderLogFormItemList);
        if (needSendMsg) {
            unDispenseSheet.setDispensingOrder(unDispensingOrder);
            sndMessageDispenseList.add(unDispenseSheet);
        }
        saveAssembleDispenseSheet(unDispenseSheet);
        dispensingLogService.logDispensingSheetUndispense(unDispenseSheet, logUndispensingFormItems, operationId, operatorId);

        String dispensingSheetId = unDispenseSheet.getId();
        SheetOperationBase operationBase = sheetOperationCreateFactory.createSheetOperation(DispensingSheetOperation.OperationType.WITHDRAWAL_MEDICINE, dispensingSheetId, operatorId, operatorId, unDispenseSheet.getChainId(), unDispenseSheet.getClinicId());

        List<DispensingSheetOperation> dispenseOperations = dispensingSheetOperationRepository.findAllByDispensingSheetIdAndIsDeletedOrderByCreatedDescOperationTypeDesc(dispensingSheetId, 0);
        ArrayList<GoodsDispenseResultItem> goodsDispenseResultItems = dispenseFormItemIdToResultItem != null ? new ArrayList<>(dispenseFormItemIdToResultItem.values()) : new ArrayList<>();
        List<DispensingLogItem> dispenseItemLogs = dispensingLogRepository.findAllByDispensingSheetIdAndType(dispensingSheetId, DispensingLogItem.Type.DISPENSE);
        operationBase.bindDispensingSheetV2(unDispenseSheet)
                .bindDispensingFormItemsV2(logUndispensingFormItems)
                .bindDispenseOperations(dispenseOperations)
                .bindGoodsDispenseResultItems(goodsDispenseResultItems)
                .bindDispenseItemLogs(dispenseItemLogs)
                .bindRepoId(operationId);
        operationBase.createAndSaveSheetOperation();

        if (dispenseFormItemIdToResultItem != null) {
            undispenseResult.setUndispensedCount(dispenseFormItemIdToResultItem.size());
        }
        undispenseResult.setStatus(unDispenseSheet.getStatus());
        undispenseResult.setId(dispensingSheetId);
        unDispensingOrder.getDispensingSheetV2List().add(unDispenseSheet);
        return undispenseResult;
    }

    private void saveAssembleDispenseSheet(DispensingSheetV2 dispenseSheet) {
        dispensingSheetEntityService.save(dispenseSheet);
    }

    /**
     * 这一段是比较核心的逻辑 ，算出已经退药的数量
     * 为了避免重复多退 应该包含申请中的
     */
    public void calUnDispensedCount(List<DispensingFormItemV2> svrAllFormItems, //发药单的所有 发药退药记录
                                    Map<String, BigDecimal> dispenseFormItemIdToUnDispensedUnitCount,//输出参数 已退数量
                                    Map<String, BigDecimal> dispenseFormItemIdToUnDispensedDoseCount, //输出参数 已退剂量  -- 为了避免重复多退 应该包含申请中的
                                    Map<String, Map<Long, BigDecimal>> dispenseFormItemIdToBatchIdToUndispenseUnitCount) {
        for (DispensingFormItemV2 svrFormItem : svrAllFormItems) {
            String dispenseFormItemId = svrFormItem.getAssociateFormItemId();
            if (!CollectionUtils.isEmpty(svrFormItem.getDispensingFormItemBatches())) {
                Map<Long, BigDecimal> batchIdToUndispenseUnitCount = dispenseFormItemIdToBatchIdToUndispenseUnitCount.computeIfAbsent(svrFormItem.getId(), k -> new HashMap<>());
                for (DispensingFormItemBatchInfo batch : svrFormItem.getDispensingFormItemBatches()) {
                    if (batch.getIsOld() == 1) {
                        continue;
                    }
                    BigDecimal dispenseUnitCount = batch.getDispenseUnitCount();
                    BigDecimal unDispenseUnitCount = batch.getUndispenseUnitCount();
                    batchIdToUndispenseUnitCount.put(batch.getBatchId(), MathUtils.wrapBigDecimalSubtract(dispenseUnitCount, unDispenseUnitCount));
                }
            }

            //退药的FormItem
            if (!svrFormItem.unDispensed()) {
                continue;
            }
            //发药项的FormItem
            if (StringUtils.isEmpty(dispenseFormItemId)) {
                continue;
            }
            if (svrFormItem.getUndispenseType() == DispensingFormItem.UndispenseType.BY_UNIT) {
                BigDecimal svrUnDispenseCount = MathUtils.wrapBigDecimal(svrFormItem.getUnitCount(), BigDecimal.ZERO);
                BigDecimal unDispenseCount = MathUtils.wrapBigDecimal(dispenseFormItemIdToUnDispensedUnitCount.get(dispenseFormItemId), BigDecimal.ZERO);
                dispenseFormItemIdToUnDispensedUnitCount.put(dispenseFormItemId,
                        MathUtils.wrapBigDecimalAdd(svrUnDispenseCount, unDispenseCount));
            } else if (svrFormItem.getUndispenseType() == DispensingFormItem.UndispenseType.BY_DOSE) {
                BigDecimal svrUnDispenseCount = MathUtils.wrapBigDecimal(svrFormItem.getDoseCount(), BigDecimal.ZERO);
                BigDecimal unDispenseCount = MathUtils.wrapBigDecimal(dispenseFormItemIdToUnDispensedDoseCount.get(dispenseFormItemId), BigDecimal.ZERO);
                dispenseFormItemIdToUnDispensedDoseCount.put(dispenseFormItemId,
                        MathUtils.wrapBigDecimalAdd(svrUnDispenseCount, unDispenseCount));
            }

        }
    }

    /**
     * 收集发药单 退药列表
     *
     * @param unDispenseSheet DB 发药单信息
     */
    private Map<String, GoodsDispenseResultItem> sendGoodsUnDispenseDataItemsToScGoods(
            DispensingSheetV2 unDispenseSheet,
            String patientOrderNo,
            List<DispenseDispenseOrderReq.DispenseFormItemReq> clientReqUnDispenseFormItems,
            Map<String, DispensingFormItemV2> formItemIdToFormItem,
            String operatorId,
            int unDispenseType,
            Map<String, BigDecimal> dispenseFormItemIdToUnDispensedUnitCount,
            Map<String, BigDecimal> dispenseFormItemIdToUnDispensedDoseCount,
            Map<String, Map<Long, BigDecimal>> dispenseFormItemIdToBatchIdToUndispensableUnitCount) throws DispenseSheetChangedException {
        //入参检查
        if (CollectionUtils.isEmpty(unDispenseSheet.getDispensingForms())) {
            return new HashMap<>();
        }

        /*
          1.构造发药请求 主要逻辑是找出发药记录, 减去已退数量，得到可退数量 可退数量必须要大于登录本次请求的数量
          2.背景 发药只能一次发完，所以在数据库里面只有一条 DISPENSE的记录
                 退药可以分多次退，所以一条DISEPNSE的记录 会有多条 UNDISPENSE的记录和他关联
          */
        List<GoodsDispenseDataItem> goodsDispenseDataItems = new ArrayList<>();
        for (DispenseDispenseOrderReq.DispenseFormItemReq clientUnFromReqItem : clientReqUnDispenseFormItems) {
            //要处理退药的发药ITEM
            DispensingFormItemV2 dispenseItem = formItemIdToFormItem.get(clientUnFromReqItem.getId());
            if (dispenseItem == null) {
                continue;
            }
            if (dispenseItem.getSourceItemType() == DispensingFormItem.SourceItemType.SELF_PROVIDED) {
                continue;
            }
            if (clientUnFromReqItem.getDispenseType() == DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_REJECT) {
                // item拒发不发药
                log.info("item拒退，item: {}", dispenseItem);
                continue;
            }
            DispensingFormItemV2 applyUnDispenseItem = null;
            if (!StringUtils.isEmpty(clientUnFromReqItem.getId())
                    && (unDispenseType == DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_CONFIRM
                    || unDispenseType == DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_REJECT)) {
                applyUnDispenseItem = formItemIdToFormItem.get(clientUnFromReqItem.getId());
                if (applyUnDispenseItem != null) {
                    clientUnFromReqItem.setUndispenseType(applyUnDispenseItem.getUndispenseType());
                    clientUnFromReqItem.setDoseCount(applyUnDispenseItem.getDoseCount());
                    clientUnFromReqItem.setUnitCount(applyUnDispenseItem.getUnitCount());
                }
            }
            /*
              计算退药的数量
              */
            BigDecimal toUndispenseTotalCount = null;
            if (applyUnDispenseItem != null) {
                toUndispenseTotalCount = MathUtils.calculateTotalCount(applyUnDispenseItem.getUnitCount(), applyUnDispenseItem.getDoseCount());
            } else {
                Pair<BigDecimal, BigDecimal> unDispenseUnitCountAndDoseCount = calCanUnDispenseCount(unDispenseSheet.getPharmacyType(), dispenseFormItemIdToUnDispensedUnitCount, dispenseFormItemIdToUnDispensedDoseCount, dispenseItem, clientUnFromReqItem);
                if (unDispenseUnitCountAndDoseCount == null) {
                    continue;
                }
                BigDecimal toUndispenseUnitCount = unDispenseUnitCountAndDoseCount.getFirst();
                BigDecimal toUndispenseDoseCount = unDispenseUnitCountAndDoseCount.getSecond();
                toUndispenseTotalCount = MathUtils.calculateTotalCount(toUndispenseUnitCount, toUndispenseDoseCount);
            }

            GoodsDispenseDataItem goodsDispenseDataItem = new GoodsDispenseDataItem();
            goodsDispenseDataItem.setPharmacyNo(dispenseItem.getPharmacyNo());
            goodsDispenseDataItem.setDispenseItemId(dispenseItem.getAssociateFormItemId());
            goodsDispenseDataItem.setUnDispenseItemId(dispenseItem.getId());
            goodsDispenseDataItem.setDealId(dispenseItem.getStockDealId());
            goodsDispenseDataItem.setGoodsId(dispenseItem.getProductId());
            if (dispenseItem.getUseDismounting() == 1) {
                goodsDispenseDataItem.setPieceCount(toUndispenseTotalCount);
                goodsDispenseDataItem.setPackageCount(BigDecimal.ZERO);
            } else {
                goodsDispenseDataItem.setPackageCount(toUndispenseTotalCount);
                goodsDispenseDataItem.setPieceCount(BigDecimal.ZERO);
            }
            if (!CollectionUtils.isEmpty(clientUnFromReqItem.getDispensingFormItemBatches())
                    && !CollectionUtils.isEmpty(dispenseFormItemIdToBatchIdToUndispensableUnitCount)) {
                List<DispensingFormItemBatchReq> clientUndispenseFormItemBatches = clientUnFromReqItem.getDispensingFormItemBatches();
                List<GoodsDispenseDataItem.BatchCount> batchCounts = new ArrayList<>();
                Map<Long, BigDecimal> batchIdToUndispensableUnitCount = dispenseFormItemIdToBatchIdToUndispensableUnitCount.getOrDefault(dispenseItem.getAssociateFormItemId(), Collections.emptyMap());
                clientUndispenseFormItemBatches.forEach(clientUndispenseFormItemBatch -> {
                    Long batchId = clientUndispenseFormItemBatch.getBatchId();
                    BigDecimal undispensableUnitCount = batchIdToUndispensableUnitCount.get(batchId);
                    BigDecimal clientUndispenseUnitCount = clientUndispenseFormItemBatch.getUnitCount();
                    if (undispensableUnitCount == null || MathUtils.wrapBigDecimalCompare(clientUndispenseUnitCount, undispensableUnitCount) > 0) {
                        log.error("请求退药批次数量超过该批次可退数量：dispensingFormItem:{} batchId:{} clientUndispenseUnitCount:{} undispensableUnitCount:{}", dispenseItem.getId(), batchId, clientUndispenseUnitCount, undispensableUnitCount);
                        throw new DispenseSheetChangedException();
                    }

                    GoodsDispenseDataItem.BatchCount batchCount = new GoodsDispenseDataItem.BatchCount();
                    batchCount.setBatchId(batchId);
                    if (dispenseItem.getUseDismounting() == 1) {
                        batchCount.setPieceCount(clientUndispenseUnitCount);
                    } else {
                        batchCount.setPackageCount(clientUndispenseUnitCount);
                    }
                });
                // 指定批次退药
                goodsDispenseDataItem.setBatchCountList(batchCounts);
            }
            //TODO 成本
            BigDecimal totalPrice = null;
            BigDecimal unitPrice = dispenseItem.getUnitPrice();
            if (unitPrice != null) {
                totalPrice = unitPrice.multiply(toUndispenseTotalCount).setScale(4, RoundingMode.HALF_UP);
            }
            goodsDispenseDataItem.setTotalPrice(totalPrice);
            goodsDispenseDataItem.setShebaoDismountingFlag(dispenseItem.getShebaoDismountingFlag());
            goodsDispenseDataItems.add(goodsDispenseDataItem);

            dealUnDispenseTraceCode(dispenseItem, clientUnFromReqItem, goodsDispenseDataItem);
        }
        List<GoodsDispenseResultItem> goodsDispenseResultItems = new ArrayList<>();
        if (goodsDispenseDataItems.size() != 0) {
            goodsDispenseResultItems = abcCisScGoodsService.undispense(
                    unDispenseSheet.getClinicId(),
                    unDispenseSheet.getChainId(),
                    operatorId,
                    patientOrderNo,
                    unDispenseSheet.getPatientOrderId(),
                    unDispenseSheet.getId(),
                    unDispenseSheet.getPharmacyNo(),
                    unDispenseSheet.getPharmacyType(),
                    goodsDispenseDataItems,
                    DTOConverter.dispensingFormItemV2MapToDispensingFormItemAbstractMap(formItemIdToFormItem),
                    10);
            if (goodsDispenseResultItems == null || goodsDispenseResultItems.size() == 0 || goodsDispenseResultItems.size() != goodsDispenseDataItems.size()) {
                throw new GoodsUndispenseException();
            }
        }

        //bind
        Map<String, GoodsDispenseResultItem> dispenseFormItemIdToResultItem = goodsDispenseResultItems
                .stream()
                .collect(Collectors.toMap(GoodsDispenseResultItem::getDispenseItemId, Function.identity(), (item1, item2) -> item1));
        return dispenseFormItemIdToResultItem;
    }

    /**
     * @param dispensedItem         发药的FormItem
     * @param clientUnFromReqItem   客户端传入的退药FormItem
     * @param goodsDispenseDataItem 发送给ScGoods的退药申请
     */
    private void dealUnDispenseTraceCode(DispensingFormItemV2 dispensedItem, DispenseDispenseOrderReq.DispenseFormItemReq clientUnFromReqItem, GoodsDispenseDataItem goodsDispenseDataItem) {
        //指定追溯码退药
        if (clientUnFromReqItem == null || CollectionUtils.isEmpty(clientUnFromReqItem.getTraceableCodeList())) {
            return;
        }
        clientUnFromReqItem.setTraceableCodeList(clientUnFromReqItem.getTraceableCodeList().stream().filter(traceableCode -> !TextUtils.isEmpty(traceableCode.getNo())).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(clientUnFromReqItem.getTraceableCodeList())) {
            return;
        }
        /**
         * 本次退药标记追溯码可用
         * 如果指定退追溯码，要把退的状态写服务器的状态
         * 产品这里放的很开，后台这里把各种情况都处理了
         * 退药可以不退追溯码，产品逻辑
         * */
        //服务端没extend
        if (dispensedItem.getExtendData() == null) {
            dispensedItem.setExtendData(new DispensingFormItemExtendData());
            dispensedItem.getExtendData().setTraceableCodeList(new ArrayList<>());
        }
        //支持之前没有录入追溯码，可以指定追溯码退
        if (dispensedItem.getExtendData().getTraceableCodeList() == null) {
            dispensedItem.getExtendData().setTraceableCodeList(new ArrayList<>());
        }

        for (TraceableCode clientTraceableCode : clientUnFromReqItem.getTraceableCodeList()) {
            if (clientTraceableCode.getType() != null && clientTraceableCode.getType() == GoodsConst.DrugIdentificationCodeType.NO_CODE) {
                continue;
            }

            //追溯码不会一次发药不会太多，直接for
            TraceableCode usedTraceableCode = dispensedItem.getExtendData().getTraceableCodeList().stream().filter(traceableCodeSvr -> traceableCodeSvr.getNo().compareTo(clientTraceableCode.getNo()) == 0).findFirst().orElse(null);
            if (usedTraceableCode == null) {
                throw new CisCustomException(DispensingServiceError.DISPENSING_PARAMETER_ERROR.getCode(), "退药追溯码在发药请求里面没找到");
            }
            usedTraceableCode.setBatchId(clientTraceableCode.getBatchId());
            usedTraceableCode.setUsed(DispensingConstants.TraceableCodeUsed.UNDISPENSE);
        }
        /**
         * 如果是指定追溯码退药，把追溯码加到scGoods的发药请求里面
         * */
        if (goodsDispenseDataItem != null) {
            goodsDispenseDataItem.setTraceableCodeList(clientUnFromReqItem.getTraceableCodeList());
            goodsDispenseDataItem.setShebaoDismountingFlag(dispensedItem.getShebaoDismountingFlag());
        }

    }

    /**
     * 退药的更新
     */
    private boolean updateUnDispenseToSheet(
            DispensingOrder undispensingOrder,
            String unDispenseOperatorId, //发药员ID
            DispenseDispenseOrderReq.DispenseSheetReq clientUnDispenseReq, //客户端发起的退药请求
            DispensingSheetV2 unDispenseSheet, //发药单&退药单
            Map<String, DispensingFormItemV2> formItemIdToFormItem, //发药单下所有的发药退药 map
            Map<String, GoodsDispenseResultItem> dispenseFormItemIdToResultItem,//发药&退药 结果 ，对于申请发药 可以没有结果
            Map<String, BigDecimal> dispenseFormItemIdToUnDispensedUnitCount, //已经退药的数量
            Map<String, BigDecimal> dispenseFormItemIdToUnDispensedDoseCount, //已经退药的剂量
            String employeeId,//当前登录人
            List<DispensingFormItemV2> logUnDispensingFormItems, //返回用于写日志
            List<DispensingFormItemV2> orderLogFormItems // 用于写日志
    ) {

        boolean returnUnDispenseMsg = false;
        for (DispensingFormV2 dispensingFormV2 : unDispenseSheet.getDispensingForms()) {
            //找到发药请求体
            DispenseDispenseOrderReq.DispenseFormReq clientUnDispenseForm = clientUnDispenseReq.getDispenseFormReqs().stream().filter(item -> TextUtils.equals(item.getId(), dispensingFormV2.getId())).findFirst().orElse(null);
            if (clientUnDispenseForm == null) {
                log.error("未找到发药客户请求");
                continue;
            }
            //发药formItem
            for (DispenseDispenseOrderReq.DispenseFormItemReq dispenseFormItemReq : clientUnDispenseForm.getDispenseFormItemReqs()) {
                //发药记录
                DispensingFormItemV2 undispenseFormItem = formItemIdToFormItem.get(dispenseFormItemReq.getId());
                if (undispenseFormItem == null) {
                    continue;
                }
                boolean isChineseMedicine = DispensingUtils.isChineseMedicine(undispenseFormItem.getProductType(), undispenseFormItem.getProductSubType());
                //发药结果
                GoodsDispenseResultItem goodsDispenseResultItem = null;
                DispensingFormItemV2 dispenseFormItem = null;
                if (dispenseFormItemIdToResultItem != null) {
                    goodsDispenseResultItem = dispenseFormItemIdToResultItem.get(undispenseFormItem.getAssociateFormItemId());
                    dispenseFormItem = formItemIdToFormItem.get(undispenseFormItem.getAssociateFormItemId());
                }
                DispensingFormItemV2 unDispensedFormItem = null;
                //直接退药或申请退药
                if (clientUnDispenseReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_DIRECT
                        || clientUnDispenseReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_APPLY) {
                    if (clientUnDispenseReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_APPLY) {
                        if (undispenseFormItem.getStatus() == DispenseConst.Status.RECORD_NOT_DISPENSE) {
                            throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "补记不允许退药");
                        }
                    }
                    //本次退药成功是直接 新生成一条FormItem
                    unDispensedFormItem = new DispensingFormItemV2();
                    BeanUtils.copyProperties(undispenseFormItem, unDispensedFormItem);
                    FillUtils.fillCreatedBy(unDispensedFormItem, employeeId);
                    unDispensedFormItem.setOperationId(unDispenseOperatorId);
                    unDispensedFormItem.setId(abcIdGenerator.getUUID());
                    unDispensedFormItem.setAssociateFormItemId(undispenseFormItem.getId());
                    Pair<BigDecimal, BigDecimal> unDispenseUnitCountAndDoseCount = calCanUnDispenseCount(unDispenseSheet.getPharmacyType(), dispenseFormItemIdToUnDispensedUnitCount, dispenseFormItemIdToUnDispensedDoseCount, undispenseFormItem, dispenseFormItemReq);
                    if (unDispenseUnitCountAndDoseCount != null) {
                        unDispensedFormItem.setUnitCount(unDispenseUnitCountAndDoseCount.getFirst());
                        unDispensedFormItem.setDoseCount(unDispenseUnitCountAndDoseCount.getSecond());
                    }
                    //设置UnDispenseType
                    if (unDispensedFormItem.getProductType() == Constants.ProductType.MEDICINE && unDispensedFormItem.getProductSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE) {
                        unDispensedFormItem.setUndispenseType(DispensingFormItem.UndispenseType.BY_DOSE);
                    } else {
                        unDispensedFormItem.setUndispenseType(DispensingFormItem.UndispenseType.BY_UNIT);
                    }

                    //申请退药
                    if (clientUnDispenseReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_APPLY) {
                        unDispensedFormItem.setStatus(DispenseConst.Status.APPLY_UNDISPNSE);
                    } else if (clientUnDispenseReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_DIRECT) {
                        if (goodsDispenseResultItem != null) {
                            unDispensedFormItem.setStatus(DispenseConst.Status.UNDISPENSED);
                            //打上内存标记位，标记本次退药
                            unDispensedFormItem.setThisTimeDispensed(1);
                            unDispensedFormItem.setStockDealId(DispensingUtils.getGoodsDispenseResultItemDealId(goodsDispenseResultItem, false));
                            unDispensedFormItem.setTotalCostPrice(MathUtils.wrapBigDecimal(goodsDispenseResultItem.getTotalCostPrice(), BigDecimal.ZERO));
                            returnUnDispenseMsg = true;

                            // 更新批次信息
                            updateUndispenseToDispensingFormItemBatch(unDispensedFormItem, dispenseFormItem, goodsDispenseResultItem, employeeId);

                            if (!CollectionUtils.isEmpty(undispenseFormItem.getDispensingFormItemBatches())) {
                                // 根据批次重新算 totalPrice
                                BigDecimal undispenseItemTotalPrice = undispenseFormItem.getDispensingFormItemBatches().stream()
                                        .map(DispensingFormItemBatchInfo::getTotalPrice).reduce(BigDecimal.ZERO, MathUtils::wrapBigDecimalAdd);
                                unDispensedFormItem.setTotalPrice(undispenseItemTotalPrice);
                            }
                            unDispensedFormItem.setOperationId(unDispenseOperatorId);
                        }
                    }

                    //自备药品不写入DispensingLog表
                    if (clientUnDispenseReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_DIRECT
                            && unDispensedFormItem.getSourceItemType() == DispensingFormItem.SourceItemType.NORMAL) {
                        logUnDispensingFormItems.add(unDispensedFormItem);
                    }


                    DispenseUsageInfo usageInfo = DispensingSheetServerCreateReqFactory.createUsageInfo();
                    usageInfo.setUnDispenseOrderId(undispensingOrder.getId());
                    //写入Remark
                    if (clientUnDispenseReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_DIRECT) {
                        if (!StringUtils.isEmpty(dispenseFormItemReq.getDispenseRemark())) {
                            usageInfo.setDispenseRemark(dispenseFormItemReq.getDispenseRemark());
                            unDispensedFormItem.setUsageInfoJson(JsonUtils.dump(usageInfo));
                        }
                    } else {
                        // 护士站申请退药
                        if (!StringUtils.isEmpty(dispenseFormItemReq.getApplyDispenseRemark())) {
                            usageInfo.setApplyDispenseRemark(dispenseFormItemReq.getApplyDispenseRemark());
                            unDispensedFormItem.setUsageInfoJson(JsonUtils.dump(usageInfo));
                        }
                    }
                    // 申请退药暂时不要追溯码，置空
                    if (unDispensedFormItem.getExtendData() != null) {
                        unDispensedFormItem.getExtendData().setTraceableCodeList(null);
                        if (undispenseFormItem.getExtendData().isEmpty()) {
                            unDispensedFormItem.setExtendData(null);
                        }
                    }
                    dispensingFormV2.getDispensingFormItems().add(unDispensedFormItem);

                } else {
                    unDispensedFormItem = formItemIdToFormItem.get(dispenseFormItemReq.getId());
                    if (unDispensedFormItem != null) {
                        //自备药品不写入DispensingLog表
                        if (clientUnDispenseReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_CONFIRM
                                && unDispensedFormItem.getSourceItemType() == DispensingFormItem.SourceItemType.NORMAL) {
                            logUnDispensingFormItems.add(unDispensedFormItem);
                        }

                        if (clientUnDispenseReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.RE_APPLY_UNDISPENSE) {
                            // 重新发起退药申请
                            if (unDispensedFormItem.getStatus() != DispenseConst.Status.APPLY_UNDISPENSE_REJECT) {
                                throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "非拒退药品不能重新发起退药");
                            }
                        } else {
                            if (unDispensedFormItem.getStatus() == DispenseConst.Status.APPLY_UNDISPENSE_REJECT) {
                                throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "药品" + unDispensedFormItem.getDispensingFormId() + "已经被拒绝退药");
                            }
                        }
                        if (clientUnDispenseReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_REJECT) {
                            unDispensedFormItem.setStatus(DispenseConst.Status.APPLY_UNDISPENSE_REJECT);
                            FillUtils.fillLastModifiedBy(unDispensedFormItem, employeeId);
                        } else if (clientUnDispenseReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.RE_APPLY_UNDISPENSE) {
                            unDispensedFormItem.setStatus(DispenseConst.Status.REAPPLY_UNDISPENSE);
                            FillUtils.fillLastModifiedBy(unDispensedFormItem, employeeId);
                        } else if (clientUnDispenseReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_CONFIRM) {
                            if (isChineseMedicine && dispenseFormItemReq.getDispenseType() == DispenseDispenseOrderReq.DispenseType.UN_DISPENSE_REJECT) {
                                unDispensedFormItem.setStatus(DispenseConst.Status.APPLY_UNDISPENSE_REJECT);
                                FillUtils.fillLastModifiedBy(unDispensedFormItem, employeeId);
                            } else {
                                if (goodsDispenseResultItem != null) {
                                    unDispensedFormItem.setStatus(DispenseConst.Status.UNDISPENSED);
                                    //打上内存标记位，标记本次退药
                                    unDispensedFormItem.setThisTimeDispensed(1);
                                    unDispensedFormItem.setStockDealId(DispensingUtils.getGoodsDispenseResultItemDealId(goodsDispenseResultItem, false));
                                    unDispensedFormItem.setTotalCostPrice(MathUtils.wrapBigDecimal(goodsDispenseResultItem.getTotalCostPrice(), BigDecimal.ZERO));
                                    FillUtils.fillLastModifiedBy(unDispensedFormItem, employeeId);
                                    returnUnDispenseMsg = true;

                                    // 更新批次信息
                                    updateUndispenseToDispensingFormItemBatch(unDispensedFormItem, dispenseFormItem, goodsDispenseResultItem, employeeId);

                                    if (!CollectionUtils.isEmpty(undispenseFormItem.getDispensingFormItemBatches())) {
                                        // 根据批次重新算 totalPrice
                                        BigDecimal undispenseItemTotalPrice = undispenseFormItem.getDispensingFormItemBatches().stream()
                                                .map(DispensingFormItemBatchInfo::getTotalPrice).reduce(BigDecimal.ZERO, MathUtils::wrapBigDecimalAdd);
                                        unDispensedFormItem.setTotalPrice(undispenseItemTotalPrice);
                                    }
                                    unDispensedFormItem.setOperationId(unDispenseOperatorId);
                                }
                            }
                        }
                        //设置发药备注
                        if (!StringUtils.isEmpty(dispenseFormItemReq.getDispenseRemark())) {
                            DispenseUsageInfo usageInfo = DispensingUtils.toDbUsageInfo(unDispensedFormItem.getUsageInfoJson());
                            if (usageInfo == null) {
                                usageInfo = DispensingSheetServerCreateReqFactory.createUsageInfo();
                            }
                            usageInfo.setDispenseRemark(dispenseFormItemReq.getDispenseRemark());
                            unDispensedFormItem.setUsageInfoJson(JsonUtils.dump(usageInfo));
                        }
                    }
                }
                orderLogFormItems.add(unDispensedFormItem);
            }
        }
        return returnUnDispenseMsg;
    }

    public void updateUndispenseToDispensingFormItemBatch(DispensingFormItemV2 undispenseFormItemItem,
                                                          DispensingFormItemV2 dispenseFormItemItem,
                                                          GoodsDispenseResultItem resultItem,
                                                          String employeeId) {
        if (dispenseFormItemItem == null || resultItem == null) {
            return;
        }

        List<GoodsDispenseResultItem.DispenseBatchInfo> thisTimeDispenseBatchInfo = resultItem.getOriginalDispenseBatchInfoList();
        if (!CollectionUtils.isEmpty(dispenseFormItemItem.getDispensingFormItemBatches())) {
            if (CollectionUtils.isEmpty(thisTimeDispenseBatchInfo)) {
                log.error("thisTimeDispenseBatchInfo is empty, dispenseFormItemItem:{}", dispenseFormItemItem);
                throw new DispenseSheetChangedException();
            }
            // 已经有批次信息，则更新批次信息
            List<DispensingFormItemBatchInfo> dispensingFormItemBatches = dispenseFormItemItem.getDispensingFormItemBatches();
            Map<Long, DispensingFormItemBatchInfo> batchIdToDispensingFormItemBatchInfo = ListUtils.toMap(dispensingFormItemBatches, DispensingFormItemBatchInfo::getBatchId);
            thisTimeDispenseBatchInfo.forEach(dispenseBatchInfo -> {
                Long batchId = dispenseBatchInfo.getBatchId();
                DispensingFormItemBatchInfo dispensingFormItemBatchInfo = batchIdToDispensingFormItemBatchInfo.get(batchId);
                if (dispensingFormItemBatchInfo == null) {
                    log.error("退了一个没有发的批次, batchId:{}", batchId);
                    throw new DispenseSheetChangedException();
                }

                BigDecimal stockCount = DispensingUtils.calculateStockCount(dispenseFormItemItem.getUseDismounting() == 1,
                        dispenseBatchInfo.getPackageCount(), BigDecimal.valueOf(dispenseBatchInfo.getPieceNum()), dispenseBatchInfo.getPieceCount());
                dispensingFormItemBatchInfo.setUndispenseUnitCount(MathUtils.wrapBigDecimalAdd(dispensingFormItemBatchInfo.getUndispenseUnitCount(), stockCount));
                if (MathUtils.wrapBigDecimalCompare(dispensingFormItemBatchInfo.getUndispenseUnitCount(), dispensingFormItemBatchInfo.getDispenseUnitCount()) > 0) {
                    log.error("批次退药数量大于发药数量, batchId:{}, undispenseUnitCount:{} dispenseUnitCount:{}", batchId, dispensingFormItemBatchInfo.getUndispenseUnitCount(), dispensingFormItemBatchInfo.getDispenseUnitCount());
                    throw new DispenseSheetChangedException();
                }
                FillUtils.fillLastModifiedBy(dispensingFormItemBatchInfo, employeeId);

                DispensingFormItemBatchInfo undispenseFormItemBatchInfo = buildUndispenseFormItemBatchInfo(undispenseFormItemItem, dispensingFormItemBatchInfo, stockCount, employeeId);
                if (undispenseFormItemItem.getDispensingFormItemBatches() == null) {
                    undispenseFormItemItem.setDispensingFormItemBatches(new ArrayList<>());
                }
                undispenseFormItemItem.getDispensingFormItemBatches().add(undispenseFormItemBatchInfo);
            });
        }
    }

    private DispensingFormItemBatchInfo buildUndispenseFormItemBatchInfo(DispensingFormItemV2 undispenseFormItemItem,
                                                                         DispensingFormItemBatchInfo dispensingFormItemBatchInfo,
                                                                         BigDecimal undispenseUnitCount,
                                                                         String operator) {
        if (dispensingFormItemBatchInfo == null) {
            return null;
        }

        DispensingFormItemBatchInfo undispenseFormItemBatchInfo = new DispensingFormItemBatchInfo();
        BeanUtils.copyProperties(dispensingFormItemBatchInfo, undispenseFormItemBatchInfo);
        undispenseFormItemBatchInfo.setId(abcIdGenerator.getUIDLong());
        undispenseFormItemBatchInfo.setDispensingFormItemId(undispenseFormItemItem.getId());
        undispenseFormItemBatchInfo.setAssociateFormItemBatchInfoId(dispensingFormItemBatchInfo.getId());
        undispenseFormItemBatchInfo.setUnitCount(undispenseUnitCount);
        undispenseFormItemBatchInfo.setUndispenseUnitCount(undispenseUnitCount);
        undispenseFormItemBatchInfo.setDispenseUnitCount(undispenseUnitCount);
        BigDecimal totalPrice = MathUtils.wrapBigDecimalMultiply(undispenseFormItemBatchInfo.getUnitCount(), dispensingFormItemBatchInfo.getUnitPrice());
        undispenseFormItemBatchInfo.setTotalPrice(totalPrice);
        BigDecimal sourceTotalPrice = MathUtils.wrapBigDecimalMultiply(undispenseFormItemBatchInfo.getUnitCount(), dispensingFormItemBatchInfo.getSourceUnitPrice());
        undispenseFormItemBatchInfo.setSourceTotalPrice(sourceTotalPrice);
        FillUtils.fillLastModifiedBy(undispenseFormItemBatchInfo, operator);
        return undispenseFormItemBatchInfo;
    }

    /**
     * 计算退药数量
     * 代煎代配药房只能全退
     * 重构 getToUndispenseUnitCountAndDoseCount 这个函数
     * 返回： first  可退数量 second 可退剂量
     */
    public Pair<BigDecimal, BigDecimal> calCanUnDispenseCount(
            int pharmacyType,//空中药房 必须全退
            Map<String, BigDecimal> dispenseFormItemIdToUnDispensedUnitCount,//发药formItemId的 已经数量
            Map<String, BigDecimal> dispenseFormItemIdToUnDispensedDoseCount,//发药formItemId的 已经剂量
            DispensingFormItemV2 dispensedFormItem,//发药FormItem
            DispenseDispenseOrderReq.DispenseFormItemReq clientUnFromReqItem//请求退药 参数
    ) {
        //中 西药 都有的按数量推
        BigDecimal toUnDispenseUnitCount = clientUnFromReqItem.getUnitCount();
        BigDecimal dispensedUnitCount = dispensedFormItem.getUnitCount();
        BigDecimal unDispensedUnitCount = dispenseFormItemIdToUnDispensedUnitCount.getOrDefault(clientUnFromReqItem.getId(), BigDecimal.ZERO);
        BigDecimal canUnDispenseUnitCount = MathUtils.wrapBigDecimalSubtract(dispensedUnitCount, unDispensedUnitCount);

        // 申请退药数量不能大于 可退数量
        if (toUnDispenseUnitCount.compareTo(canUnDispenseUnitCount) > 0) {
            log.info("toUndispenseItem id:{}, {} more than can undispensedUnitCount {}", clientUnFromReqItem.getId(), toUnDispenseUnitCount, canUnDispenseUnitCount);
            return null;
        }

        //如果是西药默认就是 发药fromItem的剂量 1
        BigDecimal toUnDispenseDoseCount = dispensedFormItem.getDoseCount();

        //中药单独处理剂量
        if (GoodsUtils.isChineseMedcine(dispensedFormItem.getProductType(), dispensedFormItem.getProductSubType())) {
            // unitCount设为库中已有的值
            toUnDispenseUnitCount = dispensedFormItem.getUnitCount();
            toUnDispenseDoseCount = clientUnFromReqItem.getDoseCount();
            BigDecimal dispensedDoseCount = dispensedFormItem.getDoseCount();
            BigDecimal unDispensedDoseCount = dispenseFormItemIdToUnDispensedDoseCount.getOrDefault(dispensedFormItem.getId(), BigDecimal.ZERO);
            BigDecimal canUnDispenseDoseCount = MathUtils.wrapBigDecimalSubtract(dispensedDoseCount, unDispensedDoseCount);
            if (toUnDispenseDoseCount.compareTo(canUnDispenseDoseCount) > 0) {
                log.info("toUndispenseItem id:{}, {} more than can undispensedDoseCount {}", clientUnFromReqItem.getId(), toUnDispenseDoseCount, canUnDispenseDoseCount);
                return null;
            }
            /**
             * 代煎代配药房只能全退
             * */
            if (pharmacyType == GoodsConst.PharmacyType.VIRTUAL_PHARMACY && toUnDispenseDoseCount.compareTo(canUnDispenseDoseCount) < 0) {
                log.info("toUndispenseItem virturalPharmayc id:{}, {} less than can undispensedDoseCount {}", clientUnFromReqItem.getId(), toUnDispenseDoseCount, canUnDispenseDoseCount);
                throw new DispensingServiceException(DispensingServiceError.DISPENSING_SHEET_VIRTUAL_UNDISPENSE_ALL);
            }
        }
        return Pair.of(toUnDispenseUnitCount, toUnDispenseDoseCount);
    }

    /**
     * 全部退药
     */
    @Transactional(rollbackFor = Exception.class)
    public UndispenseResult undispenseAllForOpenApi(String dispensingSheetId, String chainId, String clinicId, String operatorId) {
        // 1. 构建出退药请求
        DispensingSheet dispensingSheet = dispensingSheetEntityService.findByIdAndClinicId(dispensingSheetId, clinicId);
        if (dispensingSheet == null) {
            throw new NotFoundException();
        }
        // 只支持全部发药全部退药，不支持部分发了然后全部退
        if (dispensingSheet.getStatus() != DispensingSheet.Status.DISPENSED) {
            log.warn("dispensingSheetId:{}, status:{} is not DISPENSED", dispensingSheetId, dispensingSheet.getStatus());
            throw new DispensingServiceException(DispensingServiceError.DISPENE_SHEET_CANNOT_APPLY_UNDISPENSE);
        }
        DispensingSheet undispenseSheet = buildUndispenseSheetByDispensingSheet(dispensingSheet);
        if (undispenseSheet == null) {
            log.warn("no undispenseSheet");
            UndispenseResult undispenseResult = new UndispenseResult();
            undispenseResult.setStatus(dispensingSheet.getStatus());
            undispenseResult.setStatusName(StatusNameTranslator.translateDispensingSheetStatus(dispensingSheet.getStatus()));
            undispenseResult.setUndispensedCount(0);
            undispenseResult.setId(dispensingSheetId);
            return undispenseResult;
        }
        log.info("undispenseSheet:{}", JsonUtils.dump(undispenseSheet));

        // 2. 退药
        UndispenseResult undispenseResult = undispenseSheet(undispenseSheet, dispensingSheet, clinicId, operatorId);


        // 3. 返回退药结果
        return undispenseResult;
    }

    private DispensingSheet buildUndispenseSheetByDispensingSheet(DispensingSheet dispensingSheet) {
        if (dispensingSheet == null || CollectionUtils.isEmpty(dispensingSheet.getDispensingForms())) {
            return null;
        }

        List<DispensingFormItem> dispensingFormItems = DispensingUtils.getDispensingFormItems(dispensingSheet);
        if (CollectionUtils.isEmpty(dispensingFormItems)) {
            return null;
        }

        Map<String, BigDecimal> dispenseItemIdToUndispenseUnitCount = DispensingUtils.getUndispensedCountIdMapByUndispenseType(dispensingFormItems, DispensingFormItem.UndispenseType.BY_UNIT);
        Map<String, BigDecimal> dispenseItemIdToUndispenseDoseCount = DispensingUtils.getUndispensedCountIdMapByUndispenseType(dispensingFormItems, DispensingFormItem.UndispenseType.BY_DOSE);
        DispensingSheet undispensingSheet = new DispensingSheet();
        undispensingSheet.setId(dispensingSheet.getId());

        List<DispensingForm> undispenseForms = new ArrayList<>();
        dispensingSheet.getDispensingForms().forEach(dispensingForm -> {
            if (dispensingForm == null || CollectionUtils.isEmpty(dispensingForm.getDispensingFormItems())) {
                return;
            }
            int sourceFormType = dispensingForm.getSourceFormType();

            List<DispensingFormItem> undispenseItems = new ArrayList<>();
            dispensingForm.getDispensingFormItems().stream()
                    .filter(dispensingFormItem -> DispensingFormItem.Status.unDispenseable.contains(dispensingFormItem.getStatus()))
                    .forEach(dispenseItem -> {
                        if (sourceFormType == Constants.SourceFormType.PRESCRIPTION_CHINESE) {
                            BigDecimal undispenseDoseCount = dispenseItemIdToUndispenseDoseCount.getOrDefault(dispenseItem.getId(), BigDecimal.ZERO);
                            BigDecimal dispensedDoseCount = DispensingUtils.getDispensedDoseCount(dispenseItem);
                            BigDecimal undispensableDoseCount = MathUtils.wrapBigDecimalSubtract(dispensedDoseCount, undispenseDoseCount);
                            if (MathUtils.wrapBigDecimalCompare(undispensableDoseCount, BigDecimal.ZERO) > 0) {
                                DispensingFormItem undispenseItem = new DispensingFormItem();
                                undispenseItem.setId(dispenseItem.getId());
                                undispenseItem.setDoseCount(undispensableDoseCount);
                                undispenseItem.setUnitCount(dispenseItem.getUnitCount());
                                undispenseItems.add(undispenseItem);
                            }
                        } else {
                            BigDecimal undispenseUnitCount = dispenseItemIdToUndispenseUnitCount.getOrDefault(dispenseItem.getId(), BigDecimal.ZERO);
                            BigDecimal dispensedUnitCount = DispensingUtils.getDispensedUnitCount(dispenseItem);
                            BigDecimal undispensableUnitCount = MathUtils.wrapBigDecimalSubtract(dispensedUnitCount, undispenseUnitCount);
                            if (MathUtils.wrapBigDecimalCompare(undispensableUnitCount, BigDecimal.ZERO) > 0) {
                                DispensingFormItem undispenseItem = new DispensingFormItem();
                                undispenseItem.setId(dispenseItem.getId());
                                undispenseItem.setDoseCount(dispenseItem.getDoseCount());
                                undispenseItem.setUnitCount(undispensableUnitCount);
                                undispenseItems.add(undispenseItem);
                            }
                        }
                    });

            if (CollectionUtils.isEmpty(undispenseItems)) {
                return;
            }

            DispensingForm undispenseForm = new DispensingForm();
            undispenseForm.setId(dispensingForm.getId());
            undispenseForm.setDispensingFormItems(undispenseItems);
            undispenseForms.add(undispenseForm);
        });

        if (CollectionUtils.isEmpty(undispenseForms)) {
            return null;
        }

        undispensingSheet.setDispensingForms(undispenseForms);
        return undispensingSheet;
    }
}
