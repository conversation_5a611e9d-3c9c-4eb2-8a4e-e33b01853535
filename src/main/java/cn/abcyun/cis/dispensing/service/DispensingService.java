package cn.abcyun.cis.dispensing.service;

import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisChargeFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisOutpatientFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisSearchFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.model.UpdateOutpatientFormVendorInfoReq;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeConstants;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.UpdateChargeFormVendorInfoReq;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.OrganPrintView;
import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispenseConst;
import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispensingFormItemGoodsSnap;
import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispensingSheetUnfinishedView;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.*;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.config.GoodsClinicConfigView;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.config.GoodsConfigView;
import cn.abcyun.bis.rpc.sdk.cis.model.patientorder.PatientOrderHospitalVO;
import cn.abcyun.bis.rpc.sdk.cis.model.search.SearchResultRsp;
import cn.abcyun.bis.rpc.sdk.his.message.advice.AdviceMessage;
import cn.abcyun.bis.rpc.sdk.property.base.PropertyInfo;
import cn.abcyun.bis.rpc.sdk.property.base.PropertyKey;
import cn.abcyun.bis.rpc.sdk.property.base.UpdatePropertyItemReq;
import cn.abcyun.bis.rpc.sdk.property.model.Dispensing;
import cn.abcyun.bis.rpc.sdk.property.model.SmartDispensing;
import cn.abcyun.bis.rpc.sdk.property.model.TraceCodeConfig;
import cn.abcyun.bis.rpc.sdk.property.service.PropertyService;
import cn.abcyun.bis.rpc.sdk.property.service.model.PropertyConfigItem;
import cn.abcyun.cis.commons.amqp.message.ChargeSheetMessage;
import cn.abcyun.cis.commons.amqp.message.PatientOrderMessage;
import cn.abcyun.cis.commons.amqp.message.patient.merge.PatientMergeTaskMessage;
import cn.abcyun.cis.commons.amqp.message.patient.merge.PatientMergeTaskStatus;
import cn.abcyun.cis.commons.exception.*;
import cn.abcyun.cis.commons.message.*;
import cn.abcyun.cis.commons.model.CisPatientInfo;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.cis.commons.rpc.charge.*;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.commons.transform.ShebaoDismountingFlagConst;
import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.commons.util.MD5Utils;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import cn.abcyun.cis.core.redis.RedisLock;
import cn.abcyun.cis.core.util.FeignClientRpcTemplate;
import cn.abcyun.cis.dispensing.amqp.HAMQProducer;
import cn.abcyun.cis.dispensing.amqp.RocketMqProducer;
import cn.abcyun.cis.dispensing.api.protocol.*;
import cn.abcyun.cis.dispensing.api.protocol.builder.DispensingSheetDetailRspBuilder;
import cn.abcyun.cis.dispensing.api.protocol.config.*;
import cn.abcyun.cis.dispensing.api.protocol.order.*;
import cn.abcyun.cis.dispensing.base.DispensingConstants;
import cn.abcyun.cis.dispensing.base.exception.*;
import cn.abcyun.cis.dispensing.controller.DispensingItemMerger;
import cn.abcyun.cis.dispensing.controller.StatusNameTranslator;
import cn.abcyun.cis.dispensing.domain.*;
import cn.abcyun.cis.dispensing.domain.call.DispensingCallingItem;
import cn.abcyun.cis.dispensing.entity.TabEntity;
import cn.abcyun.cis.dispensing.listener.DispensingSheetListener;
import cn.abcyun.cis.dispensing.mybatis.mapper.DispensingMapper;
import cn.abcyun.cis.dispensing.repository.DispensingLogRepository;
import cn.abcyun.cis.dispensing.repository.DispensingSheetOperationRepository;
import cn.abcyun.cis.dispensing.repository.DispensingSheetRepository;
import cn.abcyun.cis.dispensing.repository.DispensingSheetV2Repository;
import cn.abcyun.cis.dispensing.rpc.client.OutpatientClient;
import cn.abcyun.cis.dispensing.service.calling.DispensingCallingItemService;
import cn.abcyun.cis.dispensing.service.dispense.DispenseOpt;
import cn.abcyun.cis.dispensing.service.dispense.ReDispensePreCheckOpt;
import cn.abcyun.cis.dispensing.service.dto.*;
import cn.abcyun.cis.dispensing.service.dto.operation.*;
import cn.abcyun.cis.dispensing.service.dto.print.DispensingSheetPrintView;
import cn.abcyun.cis.dispensing.service.huarun.SyncHuaRunOrderService;
import cn.abcyun.cis.dispensing.service.operation.SheetOperationBase;
import cn.abcyun.cis.dispensing.service.operation.SheetOperationCreateFactory;
import cn.abcyun.cis.dispensing.service.rpc.ClinicClient;
import cn.abcyun.cis.dispensing.service.rpc.GoodsLockingFeignClient;
import cn.abcyun.cis.dispensing.service.rpc.ScGoodsFeignClient;
import cn.abcyun.cis.dispensing.service.tianshentai.SyncTianShenTaiOrderService;
import cn.abcyun.cis.dispensing.smartdispensing.service.SmartDispensingService;
import cn.abcyun.cis.dispensing.util.*;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponseBody;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

//TODO ....重构后 DispensingService 只做分发，业务逻辑放到对应servcie里面
@Slf4j
@Service
@Transactional(rollbackFor = Throwable.class)
public class DispensingService {
    private static final Logger sLogger = LoggerFactory.getLogger(DispensingService.class);

    /**
     * 第三方煎培中心推送相关 背景： 华润三九作为ABC空中药房供应商，有自己的智慧药房。同时他也有几个诊所使用ABC诊所管家。所以这里是把华润的几个诊所的中药处方直接推送到他们的智慧药房。(通过他们的智慧药房接口推送) 天慎泰：杭州
     * 虚拟药房，天慎泰有自己的煎培中心，这里也需要把天慎泰的中药处方也推送到他们的煎培中心(通过直接写他们的中间表)
     */
    @Autowired
    private SyncHuaRunOrderService syncHuaRunOrderService;

    @Autowired
    private SyncTianShenTaiOrderService syncTianShenTaiOrderService;

    @Autowired
    private DispensingMapper dispensingMapper;

    @Autowired
    private DispensingOrderService dispensingOrderService;

    @Autowired
    private AbcCisChargeFeignClient abcCisChargeFeignClient;
    @Autowired
    private AbcCisOutpatientFeignClient abcCisOutpatientFeignClient;
    @Autowired
    private AbcCisSearchFeignClient searchFeignClient;

    @Autowired
    private DispensingSheetRepository dispensingSheetRepository;

    @Autowired
    private DispensingSheetEntityService dispensingSheetEntityService;

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private HAMQProducer hamqProducer;

    @Autowired
    private RocketMqProducer rocketMqProducer;
    @Autowired
    private ChargeService chargeService;

    @Autowired
    private SmartDispensingService smartDispensingService;

    @Autowired
    private AbcIdGenerator abcIdGenerator;

    @Autowired
    private PatientOrderService patientOrderService;

    @Autowired
    private PropertyService propertyService;

    @Autowired
    private ClinicClient clinicClient;

    @Autowired
    private DispensingProcessService dispensingProcessService;

    @Autowired
    private DispensingSheetOperationRepository dispensingSheetOperationRepository;

    /**
     * 发药单打印相关
     */
    @Autowired
    private DispensingPrintedService dispensingPrintedService;
    /**
     * 发药单
     */
    @Autowired
    private DispensingSheetService dispensingSheetService;

    /**
     * 发药日志factory
     */
    @Autowired
    private SheetOperationCreateFactory sheetOperationCreateFactory;

    /**
     * Jenkins 相关
     */
    @Autowired
    private DispensingJenkinsRpcService dispensingJenkinsRpcService;
    @Autowired
    private ScGoodsFeignClient scGoodsFeignClient;
    @Autowired
    private OutpatientClient outpatientClient;
    @Autowired
    private DispensingConfigService dispensingConfigService;
    @Autowired
    private GoodsLockingFeignClient goodsLockingFeignClient;
    @Autowired
    private DispensingTodoService dispensingTodoService;

    @Autowired
    private DispensingLogRepository dispensingItemLogRepository;

    @Autowired
    private List<DispensingSheetListener> dispensingSheetListeners;

    @Autowired
    private DispensingCallingItemService dispensingCallingItemService;

    @Autowired
    private DispensingSheetOperationService dispensingSheetOperationService;

    @Autowired
    private AbcCisScGoodsService abcCisScGoodsService;

    @Autowired
    private DispensingSheetV2Repository dispensingSheetV2Repository;

    /**
     * 发药单QuickList
     * 无关键字走db
     * 有关键字  走es
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public DispensingSheetAbstractListRsp findDispensingSheetAbstractList(DispensingSheetAbstractListReq clientReq) throws ServiceInternalException {
        DispensingSheetAbstractListRsp rspData = new DispensingSheetAbstractListRsp();
        rspData.setKeyword(clientReq.getKeyword());
        rspData.setLimit(clientReq.getLimit());
        rspData.setOffset(clientReq.getOffset());
        rspData.setTab(clientReq.getTab());

        List<DispensingSheetAbstract> results = new ArrayList<>();
        int totalCount = 0;
        DispensingSheetListSummary listSummary = null;
        DispensingSheetTypeCount dispensingSheetTypeCount = null;

        // 设置用户药房权限
        List<Integer> employeeAccessPharmacyNos = scGoodsFeignClient.getEmployeePharmacyNosByEmployeeId(clientReq.getChainId(), clientReq.getClinicId(), clientReq.getHeaderEmployeeId());
        if (CollectionUtils.isEmpty(employeeAccessPharmacyNos)) {
            rspData.setResult(new ArrayList<>());
            return rspData;
        }
        if (clientReq.getPharmacyNo() != null) {
            if (employeeAccessPharmacyNos.contains(clientReq.getPharmacyNo())) {
                clientReq.setPharmacyNos(Lists.newArrayList(clientReq.getPharmacyNo()));
            } else {
                log.warn("employee access no permission pharmacy no");
                return rspData;
            }
        } else {
            clientReq.setPharmacyNos(employeeAccessPharmacyNos);
        }

        /**
         *过滤发药单的tag
         * */
        if (TextUtils.isEmpty(clientReq.getKeyword())) {
            if (Objects.nonNull(clientReq.getTakeMedicationBeginTime()) || Objects.nonNull(clientReq.getTakeMedicationEndTime())) {
                results = dispensingMapper.getDispensingSheetAbstractList(clientReq);
                listSummary = dispensingMapper.getDispensingSheetListSummary(clientReq);
                dispensingSheetTypeCount = dispensingMapper.getDispensingSheetTypeCount(clientReq);
            } else {
                results = dispensingMapper.findDispensingSheetAbstractList(clientReq);
                // 3天内从redis中取
                if (within3Days(clientReq.getCreatedBegin(), clientReq.getCreatedEnd())) {
                    DispensingTodoCountDto todoCountDto = dispensingTodoService.getDispensingTodoCount(clientReq.getChainId(), clientReq.getClinicId(), clientReq.getHeaderEmployeeId(), null, clientReq.getPharmacyNo(), null, null, employeeAccessPharmacyNos);
                    listSummary = new DispensingSheetListSummary();
                    dispensingSheetTypeCount = new DispensingSheetTypeCount();
                    dispensingSheetTypeCount.setMedicineCount((int) todoCountDto.getMedicineCount());
                    dispensingSheetTypeCount.setDeliveryCount((int) todoCountDto.getDeliveryCount());
                    dispensingSheetTypeCount.setProcessCount((int) todoCountDto.getProcessCount());
                    listSummary.setNormalTotalCount((int) todoCountDto.getNormalTotalCount());
                    listSummary.setNormalWaitingCount((int) todoCountDto.getNormalWaitingCount());
                    listSummary.setOnlineTotalCount((int) todoCountDto.getOnlineTotalCount());
                    listSummary.setOnlineWaitingCount((int) todoCountDto.getOnlineWaitingCount());
                    listSummary.setDeliveryCount((int) todoCountDto.getDeliveryTotalCount());
                    listSummary.setProcessCount((int) todoCountDto.getProcessTotalCount());
                } else {
                    listSummary = dispensingMapper.findDispensingSheetListSummary(clientReq);
                    dispensingSheetTypeCount = dispensingMapper.findDispensingSheetTypeCount(clientReq);
                }
            }
            totalCount = getTotalCount(clientReq.getTab(), listSummary);
        } else {
            SearchResultRsp cdssSearchResultRsp = searchDispensingFromCdss(clientReq, null);
            if (cdssSearchResultRsp != null) {
                totalCount = (int) cdssSearchResultRsp.getTotal();
                if (!CollectionUtils.isEmpty(cdssSearchResultRsp.getHits())) {
                    List<String> ids = cdssSearchResultRsp.getHits()
                            .stream()
                            .filter(Objects::nonNull)
                            .map(it -> it.get("id") != null ? it.get("id").asText() : null)
                            .filter(id -> !TextUtils.isEmpty(id))
                            .collect(Collectors.toList());
                    if (ids.size() > 0) {
                        results = dispensingMapper.findDispensingSheetAbstractListByIds(ids, clientReq.getClinicId());
                        Map<String, Integer> idIndexMap = IntStream.range(0, ids.size()).boxed().collect(Collectors.toMap(ids::get, Function.identity(), (a, b) -> a));
                        results.sort((a, b) -> ObjectUtils.compare(idIndexMap.getOrDefault(a.getId(), 0), idIndexMap.getOrDefault(b.getId(), 0)));
                    }
                }
            }
            dispensingSheetTypeCount = new DispensingSheetTypeCount();
            dispensingSheetTypeCount.setDeliveryCount(0);
            dispensingSheetTypeCount.setMedicineCount(0);
            dispensingSheetTypeCount.setProcessCount(0);
        }
        bindPatientOrderInfo(clientReq.getChainId(), results);
        bindDispensingCallingItem(clientReq.getChainId(), clientReq.getClinicId(), results);

        StatusNameTranslator.translate(results);
        rspData.setResult(results);
        rspData.setTotalCount(totalCount);
        rspData.setTabInfo(listSummary);
        rspData.setProcessCount(dispensingSheetTypeCount.getProcessCount());
        rspData.setDeliveryCount(dispensingSheetTypeCount.getDeliveryCount());
        rspData.setMedicineCount(dispensingSheetTypeCount.getMedicineCount());
        return rspData;
    }

    private void bindDispensingCallingItem(String chainId, String clinicId, List<DispensingSheetAbstract> dispensingSheets) {
        if (CollectionUtils.isEmpty(dispensingSheets)) {
            return;
        }

        List<String> dispensingSheetIds = ListUtils.extractUniqueProperty(dispensingSheets, DispensingSheetAbstract::getId);
        List<DispensingCallingItem> dispensingCallingItems = dispensingCallingItemService.listCallableDispensingCallingItemByDispensingSheetId(chainId, clinicId, dispensingSheetIds);
        if (CollectionUtils.isEmpty(dispensingCallingItems)) {
            return;
        }

        Map<String, DispensingCallingItem> dispensingSheetIdToCallingItem = ListUtils.toMap(dispensingCallingItems, DispensingCallingItem::getDispensingSheetId);
        dispensingSheets.forEach(dispensingSheet -> {
            DispensingCallingItem callingItem = dispensingSheetIdToCallingItem.getOrDefault(dispensingSheet.getId(), null);
            if (callingItem == null) {
                return;
            }

            dispensingSheet.setDispensingCallingItemId(callingItem.getId());
        });

    }

    private boolean within3Days(Instant beginInstant, Instant endInstant) {
        if (beginInstant == null || endInstant == null) {
            return false;
        }
        return cn.abcyun.cis.commons.util.DateUtils.toLocalDateTime(beginInstant).getDayOfMonth() == LocalDateTime.now().minusDays(2).getDayOfMonth()
                && cn.abcyun.cis.commons.util.DateUtils.toLocalDateTime(endInstant).getDayOfMonth() == LocalDateTime.now().getDayOfMonth();
    }

    private int getTotalCount(Integer tab, DispensingSheetListSummary listSummary) {
        if (Objects.isNull(tab)) {
            return listSummary.getNormalTotalCount() + listSummary.getOnlineTotalCount();
        } else if (tab.equals(TabEntity.NORMAL_DISPENSING_SHEET)) {
            return listSummary.getNormalTotalCount();
        } else if (tab.equals(TabEntity.ONLINE_DISPENSING_SHEET)) {
            return listSummary.getOnlineTotalCount();
        } else if (tab.equals(TabEntity.PROCESSED_DISPENSING_SHEET)) {
            return listSummary.getProcessCount();
        } else if (tab.equals(TabEntity.DELIVERED_DISPENSING_SHEET)) {
            return listSummary.getDeliveryCount();
        }
        return listSummary.getNormalTotalCount() + listSummary.getOnlineTotalCount();
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public List<DispensingSheetAbstract> findDispensingSheetAbstractTodayList(DispensingSheetAbstractListReq clientReq) {
        ZonedDateTime now = ZonedDateTime.now();
        LocalDateTime startOfDay = now.toLocalDate().atStartOfDay();
        Instant startOfDayInstant = startOfDay.atOffset(now.getOffset()).toInstant();
        /**
         *过滤发药单的tag
         * */
        clientReq.setCreatedBegin(startOfDayInstant);
        if (clientReq.getPharmacyNo() != null) {
            clientReq.setPharmacyNos(Lists.newArrayList(clientReq.getPharmacyNo()));
        }


        List<DispensingSheetAbstract> dispensingSheetAbstracts = dispensingMapper.findDispensingSheetAbstractList(clientReq);
        bindPatientOrderInfo(clientReq.getChainId(), dispensingSheetAbstracts);
        return dispensingSheetAbstracts;
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public DispensingSheetListSummary findDispensingSheetListSummary(DispensingSheetAbstractListReq clientReq) {
        /**
         *过滤发药单的tag
         * */
        DispensingSheetListSummary listSummary;
        if (within3Days(clientReq.getCreatedBegin(), clientReq.getCreatedEnd())) {
            DispensingTodoCountDto todoCountDto = dispensingTodoService.getDispensingTodoCount(clientReq.getChainId(), clientReq.getClinicId(), clientReq.getHeaderEmployeeId(), null, clientReq.getPharmacyNo(), null, null);
            listSummary = new DispensingSheetListSummary();
            listSummary.setNormalTotalCount((int) todoCountDto.getNormalTotalCount());
            listSummary.setNormalWaitingCount((int) todoCountDto.getNormalWaitingCount());
            listSummary.setOnlineTotalCount((int) todoCountDto.getOnlineTotalCount());
            listSummary.setOnlineWaitingCount((int) todoCountDto.getOnlineWaitingCount());
            listSummary.setDeliveryCount((int) todoCountDto.getDeliveryTotalCount());
            listSummary.setProcessCount((int) todoCountDto.getProcessTotalCount());
        } else {
            listSummary = dispensingMapper.findDispensingSheetListSummary(clientReq);
        }

        return listSummary;
    }

    private void bindPatientOrderInfo(String chainId, List<DispensingSheetAbstract> dispensingSheetAbstracts) {
        if (CollectionUtils.isEmpty(dispensingSheetAbstracts)) {
            return;
        }

        List<String> patientOrderIds = dispensingSheetAbstracts.stream().map(DispensingSheetAbstract::getPatientOrderId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(patientOrderIds)) {
            return;
        }

        List<PatientOrder> patientOrders = patientOrderService.findPatientOrderListByIds(chainId, patientOrderIds);
        Map<String, PatientOrder> patientOrderIdMap = ListUtils.toMap(patientOrders, PatientOrder::getId);
        for (DispensingSheetAbstract dispensingSheetAbstract : dispensingSheetAbstracts) {
            PatientOrder patientOrder = patientOrderIdMap.getOrDefault(dispensingSheetAbstract.getPatientOrderId(), null);
            if (patientOrder != null) {
                CisPatientInfo patientInfo = new CisPatientInfo();
                patientInfo.setName(patientOrder.getPatientName());
                patientInfo.setId(patientOrder.getPatientId());
                patientInfo.setIsMember(patientOrder.getIsMember());
                patientInfo.setSex(patientOrder.getPatientSex());
                dispensingSheetAbstract.setPatientOrderNo(patientOrder.getNo());
                dispensingSheetAbstract.setPatient(patientInfo);
                dispensingSheetAbstract.setIsAirPharmacyOrder(StringUtils.isEmpty(patientOrder.getAirPharmacyOrderId()) ? YesOrNo.NO : YesOrNo.YES);
                dispensingSheetAbstract.setSourceFromOpenApi(patientOrder.getSourceClientType() == PatientOrder.SourceClientType.OPEN_API ? YesOrNo.YES : YesOrNo.NO);
            }
        }
    }

    private SearchResultRsp searchDispensingFromCdss(DispensingSheetAbstractListReq clientReq, Integer dispensingSheetFilterTag) throws ServiceInternalException {
        try {
            RpcSearchDispenseReq rpcSearchDispenseReq = new RpcSearchDispenseReq();
            rpcSearchDispenseReq.setChainId(clientReq.getChainId());
            rpcSearchDispenseReq.setClinicId(clientReq.getClinicId());
            rpcSearchDispenseReq.setKeyword(clientReq.getKeyword());
//            rpcSearchDispenseReq.initDispensingTag(dispensingSheetFilterTag);
            rpcSearchDispenseReq.setPharmacyType(clientReq.getPharmacyType());
            rpcSearchDispenseReq.setDispenseSheetType(DispenseConst.Type.TYPE_OUTPATIENT);
            rpcSearchDispenseReq.setPharmacyNos(clientReq.getPharmacyNos());
            rpcSearchDispenseReq.setScoreCreatedGauss(1);
            rpcSearchDispenseReq.setOffset(clientReq.getOffset());
            rpcSearchDispenseReq.setLimit(clientReq.getLimit() > 100 ? 100 : clientReq.getLimit());
            long startRequestTime = System.currentTimeMillis();
            AbcServiceResponseBody<SearchResultRsp> rspBody = searchFeignClient.doCommonSearch(rpcSearchDispenseReq);
            if (rspBody != null && rspBody.getData() != null) {
                return rspBody.getData();
            }
            sLogger.info("rpc cost time:{}ms,rsp={}", (System.currentTimeMillis() - startRequestTime), JsonUtils.dump(rspBody));
        } catch (FeignRuntimeException e) {
            sLogger.error("searchDispensing feign error", e);
            throw e;
        } catch (Exception e) {
            sLogger.error("searchDispensing error", e);
            throw new ServiceInternalException("searchDispensing error");
        }

        return null;
    }

    public void updateDispensingForRenewChargeSheet(String chargeSheetId, String operatorId) {
        boolean existsNotUnDispensedSheets = dispensingSheetRepository.existsBySourceSheetIdAndStatusNotAndPharmacyTypeAndIsDeleted(chargeSheetId, DispensingSheet.Status.UNDISPENSED, GoodsConst.PharmacyType.VIRTUAL_PHARMACY, 0);

        if (existsNotUnDispensedSheets) {
            sLogger.info("存在未自动退费完成的虚拟药房发药单, chargeSheetId: {}", chargeSheetId);
            throw new VirtualPharmacyDispenseSheetNotAutoUnDispenseException();
        }

        dispensingSheetRepository.markAsDeletedBySourceSheetId(chargeSheetId, operatorId, Instant.now());
    }

    /**
     * 是否医保支付
     */
    public boolean isPayForShebao(ChargeSheet chargeSheet) {
        if (chargeSheet == null || CollectionUtils.isEmpty(chargeSheet.getChargeTransactions())) {
            return false;
        }
        boolean payForShebao = false;
        for (ChargeTransaction chargeTransaction : chargeSheet.getChargeTransactions()) {
            //有一个社保支付就算
            if (chargeTransaction.getIsPaidback() == 0 && chargeTransaction.getPayMode() == ChargeConstants.ChargePayMode.HEALTH_CARD) {
                payForShebao = true;
                return payForShebao;
            }
        }
        return payForShebao;
    }

    /**
     * 生成或修改门诊的单  ，更新发药单
     *
     * @param chargeSheet 收费单
     * @param operatorId  收费人
     * @param message
     * @return 生成或修改的发药单
     * @note patientOrder  一次就诊
     * note： 1.一次就诊 ： n个门诊单 ： n个收费单。
     * 2.一个收费单可以收费多次(只处理最后一次收完非的消息)
     * TODO  事务在整个service上
     */
    public List<DispensingSheet> insertOrUpdateDispensingSheet(ChargeSheet chargeSheet, String operatorId, int createFromType, boolean chargeAndDirectDispense, ChargeSheetMessage message) throws ServiceInternalException {
        /**
         * 完成收费的才处理
         * */
        if (!(chargeSheet.getStatus() == Constants.ChargeSheetStatus.CHARGED ||
                chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_REFUNDED ||
                chargeSheet.getStatus() == Constants.ChargeSheetStatus.REFUNDED)) {
            sLogger.info("insertOrUpdateDispensingSheet chargeSheet status error, Id:{}, status:{}", chargeSheet.getId(), chargeSheet.getStatus());
            return null;
        }

        if (chargeSheet.getChargeForms() == null) {
            chargeSheet.setChargeForms(new ArrayList<>());
        }

        boolean isPayForShebao = isPayForShebao(chargeSheet);

        // 查询门店的药房集合
        Map<Integer, GoodsPharmacyView> pharmacyNoToPharmacyView = scGoodsFeignClient.findPharmacyByClinic(chargeSheet.getChainId(), chargeSheet.getClinicId())
                .stream()
                .collect(Collectors.toMap(GoodsPharmacyView::getNo, Function.identity(), (a, b) -> a));

        List<DispensingSheet> existedDispensingSheetList = dispensingSheetEntityService.findAllByChainIdSourceSheetId(chargeSheet.getChainId(), chargeSheet.getId());
        //打上是否社保支付过 ，更新之前的单子
        for (DispensingSheet dispensingSheet : existedDispensingSheetList) {
            if (isPayForShebao) {
                if (dispensingSheet.hasDispensingTag(DispensingUtils.DispensingTag.DISPENSING_TAG_WESTERN
                        | DispensingUtils.DispensingTag.DISPENSING_TAG_INFUSION
                        | DispensingUtils.DispensingTag.DISPENSING_TAG_MATERIAL)) {
                    dispensingSheet.addDispensingTag(DispensingUtils.DispensingTag.DISPENSING_TAG_SHEBAO_PAY_FLAG);
                } else {
                    dispensingSheet.deleteDispensingTag(DispensingUtils.DispensingTag.DISPENSING_TAG_SHEBAO_PAY_FLAG);
                }
            } else {
                dispensingSheet.deleteDispensingTag(DispensingUtils.DispensingTag.DISPENSING_TAG_SHEBAO_PAY_FLAG);
            }
        }
        List<String> dispensingSheetIds = ListUtils.extractProperty(existedDispensingSheetList, DispensingSheet::getId);
        List<DispensingSheetOperation> dispensingSheetOperations = dispensingSheetOperationService.findByDispensingSheetIds(dispensingSheetIds);
        Map<String, List<DispensingSheetOperation>> dispensingSheetIdToOperations = ListUtils.groupByKey(dispensingSheetOperations, DispensingSheetOperation::getDispensingSheetId);

        // 按药房类型-药房号分组 chargeForms
        Map<Integer, Map<Integer, List<ChargeForm>>> pharmacyTypeToChargeFroms = ChargeSheetSplitter.split(chargeSheet, existedDispensingSheetList);

        Map<Integer, Map<Integer, List<ChargeForm>>> pharmacyTypeNoChargeFromsMap = new HashMap<>();
        /*
           加工费是一个单独的处方，并且药房号固定为 0，可能会导致在退加工费的时候出处方找不到对应的加工费form不知道已经发生了退费，
           所以这里需要将加工的form单独拎出来，往下传！！！
        */
        Map<String, ChargeForm> chargeFormIdToProcessChargeForm = new HashMap<>();
        pharmacyTypeToChargeFroms.forEach((pharmacyType, pharmacyNoMap) -> {
            if (pharmacyType == GoodsConst.PharmacyType.VIRTUAL_PHARMACY) {
                pharmacyTypeNoChargeFromsMap.put(pharmacyType, pharmacyNoMap);
                return;
            }
            Map<Integer, List<ChargeForm>> pharmacyNoToChargeFormList = new HashMap<>();
            pharmacyNoMap.forEach((pharmacyNo, chargeForms) -> {
                List<ChargeForm> chargeFormsList = new ArrayList<>();
                chargeForms.stream().filter(item -> !CollectionUtils.isEmpty(item.getChargeFormItems())).forEach(chargeForm -> {
                    if (chargeForm.getSourceFormType() == Constants.SourceFormType.PROCESS) {
                        if (chargeForm.getProcessInfo() != null
                                && StringUtils.isNotBlank(chargeForm.getProcessInfo().getChargeFormId())) {
                            chargeFormIdToProcessChargeForm.put(chargeForm.getProcessInfo().getChargeFormId(), chargeForm);
                        }
                        return;
                    }

                    List<ChargeFormItem> chargeFormItemList = new ArrayList<>();
                    chargeForm.getChargeFormItems().forEach(formItem -> {
                        GoodsPharmacyView goodsPharmacyView = pharmacyNoToPharmacyView.get(formItem.getPharmacyNo());
                        if (goodsPharmacyView == null || goodsPharmacyView.getStatus() == 0 || goodsPharmacyView.getEnableDispense() == 0) {
                            sLogger.info("药品所在药房未开启发药功能,goodsPharmacyView:{}, formItem:{}", goodsPharmacyView, formItem);
                            return;
                        }
                        chargeFormItemList.add(formItem);
                    });
                    // chargeForm上的item是否可以生成发药单
                    if (CollectionUtils.isEmpty(chargeFormItemList)) {
                        return;
                    }
                    chargeForm.setChargeFormItems(chargeFormItemList);
                    chargeFormsList.add(chargeForm);
                });
                if (CollectionUtils.isEmpty(chargeFormsList)) {
                    return;
                }
                pharmacyNoToChargeFormList.put(pharmacyNo, chargeFormsList);
            });
            if (!CollectionUtils.isEmpty(pharmacyNoToChargeFormList)) {
                pharmacyTypeNoChargeFromsMap.put(pharmacyType, pharmacyNoToChargeFormList);
            }
        });

        if (!chargeAndDirectDispense) {
            boolean allClosed = existedDispensingSheetList.stream().allMatch(sheet -> sheet.getStatus() == DispenseConst.Status.UNDISPENSED || sheet.getStatus() == DispenseConst.Status.CLOSED);
            if (allClosed && chargeSheet.getStatus() == Constants.ChargeSheetStatus.CHARGED) {
                sLogger.info("insertOrUpdateDispensingSheet clear existedDispensingSheetList, chargeSheetId={}", chargeSheet.getId());
                existedDispensingSheetList.clear();
            }
        }
        sLogger.info("insertOrUpdateDispensingSheet chargeSheetId={},FromType={},拆单结果:{}", chargeSheet.getId(), createFromType, JsonUtils.dump(pharmacyTypeNoChargeFromsMap));

        // 一个药房+药房编号 一个发药单
        Map<Integer, Map<Integer, List<DispensingSheet>>> pharmacyTypeNoDispensingSheetsMap = existedDispensingSheetList.stream()
                .collect(Collectors.groupingBy(DispensingSheet::getPharmacyType, Collectors.groupingBy(DispensingSheet::getPharmacyNo)));


        List<DispensingSheet> dispensingSheets = new ArrayList<>();
        AtomicBoolean hasItemRefund = new AtomicBoolean(false);
        pharmacyTypeNoChargeFromsMap.values()
                .stream()
                .flatMap(pharmacyNoChargeFormsMap -> pharmacyNoChargeFormsMap.values().stream())
                .forEach(chargeForms -> {
                    if (CollectionUtils.isEmpty(chargeForms)) {
                        return;
                    }
                    int pharmacyType = chargeForms.get(0).getPharmacyType();
                    int pharmacyNo = chargeForms.get(0).getPharmacyNo();
                    List<DispensingSheet> existedDispensingSheets = pharmacyTypeNoDispensingSheetsMap.getOrDefault(pharmacyType, new HashMap<>(1)).get(pharmacyNo);

                    if (pharmacyType == GoodsConst.PharmacyType.LOCAL_PHARMACY) {
                        /**
                         * 本地药房 生成一个发药单
                         * */
                        DispensingSheet existedDispensingSheet = null;
                        if (!CollectionUtils.isEmpty(existedDispensingSheets)) {
                            existedDispensingSheet = existedDispensingSheets.get(0);
                        }
                        List<DispensingSheetOperation> operations = null;
                        if (existedDispensingSheet != null) {
                            operations = dispensingSheetIdToOperations.get(existedDispensingSheet.getId());
                        }
                        InsertOrUpdateDispensingSheetContext context = new InsertOrUpdateDispensingSheetContext();
                        DispensingSheet dispensingSheet = insertOrUpdateDispensingSheetForChargeSheet(chargeSheet, chargeForms, existedDispensingSheet, operations, operatorId, createFromType, pharmacyNoToPharmacyView, message, chargeFormIdToProcessChargeForm, context);
                        if (context.getItemHasRefund() == 1) {
                            hasItemRefund.set(true);
                        }
                        if (!CollectionUtils.isEmpty(dispensingSheet.getDispensingForms())) {// 如果是挂号费，生成的发药单中 forms 是为空的，不需要处理
                            dispensingSheets.add(dispensingSheet);
                        }
                    } else if (pharmacyType == GoodsConst.PharmacyType.VIRTUAL_PHARMACY) {
                        /**
                         * 虚拟药房  按form进行拆
                         * 虚拟药房需求:https://www.xiaopiu.com/web/byId?type=project&id=61b551e4cb6a891727247f08 发药流程和 本地药房发药流程一样了
                         * */
                        Map<String, DispensingSheet> chargeFormIdToDispensingSheet = new HashMap<>();
                        Map<String, List<ChargeForm>> dispensingSheetIdToChargeFormList = new HashMap<>();
                        if (!CollectionUtils.isEmpty(existedDispensingSheets)) {
                            /**
                             * 一个收费单存在多个发药单
                             * */
                            for (DispensingSheet existedDispensingSheet : existedDispensingSheets) {
                                if (CollectionUtils.isEmpty(existedDispensingSheet.getDispensingForms())) {
                                    continue;// 理论上不可能存在空的发药单 保护下
                                }

                                /**
                                 * 代煎代配之前是按照药房号拆的，可能存在一个药房号 下多个代煎代配 form的情况
                                 *虚拟药房需求:https://www.xiaopiu.com/web/byId?type=project&id=61b551e4cb6a891727247f08 改成了一个form拆一个发药单
                                 * 实现上为了保留1:n的实现，对老数据进行了兼容
                                 * */
                                for (DispensingForm dispensingForm : existedDispensingSheet.getDispensingForms()) {
                                    //记录chargeFormId和发药单的关系
                                    chargeFormIdToDispensingSheet.put(dispensingForm.getSourceFormId(), existedDispensingSheet);
                                    //记录发药单Id 和chargeForm的关系
                                    ChargeForm chargeForm = chargeForms.stream().filter(c -> c.getId().compareTo(dispensingForm.getSourceFormId()) == 0).findFirst().orElse(null);
                                    if (chargeForm != null) {
                                        List<ChargeForm> chargeFormList = dispensingSheetIdToChargeFormList.get(existedDispensingSheet.getId());
                                        if (CollectionUtils.isEmpty(chargeFormList)) {
                                            chargeFormList = new ArrayList<>();
                                            dispensingSheetIdToChargeFormList.put(existedDispensingSheet.getId(), chargeFormList);
                                        }
                                        chargeFormList.add(chargeForm);
                                    }
                                }
                            }
                        }

                        /**
                         * 新增的处理 1chargeFrom to 1 dispensingForm to 1 dispensingSheet
                         * */
                        for (ChargeForm chargeForm : chargeForms) {
                            if (!chargeFormIdToDispensingSheet.containsKey(chargeForm.getId())) {
                                InsertOrUpdateDispensingSheetContext context = new InsertOrUpdateDispensingSheetContext();
                                dispensingSheets.add(insertOrUpdateDispensingSheetForChargeSheet(chargeSheet, Collections.singletonList(chargeForm), null, null, operatorId, createFromType, pharmacyNoToPharmacyView, message, chargeFormIdToProcessChargeForm, context));
                                if (context.getItemHasRefund() == 1) {
                                    hasItemRefund.set(true);
                                }
                            }
                        }

                        /**
                         * Update的处理 old data: n chargeFrom to n dispensingForm to 1 dispensingSheet
                         * */
                        for (List<ChargeForm> chargeFormList : dispensingSheetIdToChargeFormList.values()) {
                            if (CollectionUtils.isEmpty(chargeFormList)) {
                                continue;
                            }
                            DispensingSheet dispensingSheet = chargeFormIdToDispensingSheet.get(chargeFormList.get(0).getId());
                            List<DispensingSheetOperation> operations = null;
                            if (dispensingSheet != null) {
                                operations = dispensingSheetIdToOperations.get(dispensingSheet.getId());
                            }
                            InsertOrUpdateDispensingSheetContext context = new InsertOrUpdateDispensingSheetContext();
                            dispensingSheets.add(insertOrUpdateDispensingSheetForChargeSheet(chargeSheet, chargeFormList, dispensingSheet, operations, operatorId, createFromType, pharmacyNoToPharmacyView, message, chargeFormIdToProcessChargeForm, context));
                            if (context.getItemHasRefund() == 1) {
                                hasItemRefund.set(true);
                            }
                        }
                    }
                });
        //解锁下库存
        try {
            //通知scGoods更新关系
            rocketMqProducer.sendRegisterSheetLockIdMessage(dispensingSheets);
            if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.CHARGED) {
                // 更新锁库信息
                goodsLockingFeignClient.tryLockByDispensingSheets(
                        chargeSheet.getChainId(),
                        chargeSheet.getClinicId(),
                        operatorId,
                        YesOrNo.NO,
                        dispensingSheets
                );
            }
        } catch (Exception e) {
            sLogger.error("更新收费单锁信息失败", e);
        }
        try {
            // 这里和锁库的逻辑一致，收费那次追溯码由 charge 保证正确
            if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.CHARGED || hasItemRefund.get()) {
                // 更新追溯码信息
                abcCisScGoodsService.updateUseTraceCode(chargeSheet.getChainId(), chargeSheet.getClinicId(), dispensingSheets, operatorId);
            }
        } catch (Exception e) {
            sLogger.error("更新追溯码失败", e);
        }
        return dispensingSheets;
    }

    /**
     * 生成发药单
     *
     * @param chargeForms                     收费的chargeFrom
     * @param dispensingSheet                 如果非空为要更新的发药单
     * @param dispensingSheetOperations       发药单操作记录
     * @param operatorId                      收费员
     * @param createFromType                  从消息过来还是收费直接发药过来
     * @param pharmacyNoToPharmacyView        药房类型编号发药flagMap
     * @param message                         收费单消息
     * @param chargeFormIdToProcessChargeForm key->收费formId，value->加工费form
     * @param context                         新增或修改发药单上下文
     * @return DispensingSheet 生成或修改的发药单
     */
    private DispensingSheet insertOrUpdateDispensingSheetForChargeSheet(ChargeSheet chargeSheet,
                                                                        List<ChargeForm> chargeForms,
                                                                        DispensingSheet dispensingSheet,
                                                                        List<DispensingSheetOperation> dispensingSheetOperations,
                                                                        String operatorId,
                                                                        int createFromType,
                                                                        Map<Integer, GoodsPharmacyView> pharmacyNoToPharmacyView,
                                                                        ChargeSheetMessage message,
                                                                        Map<String, ChargeForm> chargeFormIdToProcessChargeForm,
                                                                        InsertOrUpdateDispensingSheetContext context) {
        boolean isPayForShebao = isPayForShebao(chargeSheet);
        int pharmacyType = chargeForms.get(0).getPharmacyType();

        int pharmacyNo = chargeForms.get(0).getPharmacyNo();
        GoodsPharmacyView goodsPharmacyView = pharmacyNoToPharmacyView.get(pharmacyNo);
        boolean isUpdate = dispensingSheet != null;
        DispensingTodoService.DispensingSheetStatus beforeDispensingSheetStatus = DispensingTodoService.DispensingSheetStatus.of(dispensingSheet);
        if (dispensingSheet == null) {
            dispensingSheet = buildDispensingSheet(chargeSheet, pharmacyType, pharmacyNo, goodsPharmacyView, chargeForms, operatorId);
        }

        //记录下来，避免后面发药机 吐消息的时候 再拉charge
        dispensingSheet.setChargeSheet(chargeSheet);

        //Create and update fileds
        dispensingSheet.setIsPatientSelfPay(chargeSheet.getIsPatientSelfPay());
        if (pharmacyType == GoodsConst.PharmacyType.LOCAL_PHARMACY) {
            dispensingSheet.setDeliveryType(chargeSheet.getDeliveryType());
        } else if (pharmacyType == GoodsConst.PharmacyType.VIRTUAL_PHARMACY) {
            if (!CollectionUtils.isEmpty(chargeForms)) {
                dispensingSheet.setDeliveryType(DispensingSheet.DeliveryType.DELIVERY_TO_HOME);
            }
        }
        dispensingSheet.setIsDecoction(chargeSheet.getIsDecoction());
        /**
         * 多药房发药标识
         * */
        if (goodsPharmacyView != null)
            dispensingSheet.setPharmacyDispenseFlag(goodsPharmacyView.getDispenseFlag());
        else {
            dispensingSheet.setPharmacyDispenseFlag(GoodsConst.DispenseFlag.DISPENSE_BY_USER);
        }
        dispensingSheet.setSourceSheetType(chargeSheet.getType());
        if (dispensingSheet.getDispensingForms() == null) {
            dispensingSheet.setDispensingForms(new ArrayList<>());
        }
        int isPrescriptionReview = DispensingUtils.SwitchFlag.OFF; // 是否开启处方审核
        int isPrescriptionCompound = DispensingUtils.SwitchFlag.OFF;  //是否开启处方调配
        /**
         * 新建发药单时 检查 是否开启处方审核,0：不开启，1：开启
         * */
        if (!isUpdate) {
            isPrescriptionReview = dispensingSheet.getAuditedStatus() == DispensingSheet.AuditedStatus.WAITING ?
                    DispensingUtils.SwitchFlag.ON : DispensingUtils.SwitchFlag.OFF;
            isPrescriptionCompound = dispensingSheet.getCompoundedStatus() == DispensingSheet.CompoundedStatus.WAITING ?
                    DispensingUtils.SwitchFlag.ON : DispensingUtils.SwitchFlag.OFF;
        }

        /**
         * 新增或修改发药 form
         * */
        insertOrUpdateDispensingSheetForChargeForms(dispensingSheet, chargeForms, isPrescriptionReview, isPrescriptionCompound, operatorId, pharmacyNoToPharmacyView, context, chargeFormIdToProcessChargeForm);
        // 处理发药单的扩展信息
        this.fillDispensingFormAdditionalInfo(dispensingSheet.getDispensingForms(), chargeSheet.getChargeForms(), operatorId);

        /**
         *根据form初始化加工状态
         * */
        dispensingSheet.initProcessedStatus();
        /*
           根据 form 的审核、调配状态重置发药单的审核、调配状态，
           因为 sheet 上的初始状态只看了门店开关的配置，但是 form 还会同时关注处方的类型，是否需要审核、调配，
           所以这里在 form 都生成完成后需要根据 form 的状态再来重置发药单的状态
         */
        dispensingSheet.auditSheetSucceedAfterAllFormAudited();
        dispensingSheet.compoundedSheetSucceedAfterAllFormCompounded();

        /**
         * 给发药单打上标签 方便后续刷选  发药机使用
         * */
        dispensingSheet.refreshProductInfo(scGoodsFeignClient);
        dispensingSheet.tagDispensingSheetBySourceFormType();
        if (isPayForShebao) {
            if (dispensingSheet.hasDispensingTag(DispensingUtils.DispensingTag.DISPENSING_TAG_WESTERN
                    | DispensingUtils.DispensingTag.DISPENSING_TAG_INFUSION
                    | DispensingUtils.DispensingTag.DISPENSING_TAG_MATERIAL)) {
                dispensingSheet.addDispensingTag(DispensingUtils.DispensingTag.DISPENSING_TAG_SHEBAO_PAY_FLAG);
            } else {
                dispensingSheet.deleteDispensingTag(DispensingUtils.DispensingTag.DISPENSING_TAG_SHEBAO_PAY_FLAG);
            }
        } else {
            dispensingSheet.deleteDispensingTag(DispensingUtils.DispensingTag.DISPENSING_TAG_SHEBAO_PAY_FLAG);
        }
        boolean hasBatchPreLockedDispensingFormItem = dispensingSheet.getAllDispensingFormItems().stream()
                .anyMatch(dispensingFormItem -> dispensingFormItem.getLockId() != null && !CollectionUtils.isEmpty(dispensingFormItem.getDispensingFormItemBatches()));
        if (hasBatchPreLockedDispensingFormItem) {
            // 如果有药品已经预锁定了批次，发药单打上批次预锁定标签
            dispensingSheet.addDispensingTag(DispensingUtils.DispensingTag.DISPENSING_TAG_BATCH_PRE_LOCKED);
        }

        //快递费退费
        updateDeliveredStatusWhenDeliverFeeRefunded(chargeSheet, dispensingSheet, operatorId);

        long validDispensingItemCount = dispensingSheet.getDispensingForms()
                .stream()
                .filter(dispensingForm -> dispensingForm.getDispensingFormItems() != null)
                .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream())
                .filter(Objects::nonNull)
                .filter(dispensingFormItem -> dispensingFormItem.getIsHistoryItem() != 1 && dispensingFormItem.getStatus() != DispensingFormItem.Status.CANCELED)
                .count();
        boolean isAllDispensingFormItemRefunded = dispensingSheet.getDispensingForms()
                .stream()
                .filter(dispensingForm -> dispensingForm.getDispensingFormItems() != null)
                .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream())
                .filter(Objects::nonNull)
                .filter(dispensingFormItem -> dispensingFormItem.getAssociateFormItemId() == null)
                .allMatch(dispensingFormItem -> Objects.equals(dispensingFormItem.getChargeFormItemStatus(), Constants.ChargeFormItemStatus.REFUNDED));
        if (isAllDispensingFormItemRefunded) {
            dispensingSheet.setPayStatus(DispensingSheet.PayStatus.REFUNED);
        }

        // 如果发药单退费了，需要变更发药单的状态
        int closeDispensingSheet = 0;
        if (validDispensingItemCount == 0) {
            boolean canDeleteDispensingSheet = canDeleteDispensingSheet(dispensingSheetOperations);
            sLogger.info("no valid dispensing item sheetId:{}, deleteDispensingSheet:{}", dispensingSheet.getId(), canDeleteDispensingSheet);
            if (canDeleteDispensingSheet) {
                dispensingSheet.setIsDeleted(1);
            }
            if (dispensingSheet.getStatus() == DispensingSheet.Status.WAITING) {
                // 如果所有的药品都已经退费，并且当前状态为待发药，则需要关闭发药单
                dispensingSheet.setStatus(DispensingSheet.Status.CLOSED);
                closeDispensingSheet = 1;
            }

            if (!isUpdate) {
                sLogger.info("do not save new deleted:{}", dispensingSheet.getId());
                return dispensingSheet;
            }

            /*
                只有发药单删除的时候才会走到这个逻辑，因为如果发药单没有被删除的话，
                还会走到后面的另一个逻辑里面去，导致重复扣减了待办数量。
                【【客户反馈】【王帮众】待发单子没有提示；】 https://www.tapd.cn/43780818/bugtrace/bugs/view/1143780818001080670
             */
            if (dispensingSheet.getIsDeleted() == 1) {
                List<String> subKeyList = new ArrayList<>();
                boolean sendTodoMessage = dispensingTodoService.getWaitingTodoSubKey(dispensingSheet.getCreated(),
                        dispensingSheet.getIsOnline(), dispensingSheet.getDeliveredStatus(),
                        dispensingSheet.getProcessedStatus(), subKeyList, DispensingTodoService.Action.REFUND,
                        beforeDispensingSheetStatus, DispensingTodoService.DispensingSheetStatus.of(dispensingSheet));
                DispensingSheet finalDispensingSheet = dispensingSheet;
                RocketMqProducer.doAfterTransactionCommit(() -> dispensingTodoService.addDecPharmacyTodoCountTask(finalDispensingSheet.getChainId(), finalDispensingSheet.getClinicId(), finalDispensingSheet.getPharmacyNo(), operatorId, subKeyList, sendTodoMessage));
            }
        }
        //退费后,只有当之前发药单状态为waiting时才需修改(之前状态为dispensed和undispensed时,退费后状态不变).
        if (isUpdate && validDispensingItemCount != 0 && dispensingSheet.getStatus() == DispensingSheet.Status.WAITING) {
            int newSheetStatus = getDispensingSheetStatusByItemStatusCount(dispensingSheet);
            if (newSheetStatus != DispensingSheet.Status.WAITING) {
                dispensingSheet.setStatus(newSheetStatus);
                dispensingSheet.setDispensedTime(Instant.now());
                dispensingSheet.setOrderByDate(Instant.now());
                dispensingSheet.setDispensedBy(operatorId);
            }
        }

        // 如果没有可退药项，则删除标识
        long undispensableItemCount = DispensingUtils.getUndispensableItemCount(dispensingSheet);
        if (undispensableItemCount == 0) {
            dispensingSheet.deleteDispensingTag(DispensingUtils.DispensingTag.DISPENSING_TAG_HAS_UNDISPENSABLE);
        }

        dispensingSheetEntityService.save(dispensingSheet);

        // 如果发药单所有的药品都已经退费，则修改发药单的支付状态为已退费，并且写入一条退费完成的操作记录
        if (isAllDispensingFormItemRefunded) {
            SheetOperationBase sheetOperation = sheetOperationCreateFactory.createSheetOperation(DispensingSheetOperation.OperationType.REFUND_FEE, dispensingSheet.getId(), operatorId, operatorId, dispensingSheet.getChainId(), dispensingSheet.getClinicId());
            sheetOperation.createAndSaveSheetOperation();
        }

        /**
         * 这些消息都是在事务完成后提交的
         * TODO 后面有涉及到的需求把所有需要事务提交后才处理的任务都通过实现 DispensingSheetListener 接口来改造
         * */
        DispensingSheet finalDispensingSheet = dispensingSheet;
        if (dispensingSheet.getIsDeleted() == DispensingUtils.DeleteFlag.NOT_DELETED) {
            if (isUpdate) {
                if (createFromType == DispensingUtils.CreateDispenseSheetFromType.FROM_CHARGE_MESSAGE) {
                    hamqProducer.sendDispensingSheetUpdate(PatientOrderMessage.MSG_SUB_TYPE_DISPENGING_UPDATED_INFO, dispensingSheet, operatorId);
                }
                // 通知数量
                List<String> subKeyList = new ArrayList<>();
                boolean sendTodoMessage = dispensingTodoService.getWaitingTodoSubKey(dispensingSheet.getCreated(), dispensingSheet.getIsOnline(),
                        dispensingSheet.getDeliveredStatus(), dispensingSheet.getProcessedStatus(), subKeyList,
                        DispensingTodoService.Action.REFUND, beforeDispensingSheetStatus, DispensingTodoService.DispensingSheetStatus.of(dispensingSheet));
                RocketMqProducer.doAfterTransactionCommit(() -> dispensingTodoService.addDecPharmacyTodoCountTask(finalDispensingSheet.getChainId(), finalDispensingSheet.getClinicId(), finalDispensingSheet.getPharmacyNo(), operatorId, subKeyList, sendTodoMessage));

                if (closeDispensingSheet == 1) {
                    RocketMqProducer.doAfterTransactionCommit(() -> {
                        dispensingSheetListeners.forEach(listener -> listener.onDispensingSheetClosedAfterCommit(finalDispensingSheet.getChainId(), finalDispensingSheet.getClinicId(), finalDispensingSheet, operatorId));
                    });
                }
            } else {
                /**
                 * 区分收费同时发药还是消息新建发药单
                 * **/
                hamqProducer.sendDispensingSheetCreated(dispensingSheet, operatorId, chargeSheet);
                RocketMqProducer.doAfterTransactionCommit(() -> {
                    dispensingSheetListeners.forEach(listener -> listener.onDispensingSheetCreatedAfterCommit(finalDispensingSheet.getChainId(), finalDispensingSheet.getClinicId(), finalDispensingSheet, operatorId));
                });

                /**
                 * 自动打印，整个发药单全打
                 * */
                dispensingPrintedService.sendAutoPrintMessage(dispensingSheet, null, WebMessageBody.WebMessageEvent.DISPENSING_AUTO_PRINT);
                /**
                 * 新建发药单试着去智能发药
                 * */
                smartDispensing(dispensingSheet, () -> chargeSheet, DispensingUtils.getDispensingFormItems(dispensingSheet), operatorId, DispensingUtils.SmartDispensingScene.CHARGE);
                if (createFromType == DispensingUtils.CreateDispenseSheetFromType.FROM_CHARGE_MESSAGE) {
                    rocketMqProducer.broadcastDispensingSheetStatusChange(dispensingSheet, false, operatorId);
                }
                // 更新待发数量
                List<String> subKeyList = new ArrayList<>();
                boolean sendTodoMessage = dispensingTodoService.getWaitingTodoSubKey(dispensingSheet.getCreated(), dispensingSheet.getIsOnline(),
                        dispensingSheet.getDeliveredStatus(), dispensingSheet.getProcessedStatus(), subKeyList,
                        DispensingTodoService.Action.PAY, null, DispensingTodoService.DispensingSheetStatus.of(dispensingSheet));
                RocketMqProducer.doAfterTransactionCommit(() -> dispensingTodoService.addIncPharmacyTodoCountTask(finalDispensingSheet.getChainId(),
                        finalDispensingSheet.getClinicId(), finalDispensingSheet.getPharmacyNo(), operatorId, subKeyList, sendTodoMessage, ChargeSheetUtils.getLatestChargeTransactionId(chargeSheet)));
            }
        } else {
            hamqProducer.sendDispensingSheetDeleted(dispensingSheet, operatorId);
            RocketMqProducer.doAfterTransactionCommit(() -> {
                dispensingSheetListeners.forEach(listener -> listener.onDispensingSheetDeletedAfterCommit(finalDispensingSheet.getChainId(), finalDispensingSheet.getClinicId(), finalDispensingSheet, operatorId));
            });
        }

        if (context.getItemHasRefund() == 1 && (message.getType() == ChargeSheetMessage.MSG_TYPE_PARTY_REFUND
                || message.getType() == ChargeSheetMessage.MSG_TYPE_REFUNDED)) {
            // 如果是退费消息（并且真实发生了退药），需要同时通知前端打印退药小票
            dispensingPrintedService.sendRefundFeeAutoPrintMessage(dispensingSheet, message);
        }
        return dispensingSheet;
    }

    /**
     * 是否需要删除发药单
     * <p>
     * 如果发药单没有任何主动操作记录，例如发药、审核、调配，才能删除
     *
     * @param dispensingSheetOperations 操作记录
     * @return true:需要删除 false:不需要删除
     */
    private boolean canDeleteDispensingSheet(List<DispensingSheetOperation> dispensingSheetOperations) {
        if (CollectionUtils.isEmpty(dispensingSheetOperations)) {
            return true;
        }

        List<Integer> operationTypes = Arrays.asList(DispensingSheetOperation.OperationType.AUDIT,
                DispensingSheetOperation.OperationType.DELIVERED,
                DispensingSheetOperation.OperationType.COMPOUND);
        return dispensingSheetOperations.stream().noneMatch(operation -> operationTypes.contains(operation.getOperationType()));
    }

    /**
     * 生成发药单的扩展信息
     */
    private void fillDispensingFormAdditionalInfo(List<DispensingForm> dispensingForms, List<ChargeForm> chargeForms, String operatorId) {
        if (CollectionUtils.isEmpty(dispensingForms) || CollectionUtils.isEmpty(chargeForms)) {
            return;
        }
        Map<String, ChargeSheetProcessInfo> chargeFormMap = chargeForms.stream()
                .filter(form -> Objects.nonNull(form) && form.getSourceFormType() == Constants.SourceFormType.PROCESS && Objects.nonNull(form.getProcessInfo()))
                .map(ChargeForm::getProcessInfo)
                .collect(Collectors.toMap(ChargeSheetProcessInfo::getChargeFormId, Function.identity(), (v1, v2) -> v1));
        if (MapUtils.isEmpty(chargeFormMap)) {
            return;
        }
        dispensingForms.stream()
                .filter(form -> Objects.nonNull(form) && form.getSourceFormType() == DispensingForm.SourceFormType.PRESCRIPTION_CHINESE)
                .forEach(form -> this.createDispensingFormAdditional(form, chargeFormMap.get(form.getSourceFormId()), operatorId));
    }

    private DispensingSheet buildDispensingSheet(ChargeSheet chargeSheet, int pharmacyType, int pharmacyNo,
                                                 GoodsPharmacyView goodsPharmacyView, List<ChargeForm> chargeForms, String operatorId) {
        DispensingSheet dispensingSheet = new DispensingSheet();
        dispensingSheet.setId(abcIdGenerator.getUUID());
        dispensingSheet.setPatientOrderId(chargeSheet.getPatientOrderId());
        dispensingSheet.setPatientId(chargeSheet.getPatientId());
        dispensingSheet.setChainId(chargeSheet.getChainId());
        dispensingSheet.setClinicId(chargeSheet.getClinicId());
        dispensingSheet.setPharmacyNo(pharmacyNo);
        dispensingSheet.setPharmacyType(pharmacyType);
        dispensingSheet.setDoctorId(chargeSheet.getDoctorId());
        dispensingSheet.setSourceSheetId(chargeSheet.getId());
        dispensingSheet.setIsOnline(chargeSheet.getIsOnline());
        dispensingSheet.setStatus(DispensingSheet.Status.WAITING);
        dispensingSheet.setPayStatus(DispensingSheet.PayStatus.PAID);
        dispensingSheet.setOrderByDate(Instant.now());
        FillUtils.fillCreatedBy(dispensingSheet, operatorId);
        /***
         *代煎代配药房按form 进行快递状态的拆分了
         * */
        if (pharmacyType == GoodsConst.PharmacyType.LOCAL_PHARMACY) {
            dispensingSheet.setDeliveredStatus(Objects.nonNull(chargeSheet.getDeliveryInfo()) ? DispensingSheet.DeliveredStatus.WAITING : DispensingSheet.DeliveredStatus.NONE);
        } else if (pharmacyType == GoodsConst.PharmacyType.VIRTUAL_PHARMACY) {
            if (!CollectionUtils.isEmpty(chargeForms)) {
                ChargeForm chargeForm = chargeForms.get(0);
                dispensingSheet.setDeliveredStatus(Objects.nonNull(chargeForm.getChargeAirPharmacyLogistics()) ? DispensingSheet.DeliveredStatus.SUCCEED : DispensingSheet.DeliveredStatus.NONE);
            }
        }
        SheetOperationBase operationBase = sheetOperationCreateFactory.createSheetOperation(DispensingSheetOperation.OperationType.CREATED_DISPENSING_SHEET, dispensingSheet.getId(), operatorId, operatorId, dispensingSheet.getChainId(), dispensingSheet.getClinicId());
        operationBase.createAndSaveSheetOperation();

        /**
         * 叶开泰定制需求，可以通过这个开关，强制
         * */
        if (goodsPharmacyView != null
                && goodsPharmacyView.getExternalPharmacyConfig() != null
                && goodsPharmacyView.getExternalPharmacyConfig().getForceOpenAuditAndCompound() != null
                && goodsPharmacyView.getExternalPharmacyConfig().getForceOpenAuditAndCompound() == DispensingUtils.SwitchFlag.ON) {
            // 是否开启处方审核
            dispensingSheet.setAuditedStatus(DispensingSheet.AuditedStatus.WAITING);
            //是否开启处方调配
            dispensingSheet.setCompoundedStatus(DispensingSheet.CompoundedStatus.WAITING);
        } else {
            Dispensing dispensingConfig = propertyService.getPropertyValueByKey(PropertyKey.DISPENSING, dispensingSheet.getClinicId(), Dispensing.class);
            // 是否开启处方审核
            if (Objects.nonNull(dispensingConfig) && dispensingConfig.getPrescriptionReview() == DispensingUtils.SwitchFlag.ON) {
                dispensingSheet.setAuditedStatus(DispensingSheet.AuditedStatus.WAITING);
            }
            //是否开启处方调配
            if (Objects.nonNull(dispensingConfig) && dispensingConfig.getPrescriptionCompound() == DispensingUtils.SwitchFlag.ON) {
                dispensingSheet.setCompoundedStatus(DispensingSheet.CompoundedStatus.WAITING);
            }
        }

        return dispensingSheet;
    }

    /**
     * 通过发药子项的状态的数量 计算 整个发药单的状态
     */
    private int getDispensingSheetStatusByItemStatusCount(DispensingSheet dispensingSheet) {
        // 所有非套餐 和非 Cancle的发药项
        List<DispensingFormItem> validItems = dispensingSheet.getDispensingForms()
                .stream()
                .filter(dispensingForm -> dispensingForm.getDispensingFormItems() != null)
                .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream())
                .filter(dispensingFormItem -> dispensingFormItem.getIsHistoryItem() != 1)
                .filter(dispensingFormItem ->
                        dispensingFormItem.getProductType() != Constants.ProductType.COMPOSE_PRODUCT
                                && dispensingFormItem.getStatus() != DispensingFormItem.Status.CANCELED).collect(Collectors.toList());

        Collection<BigDecimal> mergedItemTotalCounts = validItems.stream()
                .collect(
                        Collectors.toMap(
                                item -> {
                                    if (item.getStatus() != DispensingFormItem.Status.UNDISPENSED) {
                                        return item.getId();
                                    } else {
                                        return item.getAssociateFormItemId();
                                    }
                                },
                                item -> {
                                    BigDecimal itemTotalCount = MathUtils.calculateTotalCount(item.getUnitCount(), item.getDoseCount());
                                    if (item.getStatus() != DispensingFormItem.Status.UNDISPENSED) {
                                        return MathUtils.wrapBigDecimal(itemTotalCount, BigDecimal.ZERO);
                                    } else {
                                        return MathUtils.wrapBigDecimal(itemTotalCount, BigDecimal.ZERO).negate();
                                    }
                                },
                                (a, b) -> a.add(b)
                        ))
                .values();

        long waitingDispensingItemCount = validItems.stream().filter(dispensingFormItem -> DispensingFormItem.Status.dispenseable.contains(dispensingFormItem.getStatus())).count();
        long dispensedAndWaitingDispensingItemCount = mergedItemTotalCounts.stream().filter(value -> value.compareTo(BigDecimal.ZERO) > 0).count();
        long dispensedDispensingItemCount = dispensedAndWaitingDispensingItemCount - waitingDispensingItemCount;
        long undispensedDispensingItemCount = mergedItemTotalCounts.stream().filter(value -> value.compareTo(BigDecimal.ZERO) <= 0).count();

        int sheetStatus = DispensingSheet.Status.WAITING;
        if (waitingDispensingItemCount == 0) {
            /**
             * 所有药的发过了
             * */
            if (dispensedDispensingItemCount != 0) {
                sheetStatus = DispensingSheet.Status.DISPENSED;
            }
            if (dispensedDispensingItemCount == 0 && undispensedDispensingItemCount != 0) {
                sheetStatus = DispensingSheet.Status.UNDISPENSED;
            }
        } else { //有未发的药，整个发药单都是待发
            sheetStatus = DispensingSheet.Status.WAITING;
        }
        return sheetStatus;
    }

    /**
     * 快递费退费后修改dispensingSheet的DeliveredStatus为NONE
     * 1.只处理本地药房发药单的快递颓废
     * 2.本地药房的收费单如果有快递费，会有一个 Constants.SourceFormType.EXPRESS_DELIVERY 的chargeForm 里面有一个 chargeFormItem
     *
     * @param chargeSheet
     * @param dispensingSheet
     */
    private void updateDeliveredStatusWhenDeliverFeeRefunded(ChargeSheet chargeSheet, DispensingSheet dispensingSheet, String operatorId) {
        if (dispensingSheet.getDeliveredStatus() == DispensingSheet.DeliveredStatus.NONE) { //发药单本来就没快递
            return;
        }
        if (dispensingSheet.getPharmacyType() != GoodsConst.PharmacyType.LOCAL_PHARMACY) {
            return; //非本地药房
        }
        ChargeForm deliveryChargeForm = chargeSheet.getChargeForms().stream().filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.EXPRESS_DELIVERY).findFirst().orElse(null);
        if (Objects.isNull(deliveryChargeForm)) {
            return; // 无快递费
        }
        ChargeFormItem deliveryChargeFormItem = deliveryChargeForm.getChargeFormItems().stream().findFirst().orElse(null);
        if (Objects.isNull(deliveryChargeFormItem) || deliveryChargeFormItem.getStatus() != Constants.ChargeFormItemStatus.REFUNDED) {
            return; // 无快递收费条目 或 不为退费状态
        }

        sLogger.info("update Delivered Status When Refunded for dispensingSheetId:{},dispensingSheet.setDeliveredStatus({})", dispensingSheet.getId(), DispensingSheet.DeliveredStatus.NONE);
        dispensingSheet.setDeliveredStatus(DispensingSheet.DeliveredStatus.NONE);
        SheetOperationBase operationBase = sheetOperationCreateFactory.createSheetOperation(DispensingSheetOperation.OperationType.CANCEL_DELIVERY, dispensingSheet.getId(), operatorId, operatorId, dispensingSheet.getChainId(), dispensingSheet.getClinicId());
        operationBase.createAndSaveSheetOperation();
    }

    /**
     * 新增或修改发药form
     *
     * @param dispensingSheet                 新建或修改的发药sheet
     * @param chargeForms                     收费的chargeFrom
     * @param isPrescriptionReview            是否开启处方审核
     * @param isPrescriptionCompound          是否开启处方调配
     * @param pharmacyNoToPharmacyView        药房类型编号发药flagMap
     * @param context                         新增或修改发药单上下文
     * @param chargeFormIdToProcessChargeForm key->收费formId，value->加工费form
     */
    public void insertOrUpdateDispensingSheetForChargeForms(DispensingSheet dispensingSheet,
                                                            List<ChargeForm> chargeForms,
                                                            int isPrescriptionReview,
                                                            int isPrescriptionCompound,
                                                            String operatorId,
                                                            Map<Integer, GoodsPharmacyView> pharmacyNoToPharmacyView,
                                                            InsertOrUpdateDispensingSheetContext context,
                                                            Map<String, ChargeForm> chargeFormIdToProcessChargeForm) {
        /**
         * source:ChargeForm --- chargeForm.getId()
         * target: DispensingForm --- dispensingForm.getSourceFormId()
         * */
        BiFunction<ChargeForm, DispensingForm, Boolean> isEqualKeyFunc = (chargeForm, dispensingForm) -> TextUtils.equals(chargeForm.getId(), dispensingForm.getSourceFormId());

        /**
         * 增:用source构造target
         * */
        Function<ChargeForm, DispensingForm> insertFunc = chargeForm -> {
            ChargeForm processChargeForm = chargeFormIdToProcessChargeForm.get(chargeForm.getId());
            DispensingForm dispensingForm = createDispensingForm(dispensingSheet, chargeForm, processChargeForm, isPrescriptionReview, isPrescriptionCompound, operatorId);
            sLogger.info("create dispensing form for chargeId:{}", chargeForm.getId());
            return dispensingForm;
        };

        /**
         * 删 : 参数是要删除的target
         * */
        Function<DispensingForm, Boolean> deleteFunc = dispensingForm -> {
            if (dispensingForm.getDispensingFormItems() == null) {
                sLogger.info("remove dispensing form id:{}", dispensingForm.getId());
                return true;
            }
            long nonWaitingCount = dispensingForm.getDispensingFormItems()
                    .stream()
                    .filter(dispensingFormItem -> !DispensingFormItem.Status.dispenseable.contains(dispensingFormItem.getStatus()))
                    .count();
            sLogger.info("to remove dispensing form id:{}, nonWaitingCount:{}", dispensingForm.getId(), nonWaitingCount);
            return nonWaitingCount == 0;
        };

        /**
         * 改 ： 改前的Target ，要改的source
         * */
        BiConsumer<ChargeForm, DispensingForm> updateFunc = (chargeForm, dispensingForm) -> {
            sLogger.info("update dispensing sourceFormId:{}, dispensingFormId:{}", chargeForm.getId(), dispensingForm.getId());
            mergeDispensingForm(chargeForm, dispensingForm, operatorId, context);

            if (dispensingForm.getProcessedStatus() != DispensingForm.ProcessedStatus.NONE) {
                ChargeForm processForm = chargeFormIdToProcessChargeForm.get(chargeForm.getId());
                if (processForm != null) {
                    updateFormProcessedStatusWhenProcessFeeRefunded(processForm, chargeForm, dispensingForm, operatorId);
                } else {
                    log.warn("processForm is null for chargeFormId:{}", chargeForm.getId());
                }
            }
            //退费退药 保留审核和调配状态 前端通过 chargeSheet的stats进行管理
//            updateFormAuditedAndCompoundedStatusWhenRefunded(dispensingForm);
        };

        MergeTool<ChargeForm, DispensingForm> mergeTool = new MergeTool<>();
        mergeTool.setSrc(chargeForms)
                .setDst(dispensingSheet.getDispensingForms())
                .setIsEqualKeyFunc(isEqualKeyFunc)
                .setInsertFunc(insertFunc)
                .setDeleteFunc(deleteFunc)
                .setUpdateFunc(updateFunc);
        mergeTool.doMerge();

        // 在dispensingFormItem上 设置 pharmacyDispenseFlag
        Optional.of(dispensingSheet.getDispensingForms())
                .orElse(Lists.newArrayList())
                .stream()
                .filter(dispensingForm -> !CollectionUtils.isEmpty(dispensingForm.getDispensingFormItems()))
                .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream())
                .forEach(dispensingFormItem -> {
                    GoodsPharmacyView pharmacyView = pharmacyNoToPharmacyView.get(dispensingFormItem.getPharmacyNo());
                    if (pharmacyView != null) {
                        dispensingFormItem.markPharmacyDispenseFlag(pharmacyView.getDispenseFlag());
                    } else {
                        dispensingFormItem.markPharmacyDispenseFlag(GoodsConst.DispenseFlag.DISPENSE_BY_USER);
                    }

                });
    }

    /**
     * 加工费退费后,修改处方ProcessedStatus为NONE
     */
    private void updateFormProcessedStatusWhenProcessFeeRefunded(ChargeForm processChargeForm, ChargeForm chargeForm, DispensingForm dispensingForm, String operatorId) {
        if (processChargeForm != null && processChargeForm.getChargeFormItems() != null) {
            ChargeFormItem processChargeFormItem = processChargeForm.getChargeFormItems().stream().findFirst().orElse(null);
            if (processChargeFormItem != null && processChargeFormItem.getStatus() == Constants.ChargeFormItemStatus.REFUNDED) {
                sLogger.info("update Processed Status When Refunded for dispensingSheetId:{} and dispensingFromId:{},dispensingForm.setProcessedStatus:{}", dispensingForm.getDispensingSheetId(), dispensingForm.getId(), DispensingForm.ProcessedStatus.NONE);
                dispensingForm.setProcessedStatus(DispensingForm.ProcessedStatus.NONE);

                SheetOperationBase operationBase = sheetOperationCreateFactory.createSheetOperation(DispensingSheetOperation.OperationType.CANCEL_PROCESS, dispensingForm.getDispensingSheetId(), operatorId, operatorId, processChargeForm.getChainId(), processChargeForm.getClinicId());
                operationBase.bindDispensingForms(Arrays.asList(dispensingForm));
                operationBase.createAndSaveSheetOperation();
            }
        }
    }


    /**
     * 收费单修改 导致 发药项目 修改
     */
    private void mergeDispensingForm(ChargeForm chargeForm, DispensingForm dispensingForm, String operatorId, InsertOrUpdateDispensingSheetContext context) {
        /**
         * 代煎代配 收费处支持切换供应商
         * */
        if (!StringUtils.isEmpty(chargeForm.getVendorId())) {
            dispensingForm.setVendorId(Long.parseLong(chargeForm.getVendorId()));
        }
        dispensingForm.setVendorName(chargeForm.getVendorName());

        //change waiting item to canceled
        if (chargeForm.getChargeFormItems() == null || dispensingForm.getDispensingFormItems() == null) {
            return;
        }

        List<String> insertChargeFormItemIds = new ArrayList<>();
        int sourceFormType = dispensingForm.getSourceFormType();

        /**
         * ChargeFormItem 和 DispensingFormItem 的映射关系
         * */
        BiFunction<ChargeFormItem, DispensingFormItem, Boolean> isEqualKeyFunc = (chargeFormItem, dispensingFormItem) -> TextUtils.equals(chargeFormItem.getId(), dispensingFormItem.getSourceFormItemId());

        /**
         * DispensingFormItem的新增
         * */
        Function<ChargeFormItem, DispensingFormItem> insertFunc = chargeFormItem -> {
            insertChargeFormItemIds.add(chargeFormItem.getId());
            return createDispensingFormItem(dispensingForm, chargeFormItem, dispensingForm.getCreatedBy());
        };

        /***
         * DispensingFormItem的  删除
         * */
        Function<DispensingFormItem, Boolean> deleteFunc = dispensingFormItem -> false;


        /***
         * DispensingFormItem的  更新
         * */
        BiConsumer<ChargeFormItem, DispensingFormItem> updateFunc = (chargeFormItem, dispensingFormItem) -> {
            BigDecimal originalUnitCount = dispensingFormItem.getUnitCount();
            BigDecimal originalDoseCount = dispensingFormItem.getDoseCount();
            if (dispensingFormItem.getStatus() == DispensingFormItem.Status.WAITING) {
                dispensingFormItem.setUnitCount(chargeFormItem.getUnitCount());
                dispensingFormItem.setDoseCount(chargeFormItem.getDoseCount());
                dispensingFormItem.setRemainingUnitCount(chargeFormItem.getUnitCount());
                dispensingFormItem.setRemainingDoseCount(chargeFormItem.getDoseCount());
                /**
                 * 更新Goods快照信息
                 * */
                dispensingFormItem.setProductInfo(chargeFormItem.getProductInfo());
                if (chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.REFUNDED) {
                    dispensingFormItem.setStatus(DispensingFormItem.Status.CANCELED);
                    context.setItemHasRefund(YesOrNo.YES);
                }
                if (!CollectionUtils.isEmpty(dispensingFormItem.getDispensingFormItemBatches())
                        && !CollectionUtils.isEmpty(chargeFormItem.getChargeFormItemBatchInfos())) {
                    mergeDispensingFormItemBatch(chargeFormItem, dispensingFormItem, operatorId);
                }
            } else if (dispensingFormItem.getStatus() == DispensingFormItem.Status.PART_DISPENSE) {
                // 部分发药
                /*
                    部分发药场景下部分退费，这里的 count 数量更新出了很多的 bug，
                    示例一：
                    开单3盒，发1盒，退1盒
                    全部退费，应该是 chargeFormItem.getDoseCount().min(dispenseDoseCount)

                    示例二：
                    开单3盒，发1盒，退1盒
                    部分退费，应该是 chargeFormItem.getDoseCount().max(dispenseDoseCount)
                 */
                if (sourceFormType == DispenseConst.SourceFormType.PRESCRIPTION_CHINESE) {
                    BigDecimal dispenseDoseCount = MathUtils.wrapBigDecimalSubtract(originalDoseCount, dispensingFormItem.getRemainingDoseCount());
                    if (chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.REFUNDED) {
                        dispensingFormItem.setDoseCount(chargeFormItem.getDoseCount().min(dispenseDoseCount));
                    } else {
                        dispensingFormItem.setDoseCount(chargeFormItem.getDoseCount().max(dispenseDoseCount));
                    }
                    dispensingFormItem.setUnitCount(chargeFormItem.getUnitCount());

                    BigDecimal remainingDoseCount = MathUtils.wrapBigDecimalSubtract(dispensingFormItem.getRemainingDoseCount(),
                            MathUtils.wrapBigDecimalSubtract(originalDoseCount, dispensingFormItem.getDoseCount()));
                    dispensingFormItem.setRemainingDoseCount(remainingDoseCount);
                    if (MathUtils.isZeroOrNull(remainingDoseCount)) {
                        dispensingFormItem.setRemainingUnitCount(BigDecimal.ZERO);
                        dispensingFormItem.setStatus(DispensingFormItem.Status.DISPENSED);
                    }
                } else {
                    BigDecimal dispenseUnitCount = MathUtils.wrapBigDecimalSubtract(originalUnitCount, dispensingFormItem.getRemainingUnitCount());
                    if (chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.REFUNDED) {
                        dispensingFormItem.setUnitCount(chargeFormItem.getUnitCount().min(dispenseUnitCount));
                    } else {
                        dispensingFormItem.setUnitCount(chargeFormItem.getUnitCount().max(dispenseUnitCount));
                    }
                    dispensingFormItem.setDoseCount(chargeFormItem.getDoseCount());

                    BigDecimal remainingUnitCount = MathUtils.wrapBigDecimalSubtract(dispensingFormItem.getRemainingUnitCount(),
                            MathUtils.wrapBigDecimalSubtract(originalUnitCount, dispensingFormItem.getUnitCount()));
                    dispensingFormItem.setRemainingUnitCount(remainingUnitCount);
                    if (MathUtils.isZeroOrNull(remainingUnitCount)) {
                        dispensingFormItem.setRemainingDoseCount(BigDecimal.ZERO);
                        dispensingFormItem.setStatus(DispensingFormItem.Status.DISPENSED);
                    }
                }

                if (chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.REFUNDED) {
                    // 只有药全退的时候才会走到这块逻辑，部分发状态下能全退说明一定是先把发过的药都退了
                    // 如果当费用全退的时候，可以直接把剩余可发的数量变为0，然后修改发药状态为已退
                    dispensingFormItem.setRemainingDoseCount(BigDecimal.ZERO);
                    dispensingFormItem.setRemainingUnitCount(BigDecimal.ZERO);
                }
                if (!CollectionUtils.isEmpty(dispensingFormItem.getDispensingFormItemBatches())
                        && !CollectionUtils.isEmpty(chargeFormItem.getChargeFormItemBatchInfos())) {
                    mergeDispensingFormItemBatch(chargeFormItem, dispensingFormItem, operatorId);
                }
            }

            if (chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.REFUNDED
                    || MathUtils.wrapBigDecimalCompare(chargeFormItem.getRefundDoseCount(), BigDecimal.ZERO) > 0
                    || MathUtils.wrapBigDecimalCompare(chargeFormItem.getRefundDoseCount(), BigDecimal.ZERO) > 0) {
                // 如果项目全部已退费，或者部分退费，都需要标记 itemHasRefund，因为后续需要通知前端打印退费小票
                context.setItemHasRefund(YesOrNo.YES);
            }
            dispensingFormItem.setChargeFormItemStatus(chargeFormItem.getStatus());

            if (!CollectionUtils.isEmpty(dispensingFormItem.getTraceableCodeList()) &&
                    (dispensingFormItem.getStatus() == DispensingFormItem.Status.DISPENSED || dispensingFormItem.getStatus() == DispensingFormItem.Status.CANCELED)) {
                // 如果发药项到了终态的话，则需要清理没有使用的追溯码
                dispensingFormItem.setTraceableCodeList(
                        dispensingFormItem.getTraceableCodeList().stream()
                                .filter(traceableCode -> traceableCode.getUsed() != DispensingConstants.TraceableCodeUsed.WAITING)
                                .collect(Collectors.toList()));
            }
        };

        MergeTool<ChargeFormItem, DispensingFormItem> mergeTool = new MergeTool<>();
        mergeTool.setSrc(chargeForm.getChargeFormItems())
                .setDst(dispensingForm.getDispensingFormItems())
                .setIsEqualKeyFunc(isEqualKeyFunc)
                .setInsertFunc(insertFunc)
                .setDeleteFunc(deleteFunc)
                .setUpdateFunc(updateFunc);
        mergeTool.doMerge();

        //处理套餐母项也要insert
        if (!CollectionUtils.isEmpty(insertChargeFormItemIds)) {

            List<DispensingFormItem> composeSubItems = dispensingForm.getDispensingFormItems().stream()
                    .filter(dispensingFormItem -> dispensingFormItem.getComposeType() == ComposeType.COMPOSE_SUB_ITEM && insertChargeFormItemIds.contains(dispensingForm.getSourceFormId()))
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(composeSubItems)) {
                List<DispensingFormItem> composeDispensingFormItems = createComposeDispensingFormItem(dispensingForm, composeSubItems, chargeForm.getChargeFormItems(), operatorId);

                if (!CollectionUtils.isEmpty(composeDispensingFormItems)) {
                    dispensingForm.getDispensingFormItems().addAll(composeDispensingFormItems);
                }
            }
        }

    }

    private void mergeDispensingFormItemBatch(ChargeFormItem chargeFormItem, DispensingFormItem dispensingFormItem, String operatorId) {
        if (chargeFormItem == null || CollectionUtils.isEmpty(chargeFormItem.getChargeFormItemBatchInfos())
                || dispensingFormItem == null || CollectionUtils.isEmpty(dispensingFormItem.getDispensingFormItemBatches())) {
            return;
        }

        List<ChargeFormItemBatchInfo> chargeFormItemBatchInfos = chargeFormItem.getChargeFormItemBatchInfos();
        List<DispensingFormItemBatchInfo> dispensingFormItemBatches = dispensingFormItem.getDispensingFormItemBatches();
        Map<Long, DispensingFormItemBatchInfo> batchIdToDispensingFormItemBatchInfo = ListUtils.toMap(dispensingFormItemBatches, DispensingFormItemBatchInfo::getBatchId);
        for (ChargeFormItemBatchInfo chargeFormItemBatchInfo : chargeFormItemBatchInfos) {
            Long batchId = Long.parseLong(chargeFormItemBatchInfo.getBatchId());
            DispensingFormItemBatchInfo dispensingFormItemBatchInfo = batchIdToDispensingFormItemBatchInfo.get(batchId);
            if (dispensingFormItemBatchInfo == null) {
                log.error("收费单批次信息未找到对应的发药单批次信息 chargeFormItemBatchInfoId:{}, batchId:{}", chargeFormItemBatchInfo.getId(), batchId);
                continue;
            }

            if (chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.REFUNDED) {
                // 总共可退费的数量
                BigDecimal chargedUnitCount = chargeFormItemBatchInfo.getUnitCount();
                BigDecimal refundableUnitCount = dispensingFormItemBatchInfo.getRemainUnitCount();
                if (MathUtils.wrapBigDecimalCompare(chargedUnitCount, refundableUnitCount) > 0) {
                    log.error("退费数量大于可退费数量 chargeFormItemBatchInfoId:{}, batchId:{}, chargedUnitCount:{}, refundableUnitCount:{}",
                            chargeFormItemBatchInfo.getId(), batchId, chargedUnitCount, refundableUnitCount);
                    continue;
                }
                dispensingFormItemBatchInfo.setUnitCount(chargeFormItemBatchInfo.getUnitCount());
            } else {
                dispensingFormItemBatchInfo.setUnitCount(BigDecimal.ZERO);
            }
            FillUtils.fillLastModifiedBy(dispensingFormItemBatchInfo, operatorId);
        }
    }

    /**
     * 新增发药form
     *
     * @param dispensingSheet        新建或修改的发药sheet
     * @param chargeForm             收费的chargeFrom
     * @param processChargeForm      加工费处方
     * @param isPrescriptionReview   是否开启处方审核
     * @param isPrescriptionCompound 是否开启处方调配
     */
    public DispensingForm createDispensingForm(DispensingSheet dispensingSheet, ChargeForm chargeForm, ChargeForm processChargeForm, int isPrescriptionReview, int isPrescriptionCompound, String operatorId) {
        if (CollectionUtils.isEmpty(chargeForm.getChargeFormItems())
                || !DispensingUtils.canDispenseChargeFormSourceFormType(chargeForm.getSourceFormType())) {
            return null;
        }
        DispensingForm dispensingForm = new DispensingForm();
        dispensingForm.setId(abcIdGenerator.getUUID());
        dispensingForm.setPatientOrderId(dispensingSheet.getPatientOrderId());
        dispensingForm.setPatientId(dispensingSheet.getPatientId());
        dispensingForm.setChainId(dispensingSheet.getChainId());
        dispensingForm.setClinicId(dispensingSheet.getClinicId());
        /**
         * 发药Form关联的chargeForm 和 outpatientForm Id
         * */
        dispensingForm.setSourceFormId(chargeForm.getId());
        dispensingForm.setOutpatientFormId(chargeForm.getSourceFormId());

        dispensingForm.setSort(chargeForm.getSort());
        dispensingForm.setUsageInfoJson(JsonUtils.dump(chargeForm.getUsageInfo()));

        /**
         *  设置发药formType
         * */
        dispensingForm.setSourceFormType(DispensingUtils.getDispensingFormType(chargeForm.getSourceFormType()));

        dispensingForm.setDispensingSheetId(dispensingSheet.getId());

        /**
         * 初始化正确的 审核方状态/调配状态
         * 代煎代配的发药单，不支持审核和调配
         * */
        if (chargeForm.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY) {
            if (chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE) {
                dispensingForm.setProcessedStatus(DispensingForm.ProcessedStatus.WAITING);
            }
        } else {
            dispensingForm.initRightAuditStatus(isPrescriptionReview);
            dispensingForm.initRightCompoundedStatus(isPrescriptionCompound);
            if (processChargeForm != null && !CollectionUtils.isEmpty(processChargeForm.getChargeFormItems())) {
                ChargeFormItem processChargeFormItem = processChargeForm.getChargeFormItems().stream().findFirst().orElse(null);
                if (processChargeFormItem != null && processChargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED) {
                    dispensingForm.setProcessedStatus(DispensingForm.ProcessedStatus.WAITING);
                }
            }

        }

        /**
         * 代煎代配  设置供应商
         * */
        if (!StringUtils.isEmpty(chargeForm.getVendorId())) {
            dispensingForm.setVendorId(Long.parseLong(chargeForm.getVendorId()));
        }
        dispensingForm.setVendorName(chargeForm.getVendorName());

        FillUtils.fillCreatedBy(dispensingForm, operatorId);

        /**
         * 建发药单里面的发药项
         * */
        List<DispensingFormItem> dispensingFormItems = chargeForm.getChargeFormItems().stream()
                .map(chargeFormItem -> createDispensingFormItem(dispensingForm, chargeFormItem, operatorId))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (dispensingFormItems.size() == 0) {
            return null;
        }

        /**
         * TODO 为什么套餐子项 要单独处理？
         * 上面的 dispensingFormItems 和 这里的 composeDispensingFormItems 不会重复么？
         * */
        //套餐母项,key为chargeFormItem的id
        List<DispensingFormItem> composeDispensingFormItems = createComposeDispensingFormItem(dispensingForm, dispensingFormItems, chargeForm.getChargeFormItems(), operatorId);

        if (!CollectionUtils.isEmpty(composeDispensingFormItems)) {
            dispensingFormItems.addAll(composeDispensingFormItems);
        }
        /**
         * 完成了一个 dispensingForm创建
         * */
        dispensingForm.setDispensingFormItems(dispensingFormItems);
        return dispensingForm;
    }

    /**
     * 创建发药form的扩展信息
     */
    public void createDispensingFormAdditional(DispensingForm dispensingForm, ChargeSheetProcessInfo processInfo, String operatorId) {
        if (Objects.isNull(dispensingForm) || StringUtils.isBlank(operatorId)) {
            return;
        }
        DispensingFormAdditionalReq additionalReq = new DispensingFormAdditionalReq();
        if (Objects.nonNull(processInfo) && Objects.nonNull(processInfo.getTakeMedicationTime())) {
            BeanUtils.copyProperties(processInfo, additionalReq);
        }
        // 重点单独设置id,上面进行copy的时候会存在id值
        additionalReq.setId(dispensingForm.getId());
        dispensingForm.setAdditional(this.createDispensingFormAdditional(dispensingForm, operatorId, additionalReq));
    }

    /**
     * 创建发药form的扩展信息
     */
    public DispensingFormAdditional createDispensingFormAdditional(DispensingForm dispensingForm, String operatorId, DispensingFormAdditionalReq additionalReq) {
        if (StringUtils.isBlank(operatorId) || Objects.isNull(dispensingForm) || Objects.isNull(additionalReq)) {
            return null;
        }
        DispensingFormAdditional additional = new DispensingFormAdditional();
        additional.setChainId(dispensingForm.getChainId());
        additional.setClinicId(dispensingForm.getClinicId());
        additional.setPatientOrderId(dispensingForm.getPatientOrderId());
        additional.setDispensingSheetId(dispensingForm.getDispensingSheetId());
        BeanUtils.copyProperties(additionalReq, additional);
        FillUtils.fillCreatedBy(additional, operatorId);
        return additional;
    }

    /**
     * 对套餐子项的 进行额外的处理
     *
     * @param dispensingForm      发药Form
     * @param dispensingFormItems 已经捡出来的发药formItem
     * @param chargeFormItems     收费单的FormItems
     * @return 套餐子项的 DispensingFormItem
     */
    private List<DispensingFormItem> createComposeDispensingFormItem(DispensingForm dispensingForm,
                                                                     List<DispensingFormItem> dispensingFormItems,
                                                                     List<ChargeFormItem> chargeFormItems,
                                                                     String operatorId) {

        if (dispensingFormItems == null) {
            return new ArrayList<>();
        }

        if (chargeFormItems == null) {
            chargeFormItems = new ArrayList<>();
        }

        /**
         * chargeFormItemIdToDispingFormItem chargeFormItemId  到发药单Item的map
         * */
        Map<String, DispensingFormItem> composeDispensingFormItemMap = new HashMap<>();
        /**
         * chargeFormItemIdToChargeFormItem chargeFormItemId  到收费单Item的map
         * */
        Map<String, ChargeFormItem> chargeFormItemMap = ListUtils.toMap(chargeFormItems, ChargeFormItem::getId);


        dispensingFormItems.stream()
                .filter(dispensingFormItem -> dispensingFormItem.getComposeType() == ComposeType.COMPOSE_SUB_ITEM)
                .forEach(dispensingFormItem -> {
                    //1 找到套餐子项的chargeFormItem /  childChargeFormItem
                    ChargeFormItem composeSubItem = chargeFormItemMap.getOrDefault(dispensingFormItem.getSourceFormItemId(), null);
                    //2 找套餐 母项 的chargeFormItem
                    if (composeSubItem != null && !StringUtils.isEmpty(composeSubItem.getComposeParentFormItemId())) {
                        DispensingFormItem composeDispensingFormItem = null;
                        ChargeFormItem composeParentItem = chargeFormItemMap.getOrDefault(composeSubItem.getComposeParentFormItemId(), null);

                        //3 看套餐母项 对应的发药formItem是否存在
                        if (composeParentItem != null) {
                            if (!composeDispensingFormItemMap.keySet().contains(composeParentItem.getId())) {
                                composeDispensingFormItem = baseCreateDispensingFormItem(dispensingForm, composeParentItem, operatorId);
                                composeDispensingFormItemMap.put(composeParentItem.getId(), composeDispensingFormItem);
                            } else {
                                composeDispensingFormItem = composeDispensingFormItemMap.getOrDefault(composeParentItem.getId(), null);
                            }
                        }

                        /**
                         * 上面干了那么多，主要是为了这里
                         * 把孩子的发药FormItem 的paremtFormItemId 设置成父亲的Id
                         * */
                        if (composeDispensingFormItem != null) {
                            dispensingFormItem.setComposeParentFormItemId(composeDispensingFormItem.getId());
                        }
                    }
                });

        return new ArrayList<>(composeDispensingFormItemMap.values());
    }

    /**
     * 建发药项
     *
     * @param dispensingForm 发药Form
     * @param chargeFormItem 收费单里面某个goods的收费FormItem
     * @param operatorId     操作人
     */
    private DispensingFormItem baseCreateDispensingFormItem(DispensingForm dispensingForm, ChargeFormItem chargeFormItem, String operatorId) {

        DispensingFormItem dispensingFormItem = new DispensingFormItem();
        dispensingFormItem.setId(abcIdGenerator.getUUID());
        /**
         * 记录LockId
         * */
        dispensingFormItem.setLockId(chargeFormItem.getLockId());
        dispensingFormItem.setPatientOrderId(dispensingForm.getPatientOrderId());
        dispensingFormItem.setPatientId(dispensingForm.getPatientId());
        dispensingFormItem.setChainId(dispensingForm.getChainId());
        dispensingFormItem.setClinicId(dispensingForm.getClinicId());
        dispensingFormItem.setDispensingSheetId(dispensingForm.getDispensingSheetId());
        dispensingFormItem.setDispensingFormId(dispensingForm.getId());
        dispensingFormItem.setSourceFormItemId(chargeFormItem.getId());
        dispensingFormItem.setProductId(chargeFormItem.getProductId());
        dispensingFormItem.setProductType(chargeFormItem.getProductType());
        dispensingFormItem.setProductSubType(chargeFormItem.getProductSubType());
        dispensingFormItem.setName(chargeFormItem.getName());
        dispensingFormItem.setUnit(chargeFormItem.getUnit());
        dispensingFormItem.setUsageInfoJson(DispensingUtils.chargeUsageInfoToDBUsageInfo(chargeFormItem.getUsageInfo()));
        dispensingFormItem.setSort(chargeFormItem.getSort());
        dispensingFormItem.setOutpatientFormItemId(chargeFormItem.getSourceFormItemId());
        dispensingFormItem.setUnitPrice(chargeFormItem.getUnitPrice());
        dispensingFormItem.setTotalPrice(chargeFormItem.getTotalPrice());
        dispensingFormItem.setUnitCount(chargeFormItem.getUnitCount());
        dispensingFormItem.setRemainingUnitCount(chargeFormItem.getUnitCount());
        dispensingFormItem.setTotalCostPrice(BigDecimal.ZERO);
        dispensingFormItem.setDoseCount(chargeFormItem.getDoseCount());
        dispensingFormItem.setRemainingDoseCount(chargeFormItem.getDoseCount());
        dispensingFormItem.setUseDismounting(chargeFormItem.getUseDismounting());
        dispensingFormItem.setGroupId(chargeFormItem.getGroupId());
        dispensingFormItem.setComposeType(chargeFormItem.getComposeType());
        dispensingFormItem.setStatus(DispensingFormItem.Status.WAITING);
        dispensingFormItem.setSourceItemType(chargeFormItem.getSourceItemType());
        dispensingFormItem.setPharmacyType(chargeFormItem.getPharmacyType());
        dispensingFormItem.setPharmacyNo(chargeFormItem.getPharmacyNo());
        /**
         * 收费项目上的productInfo
         * */
        dispensingFormItem.setProductInfo(chargeFormItem.getProductInfo());
        GoodsItem goodsItem = JsonUtils.readValue(chargeFormItem.getProductInfo(), GoodsItem.class);
        if (goodsItem != null) {
            dispensingFormItem.setProductSnap(DispensingFormItemGoodsSnap.fromGoodsItem(goodsItem));
        }
        dispensingFormItem.setExtendData(createDispensingFormItemExtendData(chargeFormItem.getDoctorId(),
                chargeFormItem.getDepartmentId(),
                chargeFormItem.getNurseId(),
                chargeFormItem.getRemark(),
                chargeFormItem.getToothNos(),
                chargeFormItem.getTraceableCodeList(),
                chargeFormItem.getTraceableCodeNum(),
                chargeFormItem.getShebaoDismountingFlag()));
        FillUtils.fillCreatedBy(dispensingFormItem, operatorId);

        if (chargeFormItem.getLockId() != null && !CollectionUtils.isEmpty(chargeFormItem.getChargeFormItemBatchInfos())) {
            List<DispensingFormItemBatchInfo> dispensingFormItemBatchInfoList = chargeFormItem.getChargeFormItemBatchInfos()
                    .stream().map(batchInfo -> createDispensingFormItemBatchInfo(dispensingFormItem, batchInfo, operatorId))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            dispensingFormItem.setDispensingFormItemBatches(dispensingFormItemBatchInfoList);
        }
        dispensingFormItem.setChargeFormItemStatus(chargeFormItem.getStatus());

        return dispensingFormItem;
    }

    private DispensingFormItemBatchInfo createDispensingFormItemBatchInfo(DispensingFormItem dispensingFormItem,
                                                                          ChargeFormItemBatchInfo chargeBatchInfo,
                                                                          String operatorId) {
        if (chargeBatchInfo == null) {
            return null;
        }

        DispensingFormItemBatchInfo dispensingFormItemBatchInfo = new DispensingFormItemBatchInfo();
        dispensingFormItemBatchInfo.setId(abcIdGenerator.getUIDLong());
        dispensingFormItemBatchInfo.setClinicId(dispensingFormItem.getClinicId());
        dispensingFormItemBatchInfo.setChainId(dispensingFormItem.getChainId());
        dispensingFormItemBatchInfo.setPatientOrderId(dispensingFormItem.getPatientOrderId());
        dispensingFormItemBatchInfo.setDispensingSheetId(dispensingFormItem.getDispensingSheetId());
        dispensingFormItemBatchInfo.setDispensingFormId(dispensingFormItem.getDispensingFormId());
        dispensingFormItemBatchInfo.setDispensingFormItemId(dispensingFormItem.getId());
        dispensingFormItemBatchInfo.setUnitCostPrice(chargeBatchInfo.getUnitCostPrice());
        // TODO
//        dispensingFormItemBatchInfo.setTotalCostPrice(chargeBatchInfo.getTotalCostPrice());
        dispensingFormItemBatchInfo.setTotalCostPrice(BigDecimal.ZERO);
        dispensingFormItemBatchInfo.setUnitPrice(chargeBatchInfo.getUnitPrice());
        dispensingFormItemBatchInfo.setTotalPrice(chargeBatchInfo.getTotalPrice());
        dispensingFormItemBatchInfo.setSourceTotalPrice(chargeBatchInfo.getSourceTotalPrice());
        dispensingFormItemBatchInfo.setSourceUnitPrice(chargeBatchInfo.getSourceUnitPrice() != null ? chargeBatchInfo.getSourceUnitPrice() : BigDecimal.ZERO);
        dispensingFormItemBatchInfo.setUnitCount(chargeBatchInfo.getUnitCount());
        dispensingFormItemBatchInfo.setUndispenseUnitCount(BigDecimal.ZERO);
        dispensingFormItemBatchInfo.setDispenseUnitCount(BigDecimal.ZERO);
        dispensingFormItemBatchInfo.setProductId(dispensingFormItem.getProductId());
        dispensingFormItemBatchInfo.setBatchId(Long.parseLong(chargeBatchInfo.getBatchId()));
        dispensingFormItemBatchInfo.setBatchNo(chargeBatchInfo.getBatchNo());
        dispensingFormItemBatchInfo.setIsOld(0);
        FillUtils.fillCreatedBy(dispensingFormItemBatchInfo, operatorId);
        return dispensingFormItemBatchInfo;
    }

    private DispensingFormItemExtendData createDispensingFormItemExtendData(String doctorId, String departmentId,
                                                                            String nurseId, String remark,
                                                                            List<Integer> toothNos,
                                                                            List<cn.abcyun.cis.commons.amqp.message.TraceableCode> traceableCodeList,
                                                                            BigDecimal traceableCodeNum,
                                                                            Integer shebaoDismountingFlag) {
        DispensingFormItemExtendData extendData = new DispensingFormItemExtendData();
        extendData.setDoctorId(doctorId);
        extendData.setDepartmentId(departmentId);
        extendData.setNurseId(nurseId);
        extendData.setRemark(remark);
        extendData.setToothNos(toothNos);
        if (!CollectionUtils.isEmpty(traceableCodeList)) {
            List<TraceableCode> traceableCodeWriteModels = traceableCodeList.stream().filter(it -> !StringUtils.isBlank(it.getNo())).map(it -> {
                TraceableCode t = new TraceableCode();
                t.setId(it.getId());
                t.setNo(it.getNo());
                t.setType(it.getType());
                t.setBatchId(it.getBatchId());
                if (MathUtils.wrapBigDecimalCompare(it.getHisPieceCount(), BigDecimal.ZERO) > 0 || MathUtils.wrapBigDecimalCompare(it.getHisPackageCount(), BigDecimal.ZERO) > 0) {
                    t.setHisPieceCount(it.getHisPieceCount());
                    t.setHisPackageCount(it.getHisPackageCount());
                } else {
                    t.setCount(it.getCount());
                }

                t.setDismountingSn(it.getDismountingSn());
                return t;
            }).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(traceableCodeWriteModels)) {
                extendData.setTraceableCodeList(traceableCodeWriteModels);
            }
        }
        extendData.setTraceableCodeNum(traceableCodeNum);
        if (shebaoDismountingFlag != null) {
            extendData.setShebaoDismountingFlag(DispensingUtils.offFlag(shebaoDismountingFlag, ShebaoDismountingFlagConst.IS_EDITABLE));
        }
        return extendData;
    }

    /**
     * 检查收费项能否发药 && 建发药项
     *
     * @param dispensingForm 发药Form
     * @param chargeFormItem 收费单里面某个goods的收费FormItem
     * @param operatorId     操作人
     */
    public DispensingFormItem createDispensingFormItem(DispensingForm dispensingForm, ChargeFormItem chargeFormItem, String operatorId) {
        /**
         * 没收费的goodsItem不能发药
         * */
        if (chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.CHARGED) {
            return null;
        }

        /**
         * 非库存商品不能发药
         * */
        if (!(chargeFormItem.getProductType() == Constants.ProductType.MEDICINE
                || chargeFormItem.getProductType() == Constants.ProductType.MATERIAL
                || chargeFormItem.getProductType() == Constants.ProductType.SALE_PRODUCT
                || chargeFormItem.getProductType() == Constants.ProductType.EYE)) {
            return null;
        }

        /**
         * 无GoodsId 不发药
         * */
        if (TextUtils.isEmpty(chargeFormItem.getProductId())) {
            return null;
        }

        /**
         * 计算收费单上的买药数量 剂量*剂数 小于等于不需要发药
         * */
        if (MathUtils.calculateTotalCount(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount()).compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }

        /**
         * 建发药的FormItem
         * */
        return baseCreateDispensingFormItem(dispensingForm, chargeFormItem, operatorId);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public DispensingSheetView findDispensingSheetViewById(String dispensingSheetId) throws ServiceInternalException {
        return dispensingSheetService.findDispensingSheetViewById(dispensingSheetId, false);
    }

    /**
     * OpenAPI-通过发药单Id查询发药单详情
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public DispensingSheetView findDispensingSheetViewByIdForOpenApi(String dispensingSheetId) {
        DispensingSheetView dispensingSheetView = dispensingSheetService.findDispensingSheetViewById(dispensingSheetId, true);
        formatDispensingSheetViewForOpenApi(dispensingSheetView);
        // 绑定审核调配人信息
        bindCompoundAndAuditInfo(dispensingSheetView);
        return dispensingSheetView;
    }

    public void bindCompoundAndAuditInfo(DispensingSheetView dispensingSheetView) {
        if (dispensingSheetView == null) {
            return;
        }

        Map<String, DispensingFormPrintRsp> formIdToFormPrintRsp = findCompoundAndAuditInfo(Collections.singletonList(dispensingSheetView.getId()));
        DispensingViewUtils.doWithDispensingFormView(dispensingSheetView, form -> {
            DispensingFormPrintRsp formPrintRsp = formIdToFormPrintRsp.get(form.getId());
            if (formPrintRsp == null) {
                return;
            }

            form.setCompoundInfo(DispensingFormCompoundView.of(formPrintRsp.getCompoundByEmployeeId(), formPrintRsp.getCompoundByUserName(), formPrintRsp.getCompoundByHandSign(), formPrintRsp.getCompoundTime()));
            form.setAuditInfo(DispensingFormAuditView.of(formPrintRsp.getAuditBy(), formPrintRsp.getAuditName(), formPrintRsp.getAuditHandSign(), formPrintRsp.getAuditTime()));
        });
    }

    private void formatDispensingSheetViewForOpenApi(DispensingSheetView dispensingSheetView) {
        if (dispensingSheetView == null) {
            return;
        }

        List<DispensingFormView> dispensingForms = dispensingSheetView.getDispensingForms();
        if (CollectionUtils.isEmpty(dispensingForms)) {
            return;
        }

        dispensingForms.forEach(dispensingForm -> {
            if (dispensingForm == null) {
                return;
            }

            List<DispensingFormItemView> dispensingFormItems = dispensingForm.getDispensingFormItems();
            if (CollectionUtils.isEmpty(dispensingFormItems)) {
                return;
            }

            /*
                OpenAPI 协议适配
                * 返回所有发药的 item
                * 不返回退药的 item
             */
            Map<String, DispensingFormItemView> dispensingFormItemIdToDispensedFormItem = dispensingFormItems.stream()
                    .filter(dispensingFormItem -> dispensingFormItem != null && dispensingFormItem.getStatus() == DispenseConst.Status.DISPENSED)
                    .collect(Collectors.toMap(DispensingFormItemView::getId, Function.identity(), (a, b) -> a));
            List<DispensingFormItemView> addDispensedFormItems = new ArrayList<>();
            List<DispensingFormItemView> openApiDispensingFormItems = dispensingFormItems.stream().filter(dispensingFormItem -> {
                if (dispensingFormItem == null) {
                    return false;
                }
                int dispenseStatus = dispensingFormItem.getStatus();
                // 非退药的 item 不做处理，原样返回
                if (dispenseStatus != DispenseConst.Status.UNDISPENSED) {
                    return true;
                }

                // 查询退药项对应的发药项
                String associateFormItemId = dispensingFormItem.getAssociateFormItemId();
                if (StringUtils.isBlank(associateFormItemId)) {
                    return false;
                }
                DispensingFormItemView dispensedFormItemView = dispensingFormItemIdToDispensedFormItem.get(associateFormItemId);
                // 补全发药项
                if (dispensedFormItemView == null) {
                    // 由于在全部退药的情况下（多次退药但是最终全退的场景会由底层逻辑去合并为一条退药项），不会返回对应的发药项
                    // 但是开放平台只关注发药项，所以需要根据对应的退药项构建出对应的发药项
                    dispensedFormItemView = new DispensingFormItemView();
                    BeanUtils.copyProperties(dispensingFormItem, dispensedFormItemView);
                    dispensedFormItemView.setId(associateFormItemId);
                    dispensedFormItemView.setStatus(DispenseConst.Status.UNDISPENSED);
                    dispensedFormItemView.setStatusName(DispensingFormItemView.statusName(dispensedFormItemView.getStatus()));
                    dispensedFormItemView.setAssociateFormItemId(null);
                    addDispensedFormItems.add(dispensedFormItemView);
                }
                // 不返回退药项
                return false;
            }).collect(Collectors.toList());
            openApiDispensingFormItems.addAll(addDispensedFormItems);
            dispensingForm.setDispensingFormItems(openApiDispensingFormItems);
        });
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public DispensingSheetDetailRsp getDispensingSheetDetailById(String chainId, String clinicId, String dispensingSheetId) {
        // 1、发药单
        DispensingSheetV2 dispensingSheet = dispensingSheetV2Repository.findByIdAndChainIdAndIsDeleted(dispensingSheetId, chainId, 0);
        if (Objects.isNull(dispensingSheet) || !dispensingSheet.canDisplay()) {
            throw new NotFoundException();
        }

        // 2、查询收费单对应的发药单
        List<DispensingSheet> allDispensingSheets = dispensingSheetEntityService.findAllBySourceSheetIdAndChainIdForDisplay(dispensingSheet.getSourceSheetId(), chainId);
        if (CollectionUtils.isEmpty(allDispensingSheets)) {
            throw new NotFoundException();
        }
        DispensingSheet selectDispensingSheet = allDispensingSheets.stream().filter(dispensingSheet1 -> StringUtils.equals(dispensingSheet1.getId(), dispensingSheetId)).findFirst().orElse(null);

        // 3. 构建返回
        DispensingSheetDetailRspBuilder builder = new DispensingSheetDetailRspBuilder(chainId, clinicId, selectDispensingSheet, allDispensingSheets);
        builder.bindDependency(patientOrderService, scGoodsFeignClient, employeeService, outpatientClient,
                propertyService, chargeService, dispensingItemLogRepository, dispensingCallingItemService, goodsLockingFeignClient);
        return builder.build();
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    @Deprecated
    public DispensingSheetView findDispensingSheetViewBySourceSheetId(String sourceSheetId, String clinicId) throws ServiceInternalException {
        return dispensingSheetService.findDispensingSheetViewBySourceSheetId(sourceSheetId, clinicId);
    }

    /**
     * rpc拉取 单个发药单
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public DispensingSheet findDispensingSheetBySourceSheetId(String id) {
        return dispensingSheetService.findDispensingSheetBySourceSheetId(id);
    }

    /**
     * rpc拉取 整个收费单的发药单
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public List<DispensingSheet> findDispensingSheetListBySourceSheetId(String sourceSheetId, Integer queryGoodsStockLog) {
        return dispensingSheetService.findDispensingSheetListBySourceSheetId(sourceSheetId, queryGoodsStockLog);
    }

    /**
     * rpc拉取 整个收费单的发药单view
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public List<DispensingSheetView> listDispensingSheetViewsBySourceSheetId(String chainId, String sourceSheetId) {
        // 1、查询收费单对应的发药单
        List<DispensingSheet> allDispensingSheets = dispensingSheetEntityService.findAllByChainIdSourceSheetId(chainId, sourceSheetId);
        if (CollectionUtils.isEmpty(allDispensingSheets)) {
            return Lists.newArrayList();
        }
        // 2、填充数据
        DispensingSheet firstDispensingSheet = allDispensingSheets.get(0);
        return DispensingViewUtils.batchFillDispensingSheetView(chainId, firstDispensingSheet.getClinicId(),
                sourceSheetId, firstDispensingSheet.getPatientOrderId(), allDispensingSheets, patientOrderService,
                scGoodsFeignClient, employeeService, outpatientClient, propertyService, chargeService,
                dispensingItemLogRepository, goodsLockingFeignClient);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public List<DispensingBatchQueryRsp.DispensingSimpleInfo> queryDispensingSimpleInfos(String chainId, List<String> sourceSheetIds) {
        return dispensingMapper.queryDispensingSimpleInfos(chainId, sourceSheetIds);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public DispensingFormItemQuantityBatchRsp queryDispensingFormItemQuantityInfos(String chainId, String clinicId, List<String> sourceSheetIds) {
        if (CollectionUtils.isEmpty(sourceSheetIds)) {
            return new DispensingFormItemQuantityBatchRsp();
        }
        List<DispensingFormItemQuantityInfo> unmergedItems = dispensingMapper.queryDispensingFormItemQuantityInfos(chainId, clinicId, sourceSheetIds);
        List<DispensingFormItemQuantityInfo> items = DispensingItemMerger.mergeForDispensingFormItemQuantityInfo(unmergedItems);
        return new DispensingFormItemQuantityBatchRsp().setDispensingFormItems(items);
    }

    @Transactional(rollbackFor = Exception.class)
    @Retryable(value = ObjectOptimisticLockingFailureException.class, maxAttempts = 2)
    public DispenseResult directDispenseDispensingSheet(DispensingSheet dispenseSheet,
                                                        ChargeSheet chargeSheet,
                                                        String chainId,
                                                        String clinicId,
                                                        boolean isDirectDispenseChargeSheet,
                                                        List<String> dispensedByIds,
                                                        String operatorId) throws ParamRequiredException, CisCustomException, ServiceInternalException {
        return dispenseSheetCore(dispenseSheet, chargeSheet, chainId, clinicId, isDirectDispenseChargeSheet, dispensedByIds, operatorId, 0);
    }

    /**
     * 发药扣库
     *
     * @param dispenseSheet               用户本次请求要发药的发药单对象，非DB里面的发药单对象
     * @param chargeSheet                 对应收费单
     * @param clinicId                    发药门店Id
     * @param isDirectDispenseChargeSheet 是否是收费直接发药
     * @param dispensedByIds
     * @param operatorId                  发药人
     * @param isReDispense
     * @note 三条调用路径
     * 1. 药房用户点发药
     * 2. 收费处直接发药
     * 3. 代煎代配药房通过消息过来的自动发药
     */
    @Transactional(rollbackFor = Exception.class)
    public DispenseResult dispenseSheetCore(DispensingSheet dispenseSheet,
                                            ChargeSheet chargeSheet,
                                            String chainId,
                                            String clinicId,
                                            boolean isDirectDispenseChargeSheet,
                                            List<String> dispensedByIds,
                                            String operatorId,
                                            int isReDispense) throws ParamRequiredException, CisCustomException, ServiceInternalException {
        // TODO 等发药单退药状态展示需求全量一段时间后，就可以删除对 isReDispense 的支持了，前端不会再传 1 过来了
        if (isReDispense == 1) {
            ReDispensePreCheckOpt.ReDispensePreCheckOptBuilder reDispensePreCheckOptBuilder = ReDispensePreCheckOpt.ReDispensePreCheckOptBuilder.newInstant(dispenseSheet.getId(), chainId, clinicId)
                    .setWithBatchInfo(0)
                    .setChargeSheet(chargeSheet);
            ReDispensePreCheckResult reDispensePreCheckResult = reDispensePreCheckOptBuilder.build().preCheck();
            if (reDispensePreCheckResult.getReDispenseWithBatchInfo() == 1) {
                // 如果支持重新发药，但是需要批次信息的话，则不支持重新发药同时发药，需要先走重新发药的操作
                sLogger.error("发药单不支持重新发药的同时进行发药操作，因为发药单重新发药需要指定批次信息");
                throw new DispenseSheetChangedException();
            }
        }

        Map<String, JsonNode> keyPropertyConfigItem = propertyService.batchGetConfigItems(
                        Arrays.asList(
                                new PropertyInfo(PropertyKey.DISPENSING, clinicId),
                                new PropertyInfo(PropertyKey.TRACE_CODE_CONFIG, clinicId)
                        )
                ).stream()
                .collect(Collectors.toMap(PropertyConfigItem::getKey, PropertyConfigItem::getValue, (a, b) -> a));

        DispenseOpt dispenseOpt = DispenseOpt.DispenseOptBuilder.of(dispenseSheet, clinicId, operatorId)
                .setChargeSheet(chargeSheet)
                .setDirectDispenseChargeSheet(isDirectDispenseChargeSheet)
                .setDispensedByIds(dispensedByIds)
                .setIsReDispense(isReDispense)
                .setDispensingConfig(JsonUtils.readValue(keyPropertyConfigItem.get(PropertyKey.DISPENSING.getKey()), Dispensing.class))
                .setTraceCodeConfig(JsonUtils.readValue(keyPropertyConfigItem.get(PropertyKey.TRACE_CODE_CONFIG.getKey()), TraceCodeConfig.class))
                .build();

        return dispenseOpt.dispense();
    }

    /**
     * 更新发药单上的快递地址
     */
    public void updateDispensingVendorInfo(String dispensingSheetId, String clinicId, UpdateVendorInfoReq clientReq, String operatorId) throws NoExistedSheetException {
        if (StringUtils.isEmpty(clientReq.getDispenseFormId())) {
            throw new ParamNotValidException("dispenseFormId");
        }
        DispensingSheet existedSheet = dispensingSheetRepository.findByIdAndClinicIdAndIsDeleted(dispensingSheetId, clinicId, 0).orElse(null);
        if (existedSheet == null) {
            throw new NoExistedSheetException();
        }
        DispensingForm updateForm = existedSheet.getDispensingForms().stream().filter(it -> it.getId().compareTo(clientReq.getDispenseFormId()) == 0).findAny().orElse(null);
        if (updateForm == null) {
            throw new NoExistedSheetException();
        }
        /**
         *
         * */
        if (existedSheet.getStatus() != DispenseConst.Status.WAITING) {
            throw new DispensingServiceException(DispensingServiceError.DISPENSE_CANNOT_MODIFY_VENDORID);
        }
        if (DispensingUtils.compareLongEqual(updateForm.getVendorId(), Long.parseLong(clientReq.getVendorId()))) {
            throw new ParamNotValidException("vendorId相同无需修改");
        }
        // 查询之前老数据
        ChargeSheet chargeSheet = chargeService.getChargeSheetById(existedSheet.getSourceSheetId());
        if (Objects.isNull(chargeSheet)) {
            throw new NoExistedSheetException();
        }

        SheetOperationBase operationBase = sheetOperationCreateFactory.createSheetOperation(DispensingSheetOperation.OperationType.UPDATE_VENDOR, dispensingSheetId, operatorId, operatorId, existedSheet.getChainId(), existedSheet.getClinicId());
        operationBase.bindVendorInfo(updateForm.getVendorId(), updateForm.getVendorName());
        updateForm.setVendorId(Long.parseLong(clientReq.getVendorId()));
        updateForm.setVendorName(clientReq.getVendorName());
        updateForm.setLastModified(Instant.now());
        updateForm.setLastModifiedBy(clientReq.getEmployeeId());
        operationBase.bindNewVendorInfo(updateForm.getVendorId(), updateForm.getVendorName());

        if (updateForm != null) {
            try {
                UpdateChargeFormVendorInfoReq chargeRpcReq = new UpdateChargeFormVendorInfoReq();
                chargeRpcReq.setId(updateForm.getSourceFormId());//收费formId
                chargeRpcReq.setChainId(existedSheet.getChainId());
                chargeRpcReq.setClinicId(updateForm.getClinicId());
                if (updateForm.getVendorId() != null) {
                    chargeRpcReq.setVendorId(updateForm.getVendorId().toString());
                }
                chargeRpcReq.setVendorName(updateForm.getVendorName());
                chargeRpcReq.setEmployeeId(clientReq.getEmployeeId());
                FeignClientRpcTemplate.dealRpcClientMethod("updateFormVendorInfo", true,
                        () -> abcCisChargeFeignClient.updateFormVendorInfo(chargeRpcReq),
                        chargeRpcReq
                );
            } catch (Exception exp) {
                sLogger.info("修改ChargeFormVendor异常:exp={}", exp);
            }
            ChargeForm chargeForm = chargeSheet.getChargeForms().stream().filter(it -> it.getId().compareTo(updateForm.getSourceFormId()) == 0).findAny().orElse(null);
            if (chargeForm != null) {
                try {
                    UpdateOutpatientFormVendorInfoReq outRpcReq = new UpdateOutpatientFormVendorInfoReq();
                    outRpcReq.setId(chargeForm.getSourceFormId());//门诊FormId
                    outRpcReq.setChainId(existedSheet.getChainId());
                    outRpcReq.setClinicId(updateForm.getClinicId());
                    if (updateForm.getVendorId() != null) {
                        outRpcReq.setVendorId(updateForm.getVendorId().toString());
                    }
                    outRpcReq.setVendorName(updateForm.getVendorName());
                    outRpcReq.setEmployeeId(clientReq.getEmployeeId());
                    FeignClientRpcTemplate.dealRpcClientMethod("updateFormVendorInfo", true,
                            () -> abcCisOutpatientFeignClient.updateFormVendorInfo(outRpcReq),
                            outRpcReq
                    );
                } catch (Exception exp) {
                    sLogger.info("修改OutPatientFormVendor异常:exp={}", exp);
                }
            }
        }

        /**
         * 记录发药单修改快递规则的Opearton
         * */
        operationBase.createAndSaveSheetOperation();
    }

    /**
     * 更新发药单上的快递地址
     */
    public void updateDispensingSheetDeliveryInfo(String dispensingSheetId, String clinicId, DispensingDeliveryInfo deliveryInfo, String operatorId) throws NoExistedSheetException {
        DispensingSheet existedSheet = dispensingSheetRepository.findByIdAndClinicIdAndIsDeleted(dispensingSheetId, clinicId, 0).orElse(null);
        if (existedSheet == null) {
            throw new NoExistedSheetException();
        }
        // 查询之前老数据
        ChargeSheet chargeSheet = chargeService.getChargeSheetById(existedSheet.getSourceSheetId());
        if (Objects.isNull(chargeSheet)) {
            throw new NoExistedSheetException();
        }

        ChargeDeliveryInfo oldDeliveryInfo = null;
        //先更新快递单号
        if (deliveryInfo != null && existedSheet.getDeliveryType() == DispensingSheet.DeliveryType.DELIVERY_TO_HOME) {
            ChargeDeliveryInfoView chargeDeliveryInfoView = null;
            if (existedSheet.getPharmacyType() == GoodsConst.PharmacyType.LOCAL_PHARMACY) {
                chargeDeliveryInfoView = chargeService.updateDeliveryByChargeSheetId(existedSheet.getChainId(), existedSheet.getClinicId(), operatorId, existedSheet.getSourceSheetId(), deliveryInfo);
                /**
                 * 本地药房才智能发药
                 * */
                updateSmartDispensingDeliveryInfo(dispensingSheetId, clinicId, deliveryInfo);
                oldDeliveryInfo = chargeSheet.getDeliveryInfo();
            } else if (existedSheet.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY) {
                /***
                 * 代煎代配的发药单 要更新chargeform上的快递地址
                 * */
                chargeDeliveryInfoView = chargeService.updateDeliveryByChargeFormId(existedSheet.getChainId(), existedSheet.getClinicId(), operatorId, existedSheet, deliveryInfo);
                /**
                 * 代煎代配药房 一个form 拆了一个发药单
                 * */
                if (!CollectionUtils.isEmpty(existedSheet.getDispensingForms())) {
                    DispensingForm dispensingForm = existedSheet.getDispensingForms().get(0);
                    ChargeForm chargeForm = chargeSheet.getChargeForms().stream().filter(c -> c.getId().compareTo(dispensingForm.getSourceFormId()) == 0).findFirst().orElse(null);
                    /***
                     * 1.代煎代配form的快递信息在chargeForm上
                     * 2.构造的 ChargeDeliveryInfo 只设置需要写日志的字段
                     * */
                    if (chargeForm != null && chargeForm.getChargeAirPharmacyLogistics() != null) {
                        oldDeliveryInfo = DispensingUtils.transChargeLogisticsToChargeDelivery(chargeForm);
                    }
                }
            }

            /**
             * 修改发药单快递地址
             * */
            hamqProducer.sendDispensingSheetUpdate(PatientOrderMessage.MSG_SUB_TYPE_DISPENGING_UPDATED_INFO, existedSheet, operatorId);


            /**
             * 记录发药单修改快递规则的Opearton
             * */
            SheetOperationBase operationBase = sheetOperationCreateFactory.createSheetOperation(DispensingSheetOperation.OperationType.UPDATE_DELIVERY, dispensingSheetId, operatorId, operatorId, existedSheet.getChainId(), existedSheet.getClinicId());
            operationBase.bindNewDeliveryInfo(chargeDeliveryInfoView)
                    .bindDeliveryInfo(oldDeliveryInfo)
                    .bindDeliveryCompanyName(deliveryInfo.getDeliveryCompanyName());
            operationBase.createAndSaveSheetOperation();
        }
    }

    /**
     * 本地药房才智能发药
     */
    private void updateSmartDispensingDeliveryInfo(String dispensingSheetId, String clinicId, DispensingDeliveryInfo deliveryInfo) {
        SmartDispensing smartDispensing = getSmartDispensing(clinicId);
        if (smartDispensing == null || smartDispensing.getIsSmartDispensing() == 0) {
            return;
        }
        smartDispensingService.updateDeliveryInfo(dispensingSheetId, deliveryInfo);
    }

    /**
     * 发送快递发药微信消息模版
     *
     * @param chargeSheet
     * @return
     */
    public ToCMessage generateDispensingDrugToHomeMessage(ChargeSheet chargeSheet, List<DispensingSheet> dispensingSheets, String patientId) {
        OrganPrintView organPrintInfo = clinicClient.getOrganPrintInfo(chargeSheet.getClinicId());
        WeChatMessageBody.DispensingDrugToHomeMsg.DispensingDrugToHomeData dispensingDrugToHomeData = new WeChatMessageBody.DispensingDrugToHomeMsg.DispensingDrugToHomeData();
        String productInfo = "";
        if (!CollectionUtils.isEmpty(dispensingSheets)) {
            AtomicInteger chineseCount = new AtomicInteger();
            AtomicInteger westernCount = new AtomicInteger();
            AtomicInteger infusionCount = new AtomicInteger();

            dispensingSheets.stream()
                    .filter(dispensingSheet -> !CollectionUtils.isEmpty(dispensingSheet.getDispensingForms()))
                    .flatMap(dispensingSheet -> dispensingSheet.getDispensingForms().stream())
                    .filter(dispensingForm -> !CollectionUtils.isEmpty(dispensingForm.getDispensingFormItems()))
                    .forEach(dispensingForm -> {
                        int count = (int) dispensingForm.getDispensingFormItems().stream().filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.MEDICINE)
                                .count();
                        if (dispensingForm.getSourceFormType() == DispensingForm.SourceFormType.PRESCRIPTION_CHINESE) {
                            chineseCount.addAndGet(count);
                        }
                        if (dispensingForm.getSourceFormType() == DispensingForm.SourceFormType.PRESCRIPTION_WESTERN) {
                            westernCount.addAndGet(count);
                        }
                        if (dispensingForm.getSourceFormType() == DispensingForm.SourceFormType.PRESCRIPTION_INFUSION) {
                            infusionCount.addAndGet(count);
                        }
                    });

            StringBuilder sb = new StringBuilder();
            if (chineseCount.get() > 0) {
                sb.append(String.format("中药%d", chineseCount.get())).append("，");
            }
            if (westernCount.get() > 0) {
                sb.append(String.format("西药%d种", westernCount.get())).append("，");
            }
            if (infusionCount.get() > 0) {
                sb.append(String.format("输液%d种", infusionCount.get())).append("，");
            }
            productInfo = sb.toString();
            if (productInfo.endsWith("，")) {
                productInfo = productInfo.substring(0, productInfo.length() - 1);
            }
        }
        String expressCompany = Objects.nonNull(chargeSheet.getDeliveryInfo().getDeliveryCompany()) ? chargeSheet.getDeliveryInfo().getDeliveryCompany().getName() : "";
        dispensingDrugToHomeData.setTips("您于" + DateUtils.convertInstantToString(chargeSheet.getFirstChargedTime(), DateUtils.WECHAT_TIME_FORMAT) + "购买的药品已发货，请关注物流信息。");
        dispensingDrugToHomeData.setProductInfo(productInfo);
        dispensingDrugToHomeData.setStatus("已发货");
        dispensingDrugToHomeData.setExpressCompany(expressCompany);
        dispensingDrugToHomeData.setExpressNo(chargeSheet.getDeliveryInfo().getDeliveryOrderNo());
        StringBuilder addressBuilder = new StringBuilder();
        addressBuilder.append("若有任何问题请及时联系医疗结构");
        if (Objects.nonNull(organPrintInfo)) {
            String address = organPrintInfo.getAddressDistrictName() + organPrintInfo.getAddressDetail();
            if (StringUtils.isNotBlank(address)) {
                addressBuilder.append("，机构地址【").append(address).append("】");
            }
            if (StringUtils.isNotBlank(organPrintInfo.getContactPhone())) {
                addressBuilder.append("，电话【").append(organPrintInfo.getContactPhone()).append("】。");
            }
        }
        dispensingDrugToHomeData.setRemark(addressBuilder.toString());
        dispensingDrugToHomeData.setOrderTime(DateUtils.convertInstantToString(chargeSheet.getFirstChargedTime(), DateUtils.WECHAT_PAY_SUCCESS_TIME_FORMAT));
        dispensingDrugToHomeData.setDispensingTime(DateUtils.convertInstantToString(Instant.now(), DateUtils.WECHAT_PAY_SUCCESS_TIME_FORMAT));

        WeChatMessageBody.DispensingDrugToHomeMsg chargeOrderData = new WeChatMessageBody.DispensingDrugToHomeMsg();
        chargeOrderData.setData(dispensingDrugToHomeData);
        chargeOrderData.setUrl(String.format("customerOrder/%s", chargeSheet.getId()));

        WeChatMessageBody weChatMessageBody = new WeChatMessageBody();
        weChatMessageBody.setData(cn.abcyun.cis.core.util.JsonUtils.dumpAsJsonNode(chargeOrderData));
        weChatMessageBody.setType(WeChatMessageBody.VendorExpressRemindMsg.eventType);

        ToCMessage toCMessage = new ToCMessage();
        toCMessage.setMsgId(MD5Utils.sign(String.format("end-delivered-sms:%s", chargeSheet.getId())));
        toCMessage.setToPatients(Arrays.asList(patientId));
        toCMessage.setOperatorId(DispensingConstants.ANONYMOUS_PATIENT_ID);
        toCMessage.setChainId(chargeSheet.getChainId());
        toCMessage.setClinicId(chargeSheet.getClinicId());
        toCMessage.setCreated(new Date());
        toCMessage.setChannel(ToCMessage.MessageChannel.WeChat);
        toCMessage.setBody(cn.abcyun.cis.core.util.JsonUtils.dumpAsJsonNode(weChatMessageBody));

        return toCMessage;
    }

    /**
     * 发送快递发药短信模版
     *
     * @param patientId
     * @param chargeSheet
     * @return
     */
    public ToCMessage generateDispensingDrugToHomeSMSMessage(String patientId, ChargeSheet chargeSheet, String operatorId) {
        SmsMessageBody smsMessageBody = new SmsMessageBody();
        SmsMessageBody.DispensingDrugExpressData dispensingDrugExpressData = new SmsMessageBody.DispensingDrugExpressData();
        String expressCompany = Objects.nonNull(chargeSheet.getDeliveryInfo().getDeliveryCompany()) ? chargeSheet.getDeliveryInfo().getDeliveryCompany().getName() : "";
        dispensingDrugExpressData.setDate(DateUtils.convertInstantToString(chargeSheet.getFirstChargedTime(), DateUtils.WECHAT_TIME_FORMAT));
        dispensingDrugExpressData.setExpressNo(chargeSheet.getDeliveryInfo().getDeliveryOrderNo());
        dispensingDrugExpressData.setExpressName(expressCompany);

        boolean noPhone = false;
        OrganPrintView organPrintInfo = clinicClient.getOrganPrintInfo(chargeSheet.getClinicId());
        if (Objects.nonNull(organPrintInfo) && !StringUtils.isEmpty(organPrintInfo.getContactPhone())) {
            dispensingDrugExpressData.setPhone(organPrintInfo.getContactPhone());
        } else {
            noPhone = true;
        }

        smsMessageBody.setData(cn.abcyun.cis.core.util.JsonUtils.dumpAsJsonNode(dispensingDrugExpressData));
        smsMessageBody.setType(SmsMessageBody.DispensingDrugExpressData.type);
        smsMessageBody.setBillStatus(0);
        if (noPhone) {
            smsMessageBody.setType(SmsMessageType.DISPENSING_DRUG_EXPRESS_NO_PHONE);
        }

        ToCMessage toCMessage = new ToCMessage();
        toCMessage.setMsgId(MD5Utils.sign(String.format("send-delivered-sms:%s", patientId)));
        toCMessage.setToPatients(new ArrayList<>());
        toCMessage.getToPatients().add(patientId);
        toCMessage.setOperatorId(operatorId);
        toCMessage.setChainId(chargeSheet.getChainId());
        toCMessage.setClinicId(chargeSheet.getClinicId());
        toCMessage.setCreated(new Date());
        toCMessage.setChannel(ToCMessage.MessageChannel.SMS);
        toCMessage.setBody(cn.abcyun.cis.core.util.JsonUtils.dumpAsJsonNode(smsMessageBody));
        return toCMessage;
    }

    /**
     * 发送自取微信消息模版
     */
    public ToCMessage generateAndSendDispensingDrugSelfCollectionMessage(ChargeSheet chargeSheet,
                                                                         String patientName,
                                                                         int sourceFormType,
                                                                         List<DispensingFormItem> dispensingFormItems,
                                                                         String patientId,
                                                                         String pharmacyName) {
        OrganPrintView organPrintInfo = clinicClient.getOrganPrintInfo(chargeSheet.getClinicId());
        WeChatMessageBody.DispensingDrugMsg.DispensingDrugData dispensingDrugData = new WeChatMessageBody.DispensingDrugMsg.DispensingDrugData();
        dispensingDrugData.setTips("您于" + DateUtils.convertInstantToString(chargeSheet.getFirstChargedTime(), DateUtils.WECHAT_TIME_FORMAT) + "购买的药品已备好，请尽快前往" + pharmacyName + "取药。");
        dispensingDrugData.setDateTime(DateUtils.convertInstantToString(Instant.now(), DateUtils.WECHAT_TIME_FORMAT));
        dispensingDrugData.setPatientName(patientName);

        int chineseCount = 0;
        int westernCount = 0;
        int infusionCount = 0;
        if (!CollectionUtils.isEmpty(dispensingFormItems)) {
            long count = dispensingFormItems.stream().filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.MEDICINE)
                    .filter(it -> StringUtils.isEmpty(it.getAssociateFormItemId()))
                    .map(DispensingFormItem::getProductId).distinct()
                    .count();
            if (sourceFormType == DispensingForm.SourceFormType.PRESCRIPTION_CHINESE) {
                chineseCount += count;
            }
            if (sourceFormType == DispensingForm.SourceFormType.PRESCRIPTION_WESTERN) {
                westernCount += count;
            }
            if (sourceFormType == DispensingForm.SourceFormType.PRESCRIPTION_INFUSION) {
                infusionCount += count;
            }
        }
        StringBuilder remarkBuilder = new StringBuilder();
        remarkBuilder.append((chineseCount > 0 ? String.format("中药%d味，", chineseCount) : "") + (westernCount > 0 ? String.format("西药%d种，", westernCount) : "") + (infusionCount > 0 ? String.format("输液%d种，", infusionCount) : ""));
        String remark = remarkBuilder.length() > 0 ? remarkBuilder.deleteCharAt(remarkBuilder.length() - 1).toString() : "";
        dispensingDrugData.setGoods(remark);

        StringBuilder addressBuilder = new StringBuilder();
        addressBuilder.append("若有任何问题请及时联系医疗机构");
        if (Objects.nonNull(organPrintInfo)) {
            String address = organPrintInfo.getAddressDistrictName() + organPrintInfo.getAddressDetail();
            if (StringUtils.isNotBlank(address)) {
                addressBuilder.append("，机构地址【" + address + "】");
            }
            if (StringUtils.isNotBlank(organPrintInfo.getContactPhone())) {
                addressBuilder.append("，电话【" + organPrintInfo.getContactPhone() + "】。");
            }
        }

        dispensingDrugData.setRemark(addressBuilder.toString());

        WeChatMessageBody.DispensingDrugMsg dispensingDrugMsg = new WeChatMessageBody.DispensingDrugMsg();
        dispensingDrugMsg.setData(dispensingDrugData);
        dispensingDrugMsg.setUrl(String.format("customerOrder/%s", chargeSheet.getId()));

        WeChatMessageBody weChatMessageBody = new WeChatMessageBody();
        weChatMessageBody.setData(cn.abcyun.cis.core.util.JsonUtils.dumpAsJsonNode(dispensingDrugMsg));
        weChatMessageBody.setType(WeChatMessageBody.DispensingDrugMsg.eventType);

        ToCMessage toCMessage = new ToCMessage();
        toCMessage.setMsgId(MD5Utils.sign(String.format("end-delivered-sms:%s", chargeSheet.getId())));
        toCMessage.setToPatients(Arrays.asList(patientId));
        toCMessage.setOperatorId(DispensingConstants.ANONYMOUS_PATIENT_ID);
        toCMessage.setChainId(chargeSheet.getChainId());
        toCMessage.setClinicId(chargeSheet.getClinicId());
        toCMessage.setCreated(new Date());
        toCMessage.setChannel(ToCMessage.MessageChannel.WeChat);
        toCMessage.setBody(cn.abcyun.cis.core.util.JsonUtils.dumpAsJsonNode(weChatMessageBody));

        rocketMqProducer.rocketMqNotifyToCMessage(toCMessage);
        return toCMessage;
    }


    /**
     * 发送自取短信消息模版
     *
     * @param chargeSheet
     * @return
     */
    public ToCMessage generateAndSendDispensingDrugSelfCollectionSMSMessage(ChargeSheet chargeSheet,
                                                                            String operatorId, String pharmacyName) {
        SmsMessageBody smsMessageBody = new SmsMessageBody();
        SmsMessageBody.DispensingDrugTakeData dispensingDrugTakeData = new SmsMessageBody.DispensingDrugTakeData();
        dispensingDrugTakeData.setDate(DateUtils.convertInstantToString(chargeSheet.getFirstChargedTime(), DateUtils.WECHAT_TIME_FORMAT));
        OrganPrintView organPrintInfo = clinicClient.getOrganPrintInfo(chargeSheet.getClinicId());
        boolean noPhone = false;
        if (Objects.nonNull(organPrintInfo) && !StringUtils.isEmpty(organPrintInfo.getContactPhone())) {
            dispensingDrugTakeData.setPhone(organPrintInfo.getContactPhone());
        } else {
            noPhone = true;
        }
        dispensingDrugTakeData.setPharmacyName(pharmacyName);

        smsMessageBody.setData(cn.abcyun.cis.core.util.JsonUtils.dumpAsJsonNode(dispensingDrugTakeData));
        smsMessageBody.setType(SmsMessageType.DISPENSING_DRUG_XW);
        if (noPhone) {
            smsMessageBody.setType(SmsMessageType.DISPENSING_DRUG_NO_PHONE_XW);
        }
        smsMessageBody.setBillStatus(0);

        ToCMessage toCMessage = new ToCMessage();
        toCMessage.setMsgId(MD5Utils.sign(String.format("send-delivered-sms:%s", chargeSheet.getPatientId())));
        toCMessage.setToPatients(new ArrayList<>());
        toCMessage.getToPatients().add(chargeSheet.getPatientId());
        toCMessage.setOperatorId(operatorId);
        toCMessage.setChainId(chargeSheet.getChainId());
        toCMessage.setClinicId(chargeSheet.getClinicId());
        toCMessage.setCreated(new Date());
        toCMessage.setChannel(ToCMessage.MessageChannel.SMS);
        toCMessage.setBody(cn.abcyun.cis.core.util.JsonUtils.dumpAsJsonNode(smsMessageBody));
        rocketMqProducer.rocketMqNotifyToCMessage(toCMessage);
        return toCMessage;
    }

    @Transactional
    public CloseSheetResult closeDispensingSheet(String id, String clinicId, String operatorId) {
        DispensingSheet dispensingSheet = dispensingSheetEntityService.findByIdAndClinicId(id, clinicId);
        if (dispensingSheet == null) {
            throw new NoExistedSheetException();
        }

        if (!dispensingSheet.canClose()) {
            throw new WrongSheetStatusException();
        }
        DispensingTodoService.DispensingSheetStatus beforeDispensingSheetStatus = DispensingTodoService.DispensingSheetStatus.of(dispensingSheet);

        Map<String, List<TraceableCode>> dispensingFormItemIdToCleanTraceableCodeList = updateDispensingSheetByClose(dispensingSheet, operatorId);

        // 记录操作记录
        SheetOperationBase operationBase = sheetOperationCreateFactory.createSheetOperation(DispensingSheetOperation.OperationType.CLOSE_SHEET, Collections.singletonList(id), operatorId, operatorId, dispensingSheet.getChainId(), dispensingSheet.getClinicId());
        operationBase.createAndSaveSheetOperation();

        // 关闭发药单解锁-需要解锁库存
        goodsLockingFeignClient.chargeTryUnLockGoodsStock(
                dispensingSheet.getChainId(),
                dispensingSheet.getClinicId(),
                operatorId,
                Collections.singletonList(dispensingSheet),
                YesOrNo.NO
        );
        // 退回追溯码
        abcCisScGoodsService.unUseAllTraceCode(dispensingSheet, dispensingFormItemIdToCleanTraceableCodeList, operatorId);
        hamqProducer.sendDispensingSheetUpdate(PatientOrderMessage.MSG_SUB_TYPE_DISPENGING_UPDATED_INFO, dispensingSheet, operatorId);

        List<String> subKeyList = new ArrayList<>();
        boolean sendTodoMessage = dispensingTodoService.getWaitingTodoSubKey(dispensingSheet.getOrderByDate(),
                dispensingSheet.getIsOnline(), dispensingSheet.getDeliveredStatus(), dispensingSheet.getProcessedStatus(),
                subKeyList, DispensingTodoService.Action.CLOSE, beforeDispensingSheetStatus, DispensingTodoService.DispensingSheetStatus.of(dispensingSheet));
        RocketMqProducer.doAfterTransactionCommit(() -> {
            dispensingTodoService.addDecPharmacyTodoCountTask(dispensingSheet.getChainId(), clinicId, dispensingSheet.getPharmacyNo(), operatorId, subKeyList, sendTodoMessage);
            dispensingSheetListeners.forEach(dispensingSheetListener -> {
                dispensingSheetListener.onDispensingSheetClosedAfterCommit(dispensingSheet.getChainId(), dispensingSheet.getClinicId(), dispensingSheet, operatorId);
            });
        });

        CloseSheetResult closeSheetResult = new CloseSheetResult();
        closeSheetResult.setId(dispensingSheet.getId());
        closeSheetResult.setStatus(dispensingSheet.getStatus());
        return closeSheetResult;
    }

    private Map<String, List<TraceableCode>> updateDispensingSheetByClose(DispensingSheet dispensingSheet, String operatorId) {
        if (dispensingSheet == null) {
            return new HashMap<>();
        }

        dispensingSheet.setStatus(DispensingSheet.Status.CLOSED);
        FillUtils.fillLastModifiedBy(dispensingSheet, operatorId);

        // 关闭发药单，需要清空掉追溯码
        Map<String, List<TraceableCode>> cleanTraceableCodeList = new HashMap<>();
        DispensingUtils.doWithDispensingItem(dispensingSheet, item -> {
            if (CollectionUtils.isEmpty(item.getTraceableCodeList())) {
                return;
            }

            cleanTraceableCodeList.computeIfAbsent(item.getId(), k -> new ArrayList<>()).addAll(item.getTraceableCodeList());
            item.setTraceableCodeList(null);
        });

        return cleanTraceableCodeList;
    }

    @Transactional
    public ReopenSheetResult reopenDispensingSheet(String id, String clinicId, String operatorId) {
        DispensingSheet existedSheet = dispensingSheetEntityService.findByIdAndClinicId(id, clinicId);
        if (existedSheet == null) {
            throw new NoExistedSheetException();
        }

        if (!existedSheet.canReopen()) {
            throw new WrongSheetStatusException();
        }

        // 重新上锁
        goodsLockingFeignClient.tryLockByDispensingSheets(
                existedSheet.getChainId(),
                existedSheet.getClinicId(),
                operatorId,
                YesOrNo.YES,
                Collections.singletonList(existedSheet)
        );

        DispensingTodoService.DispensingSheetStatus beforeDispensingSheetStatus = DispensingTodoService.DispensingSheetStatus.of(existedSheet);

        existedSheet.setStatus(DispensingSheet.Status.WAITING);
        FillUtils.fillLastModifiedBy(existedSheet, operatorId);
        hamqProducer.sendDispensingSheetUpdate(PatientOrderMessage.MSG_SUB_TYPE_DISPENGING_UPDATED_INFO, existedSheet, operatorId);

        // 记录发药单操作记录
        SheetOperationBase operationBase = sheetOperationCreateFactory.createSheetOperation(DispensingSheetOperation.OperationType.REOPEN_SHEET, Collections.singletonList(id), operatorId, operatorId, existedSheet.getChainId(), existedSheet.getClinicId());
        operationBase.createAndSaveSheetOperation();

        // 更新待发数量
        List<String> subKeyList = new ArrayList<>();
        boolean sendTodoMessage = dispensingTodoService.getWaitingTodoSubKey(existedSheet.getOrderByDate(), existedSheet.getIsOnline(),
                existedSheet.getDeliveredStatus(), existedSheet.getProcessedStatus(), subKeyList,
                DispensingTodoService.Action.OPEN, beforeDispensingSheetStatus, DispensingTodoService.DispensingSheetStatus.of(existedSheet));
        RocketMqProducer.doAfterTransactionCommit(() -> {
            dispensingTodoService.addIncPharmacyTodoCountTask(existedSheet.getChainId(),
                    existedSheet.getClinicId(), existedSheet.getPharmacyNo(), operatorId, subKeyList, sendTodoMessage);
            dispensingSheetListeners.forEach(dispensingSheetListener -> {
                dispensingSheetListener.onDispensingSheetReopenedAfterCommit(existedSheet.getChainId(), existedSheet.getClinicId(), existedSheet, operatorId);
            });
        });

        ReopenSheetResult reopenSheetResult = new ReopenSheetResult();
        BeanUtils.copyProperties(existedSheet, reopenSheetResult);
        return reopenSheetResult;
    }

    @Transactional
    public void handlePatientMergeTask(PatientMergeTaskMessage task) {
        if (task == null || TextUtils.isEmpty(task.getTaskId())) {
            return;
        }

        rocketMqProducer.rocketMqNotifyPatientMergeReport(task.getTaskId(), PatientMergeTaskStatus.DOING);

        List<String> srcPatientIds = task.getSourceIds();
        if (srcPatientIds == null || srcPatientIds.size() == 0 || TextUtils.isEmpty(task.getDestinationId()) || TextUtils.isEmpty(task.getChainId())) {
            rocketMqProducer.rocketMqNotifyPatientMergeReport(task.getTaskId(), PatientMergeTaskStatus.SUCCESS);
            return;
        }

        dispensingMapper.mergeDispensingSheetPatientId(task.getChainId(), srcPatientIds, task.getDestinationId());
        dispensingMapper.mergeDispensingFormPatientId(task.getChainId(), srcPatientIds, task.getDestinationId());
        dispensingMapper.mergeDispensingFormItemPatientId(task.getChainId(), srcPatientIds, task.getDestinationId());
        dispensingCallingItemService.mergeDispensingCallingItem(task.getChainId(), srcPatientIds, task.getDestinationId());

        rocketMqProducer.rocketMqNotifyPatientMergeReport(task.getTaskId(), PatientMergeTaskStatus.SUCCESS);
    }

    /**
     * rpc补推智能发药单
     * 不检查开关啥的了，直接推
     */
    public Integer rpcRePushSmartDispensing(String dispensingSheetId) {
        if (StringUtils.isEmpty(dispensingSheetId)) {
            return -1;
        }
        DispensingSheet dispensingSheet = dispensingSheetRepository.findById(dispensingSheetId).orElse(null);
        if (dispensingSheet == null) {
            return -2;
        }
        /**
         * 智能发药的开关要开
         * */
        SmartDispensing smartDispensing = getSmartDispensing(dispensingSheet.getClinicId());
        if (smartDispensing == null || smartDispensing.getIsSmartDispensing() == DispensingUtils.SwitchFlag.OFF) {
            return -4;
        }

        /**
         * 加个保护，只有指定了药房类型（不指定就是本地药房）才能智能发药
         * */
        List<Integer> supportPharmacyTypes = smartDispensing.getSupportPharmacyTypes();
        if (CollectionUtils.isEmpty(smartDispensing.getSupportPharmacyTypes())) {
            supportPharmacyTypes = Collections.singletonList(GoodsConst.PharmacyType.LOCAL_PHARMACY);
        }
        if (!(supportPharmacyTypes.contains(dispensingSheet.getPharmacyType()))) {
            sLogger.warn("[智能发药]dispensingSheet dispensingSheet.getPharmacyType={} 不支持智能发药", dispensingSheet.getPharmacyType());
            return -3;
        }

        ChargeSheet chargeSheet = chargeService.getChargeSheetById(dispensingSheet.getSourceSheetId());
        if (chargeSheet == null) {
            return -5;
        }

        //获取云煎药服务配置 推送唐古也要写表 推给其他服务或者前端
        if (dispensingSheet.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY) {
            GoodsSupplierView cloudDecoctionSupplier = scGoodsFeignClient.getCloudDecoctionSupplier(dispensingSheet.getChainId(), dispensingSheet.getClinicId());
            boolean isSupportCloudDecoction = DispensingUtils.isSupportCloudDecoction(cloudDecoctionSupplier, dispensingSheet);
            if (isSupportCloudDecoction) {
                smartDispensingService.dispensingCloudDecoction(dispensingSheet, chargeSheet,
                        DispensingUtils.getDispensingFormItems(dispensingSheet), dispensingSheet.getLastModifiedBy(), cloudDecoctionSupplier);
                return 0;
            }
        }

        smartDispensingService.dispensing(dispensingSheet, chargeSheet, DispensingUtils.getDispensingFormItems(dispensingSheet), smartDispensing.getIsAllChineseMedicine(), dispensingSheet.getLastModifiedBy(), DispensingUtils.SmartDispensingScene.DISPENSE);
        return 0;

    }

    /**
     * 根据开关决定是否要在事务结束后开始z发药
     *
     * @param dispensingSheet             发药单
     * @param chargeSheetSupplier         发药单对应的收费单
     * @param thisTimeDispensingFormItems 本次发药的formItem
     * @param operatorId                  发药人
     * @param smartDispensingScene        当前智能发药的场景 {@link cn.abcyun.cis.dispensing.util.DispensingUtils.SmartDispensingScene}
     */
    public void smartDispensing(DispensingSheet dispensingSheet, Supplier<ChargeSheet> chargeSheetSupplier, List<DispensingFormItem> thisTimeDispensingFormItems, String operatorId, int smartDispensingScene) {
        sLogger.info("智能发药请求参数:smartDispensing thisTimeDispensingFormItems={},smartDispensingScene={}", thisTimeDispensingFormItems == null ? 0 : thisTimeDispensingFormItems.size(), smartDispensingScene);
        if (dispensingSheet == null) {
            sLogger.warn("[智能发药]dispensingSheet is null nos send to smartDispense");
            return;
        }

        //获取云煎药服务配置 推送唐古也要写表 推给其他服务或者前端
        boolean isSupportCloudDecoction = false;
        if (smartDispensingScene == DispensingUtils.SmartDispensingScene.DISPENSE) {
            GoodsSupplierView cloudDecoctionSupplier;
            if (dispensingSheet.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY) {
                cloudDecoctionSupplier = scGoodsFeignClient.getCloudDecoctionSupplier(dispensingSheet.getChainId(), dispensingSheet.getClinicId());
                isSupportCloudDecoction = DispensingUtils.isSupportCloudDecoction(cloudDecoctionSupplier, dispensingSheet);
            } else {
                cloudDecoctionSupplier = null;
            }

            if (isSupportCloudDecoction) {
                RocketMqProducer.doAfterTransactionCommit(() -> smartDispensingService.dispensingCloudDecoction(dispensingSheet, chargeSheetSupplier.get(),
                        thisTimeDispensingFormItems, operatorId, cloudDecoctionSupplier));
                return;
            }
        }

        /**
         * 总开关
         * */
        SmartDispensing smartDispensing = getSmartDispensing(dispensingSheet.getClinicId());
        if (smartDispensing == null || smartDispensing.getIsSmartDispensing() == DispensingUtils.SwitchFlag.OFF) {
//            sLogger.warn("[智能发药]dispensingSheet smartDispensing={}", smartDispensing);
            return;
        }

        /**
         * 加个保护，只有指定了药房类型（不指定就是本地药房）才能智能发药
         * */
        List<Integer> supportPharmacyTypes = smartDispensing.getSupportPharmacyTypes();
        if (CollectionUtils.isEmpty(smartDispensing.getSupportPharmacyTypes())) {
            supportPharmacyTypes = Collections.singletonList(GoodsConst.PharmacyType.LOCAL_PHARMACY);
        }
        if (!(supportPharmacyTypes.contains(dispensingSheet.getPharmacyType()))) {
            sLogger.warn("[智能发药]dispensingSheet dispensingSheet.getPharmacyType={} 不支持智能发药", dispensingSheet.getPharmacyType());
            return;
        }

        if (smartDispensing.getIsDispenseAfterCharge() == smartDispensingScene) {
            RocketMqProducer.doAfterTransactionCommit(() -> smartDispensingService.dispensing(dispensingSheet, chargeSheetSupplier.get(), thisTimeDispensingFormItems, smartDispensing.getIsAllChineseMedicine(), operatorId, smartDispensingScene));
        }
    }

    /**
     * 门店发药开关
     */
    private SmartDispensing getSmartDispensing(String clinicId) {
        SmartDispensing smartDispensing = (SmartDispensing) RequestContextHolderUtils.getRequestAttribute(PropertyKey.DISPENSING_SMARTDISPENSING.getKey());
        if (smartDispensing != null) {
            return smartDispensing;
        }

        try {
            smartDispensing = propertyService.getPropertyValueByKey(PropertyKey.DISPENSING_SMARTDISPENSING, clinicId, SmartDispensing.class);
            if (smartDispensing != null) {
                RequestContextHolderUtils.setRequestAttribute(PropertyKey.DISPENSING_SMARTDISPENSING.getKey(), smartDispensing);
            }
        } catch (Exception e) {
            sLogger.error("getSmartDispensing error", e);
        }
        return smartDispensing;
    }

    /**
     * 给门诊的rpc接口输出调配人信息
     */
    public DispensingSheetOperationRsp findDispensingSheetOperation(String patientOrderId, String chargeSheetId) {
        DispensingSheetOperationRsp dispensingSheetOperationRsp = new DispensingSheetOperationRsp();
        // 先获取所有的发药单
        List<DispensingSheet> dispensingSheets;
        if (StringUtils.isNotBlank(chargeSheetId)) {
            dispensingSheets = dispensingSheetEntityService.findAllBySourceSheetId(chargeSheetId);
//            dispensingSheets = dispensingSheetRepository.findAllBySourceSheetIdAndIsDeleted(chargeSheetId, 0);
        } else {
            dispensingSheets = dispensingSheetRepository.findAllByPatientOrderIdAndIsDeleted(patientOrderId, 0);
        }

        if (CollectionUtils.isEmpty(dispensingSheets)) {
            return dispensingSheetOperationRsp;
        }

        // 按照创建时间排序
        dispensingSheets.sort(Comparator.comparing(DispensingSheet::getCreated).reversed());
        DispensingSheet firstDispensingSheet = dispensingSheets.get(0);
        BeanUtils.copyProperties(firstDispensingSheet, dispensingSheetOperationRsp);
        if (StringUtils.isNotBlank(firstDispensingSheet.getDispensedBy())) {
            dispensingSheetOperationRsp.setDispensedBy(firstDispensingSheet.getDispensedBy());
            dispensingSheetOperationRsp.setDispensedByName(employeeService.findEmployeeNameNoExp(firstDispensingSheet.getDispensedBy(), firstDispensingSheet.getChainId()));
            dispensingSheetOperationRsp.setDispensedByHandSign(employeeService.findEmployeeHandSign(firstDispensingSheet.getDispensedBy(), firstDispensingSheet.getChainId()));
        }

        /**
         * 这个发药单的 审核 和 compound记录
         * */
        // 获取审核、调配、发药的操作记录
        List<String> dispensingSheetIds = ListUtils.extractUniqueProperty(dispensingSheets, DispensingSheet::getId);
        List<DispensingSheetOperation> dispensingSheetOperations = dispensingSheetOperationRepository.findAllByDispensingSheetIdInAndOperationTypeInAndIsDeleted(
                dispensingSheetIds,
                Arrays.asList(DispensingSheetOperation.OperationType.AUDIT, DispensingSheetOperation.OperationType.COMPOUND,
                        DispensingSheetOperation.OperationType.SEND_MEDICINE, DispensingSheetOperation.OperationType.RE_DISPENSE,
                        DispensingSheetOperation.OperationType.CANCEL_AUDIT, DispensingSheetOperation.OperationType.CANCEL_COMPOUND),
                DispensingUtils.DeleteFlag.NOT_DELETED);
        if (CollectionUtils.isEmpty(dispensingSheetOperations)) {
            return dispensingSheetOperationRsp;
        }
        dispensingSheetOperations.sort(Comparator.comparing(DispensingSheetOperation::getCreated).reversed().thenComparing(DispensingSheetOperation::getId).reversed());

        Map<String, DispensingFormPrintRsp> formIdToForm = new HashMap<>();
        // 绑定审核信息
        bindFormAuditInfo(formIdToForm, dispensingSheetOperations);
        // 绑定调配信息
        bindFormCompoundInfo(formIdToForm, dispensingSheetOperations);
        // 绑定发药信息
        bindFormDispenseInfo(formIdToForm, dispensingSheetOperations);

        // 绑定 sourceFormId
        Map<String, String> formIdToSourceFormId = dispensingSheets.stream()
                .map(DispensingSheet::getDispensingForms).flatMap(Collection::stream)
                .collect(Collectors.toMap(DispensingForm::getId, DispensingForm::getSourceFormId, (a, b) -> a));
        formIdToForm.forEach((formId, formPrintRsp) -> {
            formPrintRsp.setSourceFormId(formIdToSourceFormId.get(formId));
        });

        dispensingSheetOperationRsp.setForms(new ArrayList<>(formIdToForm.values()));
        return dispensingSheetOperationRsp;
    }

    private Map<String, DispensingFormPrintRsp> findCompoundAndAuditInfo(List<String> dispensingSheetIds) {
        if (CollectionUtils.isEmpty(dispensingSheetIds)) {
            return new HashMap<>();
        }

        List<DispensingSheetOperation> dispensingSheetOperations = dispensingSheetOperationRepository.findAllByDispensingSheetIdInAndOperationTypeInAndIsDeleted(
                dispensingSheetIds,
                Arrays.asList(DispensingSheetOperation.OperationType.AUDIT, DispensingSheetOperation.OperationType.COMPOUND,
                        DispensingSheetOperation.OperationType.SEND_MEDICINE, DispensingSheetOperation.OperationType.RE_DISPENSE,
                        DispensingSheetOperation.OperationType.CANCEL_AUDIT, DispensingSheetOperation.OperationType.CANCEL_COMPOUND),
                DispensingUtils.DeleteFlag.NOT_DELETED);
        if (CollectionUtils.isEmpty(dispensingSheetOperations)) {
            return new HashMap<>();
        }
        dispensingSheetOperations.sort(Comparator.comparing(DispensingSheetOperation::getCreated).reversed().thenComparing(DispensingSheetOperation::getId).reversed());

        Map<String, DispensingFormPrintRsp> formIdToForm = new HashMap<>();
        // 绑定审核信息
        bindFormAuditInfo(formIdToForm, dispensingSheetOperations);
        // 绑定调配信息
        bindFormCompoundInfo(formIdToForm, dispensingSheetOperations);
        // 绑定发药信息
        bindFormDispenseInfo(formIdToForm, dispensingSheetOperations);

        return formIdToForm;
    }

    private void bindFormDispenseInfo(Map<String, DispensingFormPrintRsp> formIdToForm, List<DispensingSheetOperation> dispensingSheetOperations) {
        if (CollectionUtils.isEmpty(dispensingSheetOperations)) {
            return;
        }

        ArrayList<DispensingSheetOperation> sortedDispensingSheetOperations = new ArrayList<>(dispensingSheetOperations);
        sortedDispensingSheetOperations.sort(Comparator.comparing(DispensingSheetOperation::getCreated).reversed());
        for (DispensingSheetOperation dispensingSheetOperation : sortedDispensingSheetOperations) {
            if (dispensingSheetOperation.getOperationType() == DispensingSheetOperation.OperationType.RE_DISPENSE) {
                // 重新发药前的发药信息直接忽略
                break;
            }

            if (dispensingSheetOperation.getOperationType() != DispensingSheetOperation.OperationType.SEND_MEDICINE) {
                continue;
            }

            OperationRecordForDispense operationRecordForDispense = JsonUtils.readValue(dispensingSheetOperation.getRecordJson(), OperationRecordForDispense.class);
            if (operationRecordForDispense == null || (StringUtils.isEmpty(operationRecordForDispense.getDispensingName())
                    && StringUtils.isEmpty(dispensingSheetOperation.getOperatorName()))
                    || CollectionUtils.isEmpty(operationRecordForDispense.getFormRecordViews())) {
                return;
            }

            String dispenseById;
            String dispenseByName;
            String dispenseByHandSign;
            if (StringUtils.isNotBlank(operationRecordForDispense.getDispensingName())) {
                // 发药多人取第一个
                dispenseById = CollectionUtils.isEmpty(operationRecordForDispense.getDispensedByIds()) ? "" : operationRecordForDispense.getDispensedByIds().get(0);
                dispenseByName = operationRecordForDispense.getDispensingName().split(",")[0];
                dispenseByHandSign = employeeService.findEmployeeHandSign(dispenseById, dispensingSheetOperation.getChainId());
            } else {
                dispenseById = dispensingSheetOperation.getOperatorId();
                dispenseByName = dispensingSheetOperation.getOperatorName();
                dispenseByHandSign = employeeService.findEmployeeHandSign(dispensingSheetOperation.getOperatorId(), dispensingSheetOperation.getChainId());
            }

            List<DispensingFormRecordViewForDispense> dispenseForms = operationRecordForDispense.getFormRecordViews();
            dispenseForms.forEach(dispenseForm -> {
                DispensingFormPrintRsp formPrintRsp = formIdToForm.computeIfAbsent(dispenseForm.getId(), k -> new DispensingFormPrintRsp());
                if (StringUtils.isNotBlank(formPrintRsp.getDispensedBy())) {
                    return;
                }

                formPrintRsp.setDispensedBy(dispenseById);
                formPrintRsp.setDispensedByName(dispenseByName);
                formPrintRsp.setDispensedByHandSign(dispenseByHandSign);
                formPrintRsp.setDispenseTime(dispensingSheetOperation.getCreated());
            });
        }
    }

    private void bindFormCompoundInfo(Map<String, DispensingFormPrintRsp> formIdToForm, List<DispensingSheetOperation> dispensingSheetOperations) {
        if (CollectionUtils.isEmpty(dispensingSheetOperations)) {
            return;
        }
        dispensingSheetOperations = dispensingSheetOperations.stream().filter(it -> it.getOperationType() == DispensingSheetOperation.OperationType.COMPOUND || it.getOperationType() == DispensingSheetOperation.OperationType.CANCEL_COMPOUND)
                .sorted(Comparator.comparing(DispensingSheetOperation::getCreated).thenComparing(DispensingSheetOperation::getId)).collect(Collectors.toList());

        dispensingSheetOperations.forEach(dispensingSheetOperation -> {
            //if (dispensingSheetOperation.getOperationType() != DispensingSheetOperation.OperationType.COMPOUND) {
            //    return;
            //}
            OperationRecordForCompound operationRecordForCompound = JsonUtils.readValue(dispensingSheetOperation.getRecordJson(), OperationRecordForCompound.class);
            if (operationRecordForCompound == null || (StringUtils.isEmpty(operationRecordForCompound.getDispensedCompoundName())
                    && StringUtils.isEmpty(dispensingSheetOperation.getOperatorName()))
                    || CollectionUtils.isEmpty(operationRecordForCompound.getDispensingForms())) {
                return;
            }
            String compoundId;
            String compoundName;
            if (!StringUtils.isEmpty(operationRecordForCompound.getDispensedCompoundName())) {
                // 调配多人取第一个
                compoundId = CollectionUtils.isEmpty(operationRecordForCompound.getDispensedCompoundByIds()) ? "" : operationRecordForCompound.getDispensedCompoundByIds().get(0);
                compoundName = operationRecordForCompound.getDispensedCompoundName().split(",")[0];
            } else {
                compoundId = dispensingSheetOperation.getOperatorId();
                compoundName = dispensingSheetOperation.getOperatorName();
            }

            operationRecordForCompound.getDispensingForms().forEach(compoundForm -> {
                DispensingFormPrintRsp formPrintRsp = formIdToForm.computeIfAbsent(compoundForm.getId(), k -> new DispensingFormPrintRsp());
                //if (StringUtils.isNotBlank(formPrintRsp.getCompoundByEmployeeId())) {
                //    return;
                //}

                if (dispensingSheetOperation.getOperationType() == DispensingSheetOperation.OperationType.COMPOUND) {
                    formPrintRsp.setCompoundByEmployeeId(compoundId);
                    formPrintRsp.setCompoundByUserName(compoundName);
                    formPrintRsp.setCompoundByHandSign(employeeService.findEmployeeHandSign(compoundId, dispensingSheetOperation.getChainId()));
                    formPrintRsp.setCompoundTime(dispensingSheetOperation.getCreated());
                } else {
                    formPrintRsp.setCompoundByEmployeeId(null);
                    formPrintRsp.setCompoundByUserName(null);
                    formPrintRsp.setCompoundByHandSign(null);
                    formPrintRsp.setCompoundTime(null);
                }
            });
        });
    }

    private void bindFormAuditInfo(Map<String, DispensingFormPrintRsp> formIdToForm, List<DispensingSheetOperation> dispensingSheetOperations) {
        if (CollectionUtils.isEmpty(dispensingSheetOperations)) {
            return;
        }
        dispensingSheetOperations = dispensingSheetOperations.stream().filter(it -> it.getOperationType() == DispensingSheetOperation.OperationType.AUDIT || it.getOperationType() == DispensingSheetOperation.OperationType.CANCEL_AUDIT)
                .sorted(Comparator.comparing(DispensingSheetOperation::getCreated).thenComparing(DispensingSheetOperation::getId)).collect(Collectors.toList());

        dispensingSheetOperations.forEach(dispensingSheetOperation -> {
            if (StringUtils.isBlank(dispensingSheetOperation.getRecordJson())) {
                return;
            }

            OperationRecordForAudit operationRecordForAudit = dispensingProcessService.getAuditOperationRecordFromJsonRecord(dispensingSheetOperation.getRecordJson());
            if (operationRecordForAudit == null || CollectionUtils.isEmpty(operationRecordForAudit.getDispensingForms())) {
                return;
            }

            List<DispensingFormRecordViewForAudit> auditForms = operationRecordForAudit.getDispensingForms();
            auditForms.forEach(dispensingForm -> {
                DispensingFormPrintRsp formPrintRsp = formIdToForm.computeIfAbsent(dispensingForm.getId(), k -> new DispensingFormPrintRsp());
                //TODO 这里写法真是？？ 为什么printRsp 会定义这么多字段
                BeanUtils.copyProperties(dispensingForm, formPrintRsp);//copy  id ，sourceFormType,itemCount 到了pringRsp
                if (dispensingSheetOperation.getOperationType() == DispensingSheetOperation.OperationType.AUDIT) {
                    formPrintRsp.setAuditBy(dispensingSheetOperation.getOperatorId());
                    formPrintRsp.setAuditName(dispensingSheetOperation.getOperatorName());
                    formPrintRsp.setAuditHandSign(employeeService.findEmployeeHandSign(dispensingSheetOperation.getOperatorId(), dispensingSheetOperation.getChainId()));
                    formPrintRsp.setAuditTime(dispensingSheetOperation.getCreated());
                } else {
                    formPrintRsp.setAuditBy(null);
                    formPrintRsp.setAuditName(null);
                    formPrintRsp.setAuditHandSign(null);
                    formPrintRsp.setAuditTime(null);
                }
            });
        });
    }

    /**
     * api接口补推 发药单到华润 39
     *
     * @param rePush 如果为1 推送没工程的强推
     */
    public OpsCommonRsp pushToHuaRun(String dispensingSheetId,
                                     String dispensingFormId,
                                     String employeeId,
                                     Integer rePush) throws Exception {

        List<DispensingForm> chineseDispensingFormList = new ArrayList<>();
        Map<String, GoodsItem> goodsIdToGoods = new HashMap<>();
        DispensingSheet dispensingSheet = syncHuaRunOrderService.singleTransToCheckCanPush(dispensingSheetId, dispensingFormId,
                chineseDispensingFormList, goodsIdToGoods);
        if (dispensingSheet != null) {
            boolean result = syncHuaRunOrderService.doPushFormToHurRunImpl(chineseDispensingFormList, goodsIdToGoods, dispensingSheet, employeeId, rePush);
            if (result) {
                return new OpsCommonRsp(OpsCommonRsp.SUCC, "推送成功");
            } else {
                return new OpsCommonRsp(OpsCommonRsp.FAIL, "推送失败");
            }
        } else {
            return new OpsCommonRsp(OpsCommonRsp.FAIL, "推送失败");
        }
    }

    /**
     * api接口补推 发药单到天慎泰煎培中心
     *
     * @param rePush 如果为1 推送没工程的强推
     */
    public OpsCommonRsp pushToSanShenTai(String dispensingSheetId,
                                         String dispensingFormId,
                                         Integer rePush) throws Exception {
        List<DispensingForm> chineseDispensingFormList = new ArrayList<>();
        Map<String, GoodsItem> goodsIdToGoods = new HashMap<>();
        DispensingSheet dispensingSheet = syncTianShenTaiOrderService.singleTransToCheckCanPush(dispensingSheetId, dispensingFormId,
                chineseDispensingFormList, goodsIdToGoods);
        if (dispensingSheet != null) {
            boolean result = syncTianShenTaiOrderService.doPushFormToSanShenTaiImpl(chineseDispensingFormList, goodsIdToGoods, dispensingSheet, rePush);
            if (result) {
                return new OpsCommonRsp(OpsCommonRsp.SUCC, "推送成功");
            } else {
                return new OpsCommonRsp(OpsCommonRsp.FAIL, "推送失败");
            }
        } else {
            return new OpsCommonRsp(OpsCommonRsp.FAIL, "推送失败");
        }
    }

    //////////////////////////////////打印 分发到 DispensingPrintedService ////////////////////////

    /***
     * 打印发药单
     * @param dispensingSheetId 发药单Id
     * @param headerClinicId 发药单所属门店Id
     * @return 发药打印信息
     * */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public DispensingSheetPrintView getDispensingSheetPrintViewById(String dispensingSheetId, String headerChainId, String headerClinicId) {
        return dispensingPrintedService.findDispensingSheetPrintViewById(dispensingSheetId, headerChainId, headerClinicId, false, null);
    }

    /***
     * rpc获取发药单item的打印批次信息
     * 情况1：未发药没有锁批次时，去查询goods锁的虚拟批次作为打印批次
     * 情况2：未发药锁了批次时，用锁的批次作为打印批次
     * 情况3：部分发药未锁批次时，需要查goods锁的虚拟批次的同时还要查实际发的批次，两个结果merge作为打印批次
     * 情况4：部分发药锁批次时，如果发药的批次和锁的批次不一样怎么处理？如果一样，就用锁的批次和实际发的批次merge作为打印批次
     * 情况5：全发了，直接用发药批次
     * 结论：上面的逻辑是优先打印发药批次，但是目前发药单的展示没有用这个逻辑，所以逻辑都改成只要锁了批次，就用锁的批次去打印，没有锁批次就用goods锁的虚拟批次作为打印批次
     * */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public DispensingFormItemPrintListRsp getDispensingFormItemPrintListRspBySourceSheetId(String sourceSheetId, boolean printUnDispense) {
        return dispensingPrintedService.getDispensingFormItemPrintListRspBySourceSheetId(sourceSheetId, printUnDispense);
    }

    /**
     * 老的已经废弃的接口
     * 一个chargeSheet对应多个 dispensingSheet 返回已经不对
     */
    @Deprecated
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public DispensingSheetPrintView findDispensingSheetPrintViewBySourceSheetId(String chargeSheetId, String clinicId) {
        return dispensingPrintedService.findDispensingSheetPrintViewBySourceSheetId(chargeSheetId, clinicId);
    }

    /**
     * 药品标签打印
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public DispensingSheetView findDispensingPrintLabel(String dispensingId, String clinicId) {
        return dispensingPrintedService.findDispensingPrintLabel(dispensingId, clinicId);
    }

    /***
     * 打印收费单的发药单
     * @param chargeSheetId 收药单Id
     * @param headerClinicId 发药单所属门店Id
     * @return 发药打印信息
     * */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public DispensingSheetPrintViewListRsp findDispensingSheetPrintViewListBySourceSheetId(String chargeSheetId, String chainId, String headerClinicId) throws ServiceInternalException {

        return dispensingPrintedService.findDispensingSheetPrintViewListBySourceSheetId(chargeSheetId, chainId, headerClinicId, false);
    }

    /***
     * 批量 修改 发药单是否打印状态
     * */
    @Transactional
    public UpdatePrintedNumberListRsp putPrintedNumber(UpdatePrintedNumberListReq clientReq) {
        return dispensingPrintedService.putPrintedNumber(clientReq);

    }
    ///////////// 打印 End

    /**
     * 审核发药单
     */
    @Transactional
    public DispensingConfigStatusRsp auditDispensingForm(String dispensingSheetId, DispensingFormIdsReq req, String chainId, String clinicId, String employeeId, String operatorId) {
        DispensingConfigStatusRsp clientRsp = dispensingProcessService.auditDispensingForm(dispensingSheetId, req, chainId, clinicId, employeeId, operatorId);
        //没开直接过
//        if (dispensingFilterConfiguration.openDispensingSheetFilter(chainId)) {
        if (!CollectionUtils.isEmpty(clientRsp.getSelectedPrintDispensingFormIdList())) {
            /**
             * 自动打印， 打印审核部分
             * */
            dispensingPrintedService.sendAutoPrintMessage(clientRsp.getDispensingSheet(), clientRsp.getSelectedPrintDispensingFormIdList(), WebMessageBody.WebMessageEvent.DISPENSING_AUTO_PRINT_FROM_AUDIT);
        }
//        }
        return clientRsp;
    }

    /**
     * 给连锁发药单打tag
     */
    @Transactional
    public Integer tagDispensingSheet(String chainId, String dispensingSheetId) {
        return dispensingJenkinsRpcService.tagDispensingSheet(chainId, dispensingSheetId);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<DispensingSheetAbstract> getOpenApiDispensingSheetAbstractList(GetDispenseSheetListByCreatedReq reqBody) throws ServiceInternalException {
        reqBody.parameterCheck();
        AbcListPage<DispensingSheetAbstract> clientRsp = new AbcListPage<>();
        clientRsp.setLimit(reqBody.getLimit());
        clientRsp.setOffset(reqBody.getOffset());
        clientRsp.setTotal(dispensingMapper.countOpenApiDispensingSheetAbstractList(reqBody.getClinicId(), reqBody.getPatientId(), reqBody.getStatus(), reqBody.getCreatedBegin(), reqBody.getCreatedEnd()));
        List<DispensingSheetAbstract> dispensingSheetAbstract = dispensingMapper.getOpenApiDispensingSheetAbstractList(reqBody.getOffset(), reqBody.getLimit(), reqBody.getClinicId(), reqBody.getPatientId(), reqBody.getStatus(), reqBody.getCreatedBegin(), reqBody.getCreatedEnd());

        bindPatientOrderInfo(reqBody.getChainId(), dispensingSheetAbstract);

        clientRsp.setRows(dispensingSheetAbstract);
        return clientRsp;
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public List<DispensingSheetAbstract> listOpenApiDispensingSheetAbstractById(GetDispenseSheetListByIdReq req) {
        List<DispensingSheetAbstract> dispensingSheetAbstracts = dispensingMapper.queryOpenApiDispensingSheetAbstractById(req.getClinicId(), req.getDispensingSheetIds());
        if (CollectionUtils.isEmpty(dispensingSheetAbstracts)) {
            return new ArrayList<>();
        }

        bindPatientOrderInfo(req.getChainId(), dispensingSheetAbstracts);
        return dispensingSheetAbstracts;
    }

    /**
     * 从db里面拉取配置信息
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<DispensingDrawConfigView> getClinicDispenseConfig(DispensingOrderConfigListReq clientReq) {
        return dispensingConfigService.getClinicDispenseConfig(clientReq);
    }

    /**
     * 修改诊所配置信息
     */
    @Transactional(rollbackFor = Exception.class)
    public DispensingDrawConfigRsp createClinicDispenseConfig(DispensingOrderConfigReq reqBody) {
        return dispensingConfigService.createClinicDispenseConfig(reqBody);
    }

    /**
     * 修改诊所配置信息
     */
    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp updateClinicDispenseConfigItem(DispensingOrderConfigItemReq reqBody) {
        return dispensingConfigService.updateClinicDispenseConfigItem(reqBody);
    }

    //////////////////////// 医院发药单 ///////////////////////

    /**
     * 取药单QL
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public DispensingOrderAbstractListRsp findDispensingOrderAbstractList(DispenseOrderQLReq clientReq) throws ServiceInternalException {
        return dispensingOrderService.findDispensingOrderAbstractList(clientReq);
    }

    /**
     * 拉取单个取药单详情
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public DispensingOrderView findDispensingOrderViewById(String headerChainId, String headerClinicId, String dispensingOrderId, int tab, int from) throws ServiceInternalException {
        if (!(tab == 1 || tab == 2)) {
            throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR);
        }
        return dispensingOrderService.findDispensingOrderViewByDispensingOrderId(headerChainId, headerClinicId, dispensingOrderId, tab, from);
    }


    /**
     * 拉取单个取药单详情
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public List<DispensingOrderView> findDispensingOrderViewByIdList(String headerChainId, String headerClinicId, List<Long> dispenseOrderIds, int tab, int from) throws ServiceInternalException {
        if (!(tab == 1 || tab == 2)) {
            throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR);
        }
        return dispensingOrderService.findDispensingOrderViewByDispensingOrderIdList(headerChainId, headerClinicId, dispenseOrderIds, tab, from);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<DispensingSheetView> getDispensingSheetViewByPatientId(DispensingSheetPatientOrderIdsReq clientReq) throws ServiceInternalException {
        if (!(clientReq.getTab() == 1 || clientReq.getTab() == 2 || clientReq.getTab() == 3)) {
            throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR);
        }
        return dispensingOrderService.getDispensingSheetViewByPatientId(clientReq);
    }

    /**
     * 护士站向药房发起发药申请
     * 药房能看到取药单了
     * 整个发药单发送到药房发药
     */
    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp sendDispensingOrderToPharmacy(SendDispenseOrderToPharmacyReq sendDispenseOrderToPharmacyReq) throws ServiceInternalException {
        return dispensingOrderService.sendDispensingOrderToPharmacy(sendDispenseOrderToPharmacyReq);
    }

    /**
     * 护士站向药房发起退药申请
     * 药房能看到退药单了
     */
    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp sendUnDispensingOrderToPharmacy(DispenseDispenseOrderReq sendDispenseOrderToUnPharmacyReq) throws ServiceInternalException {
        return dispensingOrderService.sendUnDispensingOrderToPharmacy(sendDispenseOrderToUnPharmacyReq);
    }

    /**
     * 护士长重新申请
     */
    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp sendReDispensingOrderToPharmacy(DispenseDispenseOrderReq sendDispenseOrderToUnPharmacyReq) throws ServiceInternalException {
        return dispensingOrderService.sendReDispensingOrderToPharmacy(sendDispenseOrderToUnPharmacyReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<DispensingSheetView> getDispensingSheetViewByAdvice(GetDispenseSheetByAdviceIdReq clientReq) throws ServiceInternalException {
        return dispensingOrderService.getDispensingSheetViewByAdvice(clientReq);
    }

    /**
     * 批量患者获取发药单
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<DispensingSheetView> listDispensingSheet(DispensingSheetPatientOrderIdsReq clientReq) {
        return dispensingOrderService.listDispensingSheet(clientReq);
    }

    /**
     * 获取领药/退药患者列表
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<PatientOrderHospitalVO> getDispensingOrderPatientAbstractList(DispensingPatientAbstractListReq clientReq) {
        return dispensingOrderService.getDispensingOrderPatientAbstractList(clientReq);
    }

    @Transactional(rollbackFor = Exception.class)
    @RedisLock(key = "'dispensing.order.change.status:' + #clinicId + ':' + #wardAreaId + ':' + #orderId")
    public void changeDispensingOrderStatusFromDelayMessage(String chainId, String clinicId, Long orderId,
                                                            Long wardAreaId) {
        dispensingOrderService.changeDispensingOrderStatusFromDelayMessage(chainId, clinicId, orderId, wardAreaId);
    }

    @Transactional(rollbackFor = Exception.class)
    @RedisLock(key = "'dispensing.order.config.create:' + #clinicId + ':' + #wardAreaId")
    public void createDispensingOrderConfig(String chainId, String clinicId, Long wardAreaId,
                                            List<Integer> pharmacyNoList, int action) {
        dispensingConfigService.createDispensingOrderConfig(chainId, clinicId, wardAreaId, pharmacyNoList, action);
    }

    @UseReadOnlyDB
    public AbcListPage<DispensingSheetUnfinishedView> getUnfinishedDispensingSheetByPatientOrderId(String chainId, String clinicId, String patientOrderId) {
        return dispensingOrderService.getUnfinishedDispensingSheetByPatientOrderId(chainId, clinicId, patientOrderId);
    }

    /**
     * 撤销医嘱核对删除领药单
     */
    @Transactional(rollbackFor = Exception.class)
    @RedisLock(key = "'dispensing.advice.undo-check:' + #adviceMessage.clinicId + ':' + #adviceMessage.id")
    public void deleteDispensingOrderFromAdviceMessage(AdviceMessage adviceMessage) {
        dispensingOrderService.deleteDispensingOrderFromAdviceMessage(adviceMessage);
    }

    /**
     * 统计一个收费单对应的发药状态
     * 主要用于收费同时发药
     */
    public ChargeSheetDispenseStatusRsp getChargeSheetDispenseStatus(String chainId,
                                                                     String clinicId,
                                                                     String chargeSheetId
    ) {
        return dispensingSheetService.getChargeSheetDispenseStatus(chainId, clinicId, chargeSheetId);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GetDispensingSheetListRsp getDispensingSheetListByPatientOrderId(GetDispensingSheetListReq clientReq) {
        GetDispensingSheetListRsp rsp = new GetDispensingSheetListRsp();
        rsp.setResult(new ArrayList<>());
        List<DispensingSheetAbstract> dispensingSheetList = dispensingMapper.getDispensingSheetList(clientReq);
        if (CollectionUtils.isEmpty(dispensingSheetList)) {
            return rsp;
        }
        PatientOrder patientOrder = patientOrderService.findPatientOrder(clientReq.getPatientOrderId());
        CisPatientInfo patientInfo = null;
        if (Objects.nonNull(patientOrder)) {
            patientInfo = new CisPatientInfo();
            patientInfo.setName(patientOrder.getPatientName());
            patientInfo.setId(patientOrder.getPatientId());
            patientInfo.setIsMember(patientOrder.getIsMember());
            patientInfo.setSex(patientOrder.getPatientSex());
        }
        for (DispensingSheetAbstract dispensingSheet : dispensingSheetList) {
            dispensingSheet.setStatusName(StatusNameTranslator.translateDispensingSheetStatus(dispensingSheet.getStatus()));
            dispensingSheet.setPatient(patientInfo);
        }
        rsp.getResult().addAll(dispensingSheetList);
        return rsp;
    }

    /**
     * 查询当前登录人的药房权限
     */
    public PharmacyPermissionView getEmployeePharmacyPermission(String chainId, String clinicId, String employeeId) {
        Map<Integer, GoodsPharmacyView> pharmacyNoToPharmacy = scGoodsFeignClient.loadPharmacyByClinicMap(chainId, clinicId);
        List<Integer> pharmacyNos = PharmacyPermissionUtils.buildEmployeePharmacyNoMap(pharmacyNoToPharmacy, Collections.singletonList(employeeId)).get(employeeId);
        if (CollectionUtils.isEmpty(pharmacyNos)) {
            return new PharmacyPermissionView();
        }

        List<GoodsPharmacyBaseView> dispensablePharmacies = pharmacyNos.stream().map(pharmacyNoToPharmacy::get)
                .map(this::toGoodsPharmacyBaseView).filter(Objects::nonNull)
                .sorted(Comparator.comparing(GoodsPharmacyBaseView::getSort).thenComparing(GoodsPharmacyBaseView::getNo))
                .collect(Collectors.toList());
        PharmacyPermissionView pharmacyPermission = new PharmacyPermissionView();
        pharmacyPermission.setDispensablePharmacies(dispensablePharmacies);
        return pharmacyPermission;
    }

    private GoodsPharmacyBaseView toGoodsPharmacyBaseView(GoodsPharmacyView goodsPharmacyView) {
        if (goodsPharmacyView == null) {
            return null;
        }

        GoodsPharmacyBaseView goodsPharmacyBaseView = new GoodsPharmacyBaseView();
        goodsPharmacyBaseView.setChainId(goodsPharmacyView.getChainId());
        goodsPharmacyBaseView.setClinicId(goodsPharmacyView.getClinicId());
        goodsPharmacyBaseView.setNo(goodsPharmacyView.getNo());
        goodsPharmacyBaseView.setName(goodsPharmacyView.getName());
        goodsPharmacyBaseView.setType(goodsPharmacyView.getType());
        goodsPharmacyBaseView.setTypeName(goodsPharmacyView.getTypeName());
        goodsPharmacyBaseView.setSort(goodsPharmacyView.getSort());
        goodsPharmacyBaseView.setInnerFlag(goodsPharmacyView.getInnerFlag());
        goodsPharmacyBaseView.setStatus(goodsPharmacyView.getStatus());
        return goodsPharmacyBaseView;
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public List<DispensingSheetView> findDispensingSheetViewBySourceSheetIdForOpenApi(String chainId, String clinicId, String chargeSheetId) {
        List<DispensingSheetView> dispensingSheetViews = listDispensingSheetViewsBySourceSheetId(chainId, chargeSheetId);
        if (CollectionUtils.isEmpty(dispensingSheetViews)) {
            return Lists.newArrayList();
        }
        dispensingSheetViews.forEach(this::formatDispensingSheetViewForOpenApi);
        return dispensingSheetViews;
    }

    /**
     * 打印发药单的退药详情
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public DispensingSheetPrintView getDispensingSheetUndispensePrintViewById(String dispensingSheetId, String chainId, String clinicId, List<String> chargeTransactionIds) {
        return dispensingPrintedService.findDispensingSheetPrintViewById(dispensingSheetId, chainId, clinicId, true, chargeTransactionIds);
    }

    /**
     * 通过收费单id查询发药单的退药详情
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public DispensingSheetPrintViewListRsp findDispensingSheetUndispensePrintViewListBySourceSheetId(String chargeSheetId, String chainId, String clinicId) {
        return dispensingPrintedService.findDispensingSheetPrintViewListBySourceSheetId(chargeSheetId, chainId, clinicId, true);
    }

    public OpsCommonRsp updateDispensingPropertyConfig(String chainId, String clinicId, String operatorId, DispensingPropertyConfigReq req) {
        if (Objects.isNull(req)) {
            return new OpsCommonRsp(OpsCommonRsp.FAIL, "参数不能为空");
        }
        int wholeSheetOperateEnable = req.getWholeSheetOperateEnable();
        if (wholeSheetOperateEnable == YesOrNo.YES) {
            GoodsConfigView goodsConfig = scGoodsFeignClient.getGoodsConfig(clinicId);
            // true允许开出 false不允许开出
            boolean disableNoStockGoods = Optional.ofNullable(goodsConfig)
                    .map(GoodsConfigView::getStockGoodsConfig)
                    .map(GoodsClinicConfigView::getDisableNoStockGoods)
                    .orElse(0) != 1;
            if (disableNoStockGoods) {
                throw new DispensingServiceException(DispensingServiceError.NOT_ALLOW_WHOLE_DISPENSE_ALLOW_OPEN_STOCK_OUT);
            }
        }
        UpdatePropertyItemReq<DispensingPropertyConfigReq> propertyItemReq = new UpdatePropertyItemReq<>();
        propertyItemReq.setChainId(chainId);
        propertyItemReq.setClinicId(clinicId);
        propertyItemReq.setPropertyKey(PropertyKey.DISPENSING);
        propertyItemReq.setValue(req);
        propertyItemReq.setScopeId(clinicId);
        propertyItemReq.setOperatorId(operatorId);
        propertyService.updatePropertyValueByKey(propertyItemReq);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "操作成功");
    }

    public void smartUnDispense(DispensingSheet dispensingSheet, String operatorId) {
        if (dispensingSheet.getPharmacyType() != GoodsConst.PharmacyType.VIRTUAL_PHARMACY) {
            return;
        }

        GoodsSupplierView cloudDecoctionSupplier = scGoodsFeignClient.getCloudDecoctionSupplier(dispensingSheet.getChainId(), dispensingSheet.getClinicId());
        if (!DispensingUtils.isSupportCloudDecoction(cloudDecoctionSupplier, dispensingSheet)) {
            return;
        }

        smartDispensingService.smartUnDispense(dispensingSheet, operatorId, cloudDecoctionSupplier);
    }

    /**
     * 新增或更新发药单上下文类
     */
    @Data
    public static class InsertOrUpdateDispensingSheetContext {
        /**
         * 项目是否存在退费
         */
        private int itemHasRefund;
    }
}
