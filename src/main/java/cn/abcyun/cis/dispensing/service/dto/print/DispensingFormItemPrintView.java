package cn.abcyun.cis.dispensing.service.dto.print;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.TraceableCode;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class DispensingFormItemPrintView {

    private String id;

    private String sourceFormItemId;

    private String name;
    private String unit;
    private BigDecimal count;
    //只有中药才有doseCount
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer doseCount;

    private String specialRequirement;

    private String formatSpec; //已经格式好了的规格，可以直接展示
    private String position; //柜号

    /**
     * 厂家
     */
    private String manufacturer;

    private int productType;
    private int productSubType;

    private int sourceItemType;

    private JsonNode usageInfo;

    @JsonIgnore
    @JsonProperty("cMSpec")
    private String cMSpec;

    /**
     * 发药单打印，则是已收金额，退药单打印，则是实退金额
     */
    private BigDecimal receivedPrice;

    @ApiModelProperty("批次信息")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<DispensingFormItemBatchInfoPrintView> dispensingFormItemBatches;

    /**
     * 整个药的追溯码列表
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<TraceableCode> traceableCodeList;

    @JsonIgnore
    public String getCMSpec() {
        return cMSpec;
    }
}
