package cn.abcyun.cis.dispensing.service.dispense;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsDispenseDataItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsDispenseResultItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslog.GoodsStockLogItem;
import cn.abcyun.cis.commons.amqp.message.ChargeSheetMessage;
import cn.abcyun.cis.commons.amqp.message.PatientOrderMessage;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.rpc.charge.ChargeFormItem;
import cn.abcyun.cis.commons.rpc.charge.ChargeFormItemBatchInfo;
import cn.abcyun.cis.commons.rpc.charge.ChargeSheet;
import cn.abcyun.cis.commons.rpc.charge.Constants;
import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.dispensing.amqp.HAMQProducer;
import cn.abcyun.cis.dispensing.amqp.RocketMqProducer;
import cn.abcyun.cis.dispensing.base.exception.DispenseSheetChangedException;
import cn.abcyun.cis.dispensing.base.exception.GoodsUndispenseException;
import cn.abcyun.cis.dispensing.controller.DTOConverter;
import cn.abcyun.cis.dispensing.domain.*;
import cn.abcyun.cis.dispensing.repository.DispensingLogRepository;
import cn.abcyun.cis.dispensing.repository.DispensingSheetOperationRepository;
import cn.abcyun.cis.dispensing.service.*;
import cn.abcyun.cis.dispensing.service.operation.SheetOperationBase;
import cn.abcyun.cis.dispensing.service.operation.SheetOperationCreateFactory;
import cn.abcyun.cis.dispensing.service.rpc.ScGoodsFeignClient;
import cn.abcyun.cis.dispensing.util.*;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import cn.abcyun.common.log.marker.AbcLogMarker;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 退费后自动退药（目前仅支持全部发药后的退费自动退药）
 *
 * <AUTHOR>
 * @since 2024-01-30 17:03:01
 **/
@Slf4j
@Component
@Scope("prototype")
@Accessors(chain = true)
public class RefundDirectUnDispenseOpt extends DispensingOpsBase {

    @Autowired
    private DispensingSheetEntityService dispensingSheetEntityService;

    @Autowired
    private DispensingSheetOperationRepository dispensingSheetOperationRepository;

    @Autowired
    private ChargeService chargeService;

    @Autowired
    private DispensingLogRepository dispensingLogRepository;

    @Autowired
    private AbcIdGenerator abcIdGenerator;

    @Autowired
    private AbcCisScGoodsService abcCisScGoodsService;

    @Autowired
    private ScGoodsFeignClient scGoodsFeignClient;

    @Autowired
    private RocketMqProducer rocketMqProducer;

    @Autowired
    private HAMQProducer hamqProducer;

    @Autowired
    private DispensingLogService dispensingLogService;

    @Autowired
    private SheetOperationCreateFactory sheetOperationCreateFactory;

    @Autowired
    private PatientOrderService patientOrderService;

    @Setter
    private ChargeSheet chargeSheet;

    private List<DispensingSheetOperation> dispensingSheetOperations;

    private List<DispensingLogItem> dispenseItemLogs;

    private DispensingSheet undispenseSheet;

    private List<DispensingFormItem> undispenseItems;

    protected DispensingSheet dispensingSheet;

    protected Map<String, DispensingFormItem> dispensingItemIdToDispensingItem;

    protected String operatorId;

    /**
     * 处方ID —> 客户端退处方请求
     */
    private Map<String, DispensingForm> dispensingFormIdToClientUnDispensingForm;

    private List<DispensingFormItem> logUnDispensingFormItems = new ArrayList<>();

    private String operationId;

    private List<ChargeSheetMessage.ChargeFormItem> currentAddedItems;

    public RefundDirectUnDispenseOpt init(DispensingSheet dispensingSheet, List<ChargeSheetMessage.ChargeFormItem> currentAddedItems, String operatorId) {
        Assert.notNull(dispensingSheet, "发药单不能为空");
        this.dispensingSheet = dispensingSheet;
        this.currentAddedItems = currentAddedItems;
        this.operatorId = operatorId;
        this.operationId = abcIdGenerator.getUID();
        return this;
    }

    /**
     * 退药
     */
    @Transactional(rollbackFor = Exception.class)
    public void undispense() {
        // 数据加载
        boolean valid = loadAndCheck();
        if (!valid) {
            log.warn("校验失败，不进行退药");
            return;
        }

        // 退药前置处理
        doUnDispensePreProcess();

        // 退药
        doUnDispense();

        // 推送发送结果
        pushUnDispenseResult();
    }

    private void doUnDispense() {
        // 返还库存
        List<GoodsDispenseResultItem> goodsDispenseResultItems = returnStock();

        // 更新发药单
        updateUndispenseToSheet(goodsDispenseResultItems);

        dispensingSheetEntityService.save(dispensingSheet);

        // 记录日志
        log(goodsDispenseResultItems);

        // 更新追溯码信息
        RocketMqProducer.doAfterTransactionCommit(() -> {
            List<DispensingSheet> dispensingSheets = dispensingSheetEntityService.findAllByChainIdSourceSheetId(dispensingSheet.getChainId(), dispensingSheet.getSourceSheetId());
            abcCisScGoodsService.updateUseTraceCode(dispensingSheet.getChainId(), dispensingSheet.getClinicId(), dispensingSheets, operatorId);
        });
    }

    private void pushUnDispenseResult() {
        /***
         * 具体是不是整个发药单全退了，要检查sheet里面状态
         * */
        hamqProducer.sendDispensingSheetUpdate(PatientOrderMessage.MSG_SUB_TYPE_DISPENGING_UPDATED_UNDISPENSE, dispensingSheet, operatorId);
        rocketMqProducer.broadcastDispensingSheetStatusChange(dispensingSheet, false, operatorId);
    }

    private void updateUndispenseToSheet(List<GoodsDispenseResultItem> goodsDispenseResultItems) {
        Map<String, GoodsDispenseResultItem> dispenseItemIdToGoodsDispenseResultItem = ListUtils.toMap(goodsDispenseResultItems, GoodsDispenseResultItem::getDispenseItemId);

        // 更新处方信息
        dispensingSheet.getDispensingForms().forEach(dispensingForm -> {
            DispensingForm clientUnDispensingForm = dispensingFormIdToClientUnDispensingForm.get(dispensingForm.getId());
            if (clientUnDispensingForm == null) {
                return;
            }
            updateUndispenseToForm(clientUnDispensingForm, dispensingForm, dispenseItemIdToGoodsDispenseResultItem);
        });

        // 统计未退完的发药项数量，主要是判断总共要发的数量和已退的数量是不是相等
        long dispensedAndWaitingCount = dispensingSheet.getDispensingForms()
                .stream()
                .filter(dispensingForm -> dispensingForm.getDispensingFormItems() != null)
                .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream())
                .filter(dispensingFormItem ->
                        dispensingFormItem.getProductType() != Constants.ProductType.COMPOSE_PRODUCT
                                && dispensingFormItem.getStatus() != DispensingFormItem.Status.CANCELED
                                // 同时还不能是历史的退药项
                                && dispensingFormItem.getIsHistoryItem() == 0)
                .collect(
                        Collectors.toMap(
                                item -> {
                                    if (item.getStatus() != DispensingFormItem.Status.UNDISPENSED) {
                                        return item.getId();
                                    } else {
                                        return item.getAssociateFormItemId();
                                    }
                                },
                                item -> {
                                    BigDecimal itemTotalCount = MathUtils.calculateTotalCount(item.getUnitCount(), item.getDoseCount());
                                    if (item.getStatus() != DispensingFormItem.Status.UNDISPENSED) {
                                        return MathUtils.wrapBigDecimal(itemTotalCount, BigDecimal.ZERO);
                                    } else {
                                        return MathUtils.wrapBigDecimal(itemTotalCount, BigDecimal.ZERO).negate();
                                    }
                                },
                                (a, b) -> a.add(b)
                        ))
                .values()
                .stream().filter(value -> value.compareTo(BigDecimal.ZERO) > 0)
                .count();
        if (dispensedAndWaitingCount == 0) {
            /**
             * cis 逻辑全部退完才算退完
             * */
            dispensingSheet.setStatus(DispensingSheet.Status.UNDISPENSED);
            dispensingSheet.setOrderByDate(Instant.now());
        }

        dispensingSheet.setChineseMedicineUndispenseType(DispensingUtils.getSheetChineseMedicineUndispenseType(dispensingSheet));
    }

    private DispensingForm updateUndispenseToForm(DispensingForm clientUnDispensingForm,
                                                  DispensingForm dispensingForm,
                                                  Map<String, GoodsDispenseResultItem> dispenseItemIdToGoodsDispenseResultItem) {
        if (dispensingForm == null || dispensingForm.getDispensingFormItems() == null
                || clientUnDispensingForm == null || clientUnDispensingForm.getDispensingFormItems() == null) {
            return dispensingForm;
        }

        List<DispensingFormItem> addedItems = new ArrayList<>();
        List<DispensingFormItem> addedSelfProvidedItems = new ArrayList<>();

        Map<String, DispensingFormItem> dispenseItemIdToClientUnDispenseFormItem = ListUtils.toMap(clientUnDispensingForm.getDispensingFormItems(), DispensingFormItem::getId);
        dispensingForm.getDispensingFormItems().stream()
                .filter(existedFormItem -> DispensingFormItem.Status.unDispenseable.contains(existedFormItem.getStatus()))
                .forEach(dispenseItem -> {
                    DispensingFormItem clientUnDispenseItem = dispenseItemIdToClientUnDispenseFormItem.get(dispenseItem.getId());
                    if (clientUnDispenseItem == null) {
                        return;
                    }

                    BigDecimal toUndispenseUnitCount = clientUnDispenseItem.getUnitCount();
                    BigDecimal toUndispenseDoseCount = clientUnDispenseItem.getDoseCount();

                    DispensingFormItem undispensedFormItem = new DispensingFormItem();
                    BeanUtils.copyProperties(dispenseItem, undispensedFormItem);
                    FillUtils.fillCreatedBy(undispensedFormItem, operatorId);
                    undispensedFormItem.setOperationId(operationId);
                    undispensedFormItem.setId(clientUnDispenseItem.getAssociateFormItemId());
                    undispensedFormItem.setStatus(DispensingFormItem.Status.UNDISPENSED);
                    /***
                     * 打上内存标记位，标记本次退药
                     * */
                    undispensedFormItem.setThisTimeDispensed(1);
                    undispensedFormItem.setAssociateFormItemId(dispenseItem.getId());
                    GoodsDispenseResultItem goodsDispenseResultItem = dispenseItemIdToGoodsDispenseResultItem.get(dispenseItem.getId());
                    if (goodsDispenseResultItem != null) {
                        undispensedFormItem.setStockDealId(goodsDispenseResultItem.getDealId());
                        undispensedFormItem.setTotalCostPrice(MathUtils.wrapBigDecimalOrZero(goodsDispenseResultItem.getTotalCostPrice()));
                    }
                    undispensedFormItem.setUnitCount(toUndispenseUnitCount);
                    undispensedFormItem.setDoseCount(toUndispenseDoseCount);
                    //设置UndispenseType
                    if (dispenseItem.getProductType() == Constants.ProductType.MEDICINE && dispenseItem.getProductSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE) {
                        undispensedFormItem.setUndispenseType(DispensingFormItem.UndispenseType.BY_DOSE);
                    } else {
                        undispensedFormItem.setUndispenseType(DispensingFormItem.UndispenseType.BY_UNIT);
                    }
                    //自备药品不写入DispensingLog表
                    if (dispenseItem.getSourceItemType() == DispensingFormItem.SourceItemType.SELF_PROVIDED) {
                        addedSelfProvidedItems.add(undispensedFormItem);
                    } else {
                        addedItems.add(undispensedFormItem);
                    }
                });
        dispensingForm.getDispensingFormItems().addAll(addedItems);
        dispensingForm.getDispensingFormItems().addAll(addedSelfProvidedItems);
        logUnDispensingFormItems.addAll(addedItems);
        //退费退药 保留审核和调配状态 前端通过 chargeSheet的stats进行管理
//        updateFormAuditedAndCompoundedStatusWhenUndispensed(existedForm);
        return dispensingForm;
    }

    private List<GoodsDispenseResultItem> returnStock() {
        // 构建返还库存请求
        List<GoodsDispenseDataItem> goodsDispenseDataItems = buildGoodsDispenseDataItems();

        // 只有普通发药项才需要返还库存
        goodsDispenseDataItems = goodsDispenseDataItems.stream().filter(goodsItem -> {
            DispensingFormItem dispensedItem = dispensingItemIdToDispensingItem.get(goodsItem.getDispenseItemId());
            return dispensedItem.getSourceItemType() == DispensingFormItem.SourceItemType.NORMAL;
        }).collect(Collectors.toList());

        // 调用 goods 退还库存
        return rpcReturnStock(goodsDispenseDataItems);
    }

    private List<GoodsDispenseDataItem> buildGoodsDispenseDataItems() {
        Map<String, BigDecimal> chargeFormItemToUnDispenseItemUnitPrice = calculateChargeFormItemUnitPrice();

        List<GoodsDispenseDataItem> goodsDispenseDataItems = undispenseItems.stream()
                .map(undispenseItem -> {
                    DispensingFormItem dispensedItem = dispensingItemIdToDispensingItem.get(undispenseItem.getId());
                    BigDecimal unDispenseItemUnitPrice = chargeFormItemToUnDispenseItemUnitPrice.get(dispensedItem.getSourceFormItemId());
                    GoodsDispenseDataItem goodsDispenseDataItem = buildGoodsDispenseDataItem(dispensedItem, undispenseItem, unDispenseItemUnitPrice);
                    dealUnDispenseTraceCode(dispensedItem, undispenseItem, goodsDispenseDataItem, abcIdGenerator);
                    return goodsDispenseDataItem;
                })
                .filter(Objects::nonNull).collect(Collectors.toList());

        if (goodsDispenseDataItems.size() != undispenseItems.size()) {
            throw new DispenseSheetChangedException();
        }

        return goodsDispenseDataItems;
    }

    private GoodsDispenseDataItem buildGoodsDispenseDataItem(DispensingFormItem dispensedItem,
                                                             DispensingFormItem clientUnDispenseItem,
                                                             BigDecimal unDispenseItemUnitPrice) {
        if (dispensedItem == null || clientUnDispenseItem == null) {
            return null;
        }

        BigDecimal toUndispenseUnitCount = clientUnDispenseItem.getUnitCount();
        BigDecimal toUndispenseDoseCount = clientUnDispenseItem.getDoseCount();
        BigDecimal toUndispenseTotalCount = MathUtils.calculateTotalCount(toUndispenseUnitCount, toUndispenseDoseCount);

        GoodsDispenseDataItem goodsDispenseDataItem = new GoodsDispenseDataItem();
        goodsDispenseDataItem.setPharmacyNo(dispensedItem.getPharmacyNo());
        goodsDispenseDataItem.setDispenseItemId(dispensedItem.getId());
        goodsDispenseDataItem.setUnDispenseItemId(clientUnDispenseItem.getAssociateFormItemId());
        goodsDispenseDataItem.setSourceSheetId(dispensingSheet.getSourceSheetId());
        goodsDispenseDataItem.setSourceItemId(dispensedItem.getSourceFormItemId());
        goodsDispenseDataItem.setSourceRefundItemId(clientUnDispenseItem.getSourceRefundFormItemId());
        goodsDispenseDataItem.setMedicineName(dispensedItem.getName());
        goodsDispenseDataItem.setDisplaySpec(dispensedItem.getGoodsDispSpec());
        if(dispensedItem.getSnapPieceNum() != null){
            goodsDispenseDataItem.setPieceNum(dispensedItem.getSnapPieceNum().intValue());
        }
        //从快照上取规格
        goodsDispenseDataItem.setPieceUnit(dispensedItem.getSnapPieceUnit());
        goodsDispenseDataItem.setPackageUnit(dispensedItem.getSnapPackageUnit());

        goodsDispenseDataItem.setGoodsId(dispensedItem.getProductId());
        if (clientUnDispenseItem.getExtendData() != null) {
            goodsDispenseDataItem.setTraceableCodeList(clientUnDispenseItem.getExtendData().getTraceableCodeList());
            goodsDispenseDataItem.setShebaoDismountingFlag(dispensedItem.getShebaoDismountingFlag());
        }
        if (dispensedItem.getUseDismounting() == 1) {
            goodsDispenseDataItem.setPieceCount(toUndispenseTotalCount);
            goodsDispenseDataItem.setPackageCount(BigDecimal.ZERO);
        } else {
            goodsDispenseDataItem.setPackageCount(toUndispenseTotalCount);
            goodsDispenseDataItem.setPieceCount(BigDecimal.ZERO);
        }
        // 计算退药的实收金额
        BigDecimal totalPrice = null;
        if (unDispenseItemUnitPrice != null) {
            totalPrice = unDispenseItemUnitPrice.multiply(toUndispenseTotalCount).setScale(4, RoundingMode.HALF_UP);
        }
        List<DispensingFormItemBatchInfo> batches = clientUnDispenseItem.getDispensingFormItemBatches();
        if (!CollectionUtils.isEmpty(batches)) {
            List<GoodsDispenseDataItem.BatchCount> batchCounts = batches.stream().map(batch -> {
                GoodsDispenseDataItem.BatchCount batchCount = new GoodsDispenseDataItem.BatchCount();
                batchCount.setBatchId(batch.getBatchId());
                if (dispensedItem.getUseDismounting() == 1) {
                    batchCount.setPieceCount(batch.getUnitCount());
                } else {
                    batchCount.setPackageCount(batch.getUnitCount());
                }
                return batchCount;
            }).collect(Collectors.toList());
            batches.forEach(batch -> {
            });
            goodsDispenseDataItem.setBatchCountList(batchCounts);
        }
        goodsDispenseDataItem.setTotalPrice(totalPrice);
        goodsDispenseDataItem.setShebaoDismountingFlag(dispensedItem.getShebaoDismountingFlag());
        return goodsDispenseDataItem;
    }

    /**
     * 计算得到收费项的单价
     */
    private Map<String, BigDecimal> calculateChargeFormItemUnitPrice() {
        Map<String, BigDecimal> chargeFormItemIdToUnDispenseItemUnitPrice = new HashMap<>();
        if (chargeSheet != null && chargeSheet.getChargeForms() != null) {
            chargeFormItemIdToUnDispenseItemUnitPrice = chargeSheet.getChargeForms()
                    .stream()
                    .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                    .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(ChargeFormItem::getId, chargeFormItem -> {
                        BigDecimal totalCount = MathUtils.calculateTotalCount(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount());
                        if (totalCount.compareTo(BigDecimal.ZERO) == 0) {
                            return BigDecimal.ZERO;
                        }
                        return MathUtils.wrapBigDecimalAdd(chargeFormItem.getTotalPrice(), chargeFormItem.getDiscountPrice())
                                .divide(totalCount, 6, RoundingMode.HALF_UP);
                    }, (a, b) -> a));
        }
        return chargeFormItemIdToUnDispenseItemUnitPrice;
    }

    protected List<GoodsDispenseResultItem> rpcReturnStock(List<GoodsDispenseDataItem> goodsDispenseDataItems) {
        List<GoodsDispenseResultItem> goodsDispenseResultItems = new ArrayList<>();
        String dispensingSheetId = dispensingSheet.getId();
        if (goodsDispenseDataItems.size() != 0) {
            goodsDispenseResultItems = abcCisScGoodsService.undispense(
                    dispensingSheet.getClinicId(),
                    dispensingSheet.getChainId(),
                    operatorId,
                    patientOrder != null ? patientOrder.getNo() + "" : null,
                    dispensingSheet.getPatientOrderId(),
                    dispensingSheetId,
                    dispensingSheet.getPharmacyNo(),
                    dispensingSheet.getPharmacyType(),
                    goodsDispenseDataItems,
                    DTOConverter.dispensingFormItemMapToDispensingFormItemAbstractMap(dispensingItemIdToDispensingItem),
                    0);
            if (goodsDispenseResultItems == null || goodsDispenseResultItems.size() == 0 || goodsDispenseResultItems.size() != goodsDispenseDataItems.size()) {
                throw new GoodsUndispenseException();
            }
        }
        return goodsDispenseResultItems;
    }


    private void log(List<GoodsDispenseResultItem> goodsDispenseResultItems) {
        // 记录发药项日志
        dispensingLogService.logDispensingSheetUndispense(dispensingSheet, logUnDispensingFormItems, operationId, operatorId);

        // 记录操作日志
        SheetOperationBase operationBase = sheetOperationCreateFactory.createSheetOperation(DispensingSheetOperation.OperationType.WITHDRAWAL_MEDICINE,
                dispensingSheet.getId(), operatorId, operatorId, dispensingSheet.getChainId(), dispensingSheet.getClinicId());
        operationBase.bindDispensingSheet(dispensingSheet)
                .bindRepoId(operationId)
                .bindDispensingFormItems(logUnDispensingFormItems)
                .bindDispenseOperations(dispensingSheetOperations)
                .bindGoodsDispenseResultItems(goodsDispenseResultItems)
                .bindDispenseItemLogs(dispenseItemLogs);
        operationBase.createAndSaveSheetOperation();
    }

    private void doUnDispensePreProcess() {
        // 提前生成退药项的ID
        preGenUnDispenseItemId();
    }

    private void preGenUnDispenseItemId() {
        undispenseItems.forEach(unDispenseItem -> unDispenseItem.setAssociateFormItemId(abcIdGenerator.getUUID()));
    }

    /**
     * 数据加载并检查是否可以自动退药
     */
    private boolean loadAndCheck() {
        if (chargeSheet == null) {
            chargeSheet = chargeService.getChargeSheetById(dispensingSheet.getSourceSheetId());
            if (chargeSheet == null) {
                throw new NotFoundException("收费单不存在");
            }
        }
        patientOrder = patientOrderService.findPatientOrder(chargeSheet.getPatientOrderId());

        // 加载操作日志
        this.dispensingSheetOperations = dispensingSheetOperationRepository.findAllByDispensingSheetIdAndIsDeletedOrderByCreatedDescOperationTypeDesc(dispensingSheet.getId(), 0);
        if (CollectionUtils.isEmpty(dispensingSheetOperations)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "发药单没有操作记录，不能自动退药");
            return false;
        }
        List<DispensingSheetOperation> currentUndispenseOperations = new ArrayList<>();
        for (DispensingSheetOperation dispensingSheetOperation : dispensingSheetOperations) {
            int operationType = dispensingSheetOperation.getOperationType();
            if (operationType == DispensingSheetOperation.OperationType.RE_DISPENSE) {
                break;
            }
            if (operationType == DispensingSheetOperation.OperationType.WITHDRAWAL_MEDICINE) {
                currentUndispenseOperations.add(dispensingSheetOperation);
            }
        }

        this.dispenseItemLogs = dispensingLogRepository.findAllByDispensingSheetIdAndTypeIn(dispensingSheet.getId(), Arrays.asList(DispensingLogItem.Type.DISPENSE, DispensingLogItem.Type.UNDISPENSE));
        if (CollectionUtils.isEmpty(dispenseItemLogs)) {
            log.info("没有发药项发药退药记录，不能自动退药");
            return false;
        }

        List<String> undispenseOperationIds = ListUtils.extractUniqueProperty(currentUndispenseOperations, DispensingSheetOperation::getId);
        List<DispensingLogItem> undispenseItemLogs = dispenseItemLogs.stream().filter(itemLog -> undispenseOperationIds.contains(itemLog.getOperationId())).collect(Collectors.toList());
        List<String> stockDealIds = ListUtils.extractUniqueProperty(undispenseItemLogs, DispensingLogItem::getStockDealId);
        List<GoodsStockLogItem> undispenseGoodsStockLogList = mergeGoodsStockLogItems(scGoodsFeignClient.getGoodsStockUndispenseLogList(chargeSheet.getChainId(), dispensingSheet.getClinicId(), stockDealIds, true));

        List<DispensingFormItem> dispensingFormItems = DispensingUtils.getDispensingFormItems(dispensingSheet);
        this.dispensingItemIdToDispensingItem = ListUtils.toMap(dispensingFormItems, DispensingFormItem::getId);

        // 计算发药项退药的数量
        this.undispenseSheet = buildUndispenseReq(chargeSheet, dispensingSheet, currentUndispenseOperations,
                undispenseGoodsStockLogList, dispensingItemIdToDispensingItem, currentAddedItems);
        if (undispenseSheet == null) {
            log.info("没有可退药数量，不能自动退药");
            return false;
        }
        this.undispenseItems = DTOConverter.collectDispensingSheetItems(undispenseSheet);
        if (CollectionUtils.isEmpty(undispenseItems)) {
            log.info("没有可退药项，不能自动退药");
            return false;
        }

        this.dispensingFormIdToClientUnDispensingForm = ListUtils.toMap(undispenseSheet.getDispensingForms(), DispensingForm::getId);

        return true;
    }

    /**
     * 核心逻辑——构建退药请求
     *
     * @param chargeSheet                      收费单
     * @param dispensingSheet                  发药单
     * @param undispenseOperations             发药和退药的 operation
     * @param undispenseGoodsStockLogList      退药的日志
     * @param dispensingItemIdToDispensingItem 发药项ID->发药项
     * @param currentAddedChargeItems          当次新增的退费项列表
     */
    private static DispensingSheet buildUndispenseReq(ChargeSheet chargeSheet,
                                                      DispensingSheet dispensingSheet,
                                                      List<DispensingSheetOperation> undispenseOperations,
                                                      List<GoodsStockLogItem> undispenseGoodsStockLogList,
                                                      Map<String, DispensingFormItem> dispensingItemIdToDispensingItem,
                                                      List<ChargeSheetMessage.ChargeFormItem> currentAddedChargeItems) {
        if (chargeSheet == null || dispensingSheet == null) {
            return null;
        }

        Map<String, DispensingForm> formIdToForm = ListUtils.toMap(DispensingUtils.getDispensingForms(dispensingSheet), DispensingForm::getId);

        // 先计算出药品的已退单位数量
        Map<String, Map<Long, BigDecimal>> dispenseItemIdToBatchUndispenseUnitCount = calculateDispenseItemUndispenseUnitCount(dispensingSheet, undispenseOperations, formIdToForm, dispensingItemIdToDispensingItem, undispenseGoodsStockLogList);

        // 计算发药项的应退单位数量
        Map<String, ChargeRefundItemInfo> dispenseItemIdToRefundItemInfo = calculateDispenseItemShouldDispenseCount(chargeSheet, dispensingSheet, currentAddedChargeItems);

        return doBuildUndispenseReq(dispensingSheet, dispenseItemIdToBatchUndispenseUnitCount, dispenseItemIdToRefundItemInfo);
    }

    private static DispensingSheet doBuildUndispenseReq(DispensingSheet dispensingSheet,
                                                        Map<String, Map<Long, BigDecimal>> dispenseItemIdToBatchUndispenseUnitCount,
                                                        Map<String, ChargeRefundItemInfo> dispenseItemIdToRefundItemInfo) {
        if (dispensingSheet == null || CollectionUtils.isEmpty(dispenseItemIdToRefundItemInfo)) {
            return null;
        }

        List<DispensingForm> undispenseForms = new ArrayList<>();
        DispensingUtils.doWithDispensingForm(dispensingSheet, dispensingForm -> {
            List<DispensingFormItem> undispenseItems = new ArrayList<>();
            DispensingUtils.doWithDispensingItem(dispensingForm, dispensingFormItem -> {
                if (StringUtils.isNotBlank(dispensingFormItem.getAssociateFormItemId())) {
                    return;
                }

                String dispenseItemId = dispensingFormItem.getId();
                Map<Long, BigDecimal> batchIdToUndispenseUnitCount = dispenseItemIdToBatchUndispenseUnitCount.getOrDefault(dispenseItemId, Collections.emptyMap());
                ChargeRefundItemInfo chargeRefundItemInfo = dispenseItemIdToRefundItemInfo.get(dispenseItemId);
                Map<Long, BigDecimal> batchIdToRefundUnitCount = chargeRefundItemInfo.getBatchIdToRefundUnitCount();
                if (CollectionUtils.isEmpty(batchIdToRefundUnitCount)) {
                    log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "发药项没有应发数量，不能自动退药，dispenseItemId={}", dispenseItemId);
                    return;
                }

                List<DispensingItemBatch> dispensingItemBatches = new ArrayList<>();
                batchIdToRefundUnitCount.forEach((batchId, refundUnitCount) -> {
                    BigDecimal undispenseUnitCount = batchIdToUndispenseUnitCount.getOrDefault(batchId, BigDecimal.ZERO);
                    // 计算出当前批次当此应该退多少
                    BigDecimal currentUndispenseUnitCount = MathUtils.wrapBigDecimalSubtract(refundUnitCount, undispenseUnitCount);
                    if (MathUtils.wrapBigDecimalCompare(BigDecimal.ZERO, currentUndispenseUnitCount) == 0) {
                        return;
                    } else if (MathUtils.wrapBigDecimalCompare(BigDecimal.ZERO, currentUndispenseUnitCount) > 0) {
                        log.warn(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "批次应退数量小于零 batchId:{} undispenseUnitCount:{} refundUnitCount:{}", batchId, undispenseUnitCount, refundUnitCount);
                        return;
                    }

                    DispensingItemBatch dispensingItemBatch = new DispensingItemBatch();
                    dispensingItemBatch.setBatchId(batchId);
                    dispensingItemBatch.setUnitCount(currentUndispenseUnitCount);
                    dispensingItemBatches.add(dispensingItemBatch);
                });

                if (CollectionUtils.isEmpty(dispensingItemBatches)) {
                    log.info("发药项没有可退批次信息 dispensingFormItem:{}", dispensingFormItem.getId());
                    return;
                }

                BigDecimal currentUndispenseUnitCount = dispensingItemBatches.stream()
                        .map(DispensingItemBatch::getUnitCount)
                        .reduce(MathUtils::wrapBigDecimalAdd).orElse(BigDecimal.ZERO);
                if (MathUtils.wrapBigDecimalCompare(BigDecimal.ZERO, currentUndispenseUnitCount) >= 0) {
                    log.warn(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "发药项应退数量小于等于零，不能自动退药，dispenseItemId={}", dispenseItemId);
                    return;
                }

                /*
                    TODO 现在药店管家没有剂的概念（都是1剂），所以不需要对中药处方做特殊逻辑的判断，
                    但是其他产品线是需要的，后面再改
                 */
                BigDecimal undispenseUnitCount;
                BigDecimal undispenseDoseCount;
//                if (dispensingForm.getSourceFormType() == DispensingForm.SourceFormType.PRESCRIPTION_CHINESE) {// 这个逻辑
//                    BigDecimal doseCount = dispensingFormItem.getDoseCount();
//                    BigDecimal remainder = currentUndispenseUnitCount.remainder(dispensingFormItem.getUnitCount());
//                    if (MathUtils.wrapBigDecimalCompare(remainder, BigDecimal.ZERO) > 0) {
//                        log.warn(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "发药项应退数量不能整除剂量，不能自动退药，dispenseItemId={}, currentUndispenseUnitCount:{}, doseCount:{}", dispenseItemId, currentUndispenseUnitCount, doseCount);
//                        return;
//                    }
//
//                    undispenseDoseCount = currentUndispenseUnitCount.divide(dispensingFormItem.getUnitCount(), RoundingMode.DOWN);
//                    undispenseUnitCount = dispensingFormItem.getUnitCount();
//                } else {
                undispenseUnitCount = currentUndispenseUnitCount;
                undispenseDoseCount = BigDecimal.ONE;
//                }

                DispensingFormItem undispenseFormItem = new DispensingFormItem();
                undispenseFormItem.setId(dispenseItemId);
                undispenseFormItem.setUnitCount(undispenseUnitCount);
                undispenseFormItem.setUseDismounting(dispensingFormItem.getUseDismounting());
                undispenseFormItem.setDoseCount(undispenseDoseCount);
                undispenseFormItem.setSourceRefundFormItemId(chargeRefundItemInfo.getChargeRefundFormItemId());
                //药店dispensingFormItem 退药formItem，里面有追溯码
                if (!CollectionUtils.isEmpty(dispensingFormItem.getTraceableCodeList())) {
                    // 其实这里是有 bug 的，因为 dispensingFormItem 的里面追溯码并不代表所有需要退的追溯码，要在新的需求里面去调整了，这里只是保持了现状
                    undispenseFormItem.setTraceableCodeList(new ArrayList<>(dispensingFormItem.getTraceableCodeList()));
                }
//                undispenseFormItem.setDispensingFormItemBatches(dispensingItemBatches);
                undispenseItems.add(undispenseFormItem);
            });

            if (CollectionUtils.isEmpty(undispenseItems)) {
                return;
            }

            DispensingForm undispenseForm = new DispensingForm();
            undispenseForm.setId(dispensingForm.getId());
            undispenseForm.setSourceFormType(dispensingForm.getSourceFormType());
            undispenseForm.setDispensingFormItems(undispenseItems);
            undispenseForms.add(undispenseForm);
        });
        if (CollectionUtils.isEmpty(undispenseForms)) {
            return null;
        }

        DispensingSheet unDispenseSheet = new DispensingSheet();
        unDispenseSheet.setId(dispensingSheet.getId());
        unDispenseSheet.setDispensingForms(undispenseForms);
        return unDispenseSheet;
    }

    private static Map<String, ChargeRefundItemInfo> calculateDispenseItemShouldDispenseCount(ChargeSheet chargeSheet,
                                                                                              DispensingSheet dispensingSheet,
                                                                                              List<ChargeSheetMessage.ChargeFormItem> currentAddedChargeItems) {
        if (chargeSheet == null || dispensingSheet == null) {
            return null;
        }

        List<DispensingFormItem> dispensingFormItems = DispensingUtils.getDispensingFormItems(dispensingSheet);
        List<DispensingFormItem> dispenseItems = dispensingFormItems.stream()
                .filter(dispensingFormItem -> StringUtils.isBlank(dispensingFormItem.getAssociateFormItemId()))
                .collect(Collectors.toList());
        Map<String, DispensingFormItem> sourceItemIdToDispenseItem = ListUtils.toMap(dispenseItems, DispensingFormItem::getSourceFormItemId);
        Map<String, ChargeRefundItemInfo> dispenseItemIdToRefundItemInfo = new HashMap<>();

        Map<String, ChargeSheetMessage.ChargeFormItem> associateChargeItemIdToRefundChargeItem = new HashMap<>();
        try {
            if (CollectionUtils.isEmpty(currentAddedChargeItems)) {
                log.warn("没有退费项列表");
            } else {
                associateChargeItemIdToRefundChargeItem.putAll(currentAddedChargeItems.stream()
                        .filter(item -> item.getAssociateFormItemId() != null)
                        .collect(Collectors.toMap(ChargeSheetMessage.ChargeFormItem::getAssociateFormItemId, Function.identity())));
            }
        } catch (Exception e) {
            // 捕获 key 重复异常
            log.error("构建收费项当次的退费项映射失败", e);
        }

        ChargeSheetUtils.doWithChargeFormItem(chargeSheet, chargeFormItem -> {
            String sourceFormItemId = chargeFormItem.getId();
            DispensingFormItem dispenseItem = sourceItemIdToDispenseItem.get(sourceFormItemId);
            if (dispenseItem == null) {
                log.warn(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "发药项不存在，sourceFormItemId={}", sourceFormItemId);
                return;
            }

            List<ChargeFormItemBatchInfo> chargeFormItemBatches = chargeFormItem.getChargeFormItemBatchInfos();
            if (CollectionUtils.isEmpty(chargeFormItemBatches)) {
                log.warn(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "收费项没有批次信息，chargeFormItemId={}", chargeFormItem.getId());
                return;
            }

            ChargeRefundItemInfo chargeRefundItemInfo = dispenseItemIdToRefundItemInfo.computeIfAbsent(dispenseItem.getId(), dispenseItemId -> {
                ChargeSheetMessage.ChargeFormItem refundChargeFormItem = associateChargeItemIdToRefundChargeItem.get(chargeFormItem.getId());
                String refundFormItemId = refundChargeFormItem == null ? null : refundChargeFormItem.getId();
                if (StringUtils.isBlank(refundFormItemId)) {
                    log.warn("没有找到收费项 {} 对应的退费项", chargeFormItem.getId());
                }
                return new ChargeRefundItemInfo(dispenseItem.getId(), chargeFormItem.getId(), refundFormItemId);
            });

            for (ChargeFormItemBatchInfo chargeFormItemBatch : chargeFormItemBatches) {
                if (StringUtils.isNotBlank(chargeFormItemBatch.getAssociateItemBatchInfoId())) {
                    continue;
                }

                Map<Long, BigDecimal> batchIdToRefundUnitCount = chargeRefundItemInfo.getBatchIdToRefundUnitCount();
                BigDecimal refundUnitCount = batchIdToRefundUnitCount.getOrDefault(Long.parseLong(chargeFormItemBatch.getBatchId()), BigDecimal.ZERO);
                BigDecimal batchRefundUnitCount;
                if (chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED) {
                    batchRefundUnitCount = chargeFormItemBatch.getRefundUnitCount();
                } else if (chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.REFUNDED) {
                    batchRefundUnitCount = chargeFormItemBatch.getUnitCount();
                } else {
                    log.warn("收费项状态不是已收费或已退费，chargeFormItemId={}", chargeFormItem.getId());
                    continue;
                }
                refundUnitCount = MathUtils.wrapBigDecimalAdd(refundUnitCount, batchRefundUnitCount);
                batchIdToRefundUnitCount.put(Long.valueOf(chargeFormItemBatch.getBatchId()), refundUnitCount);
            }
        });
        return dispenseItemIdToRefundItemInfo;
    }

    private static Map<String, Map<Long, BigDecimal>> calculateDispenseItemUndispenseUnitCount(DispensingSheet dispensingSheet,
                                                                                               List<DispensingSheetOperation> undispenseOperations,
                                                                                               Map<String, DispensingForm> formIdToForm,
                                                                                               Map<String, DispensingFormItem> dispensingItemIdToDispensingItem,
                                                                                               List<GoodsStockLogItem> undispenseGoodsStockLogs) {
        if (CollectionUtils.isEmpty(undispenseOperations) || CollectionUtils.isEmpty(undispenseGoodsStockLogs)) {
            return new HashMap<>();
        }

        Map<String, DispensingFormItem> dispensingItemIdToDispenseItem = DispensingFormItemUtils.buildDispensingItemIdToDispenseItem(dispensingSheet, dispensingItemIdToDispensingItem);
        if (CollectionUtils.isEmpty(dispensingItemIdToDispenseItem)) {
            log.warn(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "项目对应的发药项为空");
            return new HashMap<>();
        }

        Map<String, Map<Long, BigDecimal>> dispenseItemIdToBatchUndispenseUnitCount = new HashMap<>();
        for (GoodsStockLogItem undispenseGoodsStockLog : undispenseGoodsStockLogs) {
            String dispensingItemId = undispenseGoodsStockLog.getOrderDetailId();
            DispensingFormItem dispenseItem = dispensingItemIdToDispenseItem.get(dispensingItemId);
            if (dispenseItem == null) {
                log.warn(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "退药项找不到对应的发药项 undispenseItemId:{}", dispensingItemId);
                continue;
            }

            Map<Long, BigDecimal> batchIdToUndispenseUnitCount = dispenseItemIdToBatchUndispenseUnitCount
                    .computeIfAbsent(dispenseItem.getId(), dispenseItemId -> new HashMap<>());
            BigDecimal undispenseUnitCount = batchIdToUndispenseUnitCount.computeIfAbsent(undispenseGoodsStockLog.getBatchId(), batchId -> BigDecimal.ZERO);
            BigDecimal stockCount = calculateStockCount(dispenseItem.getUseDismounting() == 1,
                    undispenseGoodsStockLog.getChangePackageCount(),
                    undispenseGoodsStockLog.getPieceNum(),
                    undispenseGoodsStockLog.getChangePieceCount());
            undispenseUnitCount = MathUtils.wrapBigDecimalAdd(undispenseUnitCount, stockCount);
            batchIdToUndispenseUnitCount.put(undispenseGoodsStockLog.getBatchId(), undispenseUnitCount);
        }

        return dispenseItemIdToBatchUndispenseUnitCount;
    }

    private static BigDecimal calculateStockCount(boolean isDismounting, BigDecimal packageCount, Integer pieceNum,
                                                  BigDecimal pieceCount) {
        if (isDismounting) {
            BigDecimal formattedPieceNum = formatPieceNum(pieceNum);
            return MathUtils.wrapBigDecimalAdd(cn.abcyun.cis.commons.util.MathUtils.wrapBigDecimalMultiply(packageCount, formattedPieceNum), pieceCount);
        } else {
            return cn.abcyun.cis.commons.util.MathUtils.wrapBigDecimalOrZero(packageCount);
        }
    }

    private static BigDecimal formatPieceNum(Integer pieceNum) {
        if (pieceNum == null || pieceNum <= 0) {
            return BigDecimal.ONE;
        }
        return BigDecimal.valueOf(pieceNum);
    }

    /**
     * 按照批次ID合并发药批次信息
     */
    private List<GoodsStockLogItem> mergeGoodsStockLogItems(List<GoodsStockLogItem> goodsStockLogItems) {
        if (CollectionUtils.isEmpty(goodsStockLogItems)) {
            return Lists.newArrayList();
        }

        return goodsStockLogItems.stream().collect(Collectors.groupingBy(GoodsStockLogItem::getOrderDetailId,
                        Collectors.toMap(GoodsStockLogItem::getBatchId, Function.identity(),
                                (goodsStockLogItemA, goodsStockLogItemB) -> {
                                    GoodsStockLogItem goodsStockLogItem = new GoodsStockLogItem();
                                    BeanUtils.copyProperties(goodsStockLogItemA, goodsStockLogItem);
                                    goodsStockLogItem.setChangePieceCount(MathUtils.wrapBigDecimalAdd(goodsStockLogItemA.getChangePieceCount(), goodsStockLogItemB.getChangePieceCount()));
                                    goodsStockLogItem.setChangePackageCount(MathUtils.wrapBigDecimalAdd(goodsStockLogItemA.getChangePackageCount(), goodsStockLogItemB.getChangePackageCount()));
                                    return goodsStockLogItem;
                                })))
                .values().stream().map(Map::values)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    @Data
    private static class ChargeRefundItemInfo {
        /**
         * 发药项ID
         */
        private String dispensingFormItemId;

        /**
         * 收费项ID
         */
        private String chargeFormItemId;

        /**
         * 退费项ID
         */
        private String chargeRefundFormItemId;

        /**
         * 退费批次信息
         */
        private Map<Long, BigDecimal> batchIdToRefundUnitCount = new HashMap<>();

        public ChargeRefundItemInfo(String dispensingFormItemId, String chargeFormItemId, String chargeRefundFormItemId) {
            this.dispensingFormItemId = dispensingFormItemId;
            this.chargeFormItemId = chargeFormItemId;
            this.chargeRefundFormItemId = chargeRefundFormItemId;
        }
    }


}
