package cn.abcyun.cis.dispensing.service;

import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisPatientOrderClient;
import cn.abcyun.bis.rpc.sdk.cis.model.patientorder.*;
import cn.abcyun.cis.commons.CisServiceResponseBody;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrderListReq;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrderListRsp;
import cn.abcyun.cis.core.util.FeignClientRpcTemplate;
import cn.abcyun.cis.dispensing.rpc.client.PatientOrderClient;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponseBody;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PatientOrderService {

    @Autowired
    private PatientOrderClient mPatientOrderClient;
    @Autowired
    private AbcCisPatientOrderClient abcCisPatientOrderClient;

    @HystrixCommand(fallbackMethod = "findPatientOrderFallback",
            commandProperties = {
                    @HystrixProperty(name = "execution.isolation.thread.timeoutInMilliseconds", value = "2000"),
                    @HystrixProperty(name = "execution.isolation.strategy", value = "THREAD")
            }
    )
    public PatientOrder findPatientOrder(String patientOrderId) {
        PatientOrder patientOrder = null;
        try {
            CisServiceResponseBody<PatientOrder> rsp = mPatientOrderClient.findPatientOrder(patientOrderId);
            if (rsp != null && rsp.getError() == null && rsp.getData() != null) {
                patientOrder = rsp.getData();
            }
        } catch (Exception e) {
            log.error("findPatientOrder orderId:" + patientOrderId + ", error:" + e.getMessage());
        }
        return patientOrder;
    }

    public PatientOrder findPatientOrderFallback(String patientOrderId) {
        return null;
    }

    public List<PatientOrder> findPatientOrderListByIdsFallback(String chainId, List<String> ids) {
        return new ArrayList<>();
    }
    @HystrixCommand(fallbackMethod = "findPatientOrderListByIdsFallback",
            commandProperties = {
                    @HystrixProperty(name = "execution.isolation.thread.timeoutInMilliseconds", value = "2000"),
                    @HystrixProperty(name = "execution.isolation.strategy", value = "THREAD")
            }
    )
    public List<PatientOrder> findPatientOrderListByIds(String chainId, List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        List<PatientOrder> patientOrders = new ArrayList<>();
        try {
            PatientOrderListReq req = new PatientOrderListReq();
            req.setChainId(chainId);
            req.setIds(ids);

            CisServiceResponseBody<PatientOrderListRsp> rsp = mPatientOrderClient.findPatientOrderList(req);
            if (rsp != null && rsp.getError() == null && rsp.getData() != null) {
                patientOrders = rsp.getData().getPatientOrders();
            }
        } catch (Exception e) {
            log.error("findPatientOrderListByIds", e);
        }
        return patientOrders;
    }

    public PatientOrderHospitalVO findPatientOrderHospital(String chainId, String clinicId, String patientOrderId) {
        PatientOrderHospitalVO patientOrder = null;
        try {
            AbcServiceResponseBody<PatientOrderHospitalVO> rsp = abcCisPatientOrderClient.getPatientOrderHospitalById(patientOrderId, chainId, clinicId);
            if (rsp != null && rsp.getError() == null && rsp.getData() != null) {
                patientOrder = rsp.getData();
            }
        } catch (Exception e) {
            log.error("findPatientOrder orderId:" + patientOrderId + ", error:" + e.getMessage());
        }
        return patientOrder;
    }

    public Map<String, PatientOrderHospitalVO> findPatientOrderListHospital(String chainId, String clinicId, List<String> patientOrderIdList) {
        Map<String, PatientOrderHospitalVO> patientOrderIdToPatientOrder = new HashMap<>();
        if (CollectionUtils.isEmpty(patientOrderIdList)) {
            return patientOrderIdToPatientOrder;
        }
        PatientOrderHospitalListReq req = new PatientOrderHospitalListReq();
        req.setChainId(chainId);
        req.setClinicId(clinicId);
        req.setIds(patientOrderIdList);
        try {
            AbcServiceResponseBody<AbcListPage<PatientOrderHospitalVO>> rsp = FeignClientRpcTemplate.dealThirdPartyMethodWithLogFlag("getPatientOrderHospitalListByIds", true,
                    () -> abcCisPatientOrderClient.getPatientOrderHospitalListByIds(req), true, req);
            if (rsp != null && rsp.getError() == null && rsp.getData() != null) {
                patientOrderIdToPatientOrder.putAll(rsp.getData().getRows().stream().collect(Collectors.toMap(PatientOrderHospitalVO::getId, Function.identity(), (a, b) -> a)));
            }
        } catch (Exception e) {
            log.error("findPatientOrder  error:" + e.getMessage());
        }
        return patientOrderIdToPatientOrder;
    }

    @HystrixCommand(fallbackMethod = "listLocksByPatientOrderIdFallback",
            commandProperties = {
                    @HystrixProperty(name = "execution.isolation.thread.timeoutInMilliseconds", value = "2000"),
                    @HystrixProperty(name = "execution.isolation.strategy", value = "THREAD")
            }
    )
    public List<BusinessLockWithStatusVO> listLocksByPatientOrderId(String patientOrderId, String chainId, String clinicId) {

        if (StringUtils.isAnyBlank(patientOrderId, chainId, clinicId)) {
            return new ArrayList<>();
        }

        RpcQueryLocksByBusinessKeysReq req = new RpcQueryLocksByBusinessKeysReq();
        req.setBusinessKeys(Arrays.asList(BusinessLockVO.BusinessKey.CHARGE_SHEET));
        req.setChainId(chainId);
        req.setClinicId(clinicId);

        return Optional.ofNullable(FeignClientRpcTemplate.dealRpcClientMethod("queryLocksByPatientOrderId",
                        () -> abcCisPatientOrderClient.queryLocksByPatientOrderId(patientOrderId, req),
                        patientOrderId, req))
                .map(AbcListPage::getRows)
                .orElse(new ArrayList<>())
                .stream()
                .filter(businessLockWithStatusVO -> businessLockWithStatusVO.getStatus() == BusinessLockVO.LockStatus.LOCKED)
                .collect(Collectors.toList());
    }

    /**
     * 查询patientOrderId锁列表的熔断方法
     * {@link  PatientOrderService#listLocksByPatientOrderId(String, String, String)}
     * @param patientOrderId
     * @param chainId
     * @param clinicId
     * @return
     */
    public List<BusinessLockWithStatusVO> listLocksByPatientOrderIdFallback(String patientOrderId, String chainId, String clinicId) {
        return new ArrayList<>();
    }
}
