package cn.abcyun.cis.dispensing.service;

import cn.abcyun.bis.rpc.sdk.cis.message.charge.ChargeSheetUpdateRemarkMessage;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.UpdateTraceableCodeReq;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.print.ChargeSheetAstResult;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.print.ChargeSheetAstResultRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.*;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsPharmacyView;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsStockInfo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.TraceableCode;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslog.GoodsStockLogItem;
import cn.abcyun.bis.rpc.sdk.his.message.advice.*;
import cn.abcyun.bis.rpc.sdk.his.message.charge.HisChargeFormItemDto;
import cn.abcyun.bis.rpc.sdk.his.model.advice.AdviceDetailView;
import cn.abcyun.bis.rpc.sdk.his.model.charge.HisChargeFormItemView;
import cn.abcyun.bis.rpc.sdk.his.model.charge.HisChargeSheetView;
import cn.abcyun.bis.rpc.sdk.property.base.PropertyKey;
import cn.abcyun.bis.rpc.sdk.property.model.ChainBasic;
import cn.abcyun.bis.rpc.sdk.property.model.PrintMedicalDocumentsInfusion;
import cn.abcyun.bis.rpc.sdk.property.model.TraceCodeConfig;
import cn.abcyun.bis.rpc.sdk.property.service.PropertyService;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.rpc.charge.Constants;
import cn.abcyun.cis.commons.rpc.charge.SimpleChargeInfo;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.commons.transform.ShebaoDismountingFlagConst;
import cn.abcyun.cis.commons.util.DateUtils;
import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.dispensing.api.protocol.ChargeSheetDispenseStatusRsp;
import cn.abcyun.cis.dispensing.api.protocol.SaveTraceableCodeDraftReq;
import cn.abcyun.cis.dispensing.base.DispensingConstants;
import cn.abcyun.cis.dispensing.base.exception.DispenseSheetChangedException;
import cn.abcyun.cis.dispensing.base.exception.DispensingServiceError;
import cn.abcyun.cis.dispensing.base.exception.DispensingServiceException;
import cn.abcyun.cis.dispensing.controller.DispensingItemMerger;
import cn.abcyun.cis.dispensing.domain.*;
import cn.abcyun.cis.dispensing.domain.DispensingForm;
import cn.abcyun.cis.dispensing.domain.DispensingFormItem;
import cn.abcyun.cis.dispensing.domain.DispensingFormItemBatchInfo;
import cn.abcyun.cis.dispensing.domain.DispensingFormItemExtendData;
import cn.abcyun.cis.dispensing.domain.DispensingOrder;
import cn.abcyun.cis.dispensing.domain.DispensingSheet;
import cn.abcyun.cis.dispensing.mybatis.mapper.DispensingMapper;
import cn.abcyun.cis.dispensing.mybatis.mapper.DispensingSheetMapper;
import cn.abcyun.cis.dispensing.repository.*;
import cn.abcyun.cis.dispensing.rpc.client.OutpatientClient;
import cn.abcyun.cis.dispensing.service.dto.DispensingFormItemBatchInfoView;
import cn.abcyun.cis.dispensing.service.dto.DispensingFormItemView;
import cn.abcyun.cis.dispensing.service.dto.DispensingSheetOrderRelDto;
import cn.abcyun.cis.dispensing.service.dto.DispensingSheetView;
import cn.abcyun.cis.dispensing.service.dto.print.DispensingSheetAstResultView;
import cn.abcyun.cis.dispensing.service.rpc.ClinicClient;
import cn.abcyun.cis.dispensing.service.rpc.GoodsLogService;
import cn.abcyun.cis.dispensing.service.rpc.ScGoodsFeignClient;
import cn.abcyun.cis.dispensing.util.DispensingUtils;
import cn.abcyun.cis.dispensing.util.DispensingViewUtils;
import cn.abcyun.cis.dispensing.util.RedisUtils;
import cn.abcyun.common.model.AbcListPage;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.abcyun.cis.dispensing.service.DispensingSheetHospitalCreateUpdate.ADVICE_EXECUTE;
import static cn.abcyun.cis.dispensing.service.DispensingSheetHospitalCreateUpdate.ADVICE_RULE;

/***
 * 发药单相关的 从DispensingService 里面迁移出来·
 * */
@Service
public class DispensingSheetService {
    private static final Logger sLogger = LoggerFactory.getLogger(DispensingSheetService.class);
    @Autowired
    private DispensingSheetRepository dispensingSheetRepository;
    //非级连加载的DispenseSheet
    @Autowired
    private DispensingSheetV2Repository dispensingSheetV2Repository;
    @Autowired
    private PatientOrderService patientOrderService;
    @Autowired
    private ScGoodsFeignClient scGoodsFeignClient;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private OutpatientClient outpatientClient;
    @Autowired
    private PropertyService propertyService;
    @Autowired
    private ChargeService chargeService;
    @Autowired
    private ClinicClient clinicClient;
    @Autowired
    private DispensingSheetServerCreateFactory dispensingSheetServerCreateFactory;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private GoodsLogService goodsLogService;
    @Autowired
    private DispensingSheetEntityService dispensingSheetEntityService;
    @Autowired
    private DispensingLogService dispensingLogService;
    @Autowired
    private DispensingLogRepository dispensingLogRepository;

    @Autowired
    private DispensingProcessService dispensingProcessService;
    @Autowired
    private DispensingFormItemRepository dispensingFormItemRepository;
    @Autowired
    private DispensingSheetMapper dispensingSheetMapper;
    @Autowired
    private DispensingFormItemV2Repository dispensingFormItemV2Repository;
    @Autowired
    private DispensingFormItemBatchInfoRepository dispensingFormItemBatchInfoRepository;
    @Autowired
    private DispensingOrderService dispensingOrderService;
    @Autowired
    private DispensingMapper dispensingMapper;
    @Autowired
    private AbcCisScGoodsService abcCisScGoodsService;

    /**
     * 获取单个发药单
     */
    public DispensingSheetView findDispensingSheetViewById(String dispensingSheetId, boolean withDeleted) throws ServiceInternalException {
        if (StringUtils.isEmpty(dispensingSheetId)) {
            throw new ParamRequiredException("id");
        }
        /**
         * 拉取单个发药单
         * */
        DispensingSheet dispensingSheet = dispensingSheetEntityService.findById(dispensingSheetId, withDeleted);
        if (dispensingSheet == null) {
            throw new NotFoundException();
        }
        return DispensingViewUtils.fillDispensingSheetView(dispensingSheet, patientOrderService, scGoodsFeignClient, employeeService, outpatientClient, propertyService, chargeService, null, clinicClient);
    }


    /**
     * 从dispensingServcie 迁移过来 刚接手，还不敢删
     */
    @Deprecated
    public DispensingSheetView findDispensingSheetViewBySourceSheetId(String sourceSheetId, String clinicId) throws ServiceInternalException {
        DispensingSheet dispensingSheet = dispensingSheetRepository.findFirstBySourceSheetIdAndClinicIdAndIsDeleted(sourceSheetId, clinicId, 0).orElse(null);
        if (dispensingSheet == null) {
            throw new NotFoundException();
        }

        /**
         * 拉取patientOrder
         * */
        PatientOrder patientOrder = patientOrderService.findPatientOrder(dispensingSheet.getPatientOrderId());
        dispensingSheet.setPatientOrder(patientOrder);
        DispensingSheetView dispensingSheetView = DispensingViewUtils.dispensingSheetViewFromDispensingSheet(dispensingSheet, patientOrder, scGoodsFeignClient, employeeService);
        if (dispensingSheetView == null) {
            throw new NotFoundException();
        }
        /**
         * 拉取打印配置 和收费单配置
         * */
        PrintMedicalDocumentsInfusion printConfig = propertyService.getPropertyValueByKey(PropertyKey.PRINT_MEDICAL_DOCUMENTS_INFUSION, dispensingSheetView.getClinicId(), PrintMedicalDocumentsInfusion.class);
        ChainBasic chainBasic = propertyService.getPropertyValueByKey(PropertyKey.CHAIN_BASIC, dispensingSheet.getChainId(), ChainBasic.class);
        SimpleChargeInfo simpleChargeInfo = chargeService.getSimpleChargeInfo(dispensingSheetView.getSourceSheetId());
        DispensingViewUtils.mergeDispensingSheetView(dispensingSheetView, printConfig, simpleChargeInfo, outpatientClient, chainBasic);

        return dispensingSheetView;
    }

    /***
     * 迁移 把 DispensingService 和 DispensingController
     * for Rpc
     */
    public DispensingSheet findDispensingSheetBySourceSheetId(String sourceSheetId) {
        if (TextUtils.isEmpty(sourceSheetId)) {
            throw new ParamRequiredException("sourceSheetId");
        }
        List<DispensingSheet> dispensingSheetList = dispensingSheetEntityService.findAllBySourceSheetId(sourceSheetId);
        if (CollectionUtils.isEmpty(dispensingSheetList)) {
            sLogger.info("getDispensingSheetBySourceSheetId:{}, Not Found", sourceSheetId);
            throw new NotFoundException();
        }
        DispensingSheet dispensingSheet = dispensingSheetList.get(0);

        DispensingItemMerger.mergeForDispensingSheet(dispensingSheet);
        return dispensingSheet;
    }

    /**
     * 迁移 把 DispensingService 和 DispensingController 的代码合并到这里来
     * for Rpc
     */
    public List<DispensingSheet> findDispensingSheetListBySourceSheetId(String sourceSheetId, Integer queryGoodsStockLog) {
        if (StringUtils.isEmpty(sourceSheetId)) {
            throw new ParamRequiredException("sourceSheetId");
        }
        // 1、查询所有发药单
        List<DispensingSheet> dispensingSheetList = dispensingSheetEntityService.findAllBySourceSheetId(sourceSheetId);
        if (CollectionUtils.isEmpty(dispensingSheetList)) {
            sLogger.info("sourceSheetId ={} ,dispensingSheets is null", sourceSheetId);
            throw new NotFoundException();
        }
        // 过滤掉历史退药项
        dispensingSheetList.forEach(dispensingSheet -> {
            DispensingUtils.doWithDispensingForm(dispensingSheet, form -> {
                if (CollectionUtils.isEmpty(form.getDispensingFormItems())) {
                    return;
                }

                form.setDispensingFormItems(form.getDispensingFormItems().stream().filter(item -> item.getIsHistoryItem() == 0).collect(Collectors.toList()));
            });
        });
        // 2、查询药房退费设置
        DispensingSheet dispensingSheetFirst = dispensingSheetList.get(0);
        String chainId = dispensingSheetFirst.getChainId();
        String clinicId = dispensingSheetFirst.getClinicId();
        Map<Integer, Map<Integer, Integer>> pharmacyNoGeneralStatusEnableRefundFeeMap = scGoodsFeignClient.findPharmacyByClinic(chainId, clinicId)
                .stream()
                .collect(Collectors.toMap(GoodsPharmacyView::getNo,
                        goodsPharmacyView -> Optional.ofNullable(goodsPharmacyView.getExternalPharmacyConfig())
                                .map(GoodsPharmacyView.ExternalPharmacyConfig::getRefundRules)
                                .orElse(Lists.newArrayList())
                                .stream()
                                .collect(Collectors.toMap(GoodsPharmacyView.ExternalPharmacyRefundRule::getGeneralStatus, refundRule -> Objects.isNull(refundRule.getRefundStatus()) ? 0 : refundRule.getRefundStatus()))

                ));
        if (Objects.equals(queryGoodsStockLog, YesOrNo.YES)) {
            bindGoodsStockLog(chainId, clinicId, dispensingSheetList);
        }
        dispensingSheetList.stream()
                .filter(dispensingSheet -> !CollectionUtils.isEmpty(dispensingSheet.getDispensingForms()))
                .forEach(dispensingSheet -> dispensingSheet.getDispensingForms()
                        .stream()
                        .filter(dispensingForm -> Objects.nonNull(dispensingForm.getProcessGeneralStatus()))
                        .forEach(dispensingForm -> {
                                    dispensingForm.setEnableRefundFee(
                                            pharmacyNoGeneralStatusEnableRefundFeeMap.getOrDefault(dispensingSheet.getPharmacyNo(), new HashMap<>(1))
                                                    .getOrDefault(dispensingForm.getProcessGeneralStatus(), 0));
                                }
                        )
                );

        List<DispensingSheet> mergeTraceCodeErrorSheets = new ArrayList<>();
        AtomicBoolean mergeTraceCodeError = new AtomicBoolean(false);
        for (DispensingSheet dispensingSheet : dispensingSheetList) {
            DispensingItemMerger.mergeForDispensingSheet(dispensingSheet, mergeTraceCodeError);
            if (mergeTraceCodeError.get()) {
                mergeTraceCodeErrorSheets.add(dispensingSheet);
            }
        }

        if (!CollectionUtils.isEmpty(mergeTraceCodeErrorSheets)) {
            /*
                merge 失败的原因就是因为需要 goods 信息，所以需要先绑定 goods 信息，然后再次 merge
                注：这里是一个非常恶心的逻辑，因为在 merge 追溯码的时候可能涉及 pieceCount 和 packageCount 的单位转换，而 dispensingFormItem 本身又没有 pieceNum，所以
                  如果在发现必须要 pieceNum 的时候，就只能触发一次 goods 的拉取，然后再次 merge
             */
            mergeTraceCodeErrorSheets.forEach(dispensingSheet -> {
                dispensingSheet.refreshProductInfo(scGoodsFeignClient);

                DispensingUtils.doWithDispensingForm(dispensingSheet, form -> {
                    if (form == null || CollectionUtils.isEmpty(form.getDispensingFormItems())) {
                        return;
                    }
                    DispensingItemMerger.mergeMainDispensingItemTraceCode(form.getDispensingFormItems(), null);
                });
            });
        }

        // 过滤掉整个发药单中的特殊无码，否则收费单那边就会展示出来
        DispensingUtils.doWithDispensingItem(dispensingSheetList, item -> {
            if (CollectionUtils.isEmpty(item.getTraceableCodeList())) {
                return;
            }

            item.getTraceableCodeList().removeIf(code -> code == null
                    || (Objects.equals(code.getType(), GoodsConst.DrugIdentificationCodeType.NO_CODE) && StringUtils.isBlank(code.getNo())));
        });
        return dispensingSheetList;
    }

    /**
     * 绑定发药单的库存日志
     */
    private void bindGoodsStockLog(String chainId, String clinicId, List<DispensingSheet> dispensingSheetList) {
        if (CollectionUtils.isEmpty(dispensingSheetList)) {
            return;
        }

        // 查询发药项的log
        Map<String, List<GoodsStockLogItem>> dispensingFormItemIdToGoodsItemLogs = goodsLogService.getDispensingSheetCurrentGoodsStockLogMap(chainId, clinicId, dispensingSheetList);
        if (CollectionUtils.isEmpty(dispensingFormItemIdToGoodsItemLogs)) {
            return;
        }

        List<Long> stockIds = dispensingFormItemIdToGoodsItemLogs.values().stream().flatMap(Collection::stream).map(GoodsStockLogItem::getStockId).distinct().collect(Collectors.toList());
        Map<Long, Long> stockIdToBatchId = scGoodsFeignClient.queryStockInfoByGoodsIds(chainId, clinicId, stockIds).stream()
                .collect(Collectors.toMap(GoodsStockInfo::getStockId, GoodsStockInfo::getBatchId, (a, b) -> a));

        DispensingUtils.doWithDispensingItem(dispensingSheetList, item -> {
            List<GoodsStockLogItem> goodsStockLogItems = dispensingFormItemIdToGoodsItemLogs.get(item.getId());
            if (CollectionUtils.isEmpty(goodsStockLogItems)) {
                return;
            }
            item.setGoodsStockLogItems(goodsStockLogItems);
            goodsStockLogItems.forEach(goodsStockLogItem -> {
                goodsStockLogItem.setBatchId(stockIdToBatchId.get(goodsStockLogItem.getStockId()));
            });
        });
    }

    /**
     * 医院医嘱 建发药单
     * 1.因为一个医嘱的发药单会丢到一个取药单里面 医嘱消息会并发的来，不加锁会导致取药单被重复生成
     * 2.加锁的粒度 按门店 部门 病区 药房号来锁
     */
    public void createDispensingSheetFromAdviceExecuteMessage(AdviceExecuteMessage adviceExecuteMessage,
                                                              DispensingOrder curDispensingOrder,
                                                              List<AdviceRuleItemMessage> ruleItemMessageList,
                                                              boolean supplement) {
        DispensingSheetCreateUpdateBase hospitalDis =
                dispensingSheetServerCreateFactory.dispensingSheetCreateUpdateBase(adviceExecuteMessage,
                        curDispensingOrder, ruleItemMessageList, supplement);
        hospitalDis.createOrUpdateDispenseSheet();
        hospitalDis.createAutoDispenseSheet();
    }

    /**
     * 医院医嘱 建发药单
     * 1.因为一个医嘱的发药单会丢到一个取药单里面 医嘱消息会并发的来，不加锁会导致取药单被重复生成
     * 2.加锁的粒度 按门店 部门 病区 药房号来锁
     */
    public void createDispensingSheetFromAdviceMessage(AdviceMessage adviceMessage, DispensingOrder curDispensingOrder) {
        DispensingSheetCreateUpdateBase hospitalDis = dispensingSheetServerCreateFactory.dispensingSheetCreateUpdateBase(adviceMessage, curDispensingOrder);
        hospitalDis.createOrUpdateDispenseSheet();
        hospitalDis.createAutoDispenseSheet();
    }

    /**
     * 如果发药单还没发药直接关闭发药单
     * 发药服务已经监听了医嘱的消息，发药服务不需要发送关闭后的消息
     *
     * @param adviceMessage      医嘱执行消息
     * @param curDispensingOrder 无效参数，只是为了方便复用代码
     */
    public void closeDispensingSheetFromAdviceMessage(AdviceMessage adviceMessage, DispensingOrder curDispensingOrder) {
        DispensingSheetCreateUpdateBase hospitalDis = dispensingSheetServerCreateFactory.dispensingSheetCreateUpdateBase(adviceMessage, curDispensingOrder);
        hospitalDis.closeUpdateDispenseSheet(ADVICE_RULE);
    }


    /**
     * 如果发药单还没发药直接关闭发药单
     * 发药服务已经监听了医嘱的消息，发药服务不需要发送关闭后的消息
     *
     * @param adviceExecuteMessage 医嘱执行消息
     * @param curDispensingOrder   无效参数，只是为了方便复用代码
     */
    public void closeDispensingSheetFromAdviceMessage(AdviceExecuteMessage adviceExecuteMessage, DispensingOrder curDispensingOrder) {
        DispensingSheetCreateUpdateBase hospitalDis =
                dispensingSheetServerCreateFactory.dispensingSheetCreateUpdateBase(adviceExecuteMessage,
                        curDispensingOrder, null, false);
        hospitalDis.closeUpdateDispenseSheet(ADVICE_EXECUTE);
    }

    /**
     * 统计一个收费单对应的发药状态
     * 主要用于收费同时发药
     */
    public ChargeSheetDispenseStatusRsp getChargeSheetDispenseStatus(String chainId,
                                                                     String clinicId,
                                                                     String chargeSheetId
    ) {
        ChargeSheetDispenseStatusRsp clientRsp = new ChargeSheetDispenseStatusRsp();

        //只加载sheet就可以了
        List<DispensingSheetV2> dispensingSheetV2List = dispensingSheetV2Repository.findAllByChainIdAndClinicIdAndSourceSheetIdAndIsDeleted(chainId, clinicId, chargeSheetId, DispensingUtils.DeleteFlag.NOT_DELETED);
        if (CollectionUtils.isEmpty(dispensingSheetV2List)) {
            throw new DispensingServiceException(DispensingServiceError.DISPENSE_HAS_NO_SHEET);
        }
        clientRsp.setDispenseSheetDispenseStatusList(dispensingSheetV2List.stream().map(dispensingSheetV2 -> {
            ChargeSheetDispenseStatusRsp.DispenseSheetDispenseStatus item = new ChargeSheetDispenseStatusRsp.DispenseSheetDispenseStatus();
            item.setDispenseSheetId(dispensingSheetV2.getId());
            item.setStatus(dispensingSheetV2.getStatus());
            return item;
        }).collect(Collectors.toList()));
        int undispenseCount = 0;
        int dispenseCount = 0;
        int waitingCount = 0;
        int cancleCount = 0;
        List<String> dispensingSheetIdRedisKeyList = new ArrayList<>();
        for (ChargeSheetDispenseStatusRsp.DispenseSheetDispenseStatus dispenseSheetDispenseStatus : clientRsp.getDispenseSheetDispenseStatusList()) {
            switch (dispenseSheetDispenseStatus.getStatus()) {
                case DispenseConst.Status.WAITING:
                    waitingCount++;
                    dispensingSheetIdRedisKeyList.add(RedisUtils.getChargeAndDispensingRedisKey(clinicId, dispenseSheetDispenseStatus.getDispenseSheetId()));
                    break;
                case DispenseConst.Status.DISPENSED:
                    dispenseCount++;
                    break;
                case DispenseConst.Status.UNDISPENSED:
                    undispenseCount++;
                    break;
                case DispenseConst.Status.CLOSED:
                    cancleCount++;
                    break;
            }
        }

        int totalCount = clientRsp.getDispenseSheetDispenseStatusList().size();
        if (waitingCount > 0) {//又一个发药单没发，就是没发
            List<Object> objects = redisUtils.mGet(dispensingSheetIdRedisKeyList);
            objects = objects == null ? new ArrayList<>() : objects.stream().filter(Objects::nonNull).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(objects)) {
                // 存在说明有goods存在扣库失败
                throw new DispensingServiceException(DispensingServiceError.CHARGE_AND_DISPENSING_FAIL);
            }
            clientRsp.setStatus(DispenseConst.Status.WAITING);
        } else if (dispenseCount > 0) { // 这里是else 有一个还是发药状态 整个收费单就是发药状态
            clientRsp.setStatus(DispenseConst.Status.DISPENSED);
        } else if (undispenseCount > 0) { //这里是else  有一个退药状态 整个收费单就是退药状态
            clientRsp.setStatus(DispenseConst.Status.UNDISPENSED);
        } else if (cancleCount == totalCount) { //全部推完
            clientRsp.setStatus(DispenseConst.Status.CLOSED);
        }
        return clientRsp;

    }

    public DispensingSheetAstResultView getDispensingSheetAstResult(String dispensingSheetId, String clinicId) {
        DispensingSheetAstResultView dispensingSheetAstResultView = new DispensingSheetAstResultView();
        dispensingSheetAstResultView.setList(new ArrayList<>());
        DispensingSheet dispensingSheet = dispensingSheetRepository.findByIdAndClinicIdAndIsDeleted(dispensingSheetId, clinicId, DispensingUtils.DeleteFlag.NOT_DELETED).orElse(null);
        if (Objects.isNull(dispensingSheet)) {
            return dispensingSheetAstResultView;
        }
        List<DispensingForm> dispensingForms = dispensingSheet.getDispensingForms();
        if (CollectionUtils.isEmpty(dispensingForms)) {
            return dispensingSheetAstResultView;
        }
        List<DispensingForm> infusionFormList = dispensingForms.stream()
                .filter(it -> it.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_INFUSION || it.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_WESTERN).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(infusionFormList)) {
            return dispensingSheetAstResultView;
        }
        ChargeSheetAstResultRsp chargeSheetAstResult = chargeService.getChargeSheetAstResult(dispensingSheet.getSourceSheetId(), clinicId);
        if (Objects.isNull(chargeSheetAstResult) || CollectionUtils.isEmpty(chargeSheetAstResult.getList())) {
            return dispensingSheetAstResultView;
        }
        Map<String, ChargeSheetAstResult> chargeFormItemIdToAstResult =
                chargeSheetAstResult.getList().stream().collect(Collectors.toMap(ChargeSheetAstResult::getChargeFormItemId, Function.identity(), (a, b) -> a));
        infusionFormList.forEach(form -> form.getDispensingFormItems().forEach(formItem -> {
            DispensingSheetAstResultView.AstResultItem astResultItem = new DispensingSheetAstResultView.AstResultItem();
            astResultItem.setFormItemId(formItem.getId());
            astResultItem.setGoodsId(formItem.getProductId());
            ChargeSheetAstResult chargeAstResult = chargeFormItemIdToAstResult.get(formItem.getSourceFormItemId());
            if (Objects.nonNull(chargeAstResult)) {
                astResultItem.setAst(chargeAstResult.getAst());
                astResultItem.setAstResult(chargeAstResult.getAstResult());
            }
            dispensingSheetAstResultView.getList().add(astResultItem);
        }));
        return dispensingSheetAstResultView;
    }

    public void createDispensingSheetFromHisChargeSheetMessage(HisChargeSheetView chargeSheet,
                                                               List<HisChargeFormItemView> chargeItems,
                                                               DispensingOrder dispensingOrder,
                                                               String operatorId) {
        DispensingSheetCreateUpdateBase createSheetBase = dispensingSheetServerCreateFactory.dispensingSheetCreateBase(chargeSheet, chargeItems, dispensingOrder, operatorId);
        createSheetBase.createOrUpdateDispenseSheet();
        createSheetBase.createAutoDispenseSheet();
    }

    public void createDispensingSheetFromAdviceUsageRuleItemAddMessage(AdviceUsageRuleItemsAddMessage adviceUsageRule,
                                                                       List<AdviceUsageRuleItemMessage> ruleItemMessageList,
                                                                       DispensingOrder dispensingOrder,
                                                                       String operatorId) {
        DispensingSheetCreateUpdateBase createSheetBase = dispensingSheetServerCreateFactory.dispensingSheetCreateBase(adviceUsageRule, ruleItemMessageList, dispensingOrder, operatorId);
        createSheetBase.createOrUpdateDispenseSheet();
        createSheetBase.createAutoDispenseSheet();
    }

    public void createDispensingSheetFromAdviceNeedReDispenseMessage(AdviceNeedReDispenseMessage adviceNeedReDispenseMessage,
                                                                     AdviceExecuteNeedReDispenseMessage adviceExecuteNeedReDispenseMessage,
                                                                     List<AdviceRuleItemMessage> ruleItemMesageList,
                                                                     DispensingOrder dispensingOrder,
                                                                     String operatorId,
                                                                     int reDispenseActionType) {
        DispensingSheetHospitalAdviceReDispenseCreateUpdate createSheetBase =
                dispensingSheetServerCreateFactory.dispensingSheetCreateBase(adviceNeedReDispenseMessage,
                        adviceExecuteNeedReDispenseMessage, ruleItemMesageList, dispensingOrder, operatorId, reDispenseActionType);
        if (!createSheetBase.isNeedReNewSheet()) {
            sLogger.info("createDispensingSheetFromAdviceNeedReDispenseMessage, no need to create new sheet,adviceId={}", adviceNeedReDispenseMessage.getAdviceMessage().getId());
            return;
        }
        createSheetBase.createOrUpdateDispenseSheet();
        createSheetBase.createAutoDispenseSheet();
    }

    /**
     * 更新追溯码 空列表代表删除
     */
    @Transactional(rollbackFor = Exception.class)
    public DispensingSheetView saveTraceableCode(SaveTraceableCodeDraftReq clientReq) {
        if (CollectionUtils.isEmpty(clientReq.getList())) {
            throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "追溯码不能为空");
        }
        List<DispensingSheet> dispensingSheets = dispensingSheetEntityService.findAllSameSourceSheetById(clientReq.getChainId(), clientReq.getClinicId(), clientReq.getDispensingSheetId());
        if (CollectionUtils.isEmpty(dispensingSheets)) {
            throw new NotFoundException();
        }
        TraceCodeConfig traceCodeConfig = propertyService.getPropertyValueByKey(PropertyKey.TRACE_CODE_CONFIG, clientReq.getClinicId(), TraceCodeConfig.class);
        if (traceCodeConfig != null && Objects.equals(traceCodeConfig.getCollectionSwitch(), YesOrNo.YES) && Objects.equals(traceCodeConfig.getDispenseCollCheckStrictMode(), YesOrNo.YES)) {
            for (DispensingSheet dispensingSheet : dispensingSheets) {
                // 强控模式下 已发和部分发药的发药单不允许编辑追溯码
                if (DispensingUtils.isPartDispensedSheet(dispensingSheet) || dispensingSheet.getStatus() == DispensingSheet.Status.DISPENSED) {
                    throw new DispensingServiceException(DispensingServiceError.TRACE_CODE_NOT_SUPPORT_MODIFY);
                }
            }
        }
        DispensingSheet selectDispensingSheet = dispensingSheets.stream().filter(it -> Objects.equals(it.getId(), clientReq.getDispensingSheetId())).findFirst().orElse(null);
        if (selectDispensingSheet.getStatus() != DispensingSheet.Status.WAITING) {
            throw new DispenseSheetChangedException();
        }

        Map<String, DispensingFormItem> formItemIdToItem = ListUtils.toMap(DispensingUtils.getDispensingFormItems(selectDispensingSheet), DispensingFormItem::getId);

        // 保存追溯码到发药单上
        doSaveTraceableCode(formItemIdToItem, clientReq);

        // 通知给 ScGoods
        abcCisScGoodsService.updateUseTraceCode(clientReq.getChainId(), clientReq.getClinicId(), dispensingSheets, clientReq.getEmployeeId());

        // 通知给 charge
        chargeService.updateUseTraceCode(selectDispensingSheet, clientReq.getEmployeeId());

        dispensingSheetEntityService.save(selectDispensingSheet);

        return findDispensingSheetViewById(clientReq.getDispensingSheetId(), false);
    }

    private void doSaveTraceableCode(Map<String, DispensingFormItem> formItemIdToItem, SaveTraceableCodeDraftReq clientReq) {
        for (SaveTraceableCodeDraftReq.TraceableCodeDraft itemReq : clientReq.getList()) {
            /*
              空或null标表删除
              */
            if (!CollectionUtils.isEmpty(itemReq.getComposeChildren())) {
                for (SaveTraceableCodeDraftReq.TraceableCodeDraft childItemReq : itemReq.getComposeChildren()) {
                    DispensingFormItem formItem = formItemIdToItem.get(childItemReq.getId());
                    if (formItem == null) {
                        throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "发药项不存在");
                    }
                    updateFromItemTraceableCode(childItemReq, formItem);
                }
            } else {
                DispensingFormItem formItem = formItemIdToItem.get(itemReq.getId());
                if (formItem == null) {
                    throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "发药项不存在");
                }
                updateFromItemTraceableCode(itemReq, formItem);
            }
        }
    }

    /**
     * 编辑发药项的追溯码（仅支持编辑在库状态下的追溯码）
     */
    private static void updateFromItemTraceableCode(SaveTraceableCodeDraftReq.TraceableCodeDraft itemReq,
                                                    DispensingFormItem formItem) {
        if (itemReq == null || formItem == null || !DispensingFormItem.Status.dispenseable.contains(formItem.getStatus())) {
            return;
        }

        DispensingFormItemExtendData extendData = formItem.getExtendData();
        if (extendData == null) {
            extendData = new DispensingFormItemExtendData();
            formItem.setExtendData(extendData);
        }
        if (formItem.getShebaoDismountingFlag() == null || DispensingUtils.checkFlagOn(formItem.getShebaoDismountingFlag(), ShebaoDismountingFlagConst.IS_EDITABLE)) {
            formItem.setShebaoDismountingFlag(itemReq.getShebaoDismountingFlag());
        } else {
            if (!Objects.equals(formItem.getShebaoDismountingFlag(), itemReq.getShebaoDismountingFlag())) {
                sLogger.warn("updateFromItemTraceableCode,拆零标识不能修改,formItemId={}, before={} after:{}", formItem.getId(), formItem.getShebaoDismountingFlag(), itemReq.getShebaoDismountingFlag());
//                throw new DispensingServiceException(DispensingServiceError.DISPENSING_PARAMETER_ERROR, "拆零标识不能修改");
            }
        }

        List<TraceableCode> serverTraceableCodeList = formItem.getTraceableCodeList() == null ? new ArrayList<>() : formItem.getTraceableCodeList();
        List<TraceableCode> traceableCodeList = serverTraceableCodeList.stream()
                .filter(traceableCode -> traceableCode.getUsed() != DispensingConstants.TraceableCodeUsed.WAITING)
                .collect(Collectors.toList());
        if (traceableCodeList.size() == serverTraceableCodeList.size() && CollectionUtils.isEmpty(itemReq.getTraceableCodeList())) {
            return;
        }

        if (!CollectionUtils.isEmpty(itemReq.getTraceableCodeList())) {
            traceableCodeList.addAll(itemReq.getTraceableCodeList());
        }
        formItem.setTraceableCodeList(traceableCodeList);
        /*
          节约存储
          */
        if (extendData.isEmpty()) {
            formItem.setExtendData(null);
        }
    }

    public void updateDispensingSheetTraceableCode(UpdateTraceableCodeReq req) {
        if (CollectionUtils.isEmpty(req.getList())) {
            return;
        }
        List<DispensingSheet> sheetList = dispensingSheetEntityService.findAllBySourceSheetId(req.getChainId(), req.getChargeSheetId(), 0, 0);
        if (CollectionUtils.isEmpty(sheetList)) {
            return;
        }
        TraceCodeConfig traceCodeConfig = propertyService.getPropertyValueByKey(PropertyKey.TRACE_CODE_CONFIG, req.getClinicId(), TraceCodeConfig.class);
        if (traceCodeConfig != null && Objects.equals(traceCodeConfig.getCollectionSwitch(), YesOrNo.YES) && Objects.equals(traceCodeConfig.getDispenseCollCheckStrictMode(), YesOrNo.YES)) {
            for (DispensingSheet dispensingSheet : sheetList) {
                // 强控模式下 已发和部分发药的发药单不允许编辑追溯码
                if (DispensingUtils.isPartDispensedSheet(dispensingSheet) || dispensingSheet.getStatus() == DispensingSheet.Status.DISPENSED) {
                    throw new DispensingServiceException(DispensingServiceError.TRACE_CODE_NOT_SUPPORT_MODIFY);
                }
            }
        }


        Map<String, UpdateTraceableCodeReq.UpdateTraceableCodeItem> sourceFormItemIdToTraceCodeItem = ListUtils.toMap(req.getList(), UpdateTraceableCodeReq.UpdateTraceableCodeItem::getChargeFormItemId);

        DispensingUtils.doWithDispensingItem(sheetList, item -> {
            UpdateTraceableCodeReq.UpdateTraceableCodeItem updateTraceCodeItem = sourceFormItemIdToTraceCodeItem.get(item.getSourceFormItemId());
            SaveTraceableCodeDraftReq.TraceableCodeDraft traceableCodeDraft = new SaveTraceableCodeDraftReq.TraceableCodeDraft();
            if (updateTraceCodeItem != null) {
                traceableCodeDraft.setTraceableCodeList(updateTraceCodeItem.getTraceableCodeList());
            } else {
                traceableCodeDraft.setTraceableCodeList(new ArrayList<>());
            }
            traceableCodeDraft.setShebaoDismountingFlag(item.getShebaoDismountingFlag());
            updateFromItemTraceableCode(traceableCodeDraft, item);
        });

        dispensingSheetEntityService.saveAllV1(sheetList);

        abcCisScGoodsService.updateUseTraceCode(req.getChainId(), req.getClinicId(), sheetList, req.getEmployeeId());
    }

    public void updateDispensingSheetRemark(ChargeSheetUpdateRemarkMessage updateRemarkMessage) {
        if (updateRemarkMessage == null || updateRemarkMessage.getSceneType() == ChargeSheetUpdateRemarkMessage.UpdateRemarkSceneType.SHEET) {
            return;
        }
        String businessId = updateRemarkMessage.getBusinessId();
        String remark = updateRemarkMessage.getRemark();
        List<DispensingSheet> sheetList = dispensingSheetEntityService.findAllBySourceSheetId(updateRemarkMessage.getChainId(), updateRemarkMessage.getChargeSheetId(), 0, 0);
        if (CollectionUtils.isEmpty(sheetList)) {
            return;
        }
        if (updateRemarkMessage.getSceneType() == ChargeSheetUpdateRemarkMessage.UpdateRemarkSceneType.ITEM) {
            // item
            List<DispensingFormItem> needUpdateItemList = sheetList.stream().filter(it -> !CollectionUtils.isEmpty(it.getDispensingForms()))
                    .flatMap(it -> it.getDispensingForms().stream())
                    .filter(it -> !CollectionUtils.isEmpty(it.getDispensingFormItems()))
                    .flatMap(it -> it.getDispensingFormItems().stream())
                    .filter(it -> TextUtils.equals(it.getSourceFormItemId(), businessId)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(needUpdateItemList)) {
                return;
            }
            List<String> useRemarkFormItemIdList = new ArrayList<>();
            List<String> useSpecialFormItemIdList = new ArrayList<>();
            needUpdateItemList.forEach(formItem -> {
                if (DispensingUtils.remarkUseSpecialTypeList.contains(formItem.getProductType())) {
                    useSpecialFormItemIdList.add(formItem.getId());
                } else {
                    useRemarkFormItemIdList.add(formItem.getId());
                }
            });
            if (!useSpecialFormItemIdList.isEmpty()) {
                dispensingSheetMapper.updateDispensingSheetFormItemSpecialRequirement(updateRemarkMessage.getChainId(),
                        updateRemarkMessage.getClinicId(), useSpecialFormItemIdList, remark);
            }
            if (!useRemarkFormItemIdList.isEmpty()) {
                dispensingSheetMapper.updateDispensingSheetFormItemRemark(updateRemarkMessage.getChainId(),
                        updateRemarkMessage.getClinicId(), useRemarkFormItemIdList, remark);
            }
        } else {
            // form
            List<DispensingForm> needUpdateFormList = sheetList.stream().filter(it -> !CollectionUtils.isEmpty(it.getDispensingForms()))
                    .flatMap(it -> it.getDispensingForms().stream())
                    .filter(it -> TextUtils.equals(it.getSourceFormId(), businessId)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(needUpdateFormList)) {
                return;
            }
            List<String> formIdList = needUpdateFormList.stream().map(DispensingForm::getId).collect(Collectors.toList());
            dispensingSheetMapper.updateDispensingSheetFormRemark(updateRemarkMessage.getChainId(),
                    updateRemarkMessage.getClinicId(), formIdList, remark);
        }
    }

    public AbcListPage<DispensingFormItemView> queryDispensingFormItemByPatientOrderId(
            String chainId, String clinicId, String patientOrderId, Integer offset, Integer limit) {
        AbcListPage<DispensingFormItemView> result = new AbcListPage<>();
        result.setOffset(offset);
        result.setLimit(limit);
        int page = offset / limit;
        PageRequest pageRequest = PageRequest.of(page, limit);
        Page<DispensingFormItemV2> formItemV2Page =
                dispensingFormItemV2Repository.findAllByChainIdAndClinicIdAndPatientOrderIdAndIsDeletedAndStatusIn(chainId,
                        clinicId, patientOrderId, 0, Arrays.asList(DispenseConst.Status.DISPENSED, DispenseConst.Status.UNDISPENSED), pageRequest);
        int total = (int) formItemV2Page.getTotalElements();
        result.setTotal(total);
        List<DispensingFormItemV2> formItemV2List = formItemV2Page.getContent();
        if (CollectionUtils.isEmpty(formItemV2List)) {
            return result;
        }
        List<String> sheetIdList = formItemV2List.stream().map(DispensingFormItemV2::getDispensingSheetId).distinct().collect(Collectors.toList());
        Map<String, List<DispensingFormItemBatchInfo>> formItemIdToBatchInfoList = new HashMap<>();
        if (!CollectionUtils.isEmpty(sheetIdList)) {
            List<DispensingFormItemBatchInfo> batchInfoList = dispensingFormItemBatchInfoRepository.findAllByChainIdAndDispensingSheetIdInAndIsOldAndIsDeleted(chainId, sheetIdList, 0, 0);
            formItemIdToBatchInfoList.putAll(batchInfoList.stream().collect(Collectors.groupingBy(DispensingFormItemBatchInfo::getDispensingFormItemId)));
        }
        List<DispensingFormItemView> formItemViewList = formItemV2List.stream().map(formItemV2 -> {
            DispensingFormItemView formItemView = DispensingFormItemView.from(formItemV2);
            List<DispensingFormItemBatchInfo> formItemBatchInfoList = formItemIdToBatchInfoList.get(formItemV2.getId());
            if (!CollectionUtils.isEmpty(formItemBatchInfoList)) {
                List<DispensingFormItemBatchInfoView> batchInfoViewList = formItemBatchInfoList.stream().map(DispensingFormItemBatchInfoView::from)
                        .collect(Collectors.toList());
                formItemView.setDispensingFormItemBatches(batchInfoViewList);
            }
            return formItemView;
        }).collect(Collectors.toList());
        result.setRows(formItemViewList);
        return result;
    }

    public QueryDispensingTraceableCodeRsp queryDispensingTraceableCode(QueryDispensingTraceableCodeReq req) {
        List<String> formItemIdList = req.getFormItemIdList();
        List<DispensingFormItemV2> formItemList = dispensingFormItemV2Repository.findAllByChainIdAndClinicIdAndIdInAndIsDeleted(req.getChainId(),
                req.getClinicId(), req.getFormItemIdList(), YesOrNo.NO);
        Map<String, DispensingFormItemV2> formItemIdToItem = formItemList.stream().collect(Collectors.toMap(DispensingFormItemV2::getId, Function.identity(), (a, b) -> a));
        QueryDispensingTraceableCodeRsp rsp = new QueryDispensingTraceableCodeRsp();
        rsp.setList(new ArrayList<>());
        for (String id : formItemIdList) {
            QueryDispensingTraceableCodeRsp.QueryDispensingNoTraceableCodeItem item = new QueryDispensingTraceableCodeRsp.QueryDispensingNoTraceableCodeItem();
            item.setFormItemId(id);
            DispensingFormItemV2 formItemV2 = formItemIdToItem.get(id);
            if (Objects.nonNull(formItemV2)) {
                item.setSheetId(formItemV2.getDispensingSheetId());
                DispensingFormItemExtendData extendData = formItemV2.getExtendData();
                if (Objects.nonNull(extendData) && !CollectionUtils.isEmpty(extendData.getTraceableCodeList())) {
                    List<String> noList = extendData.getTraceableCodeList().stream().map(TraceableCode::getNo).collect(Collectors.toList());
                    item.setTraceableCodeList(noList);
                    item.setHasTraceableCodeFlag(CollectionUtils.isEmpty(noList) ? YesOrNo.NO : YesOrNo.YES);
                }
            }
            rsp.getList().add(item);
        }
        return rsp;
    }

    public AbcListPage<QueryDispensingLockingInfo> queryDispensingLockingInfo(QueryDispensingLockingInfoReq req) {
        AbcListPage<QueryDispensingLockingInfo> result = new AbcListPage<>();
        result.setRows(new ArrayList<>());
        List<DispensingFormItemV2> formItemList = dispensingFormItemV2Repository.findAllByChainIdAndClinicIdAndIdInAndIsDeleted(req.getChainId(),
                req.getClinicId(), req.getFormItemIdList(), YesOrNo.NO);
        if (CollectionUtils.isEmpty(formItemList)) {
            return result;
        }
        Map<String, DispensingFormItemV2> formItemIdToFormItem = formItemList.stream()
                .collect(Collectors.toMap(DispensingFormItemV2::getId, Function.identity(), (a, b) -> a));
        List<String> sheetIdList = formItemList.stream().map(DispensingFormItemV2::getDispensingSheetId).distinct().collect(Collectors.toList());
        List<DispensingSheetV2> sheetList = dispensingSheetV2Repository.findAllByChainIdAndClinicIdAndIdInAndIsDeleted(req.getChainId(), req.getClinicId(), sheetIdList, YesOrNo.NO);
        if (CollectionUtils.isEmpty(sheetList)) {
            return result;
        }
        Map<String, DispensingSheetV2> sheetIdToSheet = sheetList.stream()
                .collect(Collectors.toMap(DispensingSheetV2::getId, Function.identity(), (a, b) -> a));
        List<DispensingSheetV2> adviceSheetList = sheetList.stream()
                .filter(it -> DispensingUtils.isAdviceOrExecuteSourceSheetType(it.getSourceSheetType())).collect(Collectors.toList());
        List<DispensingSheetV2> usageAssociationSheetList = sheetList.stream().filter(it -> it.getSourceSheetType() == DispenseConst.SourceSheetType.USAGE_ASSOCIATION).collect(Collectors.toList());
        List<DispensingSheetV2> chargeSupplementSheetList = sheetList.stream().filter(it -> it.getSourceSheetType() == DispenseConst.SourceSheetType.CHARGE_SUPPLEMENT).collect(Collectors.toList());
        List<Long> adviceIdList = adviceSheetList.stream().filter(it -> it.getAdviceId() != null).map(DispensingSheetV2::getAdviceId).distinct().collect(Collectors.toList());
        List<Long> usageAssociationIdList = usageAssociationSheetList.stream().map(DispensingSheetV2::getSourceSheetId).map(Long::parseLong).distinct().collect(Collectors.toList());
        List<String> chargeSupplementSheetIdList = chargeSupplementSheetList.stream().map(DispensingSheetV2::getId).collect(Collectors.toList());
        CompletableFuture<Map<String, HisChargeFormItemDto>> hisChargeFormItemFuture = null;
        if (!CollectionUtils.isEmpty(chargeSupplementSheetList)) {
            List<String> sourceFormItemIdList = formItemList.stream().filter(it -> chargeSupplementSheetIdList.contains(it.getDispensingSheetId()))
                    .map(DispensingFormItemV2::getSourceFormItemId).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(sourceFormItemIdList)) {
                hisChargeFormItemFuture = dispensingOrderService.getHisChargeFormItemFuture(req.getChainId(), req.getClinicId(), sourceFormItemIdList);
            }
        }
        CompletableFuture<Map<Long, AdviceDetailView>> adviceDetailViewFuture = dispensingOrderService.getAdviceDetailViewFuture(req.getChainId(), req.getClinicId(), adviceIdList);
        CompletableFuture<Map<Long, AdviceUsageRuleItemsAddMessage>> adviceUsageRuleFuture = dispensingOrderService.getAdviceUsageRuleFuture(req.getChainId(), req.getClinicId(), usageAssociationIdList);
        Map<Long, AdviceDetailView> adviceIdToAdviceView = adviceDetailViewFuture.join();
        Map<Long, AdviceUsageRuleItemsAddMessage> usageIdToUsageRule = adviceUsageRuleFuture.join();
        Map<String, HisChargeFormItemDto> chargeFormItemIdToChargeFormItemDto = hisChargeFormItemFuture != null ? hisChargeFormItemFuture.join() : Collections.emptyMap();
        // 如果是非自动发药，查是否申请发药
        List<String> needApplySheetIdList = sheetList.stream().filter(it -> !it.isAutoDispensingMethod())
                .map(DispensingSheetV2::getId).distinct().collect(Collectors.toList());
        Map<String, DispensingSheetOrderRelDto> sheetIdToRelDto;
        if (!CollectionUtils.isEmpty(needApplySheetIdList)) {
            sheetIdToRelDto = dispensingMapper.findDispensingOrderBySheet(req.getClinicId(), needApplySheetIdList).stream()
                    .collect(Collectors.toMap(DispensingSheetOrderRelDto::getDispensingSheetId, Function.identity(), (a, b) -> a));
        } else {
            sheetIdToRelDto = new HashMap<>();
        }
        req.getFormItemIdList().forEach(formItemId -> {
            QueryDispensingLockingInfo lockingInfo = new QueryDispensingLockingInfo();
            lockingInfo.setFormItemId(formItemId);
            result.getRows().add(lockingInfo);
            DispensingFormItemV2 formItemV2 = formItemIdToFormItem.get(formItemId);
            if (Objects.isNull(formItemV2)) {
                return;
            }
            DispensingSheetV2 dispensingSheetV2 = sheetIdToSheet.get(formItemV2.getDispensingSheetId());
            if (Objects.isNull(dispensingSheetV2)) {
                return;
            }
            String summary = "";
            if (dispensingSheetV2.getAdviceId() != null) {
                AdviceDetailView adviceDetailView = adviceIdToAdviceView.get(dispensingSheetV2.getAdviceId());
                if (adviceDetailView != null) {
                    String formatStr = DateUtils.formatLocalDateTime(DateUtils.toLocalDateTime(adviceDetailView.getCreated()), DateUtils.sFormatterDateHHmm);
                    summary = formatStr + "下达医嘱";
                }
            } else {
                if (dispensingSheetV2.getSourceSheetType() == DispenseConst.SourceSheetType.USAGE_ASSOCIATION) {
                    AdviceUsageRuleItemsAddMessage usageRule = usageIdToUsageRule.get(Long.parseLong(dispensingSheetV2.getSourceSheetId()));
                    if (Objects.nonNull(usageRule)) {
                        String formatStr = DateUtils.formatLocalDateTime(DateUtils.toLocalDateTime(usageRule.getOperateTime()), DateUtils.sFormatterDateHHmm);
                        summary = formatStr + "计划执行";
                    }
                } else if (dispensingSheetV2.getSourceSheetType() == DispenseConst.SourceSheetType.CHARGE_SUPPLEMENT) {
                    HisChargeFormItemDto hisChargeFormItemDto = chargeFormItemIdToChargeFormItemDto.get(formItemId);
                    if (hisChargeFormItemDto != null) {
                        String formatStr = DateUtils.formatLocalDateTime(DateUtils.toLocalDateTime(hisChargeFormItemDto.getChargedTime()), DateUtils.sFormatterDateHHmm);
                        summary = formatStr + "记账";
                    }
                }
            }
            if (dispensingSheetV2.getStatus() == DispenseConst.Status.WAITING) {
                if (dispensingSheetV2.isAutoDispensingMethod()) {
                    lockingInfo.setStatus(QueryDispensingLockingInfo.DispensingLockingStatus.WAIT_DISPENSING);
                } else {
                    DispensingSheetOrderRelDto relDto = sheetIdToRelDto.get(dispensingSheetV2.getId());
                    if (Objects.nonNull(relDto)) {
                        if (DispensingUtils.isDispensingOrderWaitingApplyStatus(relDto.getDispensingOrderStatus())) {
                            // 待申请
                            lockingInfo.setStatus(QueryDispensingLockingInfo.DispensingLockingStatus.WAIT_APPLYING);
                        } else {
                            // 待发药，已申请
                            lockingInfo.setStatus(QueryDispensingLockingInfo.DispensingLockingStatus.WAIT_DISPENSING);
                            if (!StringUtils.isEmpty(summary)) {
                                summary += "，";
                            }
                            String formatStr = DateUtils.formatLocalDateTime(DateUtils.toLocalDateTime(relDto.getApplyDispenseTime()), DateUtils.sFormatterDateHHmm);
                            summary += formatStr + "申请领药";
                        }
                    }
                }
            }
            lockingInfo.setSummary(summary);
        });
        return result;
    }
}
