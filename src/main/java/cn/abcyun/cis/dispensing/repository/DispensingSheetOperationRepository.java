package cn.abcyun.cis.dispensing.repository;

import cn.abcyun.cis.dispensing.domain.DispensingSheetOperation;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;


public interface DispensingSheetOperationRepository extends JpaRepository<DispensingSheetOperation, String> {

    List<DispensingSheetOperation> findAllByDispensingSheetIdAndIsDeletedOrderByCreatedDescOperationTypeDesc(String dispensingSheetId, int isDeleted);

    List<DispensingSheetOperation> findAllByDispensingSheetIdAndOperationTypeInAndIsDeletedOrderByCreated(String dispensingSheetId, List<Integer> operationTypeList, int isDeleted);

    /***
     * 门诊处方打印需要同时把审方人和 调剂人一起打出来
     * */
    List<DispensingSheetOperation> findAllByDispensingSheetIdInAndOperationTypeInAndIsDeleted(List<String> dispensingSheetIds,
                                                                                              List<Integer> operationTypeList,
                                                                                              int isDeleted);

    /**
     * 查询发药单指定类型操作（无排序）
     */
    List<DispensingSheetOperation> findAllByDispensingSheetIdAndOperationTypeAndIsDeleted(String dispensingSheetId,
                                                                                          int operationType,
                                                                                          int isDeleted);

    List<DispensingSheetOperation> findAllByDispensingSheetIdInAndIsDeleted(List<String> dispensingSheetIds, int isDeleted);
}
