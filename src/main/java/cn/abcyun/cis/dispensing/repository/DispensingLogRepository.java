package cn.abcyun.cis.dispensing.repository;

import cn.abcyun.cis.dispensing.domain.DispensingLogItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

public interface DispensingLogRepository extends JpaRepository<DispensingLogItem, String> {

    @Modifying
    @Transactional
    @Query(value = "UPDATE v2_dispensing_log SET patient_id = :newPatientId WHERE chain_id = :chainId and patient_order_id = :patientOrderId and patient_id = '00000000000000000000000000000000'", nativeQuery = true)
    void updatePatientIdForAnonymousPatient(@Param("patientOrderId") String patientOrderId, @Param("chainId") String chainId, @Param("newPatientId") String newPatientId);

    List<DispensingLogItem> findAllByDispensingFormItemIdAndType(String dispensingFormId, int type);

    List<DispensingLogItem> findAllByDispensingSheetIdAndType(String dispensingSheetId, int type);

    List<DispensingLogItem> findAllByDispensingSheetIdAndTypeIn(String dispensingSheetId, List<Integer> types);

    List<DispensingLogItem> findAllByDispensingSheetIdInAndTypeIn(List<String> dispensingSheetIds, List<Integer> types);

    List<DispensingLogItem> findAllByDispensingSheetIdInAndType(List<String> dispensingSheetIds, int type);

    List<DispensingLogItem> findAllByDispensingSheetIdAndOperationIdIn(String dispensingSheetId, List<String> operationIds);

    List<DispensingLogItem> findAllByDispensingSheetIdInAndOperationIdIn(List<String> dispensingSheetIds, List<String> operationIds);
}
