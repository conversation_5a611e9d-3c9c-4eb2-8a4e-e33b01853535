package cn.abcyun.cis.dispensing.repository;

import cn.abcyun.cis.dispensing.domain.DispensingSheet;
import cn.abcyun.cis.dispensing.domain.DispensingSheetV2;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface DispensingSheetV2Repository extends JpaRepository<DispensingSheetV2, String> {

    List<DispensingSheetV2> findAllByChainIdAndClinicIdAndAdviceIdAndIsDeleted(
            String chainId,
            String clinicId,
            Long adviceId,
            int isDeleted
    );

    /**
     * 找到这个医嘱Id对应的发药单
     */
    List<DispensingSheetV2> findAllByChainIdAndClinicIdAndSourceSheetIdAndIsDeleted(
            String chainId,
            String clinicId,
            String chargeSheetId,
            int isDeleted
    );

    List<DispensingSheetV2> findAllByChainIdAndClinicIdAndAdviceIdInAndStatusInAndIsDeleted(
            String chainId,
            String clinicId,
            List<Long> adviceIdList,
            List<Integer> dispenseStatusList,
            int isDeleted
    );

    List<DispensingSheetV2> findAllByChainIdAndClinicIdAndAdviceIdInAndIsDeleted(
            String chainId,
            String clinicId,
            List<Long> adviceIdList,
            int isDeleted
    );

    List<DispensingSheetV2> findAllByChainIdAndClinicIdAndIdInAndIsDeleted(
            String chainId,
            String clinicId,
            List<String> dispensingSheetIdList,
            int isDeleted
    );

    DispensingSheetV2 findAllByChainIdAndClinicIdAndIdAndIsDeleted(
            String chainId,
            String clinicId,
            String dispensingSheetId,
            int isDeleted
    );

    List<DispensingSheetV2> findAllByChainIdAndClinicIdAndIdInAndPatientIdAndIsDeleted(
            String chainId,
            String clinicId,
            List<String> dispensingSheetIdList,
            String patientId,
            int isDeleted
    );

    List<DispensingSheetV2> findAllByChainIdAndClinicIdAndPatientOrderIdAndStatusInAndIsDeleted(
            String chainId,
            String clinicId,
            String patientOrderId,
            List<Integer> dispenseStatusList,
            int isDeleted);

    List<DispensingSheetV2> findAllByChainIdAndClinicIdAndSourceSheetIdInAndIsDeleted(String chainId,
                                                                                      String clinicId,
                                                                                      List<String> sourceSheetIdList,
                                                                                      int isDeleted);

    List<DispensingSheetV2> findAllByChainIdAndClinicIdAndAdviceIdAndDispensingMethodAndIsDeleted(
            String chainId,
            String clinicId,
            Long adviceId,
            int dispensingMethod,
            int isDeleted
    );


    DispensingSheetV2 findByIdAndChainIdAndIsDeleted(String id, String chainId, int isDeleted);
}
