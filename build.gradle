buildscript {
    repositories {
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
    }
    dependencies {
        classpath('se.transmode.gradle:gradle-docker:1.2')
    }
}


plugins {
    id 'org.asciidoctor.convert' version '1.5.3'
    id 'org.springframework.boot' version '2.3.3.RELEASE'
    id 'io.spring.dependency-management' version '1.0.10.RELEASE'
    id 'java'
}

apply plugin: 'docker'

group = 'cn.abcyun.cis'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = '1.8'

ext {
    set('springCloudVersion', "Hoxton.SR7")
    set('dockerApplication', 'abc-cis-dispensing-service')
    set('dockerRegistry', 'registry.cn-shanghai.aliyuncs.com')
    set('dockerGroup', 'byteflow')
    set('dockerVersion', 'latest')
    set('nexusSnapShotUrl', "https://packages.aliyun.com/maven/repository/105566-snapshot-k87VEs/")
    set('nexusReleaseUrl', 'https://packages.aliyun.com/maven/repository/105566-release-Sy2Ug0/')
    set('nexusUsername', 'ZLmZuu')
    set('nexusPassword', 'Nl4rmLzuy7')
}
configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}
repositories {
    mavenLocal()
    maven { url 'https://maven.aliyun.com/repository/public' }
    maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
    mavenCentral()
    jcenter()
    maven { url 'https://repo.spring.io/milestone' }

    maven {
        credentials {
            username "${nexusUsername}"
            password "${nexusPassword}"
        }
        url "${nexusReleaseUrl}"
    }
    maven {
        credentials {
            username "${nexusUsername}"
            password "${nexusPassword}"
        }
        url "${nexusSnapShotUrl}"
    }
}

dependencies {
    implementation 'cn.abcyun.bis:abc-bis-rpc-sdk:2.93.54'
    implementation 'cn.abcyun.cis:abc-cis-commons:2.1.8.58'
    implementation 'cn.abcyun.cis:abc-cis-core:0.1.84'
    implementation 'cn.abcyun.cis:abc-cis-id-generator:0.0.8'
    implementation 'cn.abcyun.common:abc-common-model:1.0.6'
    implementation 'cn.abcyun.common:abc-common-log:0.0.7'
    implementation 'org.apache.logging.log4j:log4j-api:2.15.0'
    implementation 'org.apache.logging.log4j:log4j-to-slf4j:2.15.0'
    implementation 'com.github.ben-manes.caffeine:caffeine:2.8.0'
    implementation 'io.github.openfeign:feign-httpclient'

    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-data-rest'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.cloud:spring-cloud-starter-sleuth'
    implementation 'org.springframework.cloud:spring-cloud-starter-config'
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'com.google.protobuf:protobuf-java:2.5.0'
    implementation 'commons-lang:commons-lang:2.6'
    implementation 'com.aliyun.openservices:aliyun-log-logback-appender:0.1.19'
    implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter:2.0.0'
    implementation 'com.alibaba.mq-amqp:mq-amqp-client:1.0.3'
    implementation 'org.springframework.boot:spring-boot-starter-amqp'
    implementation 'org.slf4j:slf4j-api:1.7.26'
    implementation 'org.redisson:redisson:3.11.6'
    implementation 'com.microsoft.sqlserver:mssql-jdbc'
    implementation 'io.micrometer:micrometer-registry-prometheus'
//    implementation 'net.logstash.logback:logstash-logback-encoder:3.5'
    implementation 'io.springfox:springfox-boot-starter:3.0.0'
    compileOnly 'org.projectlombok:lombok:1.18.8'
    annotationProcessor 'org.projectlombok:lombok:1.18.8'
    implementation 'org.apache.commons:commons-pool2:2.8.0'
    implementation 'org.apache.commons:commons-lang3:3.4'
    implementation('org.apache.rocketmq:rocketmq-spring-boot-starter:2.2.3') {
        exclude group: 'org.apache.tomcat', module: 'annotations-api'
    }

    implementation 'com.aliyun:aliyun-java-sdk-core:4.4.9'
    implementation 'com.squareup.okhttp3:okhttp:4.2.1'

    runtimeOnly 'mysql:mysql-connector-java'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    compile 'com.vladmihalcea:hibernate-types-5:2.4.2'
    implementation 'org.springframework.cloud:spring-cloud-starter-netflix-hystrix'
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

task buildUnpackJar(type: Copy) {
    dependsOn clean
    dependsOn bootJar
    tasks.findByName('bootJar').mustRunAfter('clean')

    from(zipTree(tasks.bootJar.outputs.files.singleFile))
    into("build/dependency")
}

task buildDocker(type: Docker) {
    dependsOn buildUnpackJar
    tag = "${dockerRegistry}/${dockerGroup}/${dockerApplication}"
    tagVersion = "${dockerVersion}"
    dockerfile = file('Dockerfile')

    doFirst {
        copy {
            from "build/dependency"
            into "${stageDir}/build/dependency"
        }
    }
}

task deployDocker(type: Exec) {
    dependsOn buildDocker
    commandLine "docker", "push", "${dockerRegistry}/${dockerGroup}/${dockerApplication}:${dockerVersion}"
}